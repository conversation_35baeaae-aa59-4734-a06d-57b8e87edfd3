steps:
  - name: gcr.io/cloud-builders/docker
    args:
      - build
      - '--no-cache'
      - '-t'
      - 'gcr.io/gtm-mixer-v3/gitlab.com/o534/gtm-mixer:$COMMIT_SHA'
      - .
    dir: .
    id: Build
  - name: gcr.io/cloud-builders/docker
    args:
      - push
      - 'gcr.io/gtm-mixer-v3/gitlab.com/o534/gtm-mixer:$COMMIT_SHA'
    id: Push
  - name: ubuntu
    args:
      - echo
      - >-
        GCR_HOSTNAME: $_GCR_HOSTNAME, PROJECT_ID: $PROJECT_ID, IMAGE_NAME:
        $_IMAGE_NAME, COMMIT_SHA: $COMMIT_SHA, DJANGO_SETTINGS_MODULE:
        $_DJANGO_SETTINGS_MODULE, DJANGO_SETTINGS_SECRET:
        $_DJANGO_SETTINGS_SECRET, DEBUG_TOOLBAR: $_DEBUG_TOOLBAR
    id: Debug Env Vars
  - name: gcr.io/google-appengine/exec-wrapper
    args:
      - '-i'
      - 'gcr.io/gtm-mixer-v3/gitlab.com/o534/gtm-mixer:$COMMIT_SHA'
      - '-e'
      - DJANGO_SETTINGS_MODULE=$_DJANGO_SETTINGS_MODULE
      - '-e'
      - DJANGO_SETTINGS_SECRET=$_DJANGO_SETTINGS_SECRET
      - '-e'
      - DEBUG_TOOLBAR=$_DEBUG_TOOLBAR
      - '--'
      - python
      - manage.py
      - collectstatic
      - '--no-input'
    id: Collect Static
  - name: gcr.io/google.com/cloudsdktool/cloud-sdk
    args:
      - run
      - deploy
      - $_SERVICE_NAME
      - '--platform=managed'
      - '--region=$_REGION'
      - '--image=gcr.io/gtm-mixer-v3/gitlab.com/o534/gtm-mixer:$COMMIT_SHA'
      - '--cpu=$_CPU'
      - '--memory=$_MEMORY'
      - '--min-instances=$_MIN_INSTANCES'
      - '--max-instances=$_MAX_INSTANCES'
      - '--timeout=$_TIMEOUT'
      - '--concurrency=$_CONCURRENCY'
      - '--allow-unauthenticated'
      - '--set-env-vars'
      - REDIS_MAX_CONNS=$_REDIS_MAX_CONNS
      - '--set-env-vars'
      - DJANGO_SETTINGS_MODULE=$_DJANGO_SETTINGS_MODULE
      - '--set-env-vars'
      - DJANGO_SETTINGS_SECRET=$_DJANGO_SETTINGS_SECRET
      - '--set-env-vars'
      - DEBUG_TOOLBAR=$_DEBUG_TOOLBAR
    id: Deploy
    entrypoint: gcloud
  - name: gcr.io/google.com/cloudsdktool/cloud-sdk
    args:
      - run
      - services
      - update-traffic
      - $_SERVICE_NAME
      - '--region=$_REGION'
      - '--to-latest'
    id: Update Traffic
    entrypoint: gcloud
options:
  logging: CLOUD_LOGGING_ONLY
