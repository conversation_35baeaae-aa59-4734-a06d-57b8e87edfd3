openapi: 3.0.3
info:
  title: Sparkle GTM API
  version: 1.0.0
  description: API documentation for Sparkle GTM platform - Google Tag Manager automation
    and analytics platform
  license:
    name: Proprietary
paths:
  /audit/audit-logs/{audit_log_id}/:
    get:
      operationId: audit_audit_logs_retrieve
      description: Get detailed audit log entry
      parameters:
      - in: path
        name: audit_log_id
        schema:
          type: string
        required: true
      tags:
      - audit
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /audit/organizations/{organization_id}/audit-logs/:
    get:
      operationId: audit_organizations_audit_logs_retrieve
      description: Get audit logs for an organization (owners and admins only)
      parameters:
      - in: path
        name: organization_id
        schema:
          type: string
        required: true
      tags:
      - audit
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /audit/projects/{project_id}/audit-versions/:
    get:
      operationId: audit_projects_audit_versions_retrieve
      description: Get audit versions for a project
      parameters:
      - in: path
        name: project_id
        schema:
          type: integer
        required: true
      tags:
      - audit
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /invitations/accept/:
    post:
      operationId: invitations_accept_create
      description: Accept an invitation
      tags:
      - invitations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /invitations/decline/:
    post:
      operationId: invitations_decline_create
      description: Decline an invitation
      tags:
      - invitations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /networks/ad-networks/:
    get:
      operationId: networks_ad_networks_retrieve
      description: Get all active ad networks
      tags:
      - networks
      security:
      - cookieAuth: []
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      - {}
      responses:
        '200':
          description: No response body
  /organizations/:
    get:
      operationId: organizations_retrieve
      description: List organizations where user is owner or member
      tags:
      - organizations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /organizations/{organization_id}/delete/:
    delete:
      operationId: organizations_delete_destroy
      description: Delete an organization with proper validation and workspace transfer
      parameters:
      - in: path
        name: organization_id
        schema:
          type: string
        required: true
      tags:
      - organizations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '204':
          description: No response body
  /organizations/{organization_id}/invitations/:
    get:
      operationId: organizations_invitations_retrieve
      description: Get organization invitations
      parameters:
      - in: path
        name: organization_id
        schema:
          type: string
        required: true
      tags:
      - organizations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /organizations/{organization_id}/invitations/{invitation_id}/cancel/:
    post:
      operationId: organizations_invitations_cancel_create
      description: Cancel an invitation
      parameters:
      - in: path
        name: invitation_id
        schema:
          type: string
        required: true
      - in: path
        name: organization_id
        schema:
          type: string
        required: true
      tags:
      - organizations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /organizations/{organization_id}/invitations/{invitation_id}/resend/:
    post:
      operationId: organizations_invitations_resend_create
      description: Resend an invitation
      parameters:
      - in: path
        name: invitation_id
        schema:
          type: string
        required: true
      - in: path
        name: organization_id
        schema:
          type: string
        required: true
      tags:
      - organizations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /organizations/{organization_id}/invite/:
    post:
      operationId: organizations_invite_create
      description: Invite a user to the organization
      parameters:
      - in: path
        name: organization_id
        schema:
          type: string
        required: true
      tags:
      - organizations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /organizations/{organization_id}/members/:
    get:
      operationId: organizations_members_retrieve
      description: Get organization members
      parameters:
      - in: path
        name: organization_id
        schema:
          type: string
        required: true
      tags:
      - organizations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /organizations/{organization_id}/members/{member_id}/:
    put:
      operationId: organizations_members_update
      description: Update organization member permissions
      parameters:
      - in: path
        name: member_id
        schema:
          type: string
        required: true
      - in: path
        name: organization_id
        schema:
          type: string
        required: true
      tags:
      - organizations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
    patch:
      operationId: organizations_members_partial_update
      description: Update organization member permissions
      parameters:
      - in: path
        name: member_id
        schema:
          type: string
        required: true
      - in: path
        name: organization_id
        schema:
          type: string
        required: true
      tags:
      - organizations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
    delete:
      operationId: organizations_members_destroy
      description: Delete organization member
      parameters:
      - in: path
        name: member_id
        schema:
          type: string
        required: true
      - in: path
        name: organization_id
        schema:
          type: string
        required: true
      tags:
      - organizations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '204':
          description: No response body
  /organizations/create/:
    post:
      operationId: organizations_create_create
      description: Create a new organization
      tags:
      - organizations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /organizations/personal-organizations/:
    get:
      operationId: organizations_personal_organizations_retrieve
      description: List personal organizations
      tags:
      - organizations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /projects/{id}/:
    get:
      operationId: projects_retrieve
      description: Get project details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - projects
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
    put:
      operationId: projects_update
      description: Update project (PUT)
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - projects
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
    patch:
      operationId: projects_partial_update
      description: Update project (PATCH)
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - projects
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
    delete:
      operationId: projects_destroy
      description: Delete project
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - projects
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '204':
          description: No response body
  /projects/{id}/ad-networks/{ad_network_id}/:
    patch:
      operationId: projects_ad_networks_partial_update
      description: Update project ad network field values
      parameters:
      - in: path
        name: ad_network_id
        schema:
          type: integer
        required: true
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - projects
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /projects/project-platform/by-project-type/{project_type_id}/:
    get:
      operationId: projects_project_platform_by_project_type_retrieve
      description: Get project platforms by project type
      parameters:
      - in: path
        name: project_type_id
        schema:
          type: integer
        required: true
      tags:
      - projects
      security:
      - cookieAuth: []
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      - {}
      responses:
        '200':
          description: No response body
  /projects/project-platforms/:
    get:
      operationId: projects_project_platforms_retrieve
      description: Get all project platforms
      tags:
      - projects
      security:
      - cookieAuth: []
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      - {}
      responses:
        '200':
          description: No response body
  /projects/project-purposes/:
    get:
      operationId: projects_project_purposes_retrieve
      description: Get all project purposes
      tags:
      - projects
      security:
      - cookieAuth: []
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      - {}
      responses:
        '200':
          description: No response body
  /projects/project-types/:
    get:
      operationId: projects_project_types_retrieve
      description: Get all project types
      tags:
      - projects
      security:
      - cookieAuth: []
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      - {}
      responses:
        '200':
          description: No response body
  /projects/project/create:
    post:
      operationId: projects_project_create_create
      description: Create a new project
      tags:
      - projects
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /projects/workspaces/{id}/projects/:
    get:
      operationId: projects_workspaces_projects_retrieve
      description: Get projects by workspace with filtering and search
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - projects
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /user/:
    get:
      operationId: user_retrieve
      description: |-
        Retrieves the authenticated user's profile.
        Returns a PublicUser schema on success, or an error schema on failure.
      tags:
      - user
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      - {}
      responses:
        '200':
          description: No response body
  /user-organizations/:
    get:
      operationId: user_organizations_retrieve
      description: Get user's organization memberships
      tags:
      - user-organizations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /user-pending-invitations/:
    get:
      operationId: user_pending_invitations_retrieve
      description: Get pending invitations for the current user
      tags:
      - user-pending-invitations
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /workspaces/:
    get:
      operationId: workspaces_retrieve
      description: Get workspaces with filtering and search
      tags:
      - workspaces
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /workspaces/{id}/:
    get:
      operationId: workspaces_retrieve_2
      description: Get workspace details
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - workspaces
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
    put:
      operationId: workspaces_update
      description: Update workspace
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - workspaces
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
    delete:
      operationId: workspaces_destroy
      description: Delete workspace
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - workspaces
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '204':
          description: No response body
  /workspaces/new/:
    post:
      operationId: workspaces_new_create
      description: Create or Update workspace
      tags:
      - workspaces
      security:
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      responses:
        '200':
          description: No response body
  /auth/login/authorize-url/:
    get:
      operationId: auth_login_authorize_url_retrieve
      description: Provides the Google OAuth2 authorization URL for the client to
        redirect the user.
      tags:
      - auth
      security:
      - cookieAuth: []
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      - {}
      responses:
        '200':
          description: No response body
  /auth/login/callback/:
    get:
      operationId: auth_login_callback_retrieve
      description: |-
        Handles the OAuth2 callback from Google.
        Exchanges the code for tokens, logs in/creates a Django user,
        establishes a Django session, and returns user details.
        The 'code' (and optionally 'state') are passed as query parameters.
      tags:
      - auth
      security:
      - cookieAuth: []
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      - {}
      responses:
        '200':
          description: No response body
  /auth/logout/:
    get:
      operationId: auth_logout_retrieve
      description: Logs out the current user by clearing their Django session.
      tags:
      - auth
      security:
      - cookieAuth: []
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      - {}
      responses:
        '200':
          description: No response body
    post:
      operationId: auth_logout_create
      description: Logs out the current user by clearing their Django session.
      tags:
      - auth
      security:
      - cookieAuth: []
      - type: apiKey
        in: header
        name: X-GTM-API-KEY
        description: API Key for external integrations (development only)
      - type: http
        scheme: bearer
        description: Session-based authentication via cookies
      - {}
      responses:
        '200':
          description: No response body
components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-GTM-API-KEY
      description: API Key for external integrations (development only)
    SessionAuth:
      type: http
      scheme: bearer
      description: Session-based authentication via cookies
    cookieAuth:
      type: apiKey
      in: cookie
      name: sessionid
servers:
- url: http://localhost:8000
  description: Development server
- url: https://mixer.gettracktion.io
  description: Production server
