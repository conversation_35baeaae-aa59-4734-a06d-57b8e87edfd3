import enum
import itertools
from typing import Optional

from core.models import BaseModel
from django.conf import settings
from django.core.validators import MinLengthValidator
from django.db import models
from google_tokens.models import EncryptedTextField
from project.models.project_model import Project
from workspaces.models import Workspace


class StapeRegion(enum.Enum):
    EU = "EU"
    GLOBAL = "GLOBAL"


class AccountType(enum.Enum):
    REGULAR = "REGULAR"
    PARTNER = "PARTNER"


class StapeTokenType(models.TextChoices):
    EU_REGULAR = "EU_REGULAR", "EU Regular"
    EU_PARTNER = "EU_PARTNER", "EU Partner"
    GLOBAL_REGULAR = "GLOBAL_REGULAR", "Global Regular"
    GLOBAL_PARTNER = "GLOBAL_PARTNER", "Global Partner"

    @classmethod
    def generate_choices(cls):
        choices = []
        for region, account_type in itertools.product(StapeRegion, AccountType):
            value = f"{region.value}_{account_type.value}"
            label = f"{region.value} {account_type.value}"
            choices.append((value, label))
        return choices

    @classmethod
    def get_choices(cls):
        return tuple(cls.generate_choices())

    @classmethod
    def eu_partner(cls):
        return cls.EU_PARTNER.value

    @classmethod
    def global_partner(cls):
        return cls.GLOBAL_PARTNER.value

    @classmethod
    def eu_regular(cls):
        return cls.EU_REGULAR.value

    @classmethod
    def global_regular(cls):
        return cls.GLOBAL_REGULAR.value


class StapeUser(BaseModel):
    localUser = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="stape_user_profiles",
        db_index=True,
    )
    identifier = models.CharField(max_length=50, unique=True, db_index=True)
    username = models.EmailField(null=True, blank=True)
    response_data = models.JSONField(default=dict)
    workspaces = models.ManyToManyField(Workspace, related_name="stape_users", blank=True)
    project = models.ForeignKey(
        Project,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="stape_user",
    )
    token_type = models.CharField(max_length=20, choices=StapeTokenType.get_choices())
    api_token = EncryptedTextField(null=True, blank=True, validators=[MinLengthValidator(1)])

    def __str__(self):
        return f"{self.localUser.username} - {self.identifier} - {self.token_type}"

    @staticmethod
    def get_stape_user_by_identifier(identifier) -> Optional["StapeUser"]:
        return StapeUser.objects.filter(identifier=identifier).first()


class StapeWorkspace(BaseModel):
    localUser = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="stape_workspace_profiles",
    )
    identifier = models.CharField(max_length=50, unique=True, db_index=True)
    username = models.EmailField(null=True, blank=True)
    response_data = models.JSONField(default=dict)
    workspaces = models.ManyToManyField(Workspace, related_name="stape_workspaces", blank=True)
    project = models.ForeignKey(
        Project,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="stape_workspace",
    )
    token_type = models.CharField(
        max_length=20,
        choices=StapeTokenType.get_choices(),
    )

    def __str__(self):
        return f"{self.localUser.username} - {self.identifier} - {self.token_type}"
