# Generated by Django 5.0.6 on 2025-05-24 14:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("projects", "0002_initial"),
        ("stape", "0001_initial"),
        ("workspaces", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="stapeuser",
            name="localUser",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="stape_user_profiles",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="stapeuser",
            name="project",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="stape_user",
                to="projects.project",
            ),
        ),
        migrations.AddField(
            model_name="stapeuser",
            name="workspaces",
            field=models.ManyToManyField(blank=True, related_name="stape_users", to="workspaces.workspace"),
        ),
        migrations.AddField(
            model_name="stapeworkspace",
            name="localUser",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="stape_workspace_profiles",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="stapeworkspace",
            name="project",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="stape_workspace",
                to="projects.project",
            ),
        ),
        migrations.AddField(
            model_name="stapeworkspace",
            name="workspaces",
            field=models.ManyToManyField(blank=True, related_name="stape_workspaces", to="workspaces.workspace"),
        ),
    ]
