# Generated by Django 5.0.6 on 2025-05-24 14:49

import core.utils.model_utils
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="StapeUser",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "identifier",
                    models.Char<PERSON>ield(db_index=True, max_length=50, unique=True),
                ),
                ("username", models.EmailField(blank=True, max_length=254, null=True)),
                ("response_data", models.J<PERSON>NField(default=dict)),
                (
                    "token_type",
                    models.CharField(
                        choices=[
                            ("EU_REGULAR", "EU REGULAR"),
                            ("EU_PARTNER", "EU PARTNER"),
                            ("GLOBAL_REGULAR", "GLOBAL REGULAR"),
                            ("GLOBAL_PARTNER", "GLOBAL PARTNER"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "api_token",
                    core.utils.model_utils.EncryptedTextField(
                        blank=True,
                        null=True,
                        validators=[django.core.validators.MinLengthValidator(1)],
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="StapeWorkspace",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "identifier",
                    models.CharField(db_index=True, max_length=50, unique=True),
                ),
                ("username", models.EmailField(blank=True, max_length=254, null=True)),
                ("response_data", models.JSONField(default=dict)),
                (
                    "token_type",
                    models.CharField(
                        choices=[
                            ("EU_REGULAR", "EU REGULAR"),
                            ("EU_PARTNER", "EU PARTNER"),
                            ("GLOBAL_REGULAR", "GLOBAL REGULAR"),
                            ("GLOBAL_PARTNER", "GLOBAL PARTNER"),
                        ],
                        max_length=20,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
