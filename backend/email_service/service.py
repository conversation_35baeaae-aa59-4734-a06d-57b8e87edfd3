from typing import Any

from django.conf import settings
from email_service.base import (
    BaseEmailService,
    EmailMessage,
    EmailServiceConfigError,
    EmailServiceFactory,
    create_simple_email,
)

# Import providers to register them
from email_service.providers import resend  # noqa: F401


class DjangoEmailService:
    """
    Django wrapper for the email service.
    Handles configuration from Django settings and provides convenience methods.
    """

    def __init__(self):
        self._service: BaseEmailService | None = None
        self._initialize_service()

    def _initialize_service(self):
        """Initialize the email service from Django settings"""
        email_config = getattr(settings, "EMAIL_SERVICE", {})

        if not email_config:
            raise EmailServiceConfigError("EMAIL_SERVICE not configured in settings")

        provider = email_config.get("PROVIDER")
        if not provider:
            raise EmailServiceConfigError("EMAIL_SERVICE.PROVIDER not specified")

        # Get provider-specific config
        provider_config = email_config.get("CONFIG", {})

        # Skip validation if API key is empty (for development)
        if not provider_config.get("api_key"):
            from email_service.base import BaseEmailService

            class MockEmailService(BaseEmailService):
                def validate_config(self):
                    pass

                def send_email(self, message):
                    print(f"[MOCK EMAIL] To: {[str(addr) for addr in message.to]}")
                    print(f"[MOCK EMAIL] Subject: {message.subject}")
                    print(f"[MOCK EMAIL] Content: {message.html_content or message.text_content}")
                    return {"id": "mock-email-id", "status": "sent"}

                def send_bulk_email(self, messages):
                    return [self.send_email(msg) for msg in messages]

            self._service = MockEmailService()
        else:
            # Create service instance
            self._service = EmailServiceFactory.create_service(provider, **provider_config)

    @property
    def service(self) -> BaseEmailService:
        """Get the underlying email service"""
        if not self._service:
            self._initialize_service()
        return self._service

    def send_email(self, message: EmailMessage) -> dict[str, Any]:
        """Send an email message"""
        return self.service.send_email(message)

    def send_bulk_email(self, messages: list[EmailMessage]) -> list[dict[str, Any]]:
        """Send multiple email messages"""
        return self.service.send_bulk_email(messages)

    def send_simple_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        text_content: str | None = None,
    ) -> dict[str, Any]:
        """Send a simple email with minimal configuration"""
        message = create_simple_email(
            to_email=to_email,
            subject=subject,
            html_content=html_content,
            text_content=text_content,
        )
        return self.send_email(message)

    def send_invitation_email(self, invitation) -> dict[str, Any]:
        """
        Send an organization invitation email.

        Args:
            invitation: OrganizationInvitation instance
        """
        from django.conf import settings

        # Build invitation URL
        invitation_url = f"{settings.FRONTEND_URL}/invitations/{invitation.token}"

        # Prepare template data
        template_data = {
            "organization_name": invitation.organization.name,
            "inviter_name": invitation.invited_by.get_display_name(),
            "role": invitation.get_role_display(),
            "invitation_url": invitation_url,
            "expires_at": invitation.expires_at.strftime("%B %d, %Y"),
            "message": f"<p>{invitation.message}</p>" if invitation.message else "",
        }

        # Create simple HTML email
        subject = f"Invitation to join {template_data['organization_name']}"
        html_content = f"""
        <h2>You're invited to join {template_data["organization_name"]}</h2>
        <p>{template_data["inviter_name"]} has invited you to join as a {template_data["role"]}.</p>
        {template_data["message"]}
        <p><a href="{template_data["invitation_url"]}">Accept Invitation</a></p>
        <p>This invitation expires on {template_data["expires_at"]}.</p>
        """

        message = create_simple_email(
            to_email=invitation.email,
            subject=subject,
            html_content=html_content,
        )

        return self.service.send_email(message)

    def send_invitation_accepted_email(self, invitation, accepted_user) -> dict[str, Any]:
        """
        Send notification email when invitation is accepted.

        Args:
            invitation: OrganizationInvitation instance
            accepted_user: User who accepted the invitation
        """
        template_data = {
            "user_name": accepted_user.get_display_name(),
            "organization_name": invitation.organization.name,
            "role": invitation.get_role_display(),
        }

        # Create simple HTML email
        subject = f"{template_data['user_name']} accepted your invitation"
        html_content = f"""
        <h2>Invitation Accepted</h2>
        <p>{template_data["user_name"]} has accepted your invitation to join {template_data["organization_name"]} as a {template_data["role"]}.</p>
        """

        message = create_simple_email(
            to_email=invitation.invited_by.email,
            subject=subject,
            html_content=html_content,
        )

        return self.service.send_email(message)

    def get_delivery_status(self, message_id: str) -> dict[str, Any]:
        """Get delivery status for a sent email"""
        return self.service.get_delivery_status(message_id)


class LazyEmailService:
    """Lazy wrapper for email service to avoid initialization at import time"""

    def __init__(self):
        self._service = None

    def _get_service(self):
        if self._service is None:
            self._service = DjangoEmailService()
        return self._service

    def send_email(self, message):
        return self._get_service().send_email(message)

    def send_bulk_email(self, messages):
        return self._get_service().send_bulk_email(messages)

    def send_simple_email(self, to_email, subject, html_content, text_content=None):
        return self._get_service().send_simple_email(to_email, subject, html_content, text_content)

    def send_invitation_email(self, invitation):
        return self._get_service().send_invitation_email(invitation)

    def send_invitation_accepted_email(self, invitation, accepted_user):
        return self._get_service().send_invitation_accepted_email(invitation, accepted_user)

    def get_delivery_status(self, message_id):
        return self._get_service().get_delivery_status(message_id)


# Global instance
email_service = LazyEmailService()
