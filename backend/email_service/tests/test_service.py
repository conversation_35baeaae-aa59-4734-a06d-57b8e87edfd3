from unittest.mock import Mock, patch

from django.contrib.auth import get_user_model
from django.test import TestCase, override_settings
from email_service.base import EmailMessage
from email_service.service import DjangoEmailService
from organizations.models import Organization, OrganizationInvitation, OrganizationType

User = get_user_model()


class EmailServiceTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
        )

        self.org = Organization.objects.create(name="Test Organization", type=OrganizationType.TEAM, owner=self.user)

    @override_settings(EMAIL_SERVICE={"PROVIDER": "resend", "CONFIG": {"api_key": "test-key"}})
    @patch("email_service.service.EmailServiceFactory.create_service")
    def test_send_invitation_email(self, mock_factory):
        """Test sending invitation email"""
        # Mock the email service
        mock_service = Mock()
        mock_factory.return_value = mock_service

        # Create email service after settings override
        email_service = DjangoEmailService()

        # Create invitation
        invitation = OrganizationInvitation.objects.create(
            organization=self.org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
            message="Join our team!",
        )

        # Send invitation email
        email_service.send_invitation_email(invitation)

        # Verify email service was called
        mock_factory.assert_called_once_with("resend", api_key="test-key")
        mock_service.send_email.assert_called_once()

        # Check the email content
        call_args = mock_service.send_email.call_args[0][0]
        self.assertIsInstance(call_args, EmailMessage)
        self.assertEqual(call_args.to[0].email, "<EMAIL>")
        self.assertIn("Test Organization", call_args.subject)
        self.assertIn("Join our team!", call_args.html_content)

    @override_settings(EMAIL_SERVICE={"PROVIDER": "resend", "CONFIG": {"api_key": "test-key"}})
    @patch("email_service.service.EmailServiceFactory.create_service")
    def test_send_invitation_accepted_email(self, mock_factory):
        """Test sending invitation accepted email"""
        # Mock the email service
        mock_service = Mock()
        mock_factory.return_value = mock_service

        # Create email service after settings override
        email_service = DjangoEmailService()

        # Create invitation and accepting user
        invitation = OrganizationInvitation.objects.create(
            organization=self.org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
        )

        accepting_user = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
            first_name="Invited",
            last_name="User",
        )

        # Send acceptance notification
        email_service.send_invitation_accepted_email(invitation, accepting_user)

        # Verify email service was called
        mock_factory.assert_called_once_with("resend", api_key="test-key")
        mock_service.send_email.assert_called_once()

        # Check the email content
        call_args = mock_service.send_email.call_args[0][0]
        self.assertIsInstance(call_args, EmailMessage)
        self.assertEqual(call_args.to[0].email, "<EMAIL>")  # Sent to inviter
        self.assertIn("accepted", call_args.subject.lower())
        self.assertIn("Invited User", call_args.html_content)

    def test_email_address_creation(self):
        """Test creating email addresses"""
        from email_service.base import create_email_address

        # Test with name
        addr1 = create_email_address("<EMAIL>", "Test User")
        self.assertEqual(addr1.email, "<EMAIL>")
        self.assertEqual(addr1.name, "Test User")

        # Test without name
        addr2 = create_email_address("<EMAIL>")
        self.assertEqual(addr2.email, "<EMAIL>")
        self.assertIsNone(addr2.name)

    def test_simple_email_creation(self):
        """Test creating simple emails"""
        from email_service.base import create_simple_email

        email = create_simple_email(
            to_email="<EMAIL>",
            from_email="<EMAIL>",
            from_name="From User",
            subject="Test Subject",
            text_content="Test text content",
            html_content="<p>Test HTML content</p>",
        )

        self.assertEqual(email.to[0].email, "<EMAIL>")
        self.assertEqual(email.from_email.email, "<EMAIL>")
        self.assertEqual(email.from_email.name, "From User")
        self.assertEqual(email.subject, "Test Subject")
        self.assertEqual(email.text_content, "Test text content")
        self.assertEqual(email.html_content, "<p>Test HTML content</p>")
