from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any


@dataclass
class EmailAttachment:
    """Represents an email attachment"""

    filename: str
    content: bytes
    content_type: str


@dataclass
class EmailAddress:
    """Represents an email address with optional name"""

    email: str
    name: str | None = None

    def __str__(self):
        if self.name:
            return f"{self.name} <{self.email}>"
        return self.email


@dataclass
class EmailMessage:
    """Represents an email message"""

    to: list[EmailAddress]
    subject: str
    html_content: str | None = None
    text_content: str | None = None
    from_email: EmailAddress | None = None
    reply_to: EmailAddress | None = None
    cc: list[EmailAddress] | None = None
    bcc: list[EmailAddress] | None = None
    attachments: list[EmailAttachment] | None = None
    headers: dict[str, str] | None = None
    tags: list[str] | None = None


class EmailServiceError(Exception):
    """Base exception for email service errors"""

    pass


class EmailServiceConfigError(EmailServiceError):
    """Raised when email service is not properly configured"""

    pass


class EmailDeliveryError(EmailServiceError):
    """Raised when email delivery fails"""

    pass


class BaseEmailService(ABC):
    """
    Abstract base class for email services.
    Provides a common interface for different email providers.
    """

    def __init__(self, **config):
        self.config = config
        self.validate_config()

    @abstractmethod
    def validate_config(self) -> None:
        """Validate the service configuration"""
        pass

    @abstractmethod
    def send_email(self, message: EmailMessage) -> dict[str, Any]:
        """
        Send an email message.

        Args:
            message: EmailMessage instance

        Returns:
            Dict containing response data from the email service

        Raises:
            EmailDeliveryError: If email delivery fails
        """
        pass

    @abstractmethod
    def send_bulk_email(self, messages: list[EmailMessage]) -> list[dict[str, Any]]:
        """
        Send multiple email messages.

        Args:
            messages: List of EmailMessage instances

        Returns:
            List of response dicts from the email service

        Raises:
            EmailDeliveryError: If any email delivery fails
        """
        pass

    def send_template_email(
        self,
        template_id: str,
        to: list[EmailAddress],
        template_data: dict[str, Any],
        **kwargs,
    ) -> dict[str, Any]:
        """
        Send an email using a template.
        Default implementation - can be overridden by specific providers.

        Args:
            template_id: Template identifier
            to: List of recipients
            template_data: Data to populate template
            **kwargs: Additional email parameters

        Returns:
            Response data from email service
        """
        raise NotImplementedError("Template email not supported by this provider")

    def get_delivery_status(self, message_id: str) -> dict[str, Any]:
        """
        Get delivery status for a sent email.
        Default implementation - can be overridden by specific providers.

        Args:
            message_id: Message ID returned from send_email

        Returns:
            Delivery status information
        """
        raise NotImplementedError("Delivery status not supported by this provider")


class EmailServiceFactory:
    """Factory for creating email service instances"""

    _services = {}

    @classmethod
    def register_service(cls, name: str, service_class: type):
        """Register an email service implementation"""
        cls._services[name] = service_class

    @classmethod
    def create_service(cls, name: str, **config) -> BaseEmailService:
        """Create an email service instance"""
        if name not in cls._services:
            raise EmailServiceConfigError(f"Unknown email service: {name}")

        service_class = cls._services[name]
        return service_class(**config)

    @classmethod
    def get_available_services(cls) -> list[str]:
        """Get list of available email services"""
        return list(cls._services.keys())


# Helper functions for creating common email objects
def create_email_address(email: str, name: str | None = None) -> EmailAddress:
    """Helper to create EmailAddress objects"""
    return EmailAddress(email=email, name=name)


def create_simple_email(
    to_email: str,
    subject: str,
    html_content: str,
    text_content: str | None = None,
    from_email: str | None = None,
    from_name: str | None = None,
) -> EmailMessage:
    """Helper to create simple EmailMessage objects"""
    to_addresses = [create_email_address(to_email)]
    from_address = None
    if from_email:
        from_address = create_email_address(from_email, from_name)

    return EmailMessage(
        to=to_addresses,
        subject=subject,
        html_content=html_content,
        text_content=text_content,
        from_email=from_address,
    )
