from typing import Any

import requests
from email_service.base import (
    BaseEmailService,
    EmailAddress,
    EmailDeliveryError,
    EmailMessage,
    EmailServiceConfigError,
    EmailServiceError,
    EmailServiceFactory,
)


class ResendEmailService(BaseEmailService):
    """
    Resend email service implementation.
    https://resend.com/docs/api-reference/emails/send-email
    """

    def __init__(self, api_key: str, from_email: str, from_name: str | None = None, **kwargs):
        self.api_key = api_key
        self.from_email = from_email
        self.from_name = from_name
        self.base_url = "https://api.resend.com"
        super().__init__(api_key=api_key, from_email=from_email, from_name=from_name, **kwargs)

    def validate_config(self) -> None:
        """Validate Resend configuration"""
        if not self.api_key:
            raise EmailServiceConfigError("Resend API key is required")
        if not self.from_email:
            raise EmailServiceConfigError("From email address is required")

    def _get_headers(self) -> dict[str, str]:
        """Get headers for Resend API requests"""
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

    def _format_email_address(self, address: EmailAddress) -> str:
        """Format email address for Resend API"""
        if address.name:
            return f"{address.name} <{address.email}>"
        return address.email

    def _format_email_addresses(self, addresses: list[EmailAddress]) -> list[str]:
        """Format list of email addresses for Resend API"""
        return [self._format_email_address(addr) for addr in addresses]

    def _prepare_email_data(self, message: EmailMessage) -> dict[str, Any]:
        """Prepare email data for Resend API"""
        data = {
            "to": self._format_email_addresses(message.to),
            "subject": message.subject,
        }

        # Set from address
        if message.from_email:
            data["from"] = self._format_email_address(message.from_email)
        else:
            # Use default from address
            if self.from_name:
                data["from"] = f"{self.from_name} <{self.from_email}>"
            else:
                data["from"] = self.from_email

        # Set content
        if message.html_content:
            data["html"] = message.html_content
        if message.text_content:
            data["text"] = message.text_content

        # Set optional fields
        if message.reply_to:
            data["reply_to"] = self._format_email_address(message.reply_to)

        if message.cc:
            data["cc"] = self._format_email_addresses(message.cc)

        if message.bcc:
            data["bcc"] = self._format_email_addresses(message.bcc)

        if message.headers:
            data["headers"] = message.headers

        if message.tags:
            data["tags"] = message.tags

        # Handle attachments
        if message.attachments:
            data["attachments"] = []
            for attachment in message.attachments:
                data["attachments"].append(
                    {
                        "filename": attachment.filename,
                        "content": attachment.content,
                        "content_type": attachment.content_type,
                    }
                )

        return data

    def send_email(self, message: EmailMessage) -> dict[str, Any]:
        """Send a single email via Resend"""
        try:
            data = self._prepare_email_data(message)

            response = requests.post(
                f"{self.base_url}/emails",
                json=data,
                headers=self._get_headers(),
                timeout=30,
            )

            if response.status_code == 200:
                return response.json()
            else:
                error_data = response.json() if response.content else {}
                error_message = error_data.get("message", f"HTTP {response.status_code}")
                raise EmailDeliveryError(f"Failed to send email: {error_message}")

        except requests.RequestException as e:
            raise EmailDeliveryError(f"Network error sending email: {str(e)}")
        except Exception as e:
            raise EmailDeliveryError(f"Unexpected error sending email: {str(e)}")

    def send_bulk_email(self, messages: list[EmailMessage]) -> list[dict[str, Any]]:
        """Send multiple emails via Resend"""
        results = []
        errors = []

        for i, message in enumerate(messages):
            try:
                result = self.send_email(message)
                results.append(result)
            except EmailDeliveryError as e:
                errors.append(f"Message {i}: {str(e)}")
                results.append({"error": str(e)})

        if errors:
            # If any emails failed, include error information
            error_summary = "; ".join(errors)
            raise EmailDeliveryError(f"Some emails failed to send: {error_summary}")

        return results

    def send_template_email(
        self,
        template_id: str,
        to: list[EmailAddress],
        template_data: dict[str, Any],
        **kwargs,
    ) -> dict[str, Any]:
        """
        Send email using Resend template.
        Note: Resend doesn't have built-in templates, so this creates a simple template system.
        """

        templates = {
            "invitation": {
                "subject": "You're invited to join {organization_name}",
                "html": """
                <h2>You're invited to join {organization_name}</h2>
                <p>Hi there!</p>
                <p>{inviter_name} has invited you to join {organization_name} on our platform.</p>
                <p><strong>Role:</strong> {role}</p>
                {message}
                <p><a href="{invitation_url}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Accept Invitation</a></p>
                <p>This invitation will expire on {expires_at}.</p>
                <p>If you don't want to join this organization, you can safely ignore this email.</p>
                """,
                "text": """
                You're invited to join {organization_name}

                Hi there!

                {inviter_name} has invited you to join {organization_name} on our platform.

                Role: {role}
                {message}

                To accept this invitation, visit: {invitation_url}

                This invitation will expire on {expires_at}.

                If you don't want to join this organization, you can safely ignore this email.
                """,
            },
            "invitation_accepted": {
                "subject": "{user_name} accepted your invitation",
                "html": """
                <h2>Invitation Accepted</h2>
                <p>Great news! {user_name} has accepted your invitation to join {organization_name}.</p>
                <p>They now have {role} access to your organization.</p>
                """,
                "text": """
                Invitation Accepted

                Great news! {user_name} has accepted your invitation to join {organization_name}.

                They now have {role} access to your organization.
                """,
            },
        }

        if template_id not in templates:
            raise EmailServiceError(f"Unknown template: {template_id}")

        template = templates[template_id]

        # Format template with data
        subject = template["subject"].format(**template_data)
        html_content = template["html"].format(**template_data)
        text_content = template["text"].format(**template_data)

        # Create email message
        message = EmailMessage(
            to=to,
            subject=subject,
            html_content=html_content,
            text_content=text_content,
            **kwargs,
        )

        return self.send_email(message)

    def get_delivery_status(self, message_id: str) -> dict[str, Any]:
        """Get delivery status from Resend"""
        try:
            response = requests.get(
                f"{self.base_url}/emails/{message_id}",
                headers=self._get_headers(),
                timeout=30,
            )

            if response.status_code == 200:
                return response.json()
            else:
                error_data = response.json() if response.content else {}
                error_message = error_data.get("message", f"HTTP {response.status_code}")
                raise EmailServiceError(f"Failed to get delivery status: {error_message}")

        except requests.RequestException as e:
            raise EmailServiceError(f"Network error getting delivery status: {str(e)}")


# Register the Resend service
EmailServiceFactory.register_service("resend", ResendEmailService)
