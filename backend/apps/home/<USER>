from django.http import HttpResponseNotAllowed
from django.shortcuts import redirect, render
from django.urls import reverse


def index(request):
    if request.user.is_authenticated:
        return redirect(reverse("home:dashboard"))

    google_login_url = reverse("socialaccount_login")

    login_props = {"login_url": google_login_url, "provider": "google"}

    context = {
        "props": {
            "login_props": login_props,
        }
    }
    return render(request, "home/index.html", context)


def dashboard(request):
    if request.method != "GET":
        return HttpResponseNotAllowed(["GET"])

    if not request.user.is_authenticated:
        return redirect(reverse("home:index"))

    return redirect(reverse("workspaces:workspace-index"))
