import uuid

from core.models import BaseModel
from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.utils.translation import gettext_lazy as _


class AuditAction(models.TextChoices):
    CREATE = "create", _("Create")
    UPDATE = "update", _("Update")
    DELETE = "delete", _("Delete")
    VIEW = "view", _("View")
    DEPLOY = "deploy", _("Deploy")
    ARCHIVE = "archive", _("Archive")
    RESTORE = "restore", _("Restore")


class AuditLog(BaseModel):
    """
    Model for tracking all changes to projects, workspaces, and other entities.
    Provides a complete audit trail with versioning support.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # User who performed the action
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="audit_logs",
    )

    # Organization context
    organization = models.ForeignKey(
        "organizations.Organization",
        on_delete=models.CASCADE,
        related_name="audit_logs",
        null=True,
        blank=True,
    )

    # Generic foreign key to track any model
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.CharField(max_length=255)
    content_object = GenericForeignKey("content_type", "object_id")

    # Action details
    action = models.CharField(max_length=20, choices=AuditAction.choices)
    description = models.TextField(blank=True)

    # Data versioning
    old_values = models.JSONField(null=True, blank=True)
    new_values = models.JSONField(null=True, blank=True)
    changed_fields = models.JSONField(default=list)  # List of field names that changed

    # Request context
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    # Additional metadata
    metadata = models.JSONField(default=dict)

    class Meta:
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["content_type", "object_id"]),
            models.Index(fields=["user", "created_at"]),
            models.Index(fields=["organization", "created_at"]),
            models.Index(fields=["action", "created_at"]),
        ]

    def __str__(self):
        user_display = self.user.get_display_name() if self.user else "System"
        return f"{user_display} {self.action} {self.content_object} at {self.created_at}"

    @classmethod
    def log_action(
        cls,
        user,
        action,
        obj,
        organization=None,
        description="",
        old_values=None,
        new_values=None,
        changed_fields=None,
        request=None,
        **metadata,
    ):
        """
        Convenience method to create audit log entries.

        Args:
            user: User performing the action
            action: AuditAction choice
            obj: The object being acted upon
            organization: Organization context
            description: Human-readable description
            old_values: Previous values (for updates)
            new_values: New values (for updates)
            changed_fields: List of changed field names
            request: HTTP request object for context
            **metadata: Additional metadata
        """

        # Extract request context
        ip_address = None
        user_agent = ""
        if request:
            ip_address = cls._get_client_ip(request)
            user_agent = request.META.get("HTTP_USER_AGENT", "")

        return cls.objects.create(
            user=user,
            organization=organization,
            content_object=obj,
            action=action,
            description=description,
            old_values=old_values,
            new_values=new_values,
            changed_fields=changed_fields or [],
            ip_address=ip_address,
            user_agent=user_agent,
            metadata=metadata,
        )

    @staticmethod
    def _get_client_ip(request):
        """Extract client IP from request"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        ip = x_forwarded_for.split(",")[0] if x_forwarded_for else request.META.get("REMOTE_ADDR")
        return ip


class ProjectAuditVersion(BaseModel):
    """
    Model for storing project versions/snapshots for audit purposes.
    Each significant change creates a new version.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.ForeignKey(
        "projects.Project",
        on_delete=models.CASCADE,
        related_name="audit_versions",
    )

    # Version information
    version_number = models.PositiveIntegerField()
    version_name = models.CharField(max_length=255, blank=True)
    description = models.TextField(blank=True)

    # User who created this version
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="audit_versions_created",
    )

    # Snapshot of project data at this version
    project_data = models.JSONField()
    raw_data = models.JSONField(null=True, blank=True)
    deploy_data = models.JSONField(null=True, blank=True)

    # Version metadata
    is_major = models.BooleanField(default=False)  # Major vs minor version
    is_deployed = models.BooleanField(default=False)  # Was this version deployed
    deployed_at = models.DateTimeField(null=True, blank=True)

    # Tags for easy identification
    tags = models.JSONField(default=list)

    class Meta:
        ordering = ["-version_number"]
        unique_together = ["project", "version_number"]
        indexes = [
            models.Index(fields=["project", "version_number"]),
            models.Index(fields=["project", "created_at"]),
        ]

    def __str__(self):
        return f"{self.project.name} v{self.version_number}"

    def save(self, *args, **kwargs):
        if not self.version_number:
            # Auto-increment version number
            last_version = ProjectAuditVersion.objects.filter(project=self.project).order_by("-version_number").first()
            self.version_number = (last_version.version_number + 1) if last_version else 1

        super().save(*args, **kwargs)

    @classmethod
    def create_snapshot(cls, project, user, description="", is_major=False, tags=None):
        """
        Create a new version snapshot of a project.
        """
        return cls.objects.create(
            project=project,
            created_by=user,
            description=description,
            is_major=is_major,
            project_data={
                "name": project.name,
                "status": project.status,
                "server_supported": project.server_supported,
                "project_type_id": project.project_type_id,
                "project_platform_id": project.project_platform_id,
                "subdomain_id": project.subdomain_id,
            },
            raw_data=project.raw_data,
            deploy_data=project.deploy_data,
            tags=tags or [],
        )
