from rest_framework import serializers


class UserSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    username = serializers.CharField()
    email = serializers.EmailField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()

    def to_representation(self, user):
        if user is None:
            return None
        return {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
        }


class ContentTypeSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    app_label = serializers.CharField()
    model = serializers.CharField()

    def to_representation(self, content_type):
        return {
            "id": content_type.id,
            "app_label": content_type.app_label,
            "model": content_type.model,
        }


class AuditLogSerializer(serializers.Serializer):
    id = serializers.CharField()
    user = UserSerializer(required=False, allow_null=True)
    organization_id = serializers.Char<PERSON>ield(required=False, allow_null=True)
    content_type = ContentTypeSerializer()
    object_id = serializers.CharField()
    action = serializers.CharField()
    description = serializers.CharField()
    old_values = serializers.DictField(required=False, allow_null=True)
    new_values = serializers.DictField(required=False, allow_null=True)
    changed_fields = serializers.ListField(child=serializers.CharField(), default=list)
    ip_address = serializers.CharField(required=False, allow_null=True)
    user_agent = serializers.CharField(default="")
    metadata = serializers.DictField(default=dict)
    created_at = serializers.DateTimeField()
    updated_at = serializers.DateTimeField()

    def to_representation(self, audit_log):
        user_serializer = UserSerializer()
        content_type_serializer = ContentTypeSerializer()

        return {
            "id": str(audit_log.id),
            "user": (user_serializer.to_representation(audit_log.user) if audit_log.user else None),
            "organization_id": (str(audit_log.organization.id) if audit_log.organization else None),
            "content_type": content_type_serializer.to_representation(audit_log.content_type),
            "object_id": audit_log.object_id,
            "action": audit_log.action,
            "description": audit_log.description,
            "old_values": audit_log.old_values,
            "new_values": audit_log.new_values,
            "changed_fields": audit_log.changed_fields,
            "ip_address": audit_log.ip_address,
            "user_agent": audit_log.user_agent,
            "metadata": audit_log.metadata,
            "created_at": audit_log.created_at,
            "updated_at": audit_log.updated_at,
        }


class ProjectAuditVersionSerializer(serializers.Serializer):
    id = serializers.CharField()
    project_id = serializers.IntegerField()
    version_number = serializers.IntegerField()
    version_name = serializers.CharField(default="")
    description = serializers.CharField(default="")
    created_by = UserSerializer(required=False, allow_null=True)
    project_data = serializers.DictField(default=dict)
    raw_data = serializers.DictField(required=False, allow_null=True)
    deploy_data = serializers.DictField(required=False, allow_null=True)
    is_major = serializers.BooleanField(default=False)
    is_deployed = serializers.BooleanField(default=False)
    deployed_at = serializers.DateTimeField(required=False, allow_null=True)
    tags = serializers.ListField(child=serializers.CharField(), default=list)
    created_at = serializers.DateTimeField()
    updated_at = serializers.DateTimeField()

    def to_representation(self, version):
        user_serializer = UserSerializer()

        return {
            "id": str(version.id),
            "project_id": version.project.id,
            "version_number": version.version_number,
            "version_name": version.version_name,
            "description": version.description,
            "created_by": (user_serializer.to_representation(version.created_by) if version.created_by else None),
            "project_data": version.project_data,
            "raw_data": version.raw_data,
            "deploy_data": version.deploy_data,
            "is_major": version.is_major,
            "is_deployed": version.is_deployed,
            "deployed_at": version.deployed_at,
            "tags": version.tags,
            "created_at": version.created_at,
            "updated_at": version.updated_at,
        }


# Backward compatibility
UserSchema = UserSerializer
ContentTypeSchema = ContentTypeSerializer
AuditLogSchema = AuditLogSerializer
ProjectAuditVersionSchema = ProjectAuditVersionSerializer
