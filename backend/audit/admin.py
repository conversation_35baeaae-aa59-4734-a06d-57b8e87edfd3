from audit.models import AuditLog, ProjectAuditVersion
from django.contrib import admin


@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    list_display = ["user", "action", "content_object", "organization", "created_at"]
    list_filter = ["action", "created_at", "content_type"]
    search_fields = ["user__email", "user__username", "description"]
    readonly_fields = [
        "id",
        "user",
        "organization",
        "content_type",
        "object_id",
        "content_object",
        "action",
        "description",
        "old_values",
        "new_values",
        "changed_fields",
        "ip_address",
        "user_agent",
        "metadata",
        "created_at",
        "updated_at",
    ]

    fieldsets = (
        (None, {"fields": ("user", "organization", "action", "description")}),
        ("Target Object", {"fields": ("content_type", "object_id", "content_object")}),
        (
            "Changes",
            {
                "fields": ("old_values", "new_values", "changed_fields"),
                "classes": ("collapse",),
            },
        ),
        (
            "Request Context",
            {"fields": ("ip_address", "user_agent"), "classes": ("collapse",)},
        ),
        ("Metadata", {"fields": ("metadata",), "classes": ("collapse",)}),
        (
            "Timestamps",
            {"fields": ("id", "created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(ProjectAuditVersion)
class ProjectAuditVersionAdmin(admin.ModelAdmin):
    list_display = [
        "project",
        "version_number",
        "version_name",
        "created_by",
        "is_major",
        "is_deployed",
        "created_at",
    ]
    list_filter = ["is_major", "is_deployed", "created_at"]
    search_fields = [
        "project__name",
        "version_name",
        "description",
        "created_by__email",
    ]
    readonly_fields = ["id", "version_number", "created_at", "updated_at"]

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "project",
                    "version_number",
                    "version_name",
                    "description",
                    "created_by",
                )
            },
        ),
        (
            "Version Info",
            {"fields": ("is_major", "is_deployed", "deployed_at", "tags")},
        ),
        (
            "Data Snapshot",
            {
                "fields": ("project_data", "raw_data", "deploy_data"),
                "classes": ("collapse",),
            },
        ),
        (
            "Timestamps",
            {"fields": ("id", "created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )
