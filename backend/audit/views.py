from audit.models import AuditLog, ProjectAuditVersion
from audit.schemas import AuditLogSerializer, ProjectAuditVersionSerializer
from core.authentication import GTMSessionAuthentication
from core.drf_utils import create_error_response, create_success_response
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from organizations.models import Organization, Permission
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView


@method_decorator(csrf_exempt, name="dispatch")
class OrganizationAuditLogsView(APIView):
    """Get audit logs for an organization (owners and admins only)"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, organization_id):
        """Get audit logs for an organization (owners and admins only)"""
        try:
            organization = Organization.objects.get(id=organization_id)
        except Organization.DoesNotExist:
            return create_error_response(message="Organization not found", status_code=status.HTTP_404_NOT_FOUND)

        # Check if user has permission to view audit logs (owners and admins only)
        if not organization.has_permission(request.user, Permission.VIEW):
            return create_error_response(
                message="You do not have permission to view audit logs for this organization",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        # Only owners and admins can view audit logs
        user_is_owner = organization.owner == request.user
        user_is_admin = False
        try:
            member = organization.members.get(user=request.user, is_active=True)
            user_is_admin = member.role == "admin"
        except Exception:
            pass

        if not (user_is_owner or user_is_admin):
            return create_error_response(
                message="Only organization owners and admins can view audit logs",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        # Get audit logs for this organization
        audit_logs = (
            AuditLog.objects.filter(organization=organization)
            .select_related("user", "content_type")
            .order_by("-created_at")
        )

        serializer = AuditLogSerializer(audit_logs, many=True)
        return create_success_response(data=serializer.data, message="Audit logs retrieved successfully")


@method_decorator(csrf_exempt, name="dispatch")
class ProjectAuditVersionsView(APIView):
    """Get audit versions for a project"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, project_id):
        """Get audit versions for a project"""
        # Import here to avoid circular imports
        from project.models.project_model import Project

        try:
            project = Project.objects.select_related("workspace__organization").get(id=project_id)
        except Project.DoesNotExist:
            return create_error_response(message="Project not found", status_code=status.HTTP_404_NOT_FOUND)

        # Check if user has access to this project
        workspace = project.workspace
        has_access = False

        # Check direct workspace ownership
        if workspace.owner == request.user:
            has_access = True
        # Check organization membership
        elif workspace.organization:
            has_access = workspace.organization.has_permission(request.user, Permission.VIEW)

        if not has_access:
            return create_error_response(
                message="You do not have permission to view this project's audit history",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        # Get audit versions for this project
        versions = (
            ProjectAuditVersion.objects.filter(project=project).select_related("created_by").order_by("-version_number")
        )

        serializer = ProjectAuditVersionSerializer(versions, many=True)
        return create_success_response(
            data=serializer.data,
            message="Project audit versions retrieved successfully",
        )


@method_decorator(csrf_exempt, name="dispatch")
class AuditLogDetailView(APIView):
    """Get detailed audit log entry"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, audit_log_id):
        """Get detailed audit log entry"""
        try:
            audit_log = AuditLog.objects.select_related("user", "organization", "content_type").get(id=audit_log_id)
        except AuditLog.DoesNotExist:
            return create_error_response(message="Audit log not found", status_code=status.HTTP_404_NOT_FOUND)

        # Check if user has permission to view this audit log
        if audit_log.organization:
            # Check organization permissions
            if not audit_log.organization.has_permission(request.user, Permission.VIEW):
                return create_error_response(
                    message="You do not have permission to view this audit log",
                    status_code=status.HTTP_403_FORBIDDEN,
                )

            # Only owners and admins can view detailed audit logs
            user_is_owner = audit_log.organization.owner == request.user
            user_is_admin = False
            try:
                member = audit_log.organization.members.get(user=request.user, is_active=True)
                user_is_admin = member.role == "admin"
            except Exception:
                pass

            if not (user_is_owner or user_is_admin):
                return create_error_response(
                    message="Only organization owners and admins can view detailed audit logs",
                    status_code=status.HTTP_403_FORBIDDEN,
                )
        else:
            # For logs without organization context, only allow if user is the actor
            if audit_log.user != request.user:
                return create_error_response(
                    message="You do not have permission to view this audit log",
                    status_code=status.HTTP_403_FORBIDDEN,
                )

        serializer = AuditLogSerializer(audit_log)
        return create_success_response(data=serializer.data, message="Audit log retrieved successfully")


# Backward compatibility functions for existing URL patterns
get_organization_audit_logs = OrganizationAuditLogsView.as_view()
get_project_audit_versions = ProjectAuditVersionsView.as_view()
get_audit_log_detail = AuditLogDetailView.as_view()
