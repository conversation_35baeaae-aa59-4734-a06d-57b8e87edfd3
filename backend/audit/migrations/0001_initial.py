# Generated by Django 5.2.4 on 2025-07-31 23:06

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("organizations", "0001_initial"),
        ("projects", "0003_projectplatform_active_snapshot"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AuditLog",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("object_id", models.CharField(max_length=255)),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("create", "Create"),
                            ("update", "Update"),
                            ("delete", "Delete"),
                            ("view", "View"),
                            ("deploy", "Deploy"),
                            ("archive", "Archive"),
                            ("restore", "Restore"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("old_values", models.JSONField(blank=True, null=True)),
                ("new_values", models.JSONField(blank=True, null=True)),
                ("changed_fields", models.JSONField(default=list)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("metadata", models.JSONField(default=dict)),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="audit_logs",
                        to="organizations.organization",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="audit_logs",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["content_type", "object_id"],
                        name="audit_audit_content_4c2ead_idx",
                    ),
                    models.Index(
                        fields=["user", "created_at"],
                        name="audit_audit_user_id_a3c2bc_idx",
                    ),
                    models.Index(
                        fields=["organization", "created_at"],
                        name="audit_audit_organiz_01c53f_idx",
                    ),
                    models.Index(
                        fields=["action", "created_at"],
                        name="audit_audit_action_766c6d_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="ProjectAuditVersion",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("version_number", models.PositiveIntegerField()),
                ("version_name", models.CharField(blank=True, max_length=255)),
                ("description", models.TextField(blank=True)),
                ("project_data", models.JSONField()),
                ("raw_data", models.JSONField(blank=True, null=True)),
                ("deploy_data", models.JSONField(blank=True, null=True)),
                ("is_major", models.BooleanField(default=False)),
                ("is_deployed", models.BooleanField(default=False)),
                ("deployed_at", models.DateTimeField(blank=True, null=True)),
                ("tags", models.JSONField(default=list)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="audit_versions_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="audit_versions",
                        to="projects.project",
                    ),
                ),
            ],
            options={
                "ordering": ["-version_number"],
                "indexes": [
                    models.Index(
                        fields=["project", "version_number"],
                        name="audit_proje_project_abf8ae_idx",
                    ),
                    models.Index(
                        fields=["project", "created_at"],
                        name="audit_proje_project_327124_idx",
                    ),
                ],
                "unique_together": {("project", "version_number")},
            },
        ),
    ]
