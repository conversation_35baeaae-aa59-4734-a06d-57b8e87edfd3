from audit.signals import set_current_request, set_current_user


class AuditMiddleware:
    """
    Middleware to capture request context for audit logging.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Set request and user in thread-local storage
        set_current_request(request)
        if hasattr(request, "user") and request.user.is_authenticated:
            set_current_user(request.user)
        else:
            set_current_user(None)

        response = self.get_response(request)

        # Clean up thread-local storage
        set_current_request(None)
        set_current_user(None)

        return response
