import threading

from audit.models import <PERSON>t<PERSON><PERSON>, AuditLog, ProjectAuditVersion
from django.contrib.auth import get_user_model
from django.db.models.signals import post_delete, post_save, pre_save
from django.dispatch import receiver
from project.models.project_model import Project
from workspaces.models import Workspace

User = get_user_model()

# Thread-local storage for request context

_thread_locals = threading.local()


def set_current_request(request):
    """Set the current request in thread-local storage"""
    _thread_locals.request = request


def get_current_request():
    """Get the current request from thread-local storage"""
    return getattr(_thread_locals, "request", None)


def set_current_user(user):
    """Set the current user in thread-local storage"""
    _thread_locals.user = user


def get_current_user():
    """Get the current user from thread-local storage"""
    return getattr(_thread_locals, "user", None)


@receiver(pre_save, sender=Project)
def capture_project_old_values(sender, instance, **kwargs):
    """Capture old values before project update"""
    if instance.pk:
        try:
            old_instance = Project.objects.get(pk=instance.pk)
            instance._old_values = {
                "name": old_instance.name,
                "status": old_instance.status,
                "server_supported": old_instance.server_supported,
                "raw_data": old_instance.raw_data,
                "deploy_data": old_instance.deploy_data,
            }
        except Project.DoesNotExist:
            instance._old_values = None
    else:
        instance._old_values = None


@receiver(post_save, sender=Project)
def log_project_changes(sender, instance, created, **kwargs):
    """Log project creation and updates"""
    user = get_current_user()
    request = get_current_request()

    # Get organization from workspace
    organization = getattr(instance.workspace, "organization", None)

    if created:
        # Log project creation
        AuditLog.log_action(
            user=user,
            action=AuditAction.CREATE,
            obj=instance,
            organization=organization,
            description=f"Created project '{instance.name}'",
            new_values={
                "name": instance.name,
                "status": instance.status,
                "server_supported": instance.server_supported,
            },
            request=request,
        )

        # Create initial version
        if user:
            ProjectAuditVersion.create_snapshot(
                project=instance,
                user=user,
                description="Initial version",
                is_major=True,
                tags=["initial"],
            )
    else:
        # Log project updates
        old_values = getattr(instance, "_old_values", None)
        if old_values:
            new_values = {
                "name": instance.name,
                "status": instance.status,
                "server_supported": instance.server_supported,
                "raw_data": instance.raw_data,
                "deploy_data": instance.deploy_data,
            }

            # Find changed fields
            changed_fields = []
            for field, old_value in old_values.items():
                if old_value != new_values.get(field):
                    changed_fields.append(field)

            if changed_fields:
                AuditLog.log_action(
                    user=user,
                    action=AuditAction.UPDATE,
                    obj=instance,
                    organization=organization,
                    description=f"Updated project '{instance.name}': {', '.join(changed_fields)}",
                    old_values=old_values,
                    new_values=new_values,
                    changed_fields=changed_fields,
                    request=request,
                )

                # Create version snapshot for significant changes
                significant_fields = ["raw_data", "deploy_data", "status"]
                if any(field in changed_fields for field in significant_fields) and user:
                    ProjectAuditVersion.create_snapshot(
                        project=instance,
                        user=user,
                        description=f"Updated: {', '.join(changed_fields)}",
                        is_major="status" in changed_fields,
                    )


@receiver(post_delete, sender=Project)
def log_project_deletion(sender, instance, **kwargs):
    """Log project deletion"""
    user = get_current_user()
    request = get_current_request()

    # Get organization from workspace
    organization = getattr(instance.workspace, "organization", None)

    AuditLog.log_action(
        user=user,
        action=AuditAction.DELETE,
        obj=instance,
        organization=organization,
        description=f"Deleted project '{instance.name}'",
        old_values={
            "name": instance.name,
            "status": instance.status,
        },
        request=request,
    )


@receiver(pre_save, sender=Workspace)
def capture_workspace_old_values(sender, instance, **kwargs):
    """Capture old values before workspace update"""
    if instance.pk:
        try:
            old_instance = Workspace.objects.get(pk=instance.pk)
            instance._old_values = {
                "title": old_instance.title,
                "description": old_instance.description,
                "status": old_instance.status,
            }
        except Workspace.DoesNotExist:
            instance._old_values = None
    else:
        instance._old_values = None


@receiver(post_save, sender=Workspace)
def log_workspace_changes(sender, instance, created, **kwargs):
    """Log workspace creation and updates"""
    user = get_current_user()
    request = get_current_request()

    # Get organization
    organization = getattr(instance, "organization", None)

    if created:
        # Log workspace creation
        AuditLog.log_action(
            user=user,
            action=AuditAction.CREATE,
            obj=instance,
            organization=organization,
            description=f"Created workspace '{instance.title}'",
            new_values={
                "title": instance.title,
                "description": instance.description,
                "status": instance.status,
            },
            request=request,
        )
    else:
        # Log workspace updates
        old_values = getattr(instance, "_old_values", None)
        if old_values:
            new_values = {
                "title": instance.title,
                "description": instance.description,
                "status": instance.status,
            }

            # Find changed fields
            changed_fields = []
            for field, old_value in old_values.items():
                if old_value != new_values.get(field):
                    changed_fields.append(field)

            if changed_fields:
                AuditLog.log_action(
                    user=user,
                    action=AuditAction.UPDATE,
                    obj=instance,
                    organization=organization,
                    description=f"Updated workspace '{instance.title}': {', '.join(changed_fields)}",
                    old_values=old_values,
                    new_values=new_values,
                    changed_fields=changed_fields,
                    request=request,
                )


@receiver(post_delete, sender=Workspace)
def log_workspace_deletion(sender, instance, **kwargs):
    """Log workspace deletion"""
    user = get_current_user()
    request = get_current_request()

    # Get organization
    organization = getattr(instance, "organization", None)

    AuditLog.log_action(
        user=user,
        action=AuditAction.DELETE,
        obj=instance,
        organization=organization,
        description=f"Deleted workspace '{instance.title}'",
        old_values={
            "title": instance.title,
            "description": instance.description,
            "status": instance.status,
        },
        request=request,
    )
