/* static/admin/css/custom_admin_reports.css */

.report-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1em;
  margin-top: 0.5em;
  font-size: 0.9em; /* Slightly smaller font for tables */
}

.report-table th,
.report-table td {
  border: 1px solid #ccc;
  padding: 6px 8px; /* Adjust padding */
  text-align: left;
  vertical-align: top;
}

.report-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.report-table td pre {
  white-space: pre-wrap; /* CSS3 */
  white-space: -moz-pre-wrap; /* Firefox */
  white-space: -pre-wrap; /* Opera <7 */
  white-space: -o-pre-wrap; /* Opera 7 */
  word-wrap: break-word; /* IE */
  margin: 0;
  font-size: 1em; /* Inherit from table or set specifically */
  background-color: #f9f9f9;
  border: 1px dashed #ddd;
  padding: 5px;
}

/* Styling for collapsible links */
h5 a,
h4 a {
  color: var(--accent, #007bff); /* Use Django admin's accent color if possible */
  text-decoration: none;
  cursor: pointer;
}

h5 a:hover,
h4 a:hover {
  text-decoration: underline;
}

/* Styling for the main titles of sections */
h4 {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-size: 1.2em;
  color:  #333
  border-bottom: 1px solid #eee;
  padding-bottom: 0.2em;
}

h5 {
  margin-top: 0.8em;
  margin-bottom: 0.3em;
  font-size: 1em;
  color:  #555;
}

/* Styling for preformatted JSON blocks */
pre {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 10px;
  border-radius: 4px;
  font-size: 0.85em;
  line-height: 1.4;
  white-space: pre-wrap; /* Handles long lines */
  word-break: break-all; /* Breaks long words/strings */
  max-height: 400px; /* Add a max height for very long JSON */
  overflow-y: auto; /* Add scroll for overflow */
}

/* Styling for the hidden/collapsible divs */
div[id^='snap'][style*='display:none'],
div[id^='full_json_'][style*='display:none'],
div[id$='_details'][style*='display:none'] {
  /* You can add specific styles for when they are hidden if needed, */
  /* but display:none already handles hiding. */
}

div[id^='snap'][style*='display:block'],
div[id^='full_json_'][style*='display:block'],
div[id$='_details'][style*='display:block'] {
  margin-top: 5px;
  padding: 10px;
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-radius: 3px;
}

/* Style for list items in summary */
ul {
  list-style-type: disc;
  margin-left: 20px;
  padding-left: 0;
}

ul li {
  margin-bottom: 0.3em;
}

hr {
  border: 0;
  height: 1px;
  background-color: #eee;
  margin-top: 1em;
  margin-bottom: 1em;
}
