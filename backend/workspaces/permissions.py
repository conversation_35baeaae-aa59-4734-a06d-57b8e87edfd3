# from django.http import HttpRequest
#
# from authentication.utils import GTMAuth
# from workspaces.models import Workspace
#
#
# class WorkspaceModifierAuth(GTMAuth):  # Removed - replaced with DRF permissions
#     def __init__(
#         self,
#         permissions: list[str] = None,
#         message_response: dict = None,
#         *args,
#         **kwargs,
#     ):
#         super().__init__(permissions, message_response, *args, **kwargs)
#
#     def authenticate(self, request, **kwargs):
#         auth_result = super().authenticate(request)
#         if isinstance(auth_result, tuple):
#             return auth_result
#
#         return self.authorize(request, **kwargs)
#
#     def authorize(self, request: HttpRequest, **kwargs):
#         if not request.user.is_authenticated:
#             return 403, self.message_response
#
#         is_get = request.method == "GET"
#
#         # if request.method == "GET":
#         #     return super().authorize(request)
#
#         workspace_id = kwargs.get("id")
#
#         if not workspace_id and is_get:
#             return super().authorize(request)
#
#         if not workspace_id:
#             return 400, "Method requires a workspace id"
#
#         # Check workspace access through ownership or organization membership
#         try:
#             workspace = Workspace.objects.get(id=workspace_id)
#         except Workspace.DoesNotExist:
#             return 404, {"message": "Workspace not found."}
#
#         # Check if user has access to this workspace
#         has_access = False
#
#         # Check direct ownership
#         if workspace.owner == request.user:
#             has_access = True
#
#         # Check organization membership if workspace belongs to an organization
#         elif workspace.organization:
#             # Determine required permission based on request method
#             required_permission = "view"
#             if request.method in ["POST", "PUT", "PATCH"]:
#                 required_permission = "edit"
#             elif request.method == "DELETE":
#                 required_permission = "delete"
#
#             has_access = workspace.organization.has_permission(request.user, required_permission)
#
#         if not has_access:
#             return 403, {"message": "You do not have permission to access this workspace."}
#
#         # Store workspace in request for later use
#         request.workspace = workspace
#         return super().authorize(request, **kwargs)
