from apps.workspaces.models.project_models import Project
from apps.workspaces.models.stape_models import (
    StapeTokenType,
    StapeUser,
    StapeWorkspace,
)
from apps.workspaces.permissions import IsWorkspaceOwner
from apps.workspaces.serializers.stape_serializer import (
    StapeUserSerializer,
    StapeWorkspaceSerializer,
)
from core.utils.response import api_response
from django.shortcuts import get_object_or_404
from rest_framework import filters, permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response


def parse_drf_errors(errors):
    """
    Parses DRF's validation error dictionary into a single string.
    Example input:
    {
        "identifier": ["stape user with this identifier already exists."],
        "project": ["stape user with this project already exists."]
    }
    Example output:
    "Identifier: stape user with this identifier already exists. Project: stape user with this project already exists."
    """
    error_messages = []
    if isinstance(errors, dict):
        for field, messages in errors.items():
            field_name = field.replace("_", " ").capitalize()
            message_text = " ".join(messages)
            error_messages.append(f"{field_name}: {message_text}")
    elif isinstance(errors, list):
        # Handle non-field errors (less common for unique constraints)
        error_messages.extend(errors)
    else:
        # Handle unexpected error format
        return str(errors)

    return ". ".join(error_messages)


# -------------------------------------


class StapeUserViewSet(viewsets.ModelViewSet):
    queryset = StapeUser.objects.all()
    serializer_class = StapeUserSerializer
    permission_classes = [permissions.IsAuthenticated, IsWorkspaceOwner]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["identifier", "username"]
    ordering_fields = ["created_at", "updated_at"]
    ordering = ["-created_at"]

    def get_queryset(self):
        queryset = super().get_queryset()

        identifier = self.request.query_params.get("identifier", None)
        username = self.request.query_params.get("username", None)
        project_id = self.request.query_params.get("project", None)
        workspace_id = self.request.query_params.get("workspace", None)
        token_type = self.request.query_params.get("token_type", None)

        if identifier:
            queryset = queryset.filter(identifier=identifier)
        if username:
            queryset = queryset.filter(username=username)
        if project_id:
            queryset = queryset.filter(project_id=project_id)
        if workspace_id:
            queryset = queryset.filter(workspaces__id=workspace_id)
        if token_type:
            queryset = queryset.filter(token_type=token_type)

        return queryset

    def create(self, request, *args, **kwargs):
        token_type = request.data.get("token_type")
        project_id = request.data.get("project")

        if token_type not in StapeTokenType.values:
            return Response({"message": "Invalid token_type"}, status=status.HTTP_400_BAD_REQUEST)

        if not project_id:
            return Response(
                {"message": "Project ID is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            project = Project.objects.get(pk=project_id)
            workspace = project.workspace
        except Project.DoesNotExist:
            return Response({"message": "Project not found"}, status=status.HTTP_404_NOT_FOUND)

        local_user = request.user

        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except Exception as e:
            error_message = parse_drf_errors(e.detail)
            return Response({"message": error_message}, status=status.HTTP_400_BAD_REQUEST)

        serializer.save(localUser=local_user, workspaces=[workspace], project=project)

        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @action(detail=False, methods=["get"])
    def me(self, request):
        stape_user = get_object_or_404(StapeUser, localUser=request.user)
        serializer = self.get_serializer(stape_user)
        return Response(serializer.data)

    @action(detail=True, methods=["get"])
    def workspaces(self, request, pk=None):
        stape_user = self.get_object()
        workspaces = stape_user.workspaces.all()
        return Response([w.id for w in workspaces])

    @action(detail=True, methods=["get"])
    def project(self, request, pk=None):
        stape_user = self.get_object()
        return Response(stape_user.project.id if stape_user.project else None)


class StapeWorkspaceViewSet(viewsets.ModelViewSet):
    queryset = StapeWorkspace.objects.all()
    serializer_class = StapeWorkspaceSerializer
    permission_classes = [permissions.IsAuthenticated, IsWorkspaceOwner]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["identifier", "username"]
    ordering_fields = ["created_at", "updated_at"]
    ordering = ["-created_at"]

    def get_queryset(self):
        queryset = super().get_queryset()

        identifier = self.request.query_params.get("identifier", None)
        username = self.request.query_params.get("username", None)
        project_id = self.request.query_params.get("project", None)
        workspace_id = self.request.query_params.get("workspace", None)
        token_type = self.request.query_params.get("token_type", None)
        stape_user_id = self.request.query_params.get("stape_user", None)

        if identifier:
            queryset = queryset.filter(identifier=identifier)
        if username:
            queryset = queryset.filter(username=username)
        if project_id:
            queryset = queryset.filter(project_id=project_id)
        if workspace_id:
            queryset = queryset.filter(workspaces__id=workspace_id)
        if token_type:
            queryset = queryset.filter(token_type=token_type)
        if stape_user_id:
            queryset = queryset.filter(stape_user_id=stape_user_id)

        return queryset

    def create(self, request, *args, **kwargs):
        token_type = request.data.get("token_type")
        project_id = request.data.get("project")

        if token_type not in StapeTokenType.values:
            return api_response(message="Invalid token_type", status=status.HTTP_400_BAD_REQUEST)

        if not project_id:
            return api_response(message="Project ID is required", status=status.HTTP_400_BAD_REQUEST)

        try:
            project = Project.objects.get(pk=project_id)
            workspace = project.workspace
        except Project.DoesNotExist:
            return Response({"message": "Project not found"}, status=status.HTTP_404_NOT_FOUND)

        local_user = request.user

        serializer = self.get_serializer(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)
        except Exception as e:
            error_message = parse_drf_errors(e.detail)
            return Response({"message": error_message}, status=status.HTTP_400_BAD_REQUEST)

        serializer.save(localUser=local_user, workspaces=[workspace], project=project)

        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @action(detail=False, methods=["get"])
    def me(self, request):
        stape_workspace = get_object_or_404(StapeWorkspace, localUser=request.user)
        serializer = self.get_serializer(stape_workspace)
        return Response(serializer.data)

    @action(detail=True, methods=["get"])
    def workspaces(self, request, pk=None):
        stape_workspace = self.get_object()
        workspaces = stape_workspace.workspaces.all()
        return Response([w.id for w in workspaces])

    @action(detail=True, methods=["get"])
    def project(self, request, pk=None):
        stape_workspace = self.get_object()
        return Response(stape_workspace.project.id if stape_workspace.project else None)

    @action(detail=True, methods=["get"])
    def stape_user(self, request, pk=None):
        stape_workspace = self.get_object()
        return Response(stape_workspace.stape_user.id if stape_workspace.stape_user else None)
