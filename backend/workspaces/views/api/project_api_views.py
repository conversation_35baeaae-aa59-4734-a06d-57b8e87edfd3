from apps.workspaces.models.project_models import (
    AdNetwork,
    Project,
    ProjectAdNetwork,
    ProjectPlatform,
    ProjectPurpose,
    ProjectType,
    ProjectVersion,
)
from apps.workspaces.permissions import IsWorkspaceOwner
from apps.workspaces.serializers.workspace_and_project_serializers import (
    AdNetworkFieldSerializer,
    AdNetworkSerializer,
    ProjectAdNetworkSerializer,
    ProjectPlatformSerializer,
    ProjectPurposeSerializer,
    ProjectSerializer,
    ProjectTypeSerializer,
    ProjectVersionSerializer,
)
from core.utils.response import api_response
from django.shortcuts import get_object_or_404
from django.utils.translation import gettext as _
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import BasePermission
from rest_framework.response import Response


class IsAdminOrReadOnly(BasePermission):
    def has_permission(self, request, view):
        if request.method in ["GET", "HEAD", "OPTIONS"]:
            return request.user.is_authenticated
        return request.user.is_staff


class ProjectPurposeViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = ProjectPurpose.objects.all()
    serializer_class = ProjectPurposeSerializer
    permission_classes = [IsAdminOrReadOnly]


class ProjectTypeViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = ProjectType.objects.all()
    serializer_class = ProjectTypeSerializer
    permission_classes = [IsAdminOrReadOnly]

    @action(detail=True, methods=["get"])
    def platforms(self, request, pk=None):
        project_type = get_object_or_404(ProjectType, pk=pk)
        platforms = ProjectPlatform.objects.filter(type_associations__project_type=project_type)
        serializer = ProjectPlatformSerializer(platforms, many=True, context={"request": request})
        return Response(
            data={"results": serializer.data},
            status=status.HTTP_200_OK,
        )


class ProjectPlatformViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = ProjectPlatform.objects.all()
    serializer_class = ProjectPlatformSerializer
    permission_classes = [IsAdminOrReadOnly]


class ProjectViewSet(viewsets.ModelViewSet):
    queryset = Project.objects.all()
    serializer_class = ProjectSerializer
    permission_classes = [permissions.IsAuthenticated, IsWorkspaceOwner]

    @action(detail=True, methods=["get"])
    def versions(self, request, pk=None):
        project = self.get_object()
        page = self.paginate_queryset(ProjectVersion.objects.filter(project=project).order_by("-created_at"))
        serializer = ProjectVersionSerializer(page, many=True)
        return self.get_paginated_response(serializer.data)

    @action(detail=True, methods=["post"])
    def create_version(self, request, pk=None):
        project = self.get_object()
        latest_version = ProjectVersion.objects.filter(project=project).order_by("-created_at").first()

        new_version_number = ProjectVersion.increment_version(
            latest_version.version_number if latest_version else "0.1.0"
        )

        version = ProjectVersion.objects.create(
            project=project,
            version_number=new_version_number,
            data=request.data.get("data", {}),
            created_by=request.user,
            is_remote=True,
            parent_version=latest_version,
        )

        serializer = ProjectVersionSerializer(version)
        return api_response(
            message=_("Version created successfully"),
            data=serializer.data,
            status=status.HTTP_201_CREATED,
        )

    def create(self, request, *args, **kwargs):
        response = super().create(request, *args, **kwargs)
        if response.status_code == status.HTTP_201_CREATED:
            # Add initial version info to response data
            project = Project.objects.get(id=response.data["id"])
            version = project.versions.latest("created_at")
            response.data["latest_version"] = ProjectVersionSerializer(version).data
        return response

    @action(detail=True, methods=["post"])
    def add_ad_network(self, request, pk=None):
        project = self.get_object()
        serializer = ProjectAdNetworkSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(project=project, created_by=request.user)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def remove_ad_network(self, request, pk=None):
        project = self.get_object()
        ad_network_id = request.data.get("ad_network_id")
        project.ad_networks.filter(ad_network_id=ad_network_id).delete()
        return Response(status=204)

    @action(detail=True, methods=["patch"], url_path="ad-networks/(?P<ad_network_id>[^/.]+)")
    def update_ad_network(self, request, pk=None, ad_network_id=None):
        """
        Update project ad network configuration
        """
        project = self.get_object()

        try:
            project_ad_network = ProjectAdNetwork.objects.get(project=project, ad_network_id=ad_network_id)
        except ProjectAdNetwork.DoesNotExist:
            return Response(
                {"message": "Ad network not found for this project"},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = ProjectAdNetworkSerializer(project_ad_network, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save(updated_by=request.user)
            return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ProjectVersionViewSet(viewsets.ModelViewSet):
    serializer_class = ProjectVersionSerializer
    permission_classes = [permissions.IsAuthenticated, IsWorkspaceOwner]

    def get_queryset(self):
        """
        Get versions for the specific project
        """
        project_id = self.kwargs["project_pk"]
        return ProjectVersion.objects.filter(
            project_id=project_id, project__workspace__owner=self.request.user
        ).order_by("-created_at")

    def perform_create(self, serializer):
        """
        Create a new version for the specific project
        """
        project_id = self.kwargs["project_pk"]
        project = Project.objects.get(id=project_id)

        # Get the latest version for version number increment
        latest_version = self.get_queryset().first()
        new_version_number = ProjectVersion.increment_version(
            latest_version.version_number if latest_version else "0.1.0"
        )

        serializer.save(
            project=project,
            created_by=self.request.user,
            version_number=new_version_number,
            is_remote=True,
            parent_version=latest_version,
        )

    @action(detail=False, methods=["get"])
    def latest(self, request, project_pk=None):
        """
        Get the latest version for a project
        """
        latest_version = self.get_queryset().first()
        if not latest_version:
            return Response(
                {"message": "No versions found for this project"},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = self.get_serializer(latest_version)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def restore(self, request, project_pk=None, pk=None):
        """
        Restore project to this version
        """
        version = self.get_object()
        project = version.project

        # Update project's raw_data with version data
        project.raw_data = version.data
        project.save()

        # Create new version to record this restore
        latest_version = self.get_queryset().first()
        new_version_number = ProjectVersion.increment_version(latest_version.version_number)

        restored_version = ProjectVersion.objects.create(
            project=project,
            version_number=new_version_number,
            data=version.data,
            created_by=request.user,
            is_remote=True,
            parent_version=latest_version,
        )

        serializer = self.get_serializer(restored_version)
        return Response(
            {
                "message": "Project restored to version " + version.version_number,
                "version": serializer.data,
            }
        )


class AdNetworkViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = AdNetwork.objects.filter(is_active=True)
    serializer_class = AdNetworkSerializer
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=True, methods=["get"])
    def fields(self, request, pk=None):
        ad_network = self.get_object()
        fields = ad_network.required_fields.all()
        return Response(AdNetworkFieldSerializer(fields, many=True).data)
