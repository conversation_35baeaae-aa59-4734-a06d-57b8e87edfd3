# import bleach
# from django.db.models import Count
# from django.utils.translation import gettext as _
# from django_filters.rest_framework import DjangoFilterBackend
# from rest_framework import filters, permissions, viewsets

# from workspaces.models.workspace_models import Workspace
# from workspaces.permissions import IsWorkspaceOwner
# from workspaces.serializers.workspace_and_project_serializers import WorkspaceSerializer
# from core.utils.response import api_response


# class WorkspaceViewSet(viewsets.ModelViewSet):
#     queryset = Workspace.objects.all()
#     serializer_class = WorkspaceSerializer
#     permission_classes = [permissions.IsAuthenticated, IsWorkspaceOwner]
#     filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
#     filterset_fields = ["status"]
#     search_fields = ["title", "description"]
#     ordering_fields = ["created_at", "updated_at", "title"]
#     ordering = ["-created_at"]

#     def create(self, request, *args, **kwargs):

#         sanitized_data = self.sanitize_input(request.data)
#         serializer = self.get_serializer(data=sanitized_data)
#         serializer.is_valid(raise_exception=True)

#         self.perform_create(serializer)
#         headers = self.get_success_headers(serializer.data)
#         return api_response(
#             data=serializer.data, message=_("Resource created successfully"), status=201, headers=headers
#         )

#     def sanitize_input(self, data):
#         if "description" in data:
#             data["description"] = bleach.clean(data["description"])
#         if "title" in data:
#             data["title"] = bleach.clean(data["title"])
#         return data

#     def perform_create(self, serializer):
#         serializer.save(owner=self.request.user)

#     def get_queryset(self):
#         return Workspace.objects.filter(owner=self.request.user)

#         return self.queryset.filter(owner=self.request.user).annotate(project_count=Count("projects"))
