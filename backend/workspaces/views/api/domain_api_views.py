# import bleach
# from django.utils.translation import gettext as _
# from django_filters.rest_framework import DjangoFilterBackend
# from rest_framework import filters, permissions, viewsets

# from apps.workspaces.models.domain_models import Domain, Subdomain
# from apps.workspaces.permissions import IsWorkspaceOwner
# from apps.workspaces.serializers.workspace_and_project_serializers import DomainWithSubdomainsSerializer
# from core.utils.response import api_response


# class DomainViewSet(viewsets.ModelViewSet):
#     queryset = Domain.objects.all()
#     serializer_class = DomainWithSubdomainsSerializer
#     permission_classes = [permissions.IsAuthenticated, IsWorkspaceOwner]
#     filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
#     search_fields = ["name"]
#     ordering_fields = ["name"]
#     ordering = ["name"]

#     def create(self, request, *args, **kwargs):
#         serializer = self.get_serializer(data=request.data)
#         serializer.is_valid(raise_exception=True)
#         self.perform_create(serializer)
#         headers = self.get_success_headers(serializer.data)
#         return api_response(
#             data=serializer.data, message=_("Resource created successfully"), status=201, headers=headers
#         )

#     def perform_create(self, serializer):
#         serializer.save(created_by=self.request.user)

#     def update(self, request, *args, **kwargs):
#         partial = kwargs.pop("partial", False)
#         instance = self.get_object()
#         serializer = self.get_serializer(instance, data=request.data, partial=partial)
#         serializer.is_valid(raise_exception=True)
#         self.perform_update(serializer)

#         if getattr(instance, "_prefetched_objects_cache", None):
#             # If 'prefetch_related' has been applied to a queryset, we need to
#             # forcibly invalidate the prefetch cache on the instance.
#             instance._prefetched_objects_cache = {}

#         return api_response(serializer.data)

#     def perform_update(self, serializer):
#         serializer.save()
