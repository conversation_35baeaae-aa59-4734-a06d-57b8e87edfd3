# from rest_framework import routers
# from rest_framework_nested import routers as nested_routers

# from .views.api import workspace_api_views
# from .views.api.domain_api_views import DomainViewSet
# from .views.api.project_api_views import (
#     AdNetworkViewSet,
#     ProjectPlatformViewSet,
#     ProjectPurposeViewSet,
#     ProjectTypeViewSet,
#     ProjectVersionViewSet,
#     ProjectViewSet,
# )
# from .views.api.stape_api_views import StapeUserViewSet, StapeWorkspaceViewSet

# router = routers.DefaultRouter()
# router.register(r"workspaces", workspace_api_views.WorkspaceViewSet, basename="workspace")
# router.register(r"domains", DomainViewSet, basename="domain")
# router.register(r"project-purposes", ProjectPurposeViewSet, basename="project-purpose")
# router.register(r"project-types", ProjectTypeViewSet, basename="project-type")
# router.register(r"project-platforms", ProjectPlatformViewSet, basename="project-platform")
# router.register(r"projects", ProjectViewSet, basename="project")
# router.register(r"ad-networks", AdNetworkViewSet, basename="ad-network")
# router.register(r"stape-users", StapeUserViewSet, basename="stape-user")
# router.register(r"stape-workspaces", StapeWorkspaceViewSet, basename="stape-workspace")

# # Nested router for project versions
# project_router = nested_routers.NestedSimpleRouter(router, r"projects", lookup="project")
# project_router.register(r"versions", ProjectVersionViewSet, basename="project-version")
