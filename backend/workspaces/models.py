from core.models import BaseModel
from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _


class WorkspaceStatus(models.TextChoices):
    DEPLOYED = "deployed", _("Deployed")
    ARCHIVED = "archived", _("Archived")
    DRAFT = "draft", _("Draft")


class Workspace(BaseModel):
    """
    Model representing a workspace.
    All workspaces now belong to an organization.
    """

    title = models.CharField(max_length=255)
    description = models.TextField(blank=True)

    # Organization relationship (new)
    organization = models.ForeignKey(
        "organizations.Organization",
        on_delete=models.CASCADE,
        related_name="workspaces",
        null=True,  # Temporarily nullable for migration
        blank=True,
    )

    # Keep owner for backward compatibility and direct access
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="workspaces",
    )
    status = models.CharField(max_length=20, choices=WorkspaceStatus.choices, default=WorkspaceStatus.DRAFT)

    class Meta:
        ordering = ["created_at"]

    def __str__(self):
        return self.title

    def project_count(self):
        return self.projects.count()

    def get_owner(self):
        return self.owner
