from core.authentication import GTMSessionAuthentication
from core.drf_utils import (
    create_error_response,
    create_paginated_response,
    create_success_response,
)
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from workspaces.models import Workspace

# from workspaces.permissions import WorkspaceModifierAuth  # Removed - replaced with DRF permissions
from workspaces.schemas import (  # Backward compatibility
    CreateWorkspaceSerializer,
    PublicWorkspaceDetailSerializer,
    UpdateWorkspaceSerializer,
)


@method_decorator(csrf_exempt, name="dispatch")
class WorkspaceCreateView(APIView):
    """Create or Update workspace"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = []

    def post(self, request):
        """Create or Update workspace"""
        serializer = CreateWorkspaceSerializer(data=request.data)
        if not serializer.is_valid():
            return create_error_response(
                message="Invalid data",
                detail=serializer.errors,
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        data = serializer.validated_data
        workspace_id = data.get("id")

        # Initialize has_access for both create and update scenarios
        has_access = True  # For new workspaces, user has access by default

        if workspace_id:
            try:
                _workspace = Workspace.objects.get(id=workspace_id)
            except Workspace.DoesNotExist:
                return create_error_response(message="Workspace not found", status_code=status.HTTP_404_NOT_FOUND)

            # Check if user has permission to update this workspace
            has_access = False

            # Check direct ownership
            if _workspace.owner == request.user:
                has_access = True

            # Check organization membership if workspace belongs to an organization
            elif _workspace.organization:
                has_access = _workspace.organization.has_permission(request.user, "edit")

        if not has_access:
            return create_error_response(
                status_code=status.HTTP_403_FORBIDDEN,
                message="You are not authorized to update this workspace",
            )

        update_fields = data.copy()
        # remove id
        update_fields.pop("id", None)

        # Handle organization_id separately
        organization_id = update_fields.pop("organization_id", None)

        # Handle workspace creation vs update
        if workspace_id:
            # Update existing workspace
            for field, value in update_fields.items():
                setattr(_workspace, field, value)

            # Handle organization change for existing workspace
            if organization_id:
                from organizations.models import Organization

                try:
                    organization = Organization.objects.get(id=organization_id)
                    # Check if user has permission to move workspace to this organization
                    if not organization.has_permission(request.user, "create"):
                        return create_error_response(
                            status_code=status.HTTP_403_FORBIDDEN,
                            message="You do not have permission to move workspaces to this organization",
                        )
                    _workspace.organization = organization
                except Organization.DoesNotExist:
                    return create_error_response(
                        status_code=status.HTTP_404_NOT_FOUND,
                        message="Organization not found",
                    )

            _workspace.save()
            _workspace.refresh_from_db()
            message = "Workspace updated successfully"
        else:
            # Create new workspace
            # Handle organization_id for new workspace
            organization = None
            if organization_id:
                from organizations.models import Organization

                try:
                    organization = Organization.objects.get(id=organization_id)
                    # Check if user has permission to create workspaces in this organization
                    if not organization.has_permission(request.user, "create"):
                        return create_error_response(
                            status_code=status.HTTP_403_FORBIDDEN,
                            message="You do not have permission to create workspaces in this organization",
                        )
                except Organization.DoesNotExist:
                    return create_error_response(
                        status_code=status.HTTP_404_NOT_FOUND,
                        message="Organization not found",
                    )

            # Create the workspace
            _workspace = Workspace.objects.create(
                owner=request.user,
                organization=organization,
                **{k: v for k, v in update_fields.items() if k != "organization_id"},
            )
            message = "Workspace created successfully"

        workspace_serializer = PublicWorkspaceDetailSerializer(_workspace)
        return create_success_response(
            data=workspace_serializer.data,
            message=message,
            status_code=(status.HTTP_201_CREATED if not workspace_id else status.HTTP_200_OK),
        )


@method_decorator(csrf_exempt, name="dispatch")
class WorkspaceListView(APIView):
    """Get workspaces with filtering and search"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get workspaces with filtering and search"""
        # Extract pagination parameters
        offset = int(request.GET.get("offset", 0))
        limit = int(request.GET.get("limit", 10))

        # Extract other query parameters
        organization_id = request.GET.get("organization_id")
        search = request.GET.get("search")
        status_filter = request.GET.get("status")

        # Include workspaces from organizations where user is owner or member
        from django.db import models
        from organizations.models import Organization

        # Get organizations where user is owner or active member
        user_organizations = Organization.objects.filter(
            models.Q(owner=request.user) | models.Q(members__user=request.user, members__is_active=True)
        ).distinct()

        # Filter workspaces by owner OR organization membership
        if organization_id:
            # Filter by specific organization if provided
            try:
                organization = Organization.objects.get(id=organization_id)
                # Check if user has access to this organization
                if not organization.has_permission(request.user, "view"):
                    return create_error_response(
                        message="You do not have permission to view workspaces from this organization",
                        status_code=status.HTTP_403_FORBIDDEN,
                    )
                q = Workspace.objects.filter(organization=organization).distinct()
            except Organization.DoesNotExist:
                return create_error_response(
                    message="Organization not found",
                    status_code=status.HTTP_404_NOT_FOUND,
                )
        else:
            # Return all workspaces user has access to
            q = Workspace.objects.filter(
                models.Q(owner=request.user) | models.Q(organization__in=user_organizations)
            ).distinct()

        if search:
            q = q.filter(title__icontains=search)
        if status_filter and status_filter in ["deployed", "archived", "draft"]:
            q = q.filter(status=status_filter)

        # Get total count before pagination
        total_items = q.count()

        # Apply pagination
        workspaces = q.order_by("-id")[offset : offset + limit]

        # Serialize the data
        serializer = PublicWorkspaceDetailSerializer(workspaces, many=True)

        return create_paginated_response(
            items=serializer.data,
            offset=offset,
            limit=limit,
            total_items=total_items,
            message="Workspaces retrieved successfully",
        )


@method_decorator(csrf_exempt, name="dispatch")
class WorkspaceDetailView(APIView):
    """Get, Update, or Delete Workspace"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get_workspace(self, request, workspace_id):
        """Helper method to get workspace with permission check"""
        try:
            workspace = Workspace.objects.get(id=workspace_id)
        except Workspace.DoesNotExist:
            return None, create_error_response(message="Workspace not found", status_code=status.HTTP_404_NOT_FOUND)

        # Check permissions (this would typically be handled by WorkspaceModifierAuth)
        # For now, we'll check basic ownership or organization membership
        has_access = False
        if workspace.owner == request.user:
            has_access = True
        elif workspace.organization:
            has_access = workspace.organization.has_permission(request.user, "view")

        if not has_access:
            return None, create_error_response(
                message="You do not have permission to access this workspace",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        return workspace, None

    def get(self, request, id):
        """Get workspace details"""
        workspace, error_response = self.get_workspace(request, id)
        if error_response:
            return error_response

        serializer = PublicWorkspaceDetailSerializer(workspace)
        return create_success_response(data=serializer.data, message="Workspace retrieved successfully")

    def put(self, request, id):
        """Update workspace"""
        workspace, error_response = self.get_workspace(request, id)
        if error_response:
            return error_response

        serializer = UpdateWorkspaceSerializer(data=request.data)
        if not serializer.is_valid():
            return create_error_response(
                message="Invalid data",
                detail=serializer.errors,
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        data = serializer.validated_data
        if not data:
            return create_error_response(
                message="No valid fields to update",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Update workspace fields
        for field, value in data.items():
            if hasattr(workspace, field) and value is not None:
                setattr(workspace, field, value)

        workspace.save()
        workspace.refresh_from_db()

        workspace_serializer = PublicWorkspaceDetailSerializer(workspace)
        return create_success_response(data=workspace_serializer.data, message="Workspace updated successfully")

    def delete(self, request, id):
        """Delete workspace"""
        workspace, error_response = self.get_workspace(request, id)
        if error_response:
            return error_response

        workspace.delete()
        return create_success_response(
            message="Workspace deleted successfully",
            status_code=status.HTTP_204_NO_CONTENT,
        )


# Backward compatibility functions for existing URL patterns
create = WorkspaceCreateView.as_view()
get_workspaces = WorkspaceListView.as_view()
detail = WorkspaceDetailView.as_view()
