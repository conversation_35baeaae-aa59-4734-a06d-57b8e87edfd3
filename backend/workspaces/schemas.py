from rest_framework import serializers
from users.schemas import PublicUserSerializer
from workspaces.models import WorkspaceStatus


class PublicWorkspaceDetailSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    title = serializers.CharField()
    description = serializers.CharField()
    owner = PublicUserSerializer()
    status = serializers.ChoiceField(choices=WorkspaceStatus.choices, default=WorkspaceStatus.DRAFT)


class PublicWorkspaceSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    title = serializers.CharField()


class CreateWorkspaceSerializer(serializers.Serializer):
    title = serializers.CharField()
    description = serializers.CharField()
    id = serializers.IntegerField(required=False, allow_null=True)
    organization_id = serializers.CharField()  # Can be string or int


class UpdateWorkspaceSerializer(serializers.Serializer):
    title = serializers.CharField(required=False, allow_null=True)
    description = serializers.Char<PERSON><PERSON>(required=False, allow_null=True)
    id = serializers.Integer<PERSON>ield(required=False, allow_null=True)
    organization_id = serializers.CharField(required=False, allow_null=True)
    status = serializers.ChoiceField(choices=WorkspaceStatus.choices, default=WorkspaceStatus.DRAFT)


# Backward compatibility
PublicWorkspaceDetailSchema = PublicWorkspaceDetailSerializer
PublicWorkspaceSchema = PublicWorkspaceSerializer
CreateWorkspaceSchema = CreateWorkspaceSerializer
UpdateWorkspaceSchema = UpdateWorkspaceSerializer
