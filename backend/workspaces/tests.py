import unittest

from workspaces.models import WorkspaceStatus


class WorkspaceStatusChoicesTest(unittest.TestCase):
    """Test that WorkspaceStatus choices are working correctly"""

    def test_workspace_status_choices_exist(self):
        """Test that WorkspaceStatus.choices exists and has expected values"""
        choices = WorkspaceStatus.choices

        # Check that choices is not empty
        self.assertTrue(choices)

        # Check that expected choices exist
        choice_values = [choice[0] for choice in choices]
        self.assertIn("deployed", choice_values)
        self.assertIn("archived", choice_values)
        self.assertIn("draft", choice_values)

        # Check that we have exactly 3 choices
        self.assertEqual(len(choices), 3)

    def test_workspace_status_values(self):
        """Test that WorkspaceStatus enum values are correct"""
        self.assertEqual(WorkspaceStatus.DEPLOYED, "deployed")
        self.assertEqual(WorkspaceStatus.ARCHIVED, "archived")
        self.assertEqual(WorkspaceStatus.DRAFT, "draft")

    def test_workspace_status_labels(self):
        """Test that WorkspaceStatus labels are correct"""
        choices_dict = dict(WorkspaceStatus.choices)
        self.assertEqual(choices_dict["deployed"], "Deployed")
        self.assertEqual(choices_dict["archived"], "Archived")
        self.assertEqual(choices_dict["draft"], "Draft")


if __name__ == "__main__":
    unittest.main()
