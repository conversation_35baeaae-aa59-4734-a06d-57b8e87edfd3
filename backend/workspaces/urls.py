from django.urls import path

from . import views as views

app_name = "workspaces"
urlpatterns = [
    path("", views.get_workspaces, name="get_workspaces"),
    path("new/", views.create, name="create_workspace"),
    path("<int:id>/", views.detail, name="detail_workspace"),
    # path("", views.index, name="workspace-index"),
    # path("<int:id>/", views.detail, name="workspace-detail"),
    # path("<int:id>/projects/new", views.create_project, name="workspace-projects"),
    # path("<int:id>/projects/<int:project_id>/edit", views.ProjectEditView.as_view(), name="workspace-projects-edit"),
    # path("<int:id>/projects/<int:project_id>", views.ProjectDetailView.as_view(), name="workspace-projects-detail"),
    # path(
    #     "<int:id>/projects/<int:project_id>/wizard", views.ProjectWizardView.as_view(), name="workspace-projects-wizard"
    # ),
    # path(
    #     "<int:id>/projects/<int:project_id>/ad-tracking",
    #     views.ProjectTrackingView.as_view(),
    #     name="workspace-projects-wizard",
    # ),
    # path(
    #     "<int:id>/projects/<int:project_id>/deploy",
    #     views.ProjectDeployView.as_view(),
    #     name="workspace-projects-wizard",
    # ),
]
