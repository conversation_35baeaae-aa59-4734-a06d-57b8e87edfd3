import json

from django.utils.html import format_html, format_html_join
from django.utils.safestring import mark_safe


class JsonReportAdminMixin:
    class Media:
        js = ("admin/js/collapsible_admin_reports.js",)  # Generic name
        css = {"all": ("admin/css/custom_admin_reports.css",)}

    def format_json_for_admin_display(self, data, title="Data", unique_id_prefix=""):
        if data is None:
            return mark_safe("<em>N/A</em>")

        parsed_data = data
        if isinstance(data, str):
            try:
                parsed_data = json.loads(data)
            except json.JSONDecodeError:
                return format_html("<pre>{}</pre>", data)

        if not isinstance(parsed_data, dict | list):
            return format_html(
                "<strong>Report Data (Unexpected type: {}):</strong><pre>{}</pre>",
                type(parsed_data).__name__,
                str(parsed_data),
            )

        html_segments = []
        base_id = f"{unique_id_prefix}_{title.replace(' ', '_').lower()}"

        if isinstance(parsed_data, dict):
            html_segments.append(format_html("<h4>{}</h4>", title))

            if "summary" in parsed_data and isinstance(parsed_data["summary"], dict):
                summary_items = []
                for key, value in parsed_data["summary"].items():
                    summary_items.append(
                        format_html(
                            "<li><strong>{}:</strong> {}</li>",
                            str(key).replace("_", " ").title(),
                            str(value),
                        )
                    )
                if summary_items:
                    summary_id = f"{base_id}_summary_details"
                    html_segments.append(
                        format_html(
                            '<h5><a href="javascript:void(0);" onclick="toggleAdminReportVisibility(\'{}\');">Summary (Click to toggle)</a></h5>',
                            summary_id,
                        )
                    )
                    html_segments.append(
                        format_html(
                            "<ul id='{}' style='display:none;'>{}</ul><hr>",
                            summary_id,
                            format_html_join("", "{}", ((item,) for item in summary_items)),
                        )
                    )

            if "issues" in parsed_data and isinstance(parsed_data["issues"], list) and parsed_data["issues"]:
                issue_rows = []
                for issue_idx, issue in enumerate(parsed_data["issues"]):
                    if isinstance(issue, dict):
                        message_id = f"{base_id}_issue_msg_{issue_idx}"
                        message_content = str(issue.get("message", "N/A"))
                        message_display = message_content
                        if len(message_content) > 150:
                            message_display = format_html(
                                '{}... <a href="javascript:void(0);" onclick="toggleAdminReportVisibility(\'{}\');">(details)</a><div id="{}" style="display:none; white-space: pre-wrap; background-color: #f0f0f0; border: 1px solid #ddd; padding: 5px; margin-top: 5px;">{}</div>',
                                message_content[:150],
                                message_id,
                                message_id,
                                message_content,
                            )
                        issue_rows.append(
                            format_html(
                                "<tr><td>{}</td><td>{}</td><td>{}</td></tr>",
                                str(issue.get("severity", "N/A")),
                                message_display,
                                str(issue.get("code", "N/A")),
                            )
                        )
                if issue_rows:
                    issues_id = f"{base_id}_issues_details"
                    html_segments.append(
                        format_html(
                            '<h5><a href="javascript:void(0);" onclick="toggleAdminReportVisibility(\'{}\');">Issues (Click to toggle)</a></h5>',
                            issues_id,
                        )
                    )
                    html_segments.append(
                        format_html(
                            "<div id='{}' style='display:none;'><table class='report-table'><thead><tr><th>Severity</th><th>Message</th><th>Code</th></tr></thead><tbody>{}</tbody></table></div><hr>",
                            issues_id,
                            format_html_join("", "{}", ((row,) for row in issue_rows)),
                        )
                    )

            detail_id = f"{base_id}_full_json"
            html_segments.append(
                format_html(
                    '<h5><a href="javascript:void(0);" onclick="toggleAdminReportVisibility(\'{}\');">Full JSON Content (Click to expand)</a></h5>',
                    detail_id,
                )
            )
            formatted_json_str = json.dumps(parsed_data, indent=2, sort_keys=True, ensure_ascii=False)
            html_segments.append(
                format_html(
                    '<div id="{}" style="display:none;"><pre>{}</pre></div>',
                    detail_id,
                    formatted_json_str,
                )
            )

        elif isinstance(parsed_data, list):
            html_segments.append(format_html("<h4>{} (List)</h4>", title))
            formatted_json_str = json.dumps(parsed_data, indent=2, ensure_ascii=False)
            html_segments.append(format_html("<pre>{}</pre>", formatted_json_str))
        else:  # Should not be reached if parsed_data check is done well
            html_segments.append(format_html("<pre>{}</pre>", str(parsed_data)))

        return mark_safe("".join(html_segments))
