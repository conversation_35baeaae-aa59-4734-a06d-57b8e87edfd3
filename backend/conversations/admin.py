from django.contrib import admin

from .models import Conversation, ConversationSession, Message


@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "user",
        "get_title",
        "total_messages",
        "is_active",
        "created_at",
        "updated_at",
    ]
    list_filter = ["is_active", "created_at", "updated_at"]
    search_fields = ["title", "user__email", "user__username"]
    readonly_fields = ["id", "created_at", "updated_at", "total_messages"]
    ordering = ["-updated_at"]

    def get_title(self, obj):
        return obj.get_title()

    get_title.short_description = "Title"


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ["id", "conversation", "role", "content_preview", "timestamp"]
    list_filter = ["role", "timestamp"]
    search_fields = ["content", "conversation__title"]
    readonly_fields = ["id", "timestamp"]
    ordering = ["-timestamp"]

    def content_preview(self, obj):
        return obj.content[:100] + "..." if len(obj.content) > 100 else obj.content

    content_preview.short_description = "Content Preview"


@admin.register(ConversationSession)
class ConversationSessionAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "conversation",
        "user",
        "is_active",
        "last_activity",
        "context_window_size",
    ]
    list_filter = ["is_active", "last_activity"]
    search_fields = ["conversation__title", "user__email"]
    readonly_fields = ["id", "last_activity"]
    ordering = ["-last_activity"]
