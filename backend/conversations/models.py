import uuid

from django.contrib.auth import get_user_model
from django.db import models
from django.utils import timezone

User = get_user_model()


class Conversation(models.Model):
    """
    Represents a conversation between a user and the AI agent
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="conversations")
    title = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    # Conversation metadata
    context_data = models.JSONField(default=dict, blank=True)
    total_messages = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ["-updated_at"]
        indexes = [
            models.Index(fields=["user", "-updated_at"]),
            models.Index(fields=["user", "is_active"]),
        ]

    def __str__(self):
        return f"Conversation {self.id} - {self.title or 'Untitled'}"

    def get_title(self):
        """Generate a title from the first message if not set"""
        if self.title:
            return self.title

        first_message = self.messages.filter(role="user").first()
        if first_message:
            # Use first 50 characters of the first user message
            return first_message.content[:50] + "..." if len(first_message.content) > 50 else first_message.content

        return f"Conversation {self.created_at.strftime('%Y-%m-%d %H:%M')}"

    def update_message_count(self):
        """Update the total message count"""
        self.total_messages = self.messages.count()
        self.save(update_fields=["total_messages"])


class Message(models.Model):
    """
    Represents a single message in a conversation
    """

    ROLE_CHOICES = [
        ("user", "User"),
        ("assistant", "Assistant"),
        ("system", "System"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name="messages")
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    content = models.TextField()
    timestamp = models.DateTimeField(default=timezone.now)

    # Message metadata
    metadata = models.JSONField(default=dict, blank=True)

    # For tracking agent actions and responses
    actions_performed = models.JSONField(default=list, blank=True)
    context_updates = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ["timestamp"]
        indexes = [
            models.Index(fields=["conversation", "timestamp"]),
            models.Index(fields=["conversation", "role"]),
        ]

    def __str__(self):
        return f"{self.role}: {self.content[:50]}..."

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # Update conversation's updated_at and message count
        self.conversation.updated_at = timezone.now()
        self.conversation.update_message_count()


class ConversationSession(models.Model):
    """
    Tracks active conversation sessions for context management
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.OneToOneField(Conversation, on_delete=models.CASCADE, related_name="session")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="conversation_sessions")

    # Session state
    last_activity = models.DateTimeField(default=timezone.now)
    is_active = models.BooleanField(default=True)

    # Context management
    context_window_size = models.PositiveIntegerField(default=20)  # Number of messages to keep in context
    session_data = models.JSONField(default=dict, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=["user", "is_active"]),
            models.Index(fields=["last_activity"]),
        ]

    def __str__(self):
        return f"Session for {self.conversation}"

    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = timezone.now()
        self.save(update_fields=["last_activity"])

    def get_context_messages(self):
        """Get recent messages for context"""
        return self.conversation.messages.order_by("-timestamp")[: self.context_window_size]
