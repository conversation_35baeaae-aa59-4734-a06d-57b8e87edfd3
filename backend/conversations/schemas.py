from django.contrib.auth import get_user_model
from rest_framework import serializers

from .models import Conversation, ConversationSession, Message

User = get_user_model()


class MessageSerializer(serializers.ModelSerializer):
    """Serializer for Message model"""

    class Meta:
        model = Message
        fields = [
            "id",
            "role",
            "content",
            "timestamp",
            "metadata",
            "actions_performed",
            "context_updates",
        ]
        read_only_fields = ["id", "timestamp"]


class ConversationListSerializer(serializers.ModelSerializer):
    """Serializer for listing conversations"""

    title = serializers.SerializerMethodField()
    last_message = serializers.SerializerMethodField()

    class Meta:
        model = Conversation
        fields = [
            "id",
            "title",
            "total_messages",
            "is_active",
            "created_at",
            "updated_at",
            "last_message",
        ]
        read_only_fields = ["id", "created_at", "updated_at", "total_messages"]

    def get_title(self, obj):
        return obj.get_title()

    def get_last_message(self, obj):
        last_message = obj.messages.last()
        if last_message:
            return {
                "content": (
                    last_message.content[:100] + "..." if len(last_message.content) > 100 else last_message.content
                ),
                "role": last_message.role,
                "timestamp": last_message.timestamp,
            }
        return None


class ConversationDetailSerializer(serializers.ModelSerializer):
    """Serializer for detailed conversation view with messages"""

    messages = MessageSerializer(many=True, read_only=True)
    title = serializers.SerializerMethodField()

    class Meta:
        model = Conversation
        fields = [
            "id",
            "title",
            "total_messages",
            "is_active",
            "created_at",
            "updated_at",
            "context_data",
            "messages",
        ]
        read_only_fields = ["id", "created_at", "updated_at", "total_messages"]

    def get_title(self, obj):
        return obj.get_title()


class ConversationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating conversations"""

    class Meta:
        model = Conversation
        fields = ["title"]

    def create(self, validated_data):
        user = self.context["request"].user
        conversation = Conversation.objects.create(user=user, **validated_data)

        # Create associated session
        ConversationSession.objects.create(conversation=conversation, user=user)

        return conversation


class MessageCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating messages"""

    class Meta:
        model = Message
        fields = ["role", "content", "metadata", "actions_performed", "context_updates"]

    def create(self, validated_data):
        conversation_id = self.context["conversation_id"]
        conversation = Conversation.objects.get(id=conversation_id)

        message = Message.objects.create(conversation=conversation, **validated_data)

        # Update session activity
        if hasattr(conversation, "session"):
            conversation.session.update_activity()

        return message


class ConversationSessionSerializer(serializers.ModelSerializer):
    """Serializer for conversation sessions"""

    class Meta:
        model = ConversationSession
        fields = [
            "id",
            "is_active",
            "last_activity",
            "context_window_size",
            "session_data",
        ]
        read_only_fields = ["id", "last_activity"]


# Request/Response schemas for AI Agent Service integration
class ChatMessageRequestSerializer(serializers.Serializer):
    """Schema for chat message requests"""

    message = serializers.CharField()
    conversation_id = serializers.UUIDField(required=False, allow_null=True)
    context = serializers.JSONField(default=dict)


class ChatMessageResponseSerializer(serializers.Serializer):
    """Schema for chat message responses"""

    response = serializers.CharField()
    conversation_id = serializers.UUIDField()
    message_id = serializers.UUIDField()
    actions = serializers.ListField(default=list)
    context_updates = serializers.JSONField(default=dict)
    success = serializers.BooleanField(default=True)
    error_message = serializers.CharField(required=False, allow_null=True)
    suggestions = serializers.ListField(default=list)
