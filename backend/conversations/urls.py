from django.urls import path

from . import views

app_name = "conversations"

urlpatterns = [
    # Public API (requires authentication)
    path("", views.ConversationListCreateView.as_view(), name="conversation-list-create"),
    path("<uuid:pk>/", views.ConversationDetailView.as_view(), name="conversation-detail"),
    path(
        "<uuid:conversation_id>/messages/",
        views.ConversationMessagesView.as_view(),
        name="conversation-messages",
    ),
    path("chat/", views.chat_with_context, name="chat-with-context"),
    # Internal API (for AI agent service, no authentication required)
    path("internal/", views.internal_conversations, name="internal-conversations"),
    path(
        "internal/<uuid:conversation_id>/",
        views.internal_conversation_detail,
        name="internal-conversation-detail",
    ),
    path(
        "internal/<uuid:conversation_id>/messages/",
        views.internal_add_message,
        name="internal-add-message",
    ),
]
