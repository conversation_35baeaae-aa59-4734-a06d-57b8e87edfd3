# Generated by Django 5.2.4 on 2025-09-19 12:05

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Conversation",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(blank=True, max_length=255, null=True)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("context_data", models.JSONField(blank=True, default=dict)),
                ("total_messages", models.PositiveIntegerField(default=0)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="conversations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="ConversationSession",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "last_activity",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("context_window_size", models.PositiveIntegerField(default=20)),
                ("session_data", models.JSONField(blank=True, default=dict)),
                (
                    "conversation",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="session",
                        to="conversations.conversation",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="conversation_sessions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Message",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("user", "User"),
                            ("assistant", "Assistant"),
                            ("system", "System"),
                        ],
                        max_length=20,
                    ),
                ),
                ("content", models.TextField()),
                ("timestamp", models.DateTimeField(default=django.utils.timezone.now)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("actions_performed", models.JSONField(blank=True, default=list)),
                ("context_updates", models.JSONField(blank=True, default=dict)),
                (
                    "conversation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="messages",
                        to="conversations.conversation",
                    ),
                ),
            ],
            options={
                "ordering": ["timestamp"],
            },
        ),
        migrations.AddIndex(
            model_name="conversation",
            index=models.Index(fields=["user", "-updated_at"], name="conversatio_user_id_a61baf_idx"),
        ),
        migrations.AddIndex(
            model_name="conversation",
            index=models.Index(fields=["user", "is_active"], name="conversatio_user_id_18e530_idx"),
        ),
        migrations.AddIndex(
            model_name="conversationsession",
            index=models.Index(fields=["user", "is_active"], name="conversatio_user_id_7558d2_idx"),
        ),
        migrations.AddIndex(
            model_name="conversationsession",
            index=models.Index(fields=["last_activity"], name="conversatio_last_ac_3e3c6e_idx"),
        ),
        migrations.AddIndex(
            model_name="message",
            index=models.Index(
                fields=["conversation", "timestamp"],
                name="conversatio_convers_70c0ca_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="message",
            index=models.Index(fields=["conversation", "role"], name="conversatio_convers_2f39c0_idx"),
        ),
    ]
