from django.contrib.auth import get_user_model
from django.db import transaction
from django.shortcuts import get_object_or_404
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response

from .models import Conversation, ConversationSession, Message
from .schemas import (
    ChatMessageRequestSerializer,
    ConversationCreateSerializer,
    ConversationDetailSerializer,
    ConversationListSerializer,
    MessageCreateSerializer,
)

User = get_user_model()


class ConversationListCreateView(generics.ListCreateAPIView):
    """
    List user's conversations or create a new conversation
    """

    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Conversation.objects.filter(user=self.request.user, is_active=True).order_by("-updated_at")

    def get_serializer_class(self):
        if self.request.method == "POST":
            return ConversationCreateSerializer
        return ConversationListSerializer


class ConversationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a conversation
    """

    permission_classes = [IsAuthenticated]
    serializer_class = ConversationDetailSerializer

    def get_queryset(self):
        return Conversation.objects.filter(user=self.request.user)

    def perform_destroy(self, instance):
        # Soft delete by setting is_active to False
        instance.is_active = False
        instance.save()


class ConversationMessagesView(generics.ListCreateAPIView):
    """
    List messages in a conversation or add a new message
    """

    permission_classes = [IsAuthenticated]
    serializer_class = MessageCreateSerializer

    def get_queryset(self):
        conversation_id = self.kwargs["conversation_id"]
        conversation = get_object_or_404(Conversation, id=conversation_id, user=self.request.user)
        return conversation.messages.all()

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["conversation_id"] = self.kwargs["conversation_id"]
        return context


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def chat_with_context(request):
    """
    Enhanced chat endpoint that maintains conversation context
    """
    serializer = ChatMessageRequestSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    data = serializer.validated_data
    user = request.user

    try:
        with transaction.atomic():
            # Get or create conversation
            conversation_id = data.get("conversation_id")
            if conversation_id:
                conversation = get_object_or_404(Conversation, id=conversation_id, user=user, is_active=True)
            else:
                # Create new conversation
                conversation = Conversation.objects.create(user=user)
                ConversationSession.objects.create(conversation=conversation, user=user)

            # Save user message
            user_message = Message.objects.create(
                conversation=conversation,
                role="user",
                content=data["message"],
                metadata=data.get("context", {}),
            )

            # Get conversation context for AI processing
            context_messages = []
            if hasattr(conversation, "session"):
                recent_messages = conversation.session.get_context_messages()
                context_messages = [
                    {
                        "role": msg.role,
                        "content": msg.content,
                        "timestamp": msg.timestamp.isoformat(),
                    }
                    for msg in recent_messages
                ]

            # TODO: Integrate with AI Agent Service
            # For now, return a placeholder response
            ai_response = f"I received your message: '{data['message']}'. This is a placeholder response with conversation context from {len(context_messages)} previous messages."

            # Save AI response
            ai_message = Message.objects.create(
                conversation=conversation,
                role="assistant",
                content=ai_response,
                metadata={"context_messages_count": len(context_messages)},
            )

            # Update session activity
            if hasattr(conversation, "session"):
                conversation.session.update_activity()

            response_data = {
                "response": ai_response,
                "conversation_id": conversation.id,
                "message_id": ai_message.id,
                "actions": [],
                "context_updates": {},
                "success": True,
                "suggestions": [],
            }

            return Response(response_data, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {"error": f"Chat processing failed: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# Internal API views for AI agent service (no authentication required)
def get_user_from_header(request):
    """Get user from X-User-ID header for internal API calls"""
    user_id = request.headers.get("X-User-ID")
    if not user_id:
        return None

    try:
        if user_id == "anonymous":
            # For development, create or get anonymous user
            user, created = User.objects.get_or_create(
                username="anonymous", defaults={"email": "<EMAIL>"}
            )
            return user
        else:
            return User.objects.get(id=user_id)
    except (User.DoesNotExist, ValueError):
        return None


@api_view(["GET", "POST"])
@permission_classes([AllowAny])
def internal_conversations(request):
    """Internal API for conversation management (for AI agent service)"""
    user = get_user_from_header(request)
    if not user:
        return Response({"error": "X-User-ID header required"}, status=status.HTTP_400_BAD_REQUEST)

    if request.method == "GET":
        # List conversations
        conversations = Conversation.objects.filter(user=user, is_active=True).order_by("-updated_at")

        serializer = ConversationListSerializer(conversations, many=True)
        return Response(serializer.data)

    elif request.method == "POST":
        # Create conversation
        serializer = ConversationCreateSerializer(data=request.data)
        if serializer.is_valid():
            conversation = Conversation.objects.create(user=user, **serializer.validated_data)

            # Create associated session
            ConversationSession.objects.create(conversation=conversation, user=user)

            response_serializer = ConversationDetailSerializer(conversation)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["GET", "DELETE"])
@permission_classes([AllowAny])
def internal_conversation_detail(request, conversation_id):
    """Internal API for specific conversation management"""
    user = get_user_from_header(request)
    if not user:
        return Response({"error": "X-User-ID header required"}, status=status.HTTP_400_BAD_REQUEST)

    try:
        conversation = Conversation.objects.get(id=conversation_id, user=user)
    except Conversation.DoesNotExist:
        return Response({"error": "Conversation not found"}, status=status.HTTP_404_NOT_FOUND)

    if request.method == "GET":
        serializer = ConversationDetailSerializer(conversation)
        return Response(serializer.data)

    elif request.method == "DELETE":
        conversation.is_active = False
        conversation.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


@api_view(["POST"])
@permission_classes([AllowAny])
def internal_add_message(request, conversation_id):
    """Internal API for adding messages to conversations"""
    user = get_user_from_header(request)
    if not user:
        return Response({"error": "X-User-ID header required"}, status=status.HTTP_400_BAD_REQUEST)

    try:
        conversation = Conversation.objects.get(id=conversation_id, user=user)
    except Conversation.DoesNotExist:
        return Response({"error": "Conversation not found"}, status=status.HTTP_404_NOT_FOUND)

    serializer = MessageCreateSerializer(data=request.data)
    if serializer.is_valid():
        message = Message.objects.create(conversation=conversation, **serializer.validated_data)

        # Update session activity
        if hasattr(conversation, "session"):
            conversation.session.update_activity()

        response_data = MessageCreateSerializer(message).data
        response_data["id"] = str(message.id)
        return Response(response_data, status=status.HTTP_201_CREATED)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
