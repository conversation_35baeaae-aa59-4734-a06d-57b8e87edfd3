import json
import logging
from typing import TypeVar

from core.utils.response import api_response
from django.core.exceptions import ObjectDoesNotExist
from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator
from django.db import models
from django.db.models import Q
from django.utils.translation import gettext as _
from django.views import View
from django_svelte.views import SvelteTemplateView
from marshmallow import Schema

logger = logging.getLogger(__name__)


class MySvelteTemplateView(SvelteTemplateView):
    template_name = "svelte_component.html"

    def get_svelte_props(self, **kwargs):
        return kwargs


class MyContextSvelteTemplateView(MySvelteTemplateView):
    def get_svelte_props(self, **kwargs):
        kwargs.update({"name": "single component view"})
        return kwargs


T = TypeVar("T", bound=models.Model)
U = TypeVar("U", bound=Schema)


class BaseView[T, U](View):
    model: type[T] | None = None
    schema_class: type[U] | None = None
    # Fields that are not allowed to be filtered
    disallowed_filter_fields: list[str] = []
    # Fields that are not allowed to be sorted
    disallowed_sort_fields: list[str] = []

    def get_object(self, pk):
        try:
            return self.model.objects.get(pk=pk)
        except ObjectDoesNotExist:
            return None

    def serialize(self, data=None, partial=False):
        return self.schema_class().dump(data, partial=partial)

    def deserialize(self, data=None, partial=False):
        return self.schema_class().load(data, partial=partial)

    def parse_json_body(self, request):
        try:
            return json.loads(request.body.decode("utf-8"))
        except (json.JSONDecodeError, UnicodeDecodeError):
            return None

    def get(self, request, pk=None):
        if pk:
            instance = self.get_object(pk)
            if not instance:
                return api_response(
                    status=404,
                    error=_("Resource not found"),
                    message=_("Resource not found"),
                )
            data = self.serialize(instance)
            return api_response(data=data, message=_("Resource retrieved successfully"))

        query = Q()

        # Filters
        filter_param = request.GET.get("filter", "{}")
        try:
            filter_dict = json.loads(filter_param)
        except json.JSONDecodeError:
            filter_dict = {}

        for key, value in filter_dict.items():
            if key in self.disallowed_filter_fields:
                continue
            if "__" in key:
                key, lookup = key.split("__")
                key = f"{key}__{lookup}"
            query &= Q(**{key: value})

        # Sort
        sort = request.GET.get("sort", None)
        order = request.GET.get("order", "desc")
        if sort:
            if sort in self.disallowed_sort_fields:
                sort = None
            if order == "desc":
                sort = f"-{sort}"

        # Search
        search = request.GET.get("search", None)
        if search:
            search_fields = [field.name for field in self.model._meta.fields]
            search_filters = Q()
            for field in search_fields:
                search_filters |= Q(**{f"{field}__icontains": search})
            query &= search_filters

        queryset = self.model.objects.filter(query)
        queryset = queryset.order_by(sort) if sort else queryset

        # Pagination
        page = request.GET.get("page", 1)
        page_size = request.GET.get("page_size", 10)
        paginator = Paginator(queryset, page_size)
        try:
            queryset = paginator.page(page)
        except PageNotAnInteger:
            queryset = paginator.page(1)
        except EmptyPage:
            queryset = paginator.page(paginator.num_pages)

        data = self.serialize(queryset, many=True)
        return api_response(data=data, message=_("Resource retrieved successfully"))

    def post(self, request):
        data = self.parse_json_body(request)
        if not data:
            return api_response(
                status=400,
                error=_("Invalid JSON request body"),
                message=_("Invalid JSON request body"),
            )
        try:
            _data = self.deserialize(data, partial=True)
        except Exception as e:
            return api_response(status=400, error=str(e), message=_("Invalid request body"))

        try:
            obj = self.model.objects.create(**_data)
            return api_response(data=self.serialize(obj), message=_("Resource created successfully"))
        except Exception as e:
            return api_response(status=400, error=str(e), message=_("Resource creation failed"))

    def put(self, request, pk):
        if not pk:
            return api_response(
                status=400,
                error=_("Resource ID is required"),
                message=_("Resource ID is required"),
            )

        instance = self.get_object(pk)

        if not instance:
            return api_response(
                status=404,
                error=_("Resource not found"),
                message=_("Resource not found"),
            )

        data = self.parse_json_body(request)
        if not data:
            return api_response(
                status=400,
                error=_("Invalid JSON request body"),
                message=_("Invalid JSON request body"),
            )
        try:
            _data = self.deserialize(data, partial=True)
        except Exception as e:
            return api_response(status=400, error=str(e), message=_("Invalid request body"))

        for key, value in _data.items():
            setattr(instance, key, value)

        try:
            instance.save()
            return api_response(
                data=self.serialize(instance),
                message=_("Resource updated successfully"),
            )
        except Exception as e:
            return api_response(status=400, error=str(e), message=_("Resource update failed"))

    def delete(self, request, pk):
        if not pk:
            return api_response(
                status=400,
                error=_("Resource ID is required"),
                message=_("Resource ID is required"),
            )

        instance = self.get_object(pk)

        if not instance:
            return api_response(
                status=404,
                error=_("Resource not found"),
                message=_("Resource not found"),
            )

        try:
            instance.delete()
            return api_response(message=_("Resource deleted successfully"))
        except Exception as e:
            return api_response(status=400, error=str(e), message=_("Resource deletion failed"))
