import json
from base64 import b64decode, b64encode

from cryptography.fernet import <PERSON><PERSON><PERSON>
from django import forms
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured, ValidationError
from django.db import models
from django.utils.translation import gettext_lazy as _


class EncryptedTextField(models.TextField):
    """Custom field that automatically encrypts/decrypts data"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    @property
    def fernet(self):
        # We can choose to use a separate key instead of ENCRYPTION_KEY
        if not hasattr(settings, "ENCRYPTION_KEY"):
            raise ImproperlyConfigured("ENCRYPTION_KEY must be defined in settings")

        key = settings.ENCRYPTION_KEY
        if not isinstance(key, bytes):
            key = key.encode()
        return Fernet(key)

    def get_prep_value(self, value):
        if value is None:
            return value
        # Encrypt the value before saving
        encrypted = self.fernet.encrypt(value.encode())
        return b64encode(encrypted).decode()

    def from_db_value(self, value, expression, connection):
        if value is None:
            return value
        # Decrypt the value when reading from DB
        try:
            decrypted = self.fernet.decrypt(b64decode(value))
            return decrypted.decode()
        except Exception as e:
            raise ValueError(f"Failed to decrypt value: {e}")


def validate_file_size(value):
    filesize = value.size

    if filesize > 200 * 1024:  # 200KB
        raise ValidationError(_("The maximum file size that can be uploaded is 200KB"))


class JSONTextAreaWidget(forms.Textarea):
    def format_value(self, value):
        if value is None:
            return ""
        return json.dumps(value, indent=2)
