import logging

from django.conf import settings
from django.conf.urls.i18n import is_language_prefix_patterns_used
from django.middleware.locale import LocaleMiddleware as BaseLocaleMiddleware
from django.utils import translation
from django.utils.cache import patch_vary_headers

logger = logging.getLogger(__name__)


class LocaleMiddleware(BaseLocaleMiddleware):
    """
    Django locale middleware with skipped 404 redirects and enabled
    setting language from `locale` querystring.
    """

    def process_request(self, request):
        super().process_request(request)
        locale = request.GET.get("locale")
        if locale and locale in [x[0] for x in settings.LANGUAGES]:
            translation.activate(locale)
            request.LANGUAGE_CODE = locale

    def process_response(self, request, response):
        language = translation.get_language()
        language_from_path = translation.get_language_from_path(request.path_info)
        urlconf = getattr(request, "urlconf", settings.ROOT_URLCONF)
        i18n_patterns_used, _ = is_language_prefix_patterns_used(urlconf)

        if not (i18n_patterns_used and language_from_path):
            patch_vary_headers(response, ("Accept-Language",))
        response.setdefault("Content-Language", language)
        return response


class FixedAdminLocaleMiddleware:
    """
    Fix admin interface to English version by default.
    """

    def __init__(self, get_response) -> None:
        self.get_response = get_response

    def __call__(self, request):
        if request.path.startswith("/admin/"):
            translation.activate("en")
        return self.get_response(request)


class InjectUserMiddleware:
    """
    Inject user data into the response context.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        self.process_user(request)

        response = self.get_response(request)

        return response

    def process_user(self, request):
        user = request.user
        gtm_user = None
        if user.is_authenticated:
            gtm_user = {
                "username": user.username,
                "email": user.email,
                "full_name": user.get_full_name(),
                "avatar_url": user.profile_picture,
            }
        request.gtm_user = gtm_user


class InjectConfigMiddleware:
    config = {
        "STAPE_EMAIL_GLOBAL": settings.STAPE_EMAIL_GLOBAL,
        "STAPE_EMAIL_EU": settings.STAPE_EMAIL_EU,
    }

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        self.process_config(request)

        response = self.get_response(request)

        return response

    def process_config(self, request):
        request.gtm_config = self.config
