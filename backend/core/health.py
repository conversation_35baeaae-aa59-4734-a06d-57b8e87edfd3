"""
Health check views for monitoring service status
"""

import logging

from django.core.cache import cache
from django.db import connection
from django.http import JsonResponse

logger = logging.getLogger(__name__)


def health_check(request):
    """
    Simple health check endpoint for load balancers and monitoring
    Returns 200 if service is healthy, 503 if not
    """
    health_status = {"status": "healthy"}
    status_code = 200

    # Check database connection
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        health_status["database"] = "ok"
        logger.info("Database health check: OK")
    except Exception as e:
        health_status["database"] = f"error: {str(e)}"
        health_status["status"] = "unhealthy"
        status_code = 503
        logger.error(f"Database health check failed: {str(e)}")

    # Check cache connection (Redis) - but don't fail if cache is not available
    try:
        # Try to set and get a test value
        test_key = "health_check_test"
        test_value = "ok"
        cache.set(test_key, test_value, 30)
        cache_result = cache.get(test_key)

        if cache_result == test_value:
            health_status["cache"] = "ok"
            logger.info("Cache health check: OK")
        else:
            health_status["cache"] = "warning: cache set/get mismatch"
            logger.warning("Cache health check: set/get mismatch")

    except Exception as e:
        health_status["cache"] = f"warning: {str(e)}"
        logger.warning(f"Cache health check failed (non-critical): {str(e)}")
        # Don't fail the entire health check for cache issues in production
        # Cache is often optional for basic functionality

    return JsonResponse(health_status, status=status_code)


def simple_health_check(request):
    """
    Very simple health check that just returns 200
    Use this if the full health check is too complex
    """
    return JsonResponse({"status": "ok"}, status=200)
