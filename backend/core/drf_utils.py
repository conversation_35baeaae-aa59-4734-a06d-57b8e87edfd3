"""
Django REST Framework utilities to replace Djapy functionality
"""

from typing import Any

from django.http import HttpRequest
from rest_framework import serializers, status
from rest_framework.authentication import SessionAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView


class BaseResponseSerializer(serializers.Serializer):
    """Base response serializer for consistent API responses"""

    status_code = serializers.IntegerField()
    message = serializers.CharField()


class SuccessResponseSerializer(BaseResponseSerializer):
    """Success response serializer"""

    data = serializers.JSONField(required=False)


class ErrorResponseSerializer(BaseResponseSerializer):
    """Error response serializer"""

    detail = serializers.CharField(required=False)
    errors = serializers.JSONField(required=False)


class PaginationResponseSerializer(serializers.Serializer):
    """Pagination response serializer"""

    count = serializers.IntegerField()
    next = serializers.URLField(allow_null=True, required=False)
    previous = serializers.URLField(allow_null=True, required=False)
    results = serializers.ListField()


def create_success_response(
    data: Any = None, message: str = "Success", status_code: int = status.HTTP_200_OK
) -> Response:
    """Create a standardized success response"""
    response_data = {
        "status_code": status_code,
        "message": message,
    }
    if data is not None:
        response_data["data"] = data

    return Response(response_data, status=status_code)


def create_paginated_response(
    items: list[Any],
    offset: int,
    limit: int,
    total_items: int,
    message: str = "Items retrieved successfully",
    status_code: int = status.HTTP_200_OK,
) -> Response:
    """Create a standardized paginated response"""
    paginated_data = {
        "items": items,
        "offset": offset,
        "limit": limit,
        "total_items": total_items,
        "has_next": (offset + limit) < total_items,
        "has_previous": offset > 0,
    }

    return create_success_response(data=paginated_data, message=message, status_code=status_code)


def create_error_response(
    message: str = "Error",
    detail: str | None = None,
    errors: dict[str, Any] | None = None,
    status_code: int = status.HTTP_400_BAD_REQUEST,
) -> Response:
    """Create a standardized error response"""
    response_data = {
        "status_code": status_code,
        "message": message,
    }
    if detail:
        response_data["detail"] = detail
    if errors:
        response_data["errors"] = errors

    return Response(response_data, status=status_code)


class BaseAPIView(APIView):
    """Base API view with common functionality"""

    authentication_classes = [SessionAuthentication]
    permission_classes = [IsAuthenticated]

    def handle_exception(self, exc):
        """Handle exceptions and return standardized error responses"""
        if hasattr(exc, "detail"):
            return create_error_response(
                message="Request failed",
                detail=str(exc.detail),
                status_code=getattr(exc, "status_code", status.HTTP_400_BAD_REQUEST),
            )
        return create_error_response(
            message="Internal server error",
            detail=str(exc),
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


class PublicAPIView(BaseAPIView):
    """Public API view that doesn't require authentication"""

    permission_classes = []


class GTMAuthMixin:
    """Mixin to add GTM-specific authentication logic"""

    def get_workspace(self, request: HttpRequest):
        """Get workspace from request (implement based on your GTMAuth logic)"""
        # This should implement the same logic as your GTMAuth class
        return getattr(request, "workspace", None)

    def check_workspace_permission(self, request: HttpRequest) -> bool:
        """Check if user has workspace permission"""
        workspace = self.get_workspace(request)
        return workspace is not None


def serialize_model_list(model_instances: list[Any], serializer_class: type[serializers.Serializer]) -> list[dict]:
    """Helper function to serialize a list of model instances"""
    return [serializer_class(instance).data for instance in model_instances]


def validate_and_serialize(data: dict[str, Any], serializer_class: type[serializers.Serializer]) -> dict[str, Any]:
    """Validate data using a serializer and return serialized data"""
    serializer = serializer_class(data=data)
    serializer.is_valid(raise_exception=True)
    return serializer.validated_data
