"""
Custom views for API documentation with authentication protection.
"""

import os

from django.conf import settings
from django.http import HttpResponse, HttpResponseForbidden
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from django.views.generic import View
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularRedocView,
    SpectacularSwaggerView,
)


class ProtectedDocumentationMixin:
    """
    Mixin to protect documentation views with authentication in production.
    """

    def dispatch(self, request, *args, **kwargs):
        # In production, require authentication
        if not settings.DEBUG:
            if not request.user.is_authenticated:
                return HttpResponseForbidden("Authentication required to access API documentation.")

            # Optional: Add additional permission checks here
            # For example, check if user is staff or has specific permissions
            if not (request.user.is_staff or request.user.is_superuser):
                return HttpResponseForbidden("Insufficient permissions to access API documentation.")

        return super().dispatch(request, *args, **kwargs)


@method_decorator(never_cache, name="dispatch")
class ProtectedSpectacularAPIView(ProtectedDocumentationMixin, SpectacularAPIView):
    """
    Protected OpenAPI schema view.
    """

    pass


@method_decorator(never_cache, name="dispatch")
class ProtectedSpectacularSwaggerView(ProtectedDocumentationMixin, SpectacularSwaggerView):
    """
    Protected Swagger UI view.
    """

    pass


@method_decorator(never_cache, name="dispatch")
class ProtectedSpectacularRedocView(ProtectedDocumentationMixin, SpectacularRedocView):
    """
    Protected ReDoc view.
    """

    pass


class DocsIndexView(ProtectedDocumentationMixin, View):
    """
    Documentation index page that redirects to Swagger UI.
    """

    def get(self, request, *args, **kwargs):
        # Simple redirect to Swagger UI
        from django.shortcuts import redirect

        return redirect("docs-swagger")


# Alternative: Simple password protection without user accounts
class SimplePasswordProtectedMixin:
    """
    Simple password protection for documentation.
    Uses a single password from environment variable.
    """

    def dispatch(self, request, *args, **kwargs):
        if not settings.DEBUG and not request.session.get("docs_authenticated"):
            # Check if password is provided
            password = request.GET.get("password") or request.POST.get("password")
            docs_password = os.environ.get("DOCS_PASSWORD")

            if not docs_password:
                return HttpResponseForbidden("Documentation access not configured.")

            if password != docs_password:
                return HttpResponse(
                    """
                        <html>
                        <head><title>API Documentation Access</title></head>
                        <body>
                            <h2>API Documentation Access</h2>
                            <form method="post">
                                <label for="password">Password:</label>
                                <input type="password" name="password" required>
                                <button type="submit">Access Documentation</button>
                            </form>
                        </body>
                        </html>
                        """,
                    content_type="text/html",
                )

            # Set session flag
            request.session["docs_authenticated"] = True

        return super().dispatch(request, *args, **kwargs)


# Alternative views using simple password protection
@method_decorator(never_cache, name="dispatch")
class SimpleProtectedSpectacularSwaggerView(SimplePasswordProtectedMixin, SpectacularSwaggerView):
    """
    Simple password-protected Swagger UI view.
    """

    pass


@method_decorator(never_cache, name="dispatch")
class SimpleProtectedSpectacularRedocView(SimplePasswordProtectedMixin, SpectacularRedocView):
    """
    Simple password-protected ReDoc view.
    """

    pass


@method_decorator(never_cache, name="dispatch")
class SimpleProtectedSpectacularAPIView(SimplePasswordProtectedMixin, SpectacularAPIView):
    """
    Simple password-protected OpenAPI schema view.
    """

    pass
