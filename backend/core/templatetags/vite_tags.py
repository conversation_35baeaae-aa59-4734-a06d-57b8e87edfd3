import json
import os

from django import template
from django.conf import settings
from django.templatetags.static import static

register = template.Library()


def get_vite_manifest():
    try:
        manifest_path = os.path.join(settings.BASE_DIR, "client", "dist", ".vite", "manifest.json")
        with open(manifest_path) as f:
            return json.load(f)
    except Exception:
        return {}


@register.simple_tag
def vite_asset(entry_point):
    manifest = get_vite_manifest()

    # Handle path normalization
    if not entry_point.startswith("src/pages/"):
        entry_point = f"src/pages/{entry_point}"
    if not entry_point.endswith(".ts"):
        entry_point = f"{entry_point}.ts"

    # Look for the entry point in manifest
    if entry_point in manifest:
        return static(manifest[entry_point]["file"])

    # Fallback
    cleaned_name = entry_point.replace("src/pages/", "").replace(".ts", "")
    return static(f"js/{cleaned_name}.js")
