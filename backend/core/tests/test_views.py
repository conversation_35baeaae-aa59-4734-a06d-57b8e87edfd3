import json
from unittest import TestCase
from unittest.mock import MagicMock

from core.views import BaseView
from django.http import HttpRequest


class BaseViewTest(TestCase):
    def setUp(self):
        # Mock the model and schema
        self.mock_model = MagicMock()
        self.mock_schema_class = MagicMock()

        # Create a subclass of BaseView for testing
        class TestView(BaseView):
            model = self.mock_model
            schema_class = self.mock_schema_class

        self.view = TestView()
        self.request = HttpRequest()

    def test_get_object_found(self):
        instance = MagicMock()
        self.mock_model.objects.get.return_value = instance
        obj = self.view.get_object(pk=1)
        self.assertEqual(obj, instance)
        self.mock_model.objects.get.assert_called_once_with(pk=1)

    def test_serialize(self):
        mock_schema = self.mock_schema_class.return_value
        mock_schema.dump.return_value = {"key": "value"}
        data = self.view.serialize(data={"some": "data"})
        self.assertEqual(data, {"key": "value"})
        mock_schema.dump.assert_called_once_with({"some": "data"}, partial=False)

    def test_deserialize(self):
        mock_schema = self.mock_schema_class.return_value
        mock_schema.load.return_value = {"key": "value"}
        data = self.view.deserialize(data={"some": "data"})
        self.assertEqual(data, {"key": "value"})
        mock_schema.load.assert_called_once_with({"some": "data"}, partial=False)

    def test_parse_json_body_valid(self):
        self.request = HttpRequest()
        self.request._body = json.dumps({"key": "value"}).encode("utf-8")
        self.request._read_started = True
        data = self.view.parse_json_body(self.request)
        self.assertEqual(data, {"key": "value"})

    def test_parse_json_body_invalid(self):
        self.request = HttpRequest()
        self.request._body = b"invalid json"
        self.request._read_started = True
        data = self.view.parse_json_body(self.request)
        self.assertIsNone(data)
