"""
Tests for GTMSessionAuthentication class
"""

from unittest.mock import patch

from core.authentication import GTMSessionAuthentication
from django.contrib.auth import get_user_model
from django.test import RequestFactory, TestCase, override_settings
from rest_framework.request import Request

User = get_user_model()


class GTMSessionAuthenticationTest(TestCase):
    """Test cases for GTMSessionAuthentication"""

    def setUp(self):
        """Set up test fixtures"""
        self.auth_instance = GTMSessionAuthentication()
        self.factory = RequestFactory()
        self.api_key_user = User.objects.create_user(
            username="ayo<PERSON><PERSON>",  # This matches GTM_CREDENTIAL_USER_USERNAME
            email="<EMAIL>",
            password="testpass123",
        )

    @override_settings(DEBUG=True, ADMIN_API_KEY="test-key-123", GTM_CREDENTIAL_USER_USERNAME="ayomipo")
    def test_api_key_authentication_in_debug_mode(self):
        """Test that API key authentication works in DEBUG mode"""
        # Create request with API key header
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should return user and auth token
        self.assertIsNotNone(result)
        user, auth = result
        self.assertEqual(user, self.api_key_user)
        self.assertIsNone(auth)  # We return None for auth token

    @override_settings(DEBUG=True, ADMIN_API_KEY="test-key-123", GTM_CREDENTIAL_USER_USERNAME="ayomipo")
    def test_api_key_authentication_wrong_key(self):
        """Test that wrong API key doesn't authenticate"""
        # Create request with wrong API key
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="wrong-key")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should return None (no authentication)
        self.assertIsNone(result)

    @override_settings(
        DEBUG=False,
        ADMIN_API_KEY="test-key-123",
        GTM_CREDENTIAL_USER_USERNAME="ayomipo",
    )
    def test_api_key_authentication_disabled_in_production(self):
        """Test that API key authentication is disabled when DEBUG=False"""
        # Create request with correct API key
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should return None because DEBUG=False
        self.assertIsNone(result)

    @override_settings(
        DEBUG=True,
        ADMIN_API_KEY="test-key-123",
        GTM_CREDENTIAL_USER_USERNAME="nonexistent",
    )
    def test_api_key_authentication_user_not_found(self):
        """Test behavior when configured user doesn't exist"""
        # Create request with correct API key
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should return None because user doesn't exist
        self.assertIsNone(result)

    @override_settings(DEBUG=True, GTM_CREDENTIAL_USER_USERNAME="ayomipo")  # Missing ADMIN_API_KEY
    def test_api_key_authentication_missing_settings(self):
        """Test behavior when required settings are missing"""
        # Create request with API key
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should return None because ADMIN_API_KEY is not configured
        self.assertIsNone(result)

    @override_settings(DEBUG=True, ADMIN_API_KEY="test-key-123", GTM_CREDENTIAL_USER_USERNAME="ayomipo")
    def test_api_key_authentication_no_header(self):
        """Test behavior when no API key header is provided"""
        # Create request without API key header
        request = self.factory.get("/test/")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should return None because no API key header
        self.assertIsNone(result)

    @override_settings(
        DEBUG=True,
        ADMIN_API_KEY="test-key-123",
        GTM_CREDENTIAL_USER_USERNAME="ayomipo",
        ADMIN_API_KEY_HEADER_NAME="X-CUSTOM-API-KEY",
    )
    def test_custom_api_key_header_name(self):
        """Test that custom API key header name works"""
        # Create request with custom header name
        request = self.factory.get("/test/", HTTP_X_CUSTOM_API_KEY="test-key-123")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should authenticate successfully
        self.assertIsNotNone(result)
        user, auth = result
        self.assertEqual(user, self.api_key_user)

    @override_settings(DEBUG=True, ADMIN_API_KEY="test-key-123", GTM_CREDENTIAL_USER_USERNAME="ayomipo")
    def test_authenticate_via_api_key_method_directly(self):
        """Test the authenticate_via_api_key method directly"""
        # Create request with API key
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
        drf_request = Request(request)

        # Call method directly
        result = self.auth_instance.authenticate_via_api_key(drf_request)

        # Should return the user
        self.assertEqual(result, self.api_key_user)

    @override_settings(DEBUG=True, ADMIN_API_KEY="test-key-123", GTM_CREDENTIAL_USER_USERNAME="ayomipo")
    @patch.object(GTMSessionAuthentication, "add_workspace_context")
    def test_add_workspace_context_called(self, mock_add_workspace):
        """Test that add_workspace_context is called when API key authentication succeeds"""
        # Create request with API key
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should call add_workspace_context
        self.assertIsNotNone(result)
        mock_add_workspace.assert_called_once_with(drf_request, self.api_key_user)


class SecurityTest(TestCase):
    """Test security aspects of the authentication system"""

    def setUp(self):
        """Set up test fixtures"""
        self.auth_instance = GTMSessionAuthentication()
        self.factory = RequestFactory()
        self.api_key_user = User.objects.create_user(
            username="ayomipo", email="<EMAIL>", password="testpass123"
        )

    def test_api_key_authentication_only_in_debug(self):
        """Comprehensive test that API key auth is completely disabled in production"""
        # Test with DEBUG=False (production mode)
        with override_settings(
            DEBUG=False,
            ADMIN_API_KEY="test-key-123",
            GTM_CREDENTIAL_USER_USERNAME="ayomipo",
        ):
            request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
            drf_request = Request(request)

            # Should not authenticate via API key
            result = self.auth_instance.authenticate_via_api_key(drf_request)
            self.assertIsNone(result)

    @override_settings(DEBUG=True, ADMIN_API_KEY="", GTM_CREDENTIAL_USER_USERNAME="ayomipo")
    def test_empty_api_key_setting_disables_auth(self):
        """Test that empty ADMIN_API_KEY setting disables API key authentication"""
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="any-key")
        drf_request = Request(request)

        result = self.auth_instance.authenticate_via_api_key(drf_request)
        self.assertIsNone(result)

    @override_settings(DEBUG=True, ADMIN_API_KEY="test-key-123", GTM_CREDENTIAL_USER_USERNAME="")
    def test_empty_username_setting_disables_auth(self):
        """Test that empty GTM_CREDENTIAL_USER_USERNAME setting disables API key authentication"""
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
        drf_request = Request(request)

        result = self.auth_instance.authenticate_via_api_key(drf_request)
        self.assertIsNone(result)
