[project]
name = "gtm"
version = "0.1.0"
description = ""
authors = [{ name = "<PERSON><PERSON> <PERSON>", email = "<EMAIL>" }]
requires-python = ">=3.12.3,<4"
readme = "README.md"
dependencies = [
    "django>=5.0.6,<6",
    "django-allauth[socialaccount]>=0.63.1,<0.64",
    "django-allauth-2fa>=0.11.1,<0.12",
    "django-waffle>=4.1.0,<5",
    "psycopg2-binary>=2.9.9,<3",
    "django-environ>=0.11.2,<0.12",
    "djangorestframework>=3.15.1,<4",
    "djangorestframework-api-key>=3.0.0,<4",
    "django-anymail~=10.3",
    "django-hijack>=3.4.5,<4",
    "django-storages[google]~=1.14.3",
    "celery-progress>=0.3,<0.4",
    "celery>=5.4.0,<6",
    "requests>=2.32.2,<3",
    "httpx>=0.27.0,<0.28",
    "sentry-sdk>=2.2.1,<3",
    "whitenoise[brotli]>=6.7.0,<7",
    "django-svelte>=0.2.1,<0.3",
    "django-debug-toolbar>=4.3.0,<5",
    "gunicorn>=22.0.0,<23",
    "uvicorn>=0.29.0,<0.30",
    "google-cloud-secret-manager>=2.6.0,<3",
    "google-cloud-logging>=2.7.0,<3",
    "psycogreen>=1.0.2,<2",
    "gevent>=24.2.1,<25",
    "django-redis>=5.4.0,<6",
    "monkey>=0.1,<0.2",
    "django-cors-headers>=4.3.1,<5",
    "collectfast>=2.2.0,<3",
    "django-filter~=24.2",
    "bleach>=6.1.0,<7",
    "marshmallow>=3.22.0,<4",
    "pillow>=10.4.0,<11",
    "semver>=3.0.2,<4",
    "drf-nested-routers>=0.94.1,<0.95",
    "black>=24.10.0,<25",
    "pydantic>=2.11.4,<3",
    "email-validator>=2.2.0,<3",
    "pycln>=2.5.0,<3",
    "drf-spectacular>=0.28.0",
]

[project.optional-dependencies]
tests = []
loadtest = []

[dependency-groups]
dev = [
    "pip-tools>=7.4.1,<8",
    "pre-commit>=3.7.1,<4",
    "ruff>=0.8.0",
    "django-querycount",
    "pytest>=8.2.1,<9",
    "pytest-django>=4.8.0,<5",
    "pytest-cov>=5.0.0,<6",
]

# Ruff configuration
[tool.ruff]
line-length = 120
target-version = "py312"

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG", # flake8-unused-arguments
    "SIM", # flake8-simplify
    "TCH", # flake8-type-checking
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "B904",  # raise from
    "ARG001", # unused function argument
    "ARG002", # unused method argument
]

[tool.ruff.lint.per-file-ignores]
"*/migrations/*" = ["E501", "F401", "F841"]
"*/tests/*" = ["ARG001", "ARG002"]
"manage.py" = ["ARG001"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.ruff.lint.isort]
known-first-party = ["gtm", "apps", "core"]
section-order = ["future", "standard-library", "third-party", "first-party", "local-folder"]

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "gtm.settings.test"
python_files = ["tests.py", "test_*.py"]
filterwarnings = ["ignore::PendingDeprecationWarning", "ignore::DeprecationWarning"]
addopts = "--cov=."

[tool.coverage.run]
omit = [
    ".venv/*",
    "*/migrations/*",
    "*/tests/*",
    "*/tests.py",
    "*/test_*.py",
    "manage.py",
    "gtm/*",
    "gunicorn_config.py",
    "worker.py"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
