#!/usr/bin/env python
"""
Test script to verify resend and cancel invitation endpoints work correctly.
"""

import json
import os
import sys

import django
from django.contrib.auth import get_user_model
from django.test import Client, TestCase
from organizations.models import (
    Organization,
    OrganizationInvitation,
    OrganizationMember,
    OrganizationType,
)

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "gtm.settings.dev")
django.setup()


User = get_user_model()


class InvitationEndpointsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
        )

        # Get the personal organization created by the signal
        self.personal_org = Organization.objects.get(owner=self.user, type=OrganizationType.PERSONAL)

        # Create team organization
        self.team_org = Organization.objects.create(name="Team Org", type=OrganizationType.TEAM, owner=self.user)

        # Create membership for the team organization owner
        OrganizationMember.objects.create(
            organization=self.team_org,
            user=self.user,
            role="owner",
            can_view=True,
            can_create=True,
            can_edit=True,
            can_delete=True,
            is_active=True,
        )

        # Login user
        self.client.force_login(self.user)

    def test_resend_invitation(self):
        """Test the resend invitation endpoint"""
        print("🧪 Testing Resend Invitation Endpoint...")

        # Create test invitation
        invitation = OrganizationInvitation.objects.create(
            organization=self.team_org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
        )

        print(f"📧 Created invitation: {invitation.id}")
        print(f"🏢 Organization: {self.team_org.name} ({self.team_org.id})")

        # Test the resend endpoint
        url = f"/api/v1/organizations/{self.team_org.id}/invitations/{invitation.id}/resend/"
        print(f"🔗 Testing URL: {url}")

        response = self.client.post(url)

        print(f"📊 Response Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("✅ Resend invitation successful!")
            print(f"📋 Response: {json.dumps(data, indent=2, default=str)}")

            # Verify invitation token was regenerated
            invitation.refresh_from_db()
            print(f"🔄 Token regenerated: {invitation.token}")
            return True
        else:
            print("❌ Resend invitation failed!")
            print(f"📋 Response: {response.content.decode()}")
            return False

    def test_cancel_invitation(self):
        """Test the cancel invitation endpoint"""
        print("\n🧪 Testing Cancel Invitation Endpoint...")

        # Create test invitation
        invitation = OrganizationInvitation.objects.create(
            organization=self.team_org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
        )

        print(f"📧 Created invitation: {invitation.id}")

        # Test the cancel endpoint
        url = f"/api/v1/organizations/{self.team_org.id}/invitations/{invitation.id}/cancel/"
        print(f"🔗 Testing URL: {url}")

        response = self.client.post(url)

        print(f"📊 Response Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("✅ Cancel invitation successful!")
            print(f"📋 Response: {json.dumps(data, indent=2, default=str)}")

            # Verify invitation was cancelled
            invitation.refresh_from_db()
            print(f"📋 Invitation status: {invitation.status}")
            return True
        else:
            print("❌ Cancel invitation failed!")
            print(f"📋 Response: {response.content.decode()}")
            return False


def run_tests():
    """Run the invitation endpoint tests"""
    print("🚀 Testing Invitation Endpoints\n")

    # Create test instance
    test_instance = InvitationEndpointsTest()
    test_instance.setUp()

    try:
        # Test resend
        resend_ok = test_instance.test_resend_invitation()

        # Test cancel
        cancel_ok = test_instance.test_cancel_invitation()

        if resend_ok and cancel_ok:
            print("\n🎉 All invitation endpoint tests passed!")
            return True
        else:
            print("\n❌ Some tests failed.")
            return False

    except Exception as e:
        print(f"\n💥 Test failed with error: {str(e)}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
