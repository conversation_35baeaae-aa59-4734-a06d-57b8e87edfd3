import json
import logging

import requests
from allauth.socialaccount.providers.google.provider import GoogleProvider
from allauth.socialaccount.providers.oauth2.views import OAuth2Adapter
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth import login as django_login
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db import transaction
from django.db.models.signals import post_save
from django.http import JsonResponse

logger = logging.getLogger(__name__)

# from djapy import SessionAuth  # Removed - replaced with DRF authentication
from google_tokens.models import GoogleAPITokens

logger = logging.getLogger(__name__)
# CustomUser = get_user_model()


CustomUser = get_user_model()


class ApiLoginRequiredMixin(LoginRequiredMixin):
    """
    A custom LoginRequiredMixin that extends authentication to include an API key.

    This mixin first checks for a valid API key in the request headers for
    programmatic access. If no API key is provided, it falls back to the
    standard session-based authentication for browser access.
    """

    def dispatch(self, request, *args, **kwargs):
        # --- API Key Authentication Path ---
        api_key_header_setting_name = getattr(settings, "ADMIN_API_KEY_HEADER_NAME", "X-Admin-API-Key")
        meta_header_key = f"HTTP_{api_key_header_setting_name.upper().replace('-', '_')}"
        api_key_from_request = request.META.get(meta_header_key)

        if api_key_from_request:
            expected_api_key = getattr(settings, "ADMIN_API_KEY", None)
            gtm_user_username = getattr(settings, "GTM_CREDENTIAL_USER_USERNAME", None)

            if not expected_api_key or not gtm_user_username:
                logger.error("ApiLoginRequiredMixin: API key authentication is not configured.")
                # Fallback to default auth if settings are missing
                return super().dispatch(request, *args, **kwargs)

            if api_key_from_request == expected_api_key:
                try:
                    user = CustomUser.objects.get(username=gtm_user_username)
                    # Log the user in for the context of this request
                    django_login(request, user)
                    logger.info(f"ApiLoginRequiredMixin: Authenticated user '{gtm_user_username}' via API key.")
                    # Now that the user is logged in, proceed with the view
                    return super(LoginRequiredMixin, self).dispatch(request, *args, **kwargs)

                except CustomUser.DoesNotExist:
                    logger.error(
                        f"ApiLoginRequiredMixin: API key is VALID, but the user '{gtm_user_username}' was NOT FOUND."
                    )
                    return JsonResponse(
                        {
                            "error": "Permission Denied",
                            "message": "Configured API user not found.",
                        },
                        status=403,
                    )
            else:
                # API key was provided but is incorrect
                logger.warning("ApiLoginRequiredMixin: Invalid API key provided.")
                return JsonResponse(
                    {"error": "Permission Denied", "message": "Invalid API key."},
                    status=403,
                )

        # --- Fallback to Session Authentication ---
        # If no API key was in the headers, let the original LoginRequiredMixin
        # handle the standard session check and redirect.
        return super().dispatch(request, *args, **kwargs)


class ExtendedGoogleOAuth2Adapter(OAuth2Adapter):
    provider_id = GoogleProvider.id
    access_token_url = "https://oauth2.googleapis.com/token"
    authorize_url = "https://accounts.google.com/o/oauth2/auth"
    profile_url = "https://www.googleapis.com/oauth2/v1/userinfo"

    def complete_login(self, request, app, token, **kwargs):
        resp = requests.get(self.profile_url, params={"access_token": token.token})

        resp.raise_for_status()
        extra_data = resp.json()

        social_login = self.get_provider().sociallogin_from_response(request, extra_data)
        code = request.GET.get("code")

        def create_token_handler(current_token, current_code):
            def store_tokens(sender, instance, created, **kwargs):
                try:
                    with transaction.atomic():
                        GoogleAPITokens.objects.update_or_create(
                            user=instance,
                            defaults={
                                "access_token": current_token.token,
                                "refresh_token": current_token.token_secret,
                                "expires_at": current_token.expires_at,
                                "code": current_code,
                            },
                        )
                finally:
                    dispatch_uid = f"store_google_tokens_{current_token.token[:10]}"
                    post_save.disconnect(sender=sender, dispatch_uid=dispatch_uid)

            return store_tokens

        User = get_user_model()
        token_handler = create_token_handler(token, code)
        dispatch_uid = f"store_google_tokens_{token.token[:10]}"

        post_save.connect(token_handler, sender=User, weak=False, dispatch_uid=dispatch_uid)
        request.oauth_dispatch_uid = dispatch_uid

        return social_login


# class GTMAuth(SessionAuth):  # Removed - replaced with DRF authentication classes
#     def __init__(
#         self,
#         permissions: list[str] | None = None,
#         message_response: dict | None = None,
#         *args,
#         **kwargs,
#     ):
#         # Correct order: *args comes before keyword arguments.
#         # Also includes the fix for the potential None value.
#         super().__init__(
#             *args,
#             permissions=permissions,
#             message_response=message_response or {},
#             **kwargs,
#         )
#
#     def authenticate(self, request, *args, **kwargs):
#         if not settings.DEBUG:
#             return super().authenticate(request, *args, **kwargs)
#
#         api_key_header_setting_name = getattr(
#             settings, "ADMIN_API_KEY_HEADER_NAME", "X-GTM-API-KEY"
#         )
#         meta_header_key = (
#             f"HTTP_{api_key_header_setting_name.upper().replace('-', '_')}"
#         )
#
#         api_key_from_request = request.META.get(meta_header_key)
#
#         if not api_key_from_request:
#             return super().authenticate(request, *args, **kwargs)
#
#         expected_api_key = getattr(settings, "ADMIN_API_KEY", None)
#         gtm_user_username = getattr(settings, "GTM_CREDENTIAL_USER_USERNAME", None)
#
#         if not expected_api_key or not gtm_user_username:
#             logger.error(
#                 "GTMAuth: ADMIN_API_KEY or GTM_CREDENTIAL_USER_USERNAME is not configured in settings. "
#                 "API key authentication path will be skipped, falling back to SessionAuth."
#             )
#             return super().authenticate(request, *args, **kwargs)
#
#         if api_key_from_request == expected_api_key:
#             try:
#                 user = CustomUser.objects.get(username=gtm_user_username)
#                 if not hasattr(user, "backend"):
#                     user.backend = "django.contrib.auth.backends.ModelBackend"
#
#                 django_login(request, user)
#
#                 logger.info(
#                     f"GTMAuth: Authenticated user '{gtm_user_username}' via API key (DEBUG mode)."
#                 )
#
#                 return super().authenticate(request, *args, **kwargs)
#             except CustomUser.DoesNotExist:
#                 logger.error(
#                     f"GTMAuth: API key is VALID, but the configured GTM user "
#                     f"'{gtm_user_username}' was NOT FOUND in the database."
#                 )
#                 return 403, self.message_response
#
#             except Exception as e:
#                 logger.exception(
#                     f"GTMAuth: An unexpected error occurred while fetching GTM user '{gtm_user_username}' "
#                     f"during API key authentication: {e}"
#                 )
#                 return 403, self.message_response
#
#         else:
#             return super().authenticate(request, *args, **kwargs)
#
#     def authorize(self, request: HttpRequest, *args, **kwargs):
#         if not request.user.is_authenticated or not request.user.has_perms(
#             self.permissions
#         ):
#             if not request.user.is_authenticated:
#                 logger.warning("GTMAuth: Authorization failed: User not authenticated.")
#             else:
#                 logger.warning(
#                     f"GTMAuth: Authorization failed: User '{request.user.username}' lacks required permissions: {self.permissions}."
#                 )
#
#             return 403, self.message_response or {
#                 "detail": (
#                     "Authentication credentials were not provided."
#                     if not request.user.is_authenticated
#                     else "You do not have permission to perform this action."
#                 )
#             }
#
#         super().authorize(request, *args, **kwargs)
