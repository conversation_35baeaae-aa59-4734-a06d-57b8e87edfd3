import logging

# from typing import TYPE_CHECKING
import requests
from allauth.socialaccount.models import SocialToken
from allauth.socialaccount.providers.oauth2.client import OAuth2Client
from authentication.utils import ExtendedGoogleOAuth2Adapter
from core.drf_utils import create_error_response, create_success_response
from django.conf import settings
from django.contrib.auth import login as django_login
from django.contrib.auth import logout as django_logout
from django.db import transaction
from django.http import JsonResponse
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_exempt
from google_tokens.models import GoogleAPITokens
from rest_framework import serializers, status
from rest_framework.permissions import AllowAny
from rest_framework.views import APIView

# if TYPE_CHECKING:
from users.models import CustomUser
from users.schemas import PublicUserSerializer

logger = logging.getLogger(__name__)


class GoogleLoginInitiateDataSerializer(serializers.Serializer):
    authorization_url = serializers.URLField()


@method_decorator(never_cache, name="dispatch")
class GoogleAuthorizeURLView(APIView):
    """
    Provides the Google OAuth2 authorization URL for the client to redirect the user.
    """

    permission_classes = [AllowAny]

    def get(self, request):
        """
        Provides the Google OAuth2 authorization URL for the client to redirect the user.
        """
        adapter = ExtendedGoogleOAuth2Adapter(request)
        try:
            api_callback_url = settings.GOOGLE_OAUTH_API_SESSION_CALLBACK_URL
            print(settings.GOOGLE_OAUTH_API_SESSION_CALLBACK_URL)

            # raise Exception(api_callback_url)
            scopes = adapter.get_provider().get_scope()

            auth_params = adapter.get_provider().get_auth_params()

            client = OAuth2Client(
                request,
                settings.GOOGLE_CLIENT_ID,
                settings.GOOGLE_CLIENT_SECRET,
                access_token_url=adapter.access_token_url,
                callback_url=api_callback_url,
                access_token_method=adapter.access_token_method,
            )
            auth_url = client.get_redirect_url(
                authorization_url=adapter.authorize_url,
                scope=scopes,
                extra_params=auth_params,
            )

            return create_success_response(
                data={"authorization_url": auth_url},
                message="Google authorization URL provided",
            )

        except Exception as e:
            logger.exception("Error generating Google auth URL for API session login")
            return create_error_response(
                message="Could not generate Google auth URL",
                detail=str(e),
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


@method_decorator(never_cache, name="dispatch")
class GoogleCallbackView(APIView):
    """
    Handles the OAuth2 callback from Google.
    """

    permission_classes = [AllowAny]

    def get(self, request):
        """
        Handles the OAuth2 callback from Google.
        Exchanges the code for tokens, logs in/creates a Django user,
        establishes a Django session, and returns user details.
        The 'code' (and optionally 'state') are passed as query parameters.
        """
        code = request.GET.get("code")
        # state = request.GET.get("state")  # Not currently used

        if not code:
            return create_error_response(
                message="Missing authorization code",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        logger.info(f"Google API callback for session login. Code: {code[:20]}...")

        adapter = ExtendedGoogleOAuth2Adapter(request)
        provider = adapter.get_provider()

        try:
            api_session_callback_url = settings.GOOGLE_OAUTH_API_SESSION_CALLBACK_URL
            print(settings.GOOGLE_OAUTH_API_SESSION_CALLBACK_URL)

            # raise Exception(api_session_callback_url)

            client = OAuth2Client(
                request,
                settings.GOOGLE_CLIENT_ID,
                settings.GOOGLE_CLIENT_SECRET,
                access_token_url=adapter.access_token_url,
                callback_url=api_session_callback_url,
                access_token_method=adapter.access_token_method,
            )

            token_data = client.get_access_token(code)
            google_access_token = token_data.get("access_token")
            google_refresh_token = token_data.get("refresh_token")
            expires_in = token_data.get("expires_in")

            if not google_access_token:
                logger.error(
                    "Could not retrieve Google access token from response.",
                    extra={"data": token_data},
                )
                return create_error_response(
                    message="Failed to obtain Google access token",
                    status_code=status.HTTP_400_BAD_REQUEST,
                )

            social_token_instance = SocialToken(token=google_access_token)
            if google_refresh_token:
                social_token_instance.token_secret = google_refresh_token
            if expires_in:
                social_token_instance.expires_at = timezone.now() + timezone.timedelta(seconds=int(expires_in))

            profile_resp = requests.get(adapter.profile_url, params={"access_token": google_access_token})
            profile_resp.raise_for_status()
            extra_data = profile_resp.json()

            social_login = provider.sociallogin_from_response(request, extra_data)
            social_login.token = social_token_instance

            # Check if user already exists by email to avoid duplicate creation
            user_email = extra_data.get("email")
            existing_user = None
            if user_email:
                try:
                    existing_user = CustomUser.objects.get(email=user_email)
                    logger.info(f"Found existing user by email: {user_email}")
                except CustomUser.DoesNotExist:
                    logger.info(f"No existing user found for email: {user_email}")

            is_new_user = not social_login.is_existing and not existing_user
            logger.info(
                f"Social login status for {user_email}: is_existing={social_login.is_existing}, existing_user_found={existing_user is not None}, is_new_user={is_new_user}"
            )
            auto_signup_enabled = provider.get_settings().get(
                "auto_signup", getattr(settings, "SOCIALACCOUNT_AUTO_SIGNUP", False)
            )
            if is_new_user and not auto_signup_enabled:
                logger.warning(
                    f"Auto-signup is disabled. User {extra_data.get('email')} cannot be signed up automatically via API."
                )
                return create_error_response(
                    message="User creation via this method is not allowed. Please register first or contact support",
                    status_code=status.HTTP_403_FORBIDDEN,
                )

            # Handle social login manually for API flow to avoid redirects
            try:
                if existing_user:
                    # User already exists - just log them in directly, skip social login process
                    logger.info(f"Logging in existing user {existing_user.email} directly")
                    existing_user.backend = "allauth.account.auth_backends.AuthenticationBackend"
                    django_login(request, existing_user)
                    final_user = existing_user
                elif social_login.is_existing:
                    # Social login recognizes existing user
                    social_login.user.backend = "allauth.account.auth_backends.AuthenticationBackend"
                    django_login(request, social_login.user)
                    final_user = social_login.user
                else:
                    # New user - create account and log them in
                    # Ensure username is set to email before saving to avoid unique constraint violations
                    if social_login.user.email and not social_login.user.username:
                        social_login.user.username = social_login.user.email
                    social_login.save(request, connect=False)
                    social_login.user.backend = "allauth.account.auth_backends.AuthenticationBackend"
                    django_login(request, social_login.user)
                    final_user = social_login.user
            except Exception as e:
                logger.exception(f"Error during social login processing for {extra_data.get('email')}")
                return create_error_response(
                    message="Login processing failed",
                    detail=str(e),
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            # final_user is already set in the try block above
            if not final_user or not final_user.is_active:
                logger.warning(
                    f"User login failed or user is inactive: {final_user.email if final_user else 'Unknown'}"
                )
                return create_error_response(
                    message="User account is not active or login failed",
                    status_code=status.HTTP_403_FORBIDDEN,
                )

            # User is already logged in from the try block above
            logger.info(f"User {final_user.email} logged into Django session successfully.")

            # update user profile picture

            try:
                with transaction.atomic():
                    final_user.profile_picture = extra_data.get("picture")
                    final_user.save()
            except Exception:
                logger.exception(f"Failed to store Profile Picture for user {final_user.email}")

            try:
                with transaction.atomic():
                    expires_at_dt = social_token_instance.expires_at if social_token_instance.expires_at else None
                    GoogleAPITokens.objects.update_or_create(
                        user=final_user,
                        defaults={
                            "access_token": google_access_token,
                            "refresh_token": google_refresh_token,
                            "expires_at": expires_at_dt,
                            "code": code,
                        },
                    )
                logger.info(f"GoogleAPITokens stored/updated for user {final_user.email}")
            except Exception:
                logger.exception(f"Failed to store GoogleAPITokens for user {final_user.email}")

            serializer = PublicUserSerializer(final_user)

            return create_success_response(
                data=serializer.data,
                message="Login/signup successful. Session established",
            )

        except requests.HTTPError as e:
            error_detail = str(e)
            if e.response is not None:
                logger.error(
                    f"HTTP error during Google OAuth (session login): {e.response.status_code} - {e.response.text}",
                    exc_info=True,
                )
                error_detail = f"Google communication error: {e.response.status_code}"
            else:
                logger.error(
                    f"HTTP error during Google OAuth (session login, no response): {e}",
                    exc_info=True,
                )
            return create_error_response(
                message="Error communicating with Google",
                detail=error_detail,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            logger.exception("Critical error in Google API callback for session login")
            return create_error_response(
                message="An unexpected error occurred during login",
                detail=str(e),
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class LogoutResponseDataSerializer(serializers.Serializer):
    message = serializers.CharField()


@csrf_exempt
@never_cache
def logout_view(request):
    """
    Logs out the current user by clearing their Django session.
    Supports both GET and POST requests.
    """
    try:
        logger.info(f"Logout request from user: {request.user}")

        if request.user.is_authenticated:
            user_email = request.user.email
            logger.info(f"Logging out authenticated user: {user_email}")

            # Clear Django session
            django_logout(request)
            response_data = {
                "status_code": 200,
                "message": "User logged out successfully",
                "data": {"message": "User logged out successfully"},
            }
        else:
            logger.info("Logout request from unauthenticated user")
            # Still return success for unauthenticated users to avoid information leakage
            response_data = {
                "status_code": 200,
                "message": "Logout completed",
                "data": {"message": "Logout completed"},
            }

        return JsonResponse(response_data)

    except Exception as e:
        logger.exception("Error during logout")
        return JsonResponse(
            {
                "status_code": 500,
                "message": "An error occurred during logout",
                "detail": str(e),
            },
            status=500,
        )


# Keep the class-based view for backward compatibility but make it use the function
class LogoutView(APIView):
    """
    Logs out the current user by clearing their Django session.
    """

    permission_classes = [AllowAny]

    def post(self, request):
        return logout_view(request)

    def get(self, request):
        return logout_view(request)


# For backward compatibility with existing URL patterns
google_get_authorize_url = GoogleAuthorizeURLView.as_view()
google_api_callback_establish_session = GoogleCallbackView.as_view()
logout_user = LogoutView.as_view()
