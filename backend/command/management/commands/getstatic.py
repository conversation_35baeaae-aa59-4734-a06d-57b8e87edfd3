# management/commands/collectstatic.py

import fnmatch

from django.conf import settings
from django.contrib.staticfiles.management.commands.collectstatic import (
    Command as BaseCollectstaticCommand,
)
from django.contrib.staticfiles.storage import StaticFilesStorage


class Command(BaseCollectstaticCommand):
    def handle(self, *args, **options):
        ignore_patterns = options.get("ignore_patterns", [])
        if hasattr(settings, "IGNORE_PATTERNS"):
            ignore_patterns.extend(settings.IGNORE_PATTERNS)

        # Define the custom storage class to enforce ignore patterns
        class CustomStaticFilesStorage(StaticFilesStorage):
            def _should_ignore(self, path, ignore_patterns=None):
                ignore_patterns = ignore_patterns or []
                return any(fnmatch.fnmatch(path, pattern) for pattern in ignore_patterns)

        self.storage = CustomStaticFilesStorage()

        # Set ignore_patterns in options for use in the storage class
        options["ignore_patterns"] = ignore_patterns
        print(f"Using ignore patterns: {ignore_patterns}")  # Log ignore patterns
        super().handle(*args, **options)
        print("Custom collectstatic command executed")
