from allauth.account import app_settings
from allauth.account.adapter import De<PERSON>ult<PERSON><PERSON>untAdapter
from allauth.account.utils import user_email, user_field
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from allauth_2fa.adapter import OTPAdapter as AllAuthOtpAdapter


class EmailAsUsernameAdapter(DefaultAccountAdapter):
    """
    Adapter that always sets the username equal to the user's email address.
    """

    def populate_username(self, request, user):
        # override the username population to always use the email
        user_field(user, app_settings.USER_MODEL_USERNAME_FIELD, user_email(user))


class NoNewUsersAccountAdapter(DefaultAccountAdapter):
    """
    Adapter that can be used to disable public sign-ups for your app.
    """

    def is_open_for_signup(self, request):
        # see https://stackoverflow.com/a/********/8207
        return False


class SocialAccountAdapter(DefaultSocialAccountAdapter):
    """
    Social account adapter that ensures username is set to email.
    """

    def populate_user(self, request, sociallogin, data):
        """
        Populate user information from social account data.
        """
        user = super().populate_user(request, sociallogin, data)
        # Ensure username is set to email
        if user.email:
            user.username = user.email
        return user


class AccountAdapter(EmailAsUsernameAdapter, AllAuthOtpAdapter):
    pass
