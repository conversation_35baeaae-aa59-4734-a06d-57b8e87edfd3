from core.authentication import GTMSessionAuthentication
from core.drf_utils import create_error_response, create_success_response
from rest_framework import status
from rest_framework.views import APIView
from users.models import CustomUser
from users.schemas import PublicUserSerializer


class UserDetailView(APIView):
    """
    Retrieve the authenticated user's profile
    """

    authentication_classes = [GTMSessionAuthentication]

    def get(self, request):
        """
        Retrieves the authenticated user's profile.
        Returns a PublicUser schema on success, or an error schema on failure.
        """
        try:
            if not request.user or not request.user.is_authenticated:
                return create_error_response(
                    message="Authentication required",
                    status_code=status.HTTP_401_UNAUTHORIZED,
                )

            profile: CustomUser = request.user
            serializer = PublicUserSerializer(profile)

            return create_success_response(data=serializer.data, message="User profile retrieved successfully")

        except CustomUser.DoesNotExist:
            return create_error_response(message="User not found", status_code=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return create_error_response(
                message="An unexpected error occurred",
                detail=str(e),
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# For backward compatibility with existing URL patterns
get_user = UserDetailView.as_view()
