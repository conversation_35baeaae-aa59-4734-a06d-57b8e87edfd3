from rest_framework import serializers


class PublicUserSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    first_name = serializers.CharField(required=False, allow_null=True)
    last_name = serializers.CharField(required=False, allow_null=True)
    email = serializers.EmailField()
    avatar = serializers.URLField(required=False, allow_null=True)


# For backward compatibility
PublicUser = PublicUserSerializer
