#!/usr/bin/env python
"""
Test script to verify resend invitation endpoint works correctly.
"""

import json
import uuid

import pytest
from django.contrib.auth import get_user_model
from django.test import Client
from organizations.models import (
    Organization,
    OrganizationInvitation,
    OrganizationMember,
)

User = get_user_model()


@pytest.mark.django_db
def test_resend_invitation():
    """Test the resend invitation endpoint"""
    print("🧪 Testing Resend Invitation Endpoint...")

    # Create test user with unique username
    unique_id = str(uuid.uuid4())[:8]
    user = User.objects.create_user(
        username=f"testuser_{unique_id}",
        email=f"test_{unique_id}@example.com",
        first_name="Test",
        last_name="User",
    )

    # Create test organization
    org = Organization.objects.create(name="Test Organization", type="team", owner=user)

    # Create organization membership for the user
    OrganizationMember.objects.create(
        organization=org,
        user=user,
        role="admin",
        can_view=True,
        can_create=True,
        can_edit=True,
        can_delete=True,
    )

    # Create test invitation
    invitation = OrganizationInvitation.objects.create(
        organization=org, email="<EMAIL>", role="member", invited_by=user
    )

    print(f"📧 Created invitation: {invitation.id}")
    print(f"🏢 Organization: {org.name} ({org.id})")

    # Test the resend endpoint
    client = Client()
    client.force_login(user)

    url = f"/api/v1/organizations/{org.id}/invitations/{invitation.id}/resend/"
    print(f"🔗 Testing URL: {url}")

    response = client.post(url)

    print(f"📊 Response Status: {response.status_code}")

    assert response.status_code == 200, f"Expected 200, got {response.status_code}: {response.content.decode()}"

    data = response.json()
    print("✅ Resend invitation successful!")
    print(f"📋 Response: {json.dumps(data, indent=2, default=str)}")

    # Verify response structure
    assert "status_code" in data
    assert "message" in data
    assert data["status_code"] == 200


@pytest.mark.django_db
def test_cancel_invitation():
    """Test the cancel invitation endpoint"""
    print("\n🧪 Testing Cancel Invitation Endpoint...")

    # Create test user with unique username
    unique_id = str(uuid.uuid4())[:8]
    user = User.objects.create_user(
        username=f"testuser2_{unique_id}",
        email=f"test2_{unique_id}@example.com",
        first_name="Test",
        last_name="User2",
    )

    # Create test organization
    org = Organization.objects.create(name="Test Organization 2", type="team", owner=user)

    # Create organization membership for the user
    OrganizationMember.objects.create(
        organization=org,
        user=user,
        role="admin",
        can_view=True,
        can_create=True,
        can_edit=True,
        can_delete=True,
    )

    # Create test invitation
    invitation = OrganizationInvitation.objects.create(
        organization=org, email="<EMAIL>", role="member", invited_by=user
    )

    print(f"📧 Created invitation: {invitation.id}")

    # Test the cancel endpoint
    client = Client()
    client.force_login(user)

    url = f"/api/v1/organizations/{org.id}/invitations/{invitation.id}/cancel/"
    print(f"🔗 Testing URL: {url}")

    response = client.post(url)

    print(f"📊 Response Status: {response.status_code}")

    assert response.status_code == 200, f"Expected 200, got {response.status_code}: {response.content.decode()}"

    data = response.json()
    print("✅ Cancel invitation successful!")
    print(f"📋 Response: {json.dumps(data, indent=2, default=str)}")

    # Verify response structure
    assert "status_code" in data
    assert "message" in data
    assert data["status_code"] == 200

    # Verify invitation was cancelled
    invitation.refresh_from_db()
    print(f"📋 Invitation status: {invitation.status}")
    assert invitation.status == "cancelled"


# Tests are run by pytest, no main execution needed
