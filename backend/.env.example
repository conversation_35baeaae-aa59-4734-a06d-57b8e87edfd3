SECRET_KEY='QRopFfkSGwXIRMWmrEoOtiDsMWqoJbSxlvIvwcNX'

# host should be "localhost" for native development or "db" for docker-based development
DATABASE_URL='**************************************/gtm'
DB_REPLICA_1_URL='**************************************/gtm'

DJANGO_SETTINGS_SECRET="{string}"

# host should be "localhost" for native development or "redis" for docker-based development
CACHE_URL='redis://redis:6379'

GOOGLE_ANALYTICS_ID=''
SENTRY_DSN=''

ENABLE_DEBUG_TOOLBAR=True
DEBUG=True


GOOGLE_CLIENT_ID=''
GOOGLE_CLIENT_SECRET=''

SITE_URL='http://localhost:8000'
GTM_WRAPPER_BASE_URL='http://localhost:8001'
ADMIN_API_KEY='1234'


STAPE_EMAIL_GLOBAL=""
STAPE_EMAIL_EU=""


STAPE_AUTH_TOKEN_EU=""
STAPE_AUTH_TOKEN_EU_PARTNER=""
STAPE_AUTH_TOKEN_GLOBAL=""
STAPE_AUTH_TOKEN_GLOBAL_PARTNER=""

# Email Service Configuration
EMAIL_PROVIDER="resend"
RESEND_API_KEY=""
FROM_EMAIL="<EMAIL>"
FROM_NAME="Your App Name"
FRONTEND_URL="http://localhost:3000"
