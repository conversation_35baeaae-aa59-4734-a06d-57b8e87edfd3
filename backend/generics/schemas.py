import math
import os

from rest_framework import serializers
from rest_framework.pagination import LimitOffsetPagination


class ErrorResponseSerializer(serializers.Serializer):
    status_code = serializers.ChoiceField(choices=[400, 401, 403, 404, 405, 409, 500])
    message = serializers.CharField()
    detail = serializers.CharField(required=False, allow_null=True)


class SuccessResponseSerializer(serializers.Serializer):
    status_code = serializers.ChoiceField(choices=[200, 201, 204])
    message = serializers.CharField()
    detail = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    data = serializers.JSONField(required=False, allow_null=True)


# For backward compatibility
ErrorResponseSchema = ErrorResponseSerializer
SuccessResponseSchema = SuccessResponseSerializer


class PaginatedDetailsSerializer(serializers.Serializer):
    items = serializers.ListField()
    offset = serializers.IntegerField()
    limit = serializers.IntegerField()
    has_next = serializers.BooleanField()
    has_previous = serializers.BooleanField()
    total_items = serializers.IntegerField()
    total_pages = serializers.IntegerField()


class CustomLimitOffsetPagination(LimitOffsetPagination):
    """
    Custom pagination that wraps Offset/Limit paginated data
    within the standardized response format.
    """

    default_limit = 10
    limit_query_param = "limit"
    offset_query_param = "offset"
    max_limit = 100

    def get_paginated_response(self, data):
        """Return a paginated style response"""
        from core.drf_utils import create_success_response

        total_items = self.count
        limit = self.limit
        offset = self.offset

        paginated_data = {
            "items": data,
            "offset": offset,
            "limit": limit,
            "has_next": self.get_next_link() is not None,
            "has_previous": self.get_previous_link() is not None,
            "total_items": total_items,
            "total_pages": math.ceil(total_items / limit) if limit > 0 else 0,
        }

        return create_success_response(data=paginated_data, message="Items retrieved successfully")


# For backward compatibility
SuccessWrappedOffsetLimitPagination = CustomLimitOffsetPagination


DEV_MODE = os.environ.get("DJANGO_SETTINGS_MODULE") == "gtm.settings.dev"


class FileFieldSerializer(serializers.Serializer):
    url = serializers.URLField(required=False, allow_null=True)

    def to_representation(self, instance):
        """Add full_url field to representation"""
        # Handle empty file fields gracefully
        if not instance or not hasattr(instance, "url"):
            return {"url": None, "full_url": None}

        try:
            # Try to get the URL - this will raise ValueError if no file is associated
            url = instance.url if instance else None
        except ValueError:
            # File field has no file associated with it
            return {"url": None, "full_url": None}

        data = {"url": url}

        if not url:
            data["full_url"] = None
        elif DEV_MODE and url and not url.startswith(("http://", "https://")):
            data["full_url"] = f"http://localhost:8000{url if url.startswith('/') else '/' + url}"
        else:
            data["full_url"] = url

        return data


# For backward compatibility
FileFieldSchema = FileFieldSerializer
