media
static/css
static/js
static_root
node_modules
npm-debug.log
.env
.direnv
.pytest_cache
fly.toml
.git/
*.sqlite3
__pycache__/
_build/
.cache
.coverage
.coverage.*
.DS_Store
.env
.pytest_cache/
.python-version
.tox/
.venv
**/*.egg-info
**/*.pyc
**/*.pyc
**/*.sw[op]
public/
coverage.xml
db.sqlite3
google_app_credentials.json
htmlcov/
temp/
fly.toml
.git/
*.sqlite3
__pycache__/
_build/
.cache
.coverage
.coverage.*
.DS_Store
.env
.pytest_cache/
.python-version
.tox/
.venv
**/*.egg-info
**/*.pyc
**/*.pyc
**/*.sw[op]
public/
coverage.xml
db.sqlite3
google_app_credentials.json
htmlcov/
temp/
