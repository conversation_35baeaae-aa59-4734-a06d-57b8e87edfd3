from core.models import BaseModel
from django.conf import settings
from django.db import models
from workspaces.models import Workspace


class Domain(BaseModel):
    name = models.CharField(max_length=255)
    workspace = models.ForeignKey(
        Workspace,
        on_delete=models.CASCADE,
        related_name="domains",
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_domains",
    )
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    deleted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name="deleted_domains",
        null=True,
        blank=True,
    )

    class Meta:
        ordering = ["created_at"]
        unique_together = ["name", "workspace"]

    def __str__(self):
        return f"{self.name} ({self.workspace.title})"


class Subdomain(BaseModel):
    name = models.Char<PERSON>ield(max_length=255)
    domain = models.Foreign<PERSON>ey(
        Domain,
        on_delete=models.CASCADE,
        related_name="subdomains",
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_subdomains",
    )
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    deleted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name="deleted_subdomains",
        null=True,
        blank=True,
    )

    class Meta:
        ordering = ["created_at"]
        unique_together = ["name", "domain"]

    def __str__(self):
        return f"{self.name}.{self.domain.name}"

    @property
    def full_uri(self):
        return f"{self.name}.{self.domain.name}"

    @property
    def domain_name(self):
        return self.domain.name

    @property
    def domain_id(self):
        return self.domain.id
