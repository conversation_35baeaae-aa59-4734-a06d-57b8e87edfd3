from ad_networks.models.ad_network import AdNetwork
from ad_networks.schemas import PublicAdNetworkSerializer
from core.drf_utils import create_error_response, create_success_response
from rest_framework.permissions import AllowAny
from rest_framework.views import APIView


class AdNetworkListView(APIView):
    """
    List all active ad networks
    """

    permission_classes = [AllowAny]

    def get(self, request):
        """Get all active ad networks"""
        try:
            queryset = AdNetwork.objects.filter(is_active=True).order_by("-id")
            serializer = PublicAdNetworkSerializer(queryset, many=True)
            return create_success_response(data=serializer.data, message="Ad networks retrieved successfully")
        except Exception as e:
            return create_error_response(message="Failed to retrieve ad networks", detail=str(e))


# For backward compatibility with existing URL patterns
get_ad_networks = AdNetworkListView.as_view()
