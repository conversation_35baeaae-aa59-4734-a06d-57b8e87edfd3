import logging

from core.models import BaseModel
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import models
from django.utils.translation import gettext_lazy as _

from .ad_network import AdNetwork

logger = logging.getLogger(__name__)


class AdNetworkFieldType(models.TextChoices):
    TEXT = "text", _("Text")
    NUMBER = "number", _("Number")
    URL = "url", _("URL")
    EMAIL = "email", _("Email")
    BOOLEAN = "boolean", _("Boolean")
    SELECT = "select", _("Select")
    MULTI_SELECT = "multi_select", _("Multiple Select")


class AdNetworkField(BaseModel):
    """
    Model representing required fields for an ad network
    """

    ad_network = models.ForeignKey(AdNetwork, on_delete=models.CASCADE, related_name="required_fields")
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    field_type = models.CharField(
        max_length=20,
        choices=AdNetworkFieldType.choices,
        default=AdNetworkFieldType.TEXT,
    )
    is_required = models.BooleanField(default=True)
    validation_regex = models.CharField(max_length=500, blank=True)
    options = models.JSONField(default=dict, blank=True, help_text="Options for select/multi-select fields")
    server_only = models.BooleanField(
        default=False,
        help_text="If True, this field should only be shown/used when server_supported is enabled",
    )

    substitution_key = models.CharField(max_length=255, blank=True, null=True)

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_ad_network_fields",
    )

    class Meta:
        unique_together = ["ad_network", "name"]
        ordering = ["ad_network", "name"]

    def __str__(self):
        return f"{self.ad_network.name} - {self.name}"

    def clean(self):
        super().clean()
        if self.server_only and self.is_required:
            raise ValidationError(
                {
                    "is_required": _("Server-only fields cannot be marked as required"),
                    "server_only": _("Required fields cannot be marked as server-only"),
                }
            )

        if self.field_type in [AdNetworkFieldType.SELECT, AdNetworkFieldType.MULTI_SELECT] and not self.options:
            raise ValidationError({"options": _("Options are required for select and multi-select fields")})

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)
