import logging

from ad_networks.models.ad_network import AdNetwork
from ad_networks.models.ad_network_field import Ad<PERSON>work<PERSON>ield, AdNetworkFieldType
from core.models import BaseModel
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import models
from django.utils.translation import gettext_lazy as _
from project.models.project_model import Project

logger = logging.getLogger(__name__)


class ProjectAdNetwork(BaseModel):
    """
    Association model between Project and AdNetwork with field values
    """

    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name="ad_networks")
    ad_network = models.ForeignKey(AdNetwork, on_delete=models.CASCADE, related_name="project_associations")
    field_values = models.JSO<PERSON>ield(default=dict)
    is_active = models.BooleanField(default=True)

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_project_ad_networks",
    )

    class Meta:
        unique_together = ["project", "ad_network"]
        ordering = ["project", "ad_network"]

    def __str__(self):
        return f"{self.project.name} - {self.ad_network.name}"

    def clean(self):
        super().clean()
        errors = {}
        # Only validate non-server-only required fields
        required_fields = self.ad_network.required_fields.filter(is_required=True, server_only=False)

        for field in required_fields:
            if field.name not in self.field_values:
                errors[field.name] = _("This field is required")
                continue

            value = self.field_values[field.name]

            # Validate based on field type
            if field.field_type == AdNetworkFieldType.NUMBER:
                try:
                    float(value)
                except (TypeError, ValueError):
                    errors[field.name] = _("Must be a number")

            elif field.field_type == AdNetworkFieldType.URL:
                from django.core.validators import URLValidator

                validator = URLValidator()
                try:
                    validator(value)
                except ValidationError:
                    errors[field.name] = _("Must be a valid URL")

            elif field.field_type == AdNetworkFieldType.EMAIL:
                from django.core.validators import EmailValidator

                validator = EmailValidator()
                try:
                    validator(value)
                except ValidationError:
                    errors[field.name] = _("Must be a valid email address")

            elif field.field_type == AdNetworkFieldType.SELECT:
                if value not in field.options.get("choices", []):
                    errors[field.name] = _("Invalid choice")

            elif field.field_type == AdNetworkFieldType.MULTI_SELECT:
                if not isinstance(value, list):
                    errors[field.name] = _("Must be a list of choices")
                else:
                    valid_choices = field.options.get("choices", [])
                    if not all(choice in valid_choices for choice in value):
                        errors[field.name] = _("Contains invalid choices")

            # Custom regex validation if specified
            if field.validation_regex and isinstance(value, str):
                import re

                if not re.match(field.validation_regex, value):
                    errors[field.name] = _("Value does not match required format")

        if errors:
            raise ValidationError(errors)

    # --- ADD THIS NEW PROPERTY ---
    @property
    def network_fields_data(self) -> list["AdNetworkField"]:
        """
        Explicitly returns the list of prefetched required_fields.
        This provides a clear, unambiguous list for Pydantic to serialize,
        using the cache populated by the view's `prefetch_related`.
        """
        if hasattr(self, "_prefetched_objects_cache") and "required_fields" in self._prefetched_objects_cache:
            return self._prefetched_objects_cache["required_fields"]

        # This is a fallback that will run if the data was not prefetched.
        return self.required_fields.all()
