import logging

from core.models import BaseModel
from core.utils.model_utils import validate_file_size
from django.conf import settings
from django.core.exceptions import SuspiciousFileOperation
from django.core.files.storage import default_storage
from django.core.validators import FileExtensionValidator
from django.db import models

logger = logging.getLogger(__name__)


class AdNetwork(BaseModel):
    """
    Model representing an advertising network (e.g., AdMob, Facebook Ads)
    """

    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    icon = models.FileField(
        upload_to="ad_network_icons/",
        blank=True,
        null=True,
        validators=[
            validate_file_size,
            FileExtensionValidator(allowed_extensions=["jpg", "jpeg", "png", "gif", "webp", "svg"]),
        ],
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_ad_networks",
    )

    network_key = models.CharField(max_length=255, blank=True)

    class Meta:
        ordering = ["name"]

    def __str__(self):
        return self.name

    def delete_icon_file(self):
        if self.icon:
            try:
                if default_storage.exists(self.icon.name):
                    default_storage.delete(self.icon.name)
            except SuspiciousFileOperation:
                logger.exception(f"Error deleting file: {self.icon.name}")

    def delete(self, *args, **kwargs):
        self.delete_icon_file()
        super().delete(*args, **kwargs)

    def get_available_fields(self, server_supported=False):
        """
        Get fields that are available based on server support status
        """
        fields = self.required_fields.all()  # type: ignore
        if not server_supported:
            fields = fields.exclude(server_only=True)
        return fields

    @property
    def available_fields(self):
        """
        This property calls the method with its default parameters.
        Pydantic can access this simple property without issues.
        """
        return self.get_available_fields()
