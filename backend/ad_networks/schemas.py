import json

from generics.schemas import <PERSON><PERSON>ieldSerializer
from rest_framework import serializers


class AdNetworkFieldSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    description = serializers.CharField(required=False, allow_null=True)
    field_type = serializers.CharField()
    is_required = serializers.BooleanField()
    options = serializers.JSONField(required=False, allow_null=True)
    server_only = serializers.BooleanField()

    def to_internal_value(self, data):
        """Parse options field if it's a string"""
        if "options" in data and isinstance(data["options"], str):
            try:
                data["options"] = json.loads(data["options"])
            except json.JSONDecodeError:
                data["options"] = None
        return super().to_internal_value(data)


class PublicAdNetworkSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    icon = FileFieldSerializer(required=False, allow_null=True)
    network_key = serializers.Char<PERSON><PERSON>(required=False, allow_null=True)
    name = serializers.Char<PERSON>ield(required=False, allow_null=True)
    description = serializers.Char<PERSON>ield(required=False, allow_null=True)
    is_active = serializers.BooleanField()
    available_fields = AdNetworkFieldSerializer(many=True, required=False)

    def to_representation(self, instance):
        """Convert model instance to serialized representation"""
        data = {
            "id": instance.id,
            "icon": ({"url": instance.icon.url if instance.icon else None} if hasattr(instance, "icon") else None),
            "network_key": getattr(instance, "network_key", None),
            "name": getattr(instance, "name", None),
            "description": getattr(instance, "description", None),
            "is_active": getattr(instance, "is_active", True),
            "available_fields": [],
        }

        # Handle available_fields if they exist
        if hasattr(instance, "available_fields"):
            fields = (
                instance.available_fields.all()
                if hasattr(instance.available_fields, "all")
                else instance.available_fields
            )
            data["available_fields"] = AdNetworkFieldSerializer(fields, many=True).data

        return data


class ProjectAdNetworkSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    is_active = serializers.BooleanField()
    field_values = serializers.JSONField(default=dict)
    ad_network = PublicAdNetworkSerializer(required=False, allow_null=True)


# For backward compatibility
AdNetworkFieldSchema = AdNetworkFieldSerializer
PublicAdNetworkSchema = PublicAdNetworkSerializer
ProjectAdNetworkSchema = ProjectAdNetworkSerializer
