# Generated by Django 5.0.6 on 2025-05-24 14:49

import core.utils.model_utils
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AdNetwork",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True)),
                ("is_active", models.<PERSON><PERSON>anField(default=True)),
                (
                    "icon",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="ad_network_icons/",
                        validators=[
                            core.utils.model_utils.validate_file_size,
                            django.core.validators.FileExtensionValidator(
                                allowed_extensions=[
                                    "jpg",
                                    "jpeg",
                                    "png",
                                    "gif",
                                    "webp",
                                    "svg",
                                ]
                            ),
                        ],
                    ),
                ),
                ("network_key", models.CharField(blank=True, max_length=255)),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="AdNetworkField",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True)),
                (
                    "field_type",
                    models.CharField(
                        choices=[
                            ("text", "Text"),
                            ("number", "Number"),
                            ("url", "URL"),
                            ("email", "Email"),
                            ("boolean", "Boolean"),
                            ("select", "Select"),
                            ("multi_select", "Multiple Select"),
                        ],
                        default="text",
                        max_length=20,
                    ),
                ),
                ("is_required", models.BooleanField(default=True)),
                ("validation_regex", models.CharField(blank=True, max_length=500)),
                (
                    "options",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Options for select/multi-select fields",
                    ),
                ),
                (
                    "server_only",
                    models.BooleanField(
                        default=False,
                        help_text="If True, this field should only be shown/used when server_supported is enabled",
                    ),
                ),
                (
                    "substitution_key",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "ordering": ["ad_network", "name"],
            },
        ),
        migrations.CreateModel(
            name="ProjectAdNetwork",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("field_values", models.JSONField(default=dict)),
                ("is_active", models.BooleanField(default=True)),
            ],
            options={
                "ordering": ["project", "ad_network"],
            },
        ),
    ]
