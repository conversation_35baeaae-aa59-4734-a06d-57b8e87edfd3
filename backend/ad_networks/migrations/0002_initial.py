# Generated by Django 5.0.6 on 2025-05-24 14:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("ad_networks", "0001_initial"),
        ("projects", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="adnetwork",
            name="created_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="created_ad_networks",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="adnetworkfield",
            name="ad_network",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="required_fields",
                to="ad_networks.adnetwork",
            ),
        ),
        migrations.AddField(
            model_name="adnetworkfield",
            name="created_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="created_ad_network_fields",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="projectadnetwork",
            name="ad_network",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="project_associations",
                to="ad_networks.adnetwork",
            ),
        ),
        migrations.AddField(
            model_name="projectadnetwork",
            name="created_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="created_project_ad_networks",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="projectadnetwork",
            name="project",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="ad_networks",
                to="projects.project",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="adnetworkfield",
            unique_together={("ad_network", "name")},
        ),
        migrations.AlterUniqueTogether(
            name="projectadnetwork",
            unique_together={("project", "ad_network")},
        ),
    ]
