import re

from ad_networks.models.ad_network import AdNetwork
from ad_networks.models.ad_network_field import AdNetworkField
from core.utils.model_utils import JSONTextAreaWidget
from django import forms
from django.contrib import admin
from django.core.exceptions import ValidationError
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _


class AdNetworkFieldForm(forms.ModelForm):
    class Meta:
        model = AdNetworkField
        fields = "__all__"

    def clean(self):
        cleaned_data = super().clean()
        server_only = cleaned_data.get("server_only")
        is_required = cleaned_data.get("is_required")

        if server_only and is_required:
            raise ValidationError(
                {
                    "is_required": _("Server-only fields cannot be marked as required"),
                    "server_only": _("Required fields cannot be marked as server-only"),
                }
            )
        return cleaned_data


class AdNetworkFieldInline(admin.TabularInline):
    model = AdNetworkField
    form = AdNetworkFieldForm
    extra = 1
    fields = [
        "name",
        "description",
        "field_type",
        "is_required",
        "substitution_key",
        "validation_regex",
        "options",
        "server_only",
        "created_by",
    ]

    def formfield_for_dbfield(self, db_field, **kwargs):
        if db_field.name == "options":
            kwargs["widget"] = JSONTextAreaWidget(attrs={"rows": 3, "cols": 40})
            kwargs["help_text"] = format_html(
                'For select/multi-select fields, use format: {{"choices": ["option1", "option2"]}}'
            )
        return super().formfield_for_dbfield(db_field, **kwargs)

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)

        class CustomFormset(formset):
            def clean(self):
                super().clean()
                for form in self.forms:
                    if not form.is_valid():
                        continue  # Skip forms with validation errors from individual field cleaning
                    # Ensure cleaned_data is available
                    if not hasattr(form, "cleaned_data"):
                        continue

                    server_only = form.cleaned_data.get("server_only")
                    is_required = form.cleaned_data.get("is_required")
                    substitution_key = form.cleaned_data.get("substitution_key")

                    if server_only and is_required:
                        # This validation is better handled in AdNetworkFieldForm.clean()
                        # to show error next to the specific field.
                        # However, keeping it here as a formset-level check is also possible.
                        # For now, let's assume AdNetworkFieldForm.clean() handles this.
                        pass  # Validation handled in AdNetworkFieldForm

                    if substitution_key and not re.match(r"^___.*___$", substitution_key):
                        form.add_error(
                            "substitution_key",
                            "Substitution key must start and end with ___",
                        )

        return CustomFormset


class AdNetworkAdminForm(forms.ModelForm):
    class Meta:
        model = AdNetwork
        fields = "__all__"

    def clean_icon(self):
        icon = self.cleaned_data.get("icon")
        if icon and icon.size > 200 * 1024:  # 200KB
            raise forms.ValidationError("The maximum file size that can be uploaded is 200KB")
        return icon


class AdNetworkAdmin(admin.ModelAdmin):
    form = AdNetworkAdminForm
    list_display = [
        "name",
        "description",
        "is_active",
        "field_count",
        "network_key",
        "created_by",
        "created_at",
    ]
    list_filter = ["is_active", "created_at"]
    search_fields = ["name", "description"]
    readonly_fields = ["created_at", "display_icon"]
    inlines = [AdNetworkFieldInline]

    def display_icon(self, obj):
        if obj.icon:
            return format_html(
                '<img src="{}" style="max-width: 100px; max-height: 100px;" />',
                obj.icon.url,
            )
        return "No icon"

    display_icon.short_description = "Current Icon"

    def get_fieldsets(self, request, obj=None):
        fieldsets = [
            (
                None,
                {
                    "fields": (
                        "name",
                        "description",
                        "is_active",
                        "network_key",
                        "created_by",
                    )
                },
            ),
            ("Icon", {"fields": ("icon", "display_icon"), "classes": ("collapse",)}),
        ]
        if obj:  # Only show metadata for existing objects
            fieldsets.append(
                (
                    "Metadata",
                    {"fields": ("created_by", "created_at"), "classes": ("collapse",)},
                )
            )
        return fieldsets

    def field_count(self, obj):
        return obj.required_fields.count()

    # # Assuming AdNetwork has a related manager named 'fields' or 'adnetworkfield_set'
    # return obj.adnetworkfield_set.count()  # Or obj.fields.count() if related_name is 'fields'

    field_count.short_description = "Number of Fields"

    def save_model(self, request, obj, form, change):
        if not obj.pk:  # Set created_by only on creation
            obj.created_by = request.user

        # Handle icon deletion if a new icon is uploaded for an existing object
        if obj.pk and "icon" in form.changed_data and form.cleaned_data.get("icon"):
            # Get the old instance to check its icon
            old_obj = self.model.objects.get(pk=obj.pk)
            if old_obj.icon:
                old_obj.delete_icon_file()  # Assuming delete_icon_file method exists on model

        super().save_model(request, obj, form, change)

    def save_formset(self, request, form, formset, change):
        instances = formset.save(commit=False)

        for obj_del in formset.deleted_objects:
            obj_del.delete()

        for instance in instances:
            # Set created_by for new inline instances if it's not set by model's save
            if not instance.pk and hasattr(instance, "created_by") and not instance.created_by:
                instance.created_by = request.user
            instance.save()
        formset.save_m2m()


class AdNetworkFieldAdmin(admin.ModelAdmin):
    form = AdNetworkFieldForm  # Use the form with clean method
    list_display = [
        "name",
        "ad_network",
        "field_type",
        "is_required",
        "substitution_key",
        "created_by",
    ]
    list_filter = ["ad_network", "field_type", "is_required"]
    search_fields = ["name", "ad_network__name"]
    readonly_fields = ["created_by", "created_at"]

    def formfield_for_dbfield(self, db_field, **kwargs):
        if db_field.name == "options":
            kwargs["widget"] = JSONTextAreaWidget(attrs={"rows": 3, "cols": 40})
            kwargs["help_text"] = format_html(
                'For select/multi-select fields, use format: {{"choices": ["option1", "option2"]}}'
            )
        return super().formfield_for_dbfield(db_field, **kwargs)

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


admin.site.register(AdNetwork, AdNetworkAdmin)
admin.site.register(AdNetworkField, AdNetworkFieldAdmin)
