# project/schemas/project_schema.py

# -*- coding: utf-8 -*-
from enum import Enum

from ad_networks.schemas import ProjectAdNetworkSerializer
from domain.schemas import PublicSubDomainSerializer
from rest_framework import serializers


class ProjectStatusEnum(str, Enum):
    DEPLOYED = "deployed"
    ARCHIVED = "archived"
    DRAFT = "draft"


class CreateProjectSerializer(serializers.Serializer):
    """
    Schema for data required to create a new project.
    This mirrors the fields collected by SvelteKit form.
    """

    name = serializers.CharField()
    subdomain = serializers.CharField()
    domain = serializers.CharField()
    is_server_side = serializers.BooleanField()
    project_reasons = serializers.ListField(child=serializers.IntegerField())
    project_type = serializers.IntegerField()
    project_platform = serializers.IntegerField()
    workspace = serializers.IntegerField()
    description = serializers.CharField(required=False, allow_blank=True, default="")
    id = serializers.IntegerField(required=False, allow_null=True)
    ad_networks = serializers.ListField(child=serializers.IntegerField())


class UpdateProjectSerializer(serializers.Serializer):
    """
    Schema for PARTIALLY updating a project. All fields are optional.
    """

    name = serializers.CharField(required=False, allow_null=True)
    subdomain = serializers.CharField(required=False, allow_null=True)
    domain = serializers.CharField(required=False, allow_null=True)
    is_server_side = serializers.BooleanField(required=False, allow_null=True)
    project_reasons = serializers.ListField(child=serializers.IntegerField(), required=False, allow_null=True)
    project_type = serializers.IntegerField(required=False, allow_null=True)
    project_platform = serializers.IntegerField(required=False, allow_null=True)
    description = serializers.CharField(required=False, allow_null=True)
    status = serializers.CharField(required=False, allow_null=True)
    ad_networks = serializers.ListField(child=serializers.IntegerField(), required=False, allow_null=True)
    raw_data = serializers.DictField(required=False, allow_null=True)


class ProjectCreateDetailSerializer(serializers.Serializer):
    """
    A basic schema for the response after creating a project.
    """

    id = serializers.IntegerField()


class ProjectPurposeSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()


class ProjectPublicDetailSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    status = serializers.CharField()
    server_supported = serializers.BooleanField()
    subdomain = PublicSubDomainSerializer(required=False, allow_null=True)
    workspace_id = serializers.IntegerField()

    project_type_id = serializers.IntegerField()
    project_platform_id = serializers.IntegerField()
    project_purposes = ProjectPurposeSerializer(many=True, default=list)
    ad_network = ProjectAdNetworkSerializer(many=True, required=False, allow_null=True, default=list)
    raw_data = serializers.DictField(required=False, allow_null=True)


# Backward compatibility
CreateProjectSchema = CreateProjectSerializer
UpdateProjectSchema = UpdateProjectSerializer
ProjectCreateDetailSchema = ProjectCreateDetailSerializer
ProjectPurposeSchema = ProjectPurposeSerializer
ProjectPublicDetailSchema = ProjectPublicDetailSerializer
