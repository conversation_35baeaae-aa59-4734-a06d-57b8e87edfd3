from generics.schemas import FileFieldSerializer
from rest_framework import serializers


class PublicProjectPlatformSerializer(serializers.Serializer):
    name = serializers.CharField()
    description = serializers.CharField(required=False, allow_null=True)
    id = serializers.IntegerField()
    icon = FileFieldSerializer(required=False, allow_null=True)


# Backward compatibility
PublicProjectPlatformSchema = PublicProjectPlatformSerializer
