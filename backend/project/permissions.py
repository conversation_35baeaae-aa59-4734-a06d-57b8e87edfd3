# from django.http import HttpRequest
#
# from authentication.utils import GTMAuth
# from project.models.project_model import Project
#
#
# class ProjectModifierAuth(GTMAuth):  # Removed - replaced with DRF permissions
#     def __init__(
#         self,
#         permissions: list[str] = None,
#         message_response: dict = None,
#         *args,
#         **kwargs,
#     ):
#         super().__init__(permissions, message_response, *args, **kwargs)
#
#     def authenticate(self, request, **kwargs):
#         auth_result = super().authenticate(request)
#         if isinstance(auth_result, tuple):
#             return auth_result
#
#         return self.authorize(request, **kwargs)
#
#     def authorize(self, request: HttpRequest, **kwargs):
#         if not request.user.is_authenticated:
#             return 403, self.message_response
#
#         is_get = request.method == "GET"
#
#         # if request.method == "GET":
#         #     return super().authorize(request)
#
#         project_id = kwargs.get("id")
#
#         if not project_id and is_get:
#             return super().authorize(request)
#
#         if not project_id:
#             return 400, {"message": "Method requires a project id"}
#
#         try:
#             project = (
#                 Project.objects.select_related(
#                     "workspace__organization",
#                     "subdomain__domain",
#                     "project_type",
#                     "project_platform",
#                 )
#                 .prefetch_related(
#                     "purpose_associations__project_purpose",  # Also prefetch purposes
#                     "ad_networks__ad_network__required_fields",  # Our main prefetch for ad networks
#                 )
#                 .get(id=project_id)
#             )
#             workspace = project.workspace
#
#             # Check if user has access to this project through workspace permissions
#             has_access = False
#
#             # Check direct workspace ownership
#             if workspace.owner == request.user:
#                 has_access = True
#
#             # Check organization membership if workspace belongs to an organization
#             elif workspace.organization:
#                 # Determine required permission based on request method
#                 required_permission = "view"
#                 if request.method in ["POST", "PUT", "PATCH"]:
#                     required_permission = "edit"
#                 elif request.method == "DELETE":
#                     required_permission = "delete"
#
#                 has_access = workspace.organization.has_permission(request.user, required_permission)
#
#             if not has_access:
#                 return 403, {"message": "You do not have permission to access this project."}
#
#         except Project.DoesNotExist:
#             return 404, {"message": "Project not found or you do not have permission to access it."}
#
#         request.project = project
#         return super().authorize(request, **kwargs)
