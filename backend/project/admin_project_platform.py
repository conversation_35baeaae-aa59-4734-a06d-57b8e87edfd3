from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.contrib.auth import get_user_model
from django.http import HttpResponseRedirect
from django.urls import path, reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from google_tokens.models import GoogleAPITokens
from project.models.project_platform_model import ProjectPlatform
from workspaces.admin_mixins import JsonReportAdminMixin

User = get_user_model()


class BaseIconAdminForm(forms.ModelForm):
    def clean_icon(self):
        icon = self.cleaned_data.get("icon")
        if icon and icon.size > 200 * 1024:
            raise forms.ValidationError("The maximum file size that can be uploaded is 200KB")
        return icon


class BaseIconAdmin(admin.ModelAdmin):
    list_display = ["name", "description", "created_by", "is_deleted"]
    fields = ["name", "description", "icon", "created_by"]

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


class ProjectPlatformAdminForm(BaseIconAdminForm):
    class Meta:
        model = ProjectPlatform
        fields = "__all__"


class ProjectPlatformAdmin(JsonReportAdminMixin, BaseIconAdmin):
    form = ProjectPlatformAdminForm

    list_display_gtm = [
        "id",
        "initiate_gtm_extraction_button_list_display",
    ]

    list_display = list(BaseIconAdmin.list_display) + list_display_gtm

    gtm_readonly_fields_tuple = (
        "initiate_gtm_extraction_button_detail",
        "display_latest_verification_report_from_active_snapshot",
        "active_snapshot_status_display",
    )
    readonly_fields = tuple(set(getattr(BaseIconAdmin, "readonly_fields", ()) + gtm_readonly_fields_tuple))

    def get_fieldsets(self, request, obj=None):
        fieldsets = super().get_fieldsets(request, obj)
        base_fieldsets = []
        if fieldsets:
            base_fieldsets.append(
                (
                    "Details",
                    {"fields": fieldsets[0][1]["fields"], "classes": ("collapse",)},
                )
            )

        current_fieldsets = list(base_fieldsets)

        gtm_config_description = (
            "Configure GTM details. Extraction will use credentials of the user "
            f"'{settings.GTM_CREDENTIAL_USER_USERNAME}' (defined in settings)."
            if hasattr(settings, "GTM_CREDENTIAL_USER_USERNAME")
            else "<b>Warning:</b> GTM_CREDENTIAL_USER_USERNAME not set in Django settings. Extraction will fail."
        )

        current_fieldsets.append(
            (
                "GTM Configuration for Template Extraction",
                {
                    "fields": (
                        "origin_platform",
                        "active_snapshot",
                        "gtm_platform_account_id",
                        "gtm_platform_container_id",
                        "gtm_platform_workspace_id",
                        "gtm_ad_network_account_id",
                        "gtm_ad_network_container_id",
                        "gtm_ad_network_workspace_id",
                        "initiate_gtm_extraction_button_detail",
                    ),
                    "description": format_html(gtm_config_description),
                    "classes": ("collapse",),
                },
            )
        )
        current_fieldsets.append(
            (
                "Reporting",
                {
                    "fields": (
                        "display_latest_verification_report_from_active_snapshot",
                        "active_snapshot_status_display",
                    ),
                    "classes": ("collapse",),
                    "description": "Latest reporting from the Snapshot",
                },
            )
        )
        return current_fieldsets

    def _can_initiate_extraction(self, obj):
        if not hasattr(settings, "GTM_CREDENTIAL_USER_USERNAME"):
            return False, "GTM_CREDENTIAL_USER_USERNAME not set in Django settings."

        platform_configured = (
            obj.pk and obj.gtm_platform_account_id and obj.gtm_platform_container_id and obj.gtm_platform_workspace_id
        )

        ad_network_configured = (
            obj.pk
            and obj.gtm_ad_network_account_id
            and obj.gtm_ad_network_container_id
            and obj.gtm_ad_network_workspace_id
        )

        if not (platform_configured and ad_network_configured):
            return (
                False,
                "Save ProjectType and set all GTM IDs (Platform, and Ad Network).",
            )  # Should be ProjectPlatform

        try:
            gtm_user = User.objects.get(username=settings.GTM_CREDENTIAL_USER_USERNAME)
            google_tokens = GoogleAPITokens.objects.get(user=gtm_user)
            if not hasattr(google_tokens, "access_token") or not google_tokens.access_token:
                return (
                    False,
                    f"Google API Tokens not found for GTM credential user '{gtm_user.username}'. Ensure they have authenticated via Google.",
                )

        except User.DoesNotExist:
            return (
                False,
                f"GTM credential user '{settings.GTM_CREDENTIAL_USER_USERNAME}' not found in the system.",
            )
        return True, ""

    def initiate_gtm_extraction_button_shared(self, obj, context_type="list"):
        can_extract, reason = self._can_initiate_extraction(obj)

        button_text = "Fetch GTM Data"
        if can_extract:
            url = reverse("admin:workspaces_projectplatform_initiate_extraction", args=[obj.pk])
            return format_html('<a class="button" href="{}">{}</a>', url, button_text)

        reason_html = (
            f'<p style="color: red; margin-top: 5px;">Cannot extract: {reason}</p>'
            if context_type == "detail" and reason
            else ""
        )
        disabled_button_html = f'<button class="button" disabled title="{reason if reason else "Extraction disabled"}">{button_text}</button>'

        if context_type == "list" and reason:
            return format_html(disabled_button_html)
        return format_html(f"{disabled_button_html}{reason_html}")

    def initiate_gtm_extraction_button_list_display(self, obj):
        return self.initiate_gtm_extraction_button_shared(obj, context_type="list")

    initiate_gtm_extraction_button_list_display.short_description = "GTM Extract"

    def initiate_gtm_extraction_button_detail(self, obj):
        return self.initiate_gtm_extraction_button_shared(obj, context_type="detail")

    initiate_gtm_extraction_button_detail.short_description = "GTM Actions"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                "<int:object_id>/initiate-extraction/",
                self.admin_site.admin_view(self.initiate_gtm_extraction_view),
                name="workspaces_projectplatform_initiate_extraction",
            )
        ]
        return custom_urls + urls

    def initiate_gtm_extraction_view(self, request, object_id):
        try:
            # Model is ProjectPlatform, not ProjectType here
            project_platform_obj = self.get_queryset(request).get(pk=object_id)
        except ProjectPlatform.DoesNotExist:
            self.message_user(request, "ProjectPlatform not found.", level=messages.ERROR)
            return HttpResponseRedirect(reverse("admin:workspaces_projectplatform_changelist"))

        can_extract, reason = self._can_initiate_extraction(project_platform_obj)
        if not can_extract:
            self.message_user(request, f"Cannot initiate extraction: {reason}", level=messages.ERROR)
            return HttpResponseRedirect(
                reverse(
                    "admin:workspaces_projectplatform_change",
                    args=[project_platform_obj.pk],
                )
            )

        from extractor.tasks import extract_gtm_data_task

        gtm_user = User.objects.get(username=settings.GTM_CREDENTIAL_USER_USERNAME)

        task = extract_gtm_data_task.delay(project_platform_obj.id, gtm_user.id, request.user.id)
        self.message_user(
            request,
            f"GTM data extraction task (ID: {task.id}) has been initiated for '{project_platform_obj.name}'.",
            level=messages.SUCCESS,
        )
        return HttpResponseRedirect(
            reverse(
                "admin:workspaces_projectplatform_change",
                args=[project_platform_obj.pk],
            )
        )

    def display_latest_verification_report_from_active_snapshot(self, obj):
        if not obj:
            return "N/A"
        if not obj.latest_verification_report:  # Check this field directly
            # Check for active snapshot to provide more context
            if not obj.active_snapshot:
                return mark_safe("<em>No active snapshot set.</em>")
            return mark_safe(
                f"<em>Active snapshot (ID: {obj.active_snapshot.pk}) found, but no verification report data on ProjectPlatform.</em>"
            )
        return self.format_json_for_admin_display(
            obj.latest_verification_report,
            title="Latest Verification Report",
            unique_id_prefix=f"pp{obj.pk or 'new'}",
        )

    display_latest_verification_report_from_active_snapshot.short_description = "Latest Verification Report"

    def active_snapshot_status_display(self, obj):
        if not obj:
            return "N/A"
        if obj.latest_verification_status and obj.active_snapshot:
            from extractor.models import SnapshotStatus  # Ensure this import is valid

            return (
                SnapshotStatus(obj.latest_verification_status).label
                if obj.latest_verification_status in SnapshotStatus.values
                else obj.latest_verification_status
            )
        elif obj.active_snapshot:
            return mark_safe("<em>Status not available for active snapshot.</em>")
        return mark_safe("<em>No active snapshot set.</em>")

    active_snapshot_status_display.short_description = "Active Snapshot Status"
