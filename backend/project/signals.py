from django.db.models.signals import post_delete, pre_save
from django.dispatch import receiver
from project.models.project_platform_model import ProjectPlatform
from project.models.project_type_model import ProjectType


@receiver(pre_save, sender=ProjectType)
def delete_old_icon_on_update(sender, instance, **kwargs):
    if instance.pk:
        try:
            old_instance = ProjectType.objects.get(pk=instance.pk)
            if old_instance.icon and old_instance.icon != instance.icon:
                old_instance.delete_icon_file()
        except ProjectType.DoesNotExist:
            pass


@receiver(post_delete, sender=ProjectType)
def delete_type_icon_on_delete(sender, instance, **kwargs):
    instance.delete_icon_file()


@receiver(pre_save, sender=ProjectPlatform)
def delete_old_platform_icon_on_update(sender, instance, **kwargs):
    if instance.pk:
        try:
            old_instance = ProjectPlatform.objects.get(pk=instance.pk)
            if old_instance.icon and old_instance.icon != instance.icon:
                old_instance.delete_icon_file()
        except ProjectPlatform.DoesNotExist:
            pass


@receiver(post_delete, sender=ProjectPlatform)
def delete_icon_on_delete(sender, instance, **kwargs):
    instance.delete_icon_file()
