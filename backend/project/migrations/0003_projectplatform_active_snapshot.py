# Generated by Django 5.0.6 on 2025-05-24 15:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("extractor", "0002_initial"),
        ("projects", "0002_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="projectplatform",
            name="active_snapshot",
            field=models.ForeignKey(
                blank=True,
                help_text="The currently selected snapshot to be used as the base template.",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="active_for_project_platforms",
                to="extractor.gtmsnapshot",
            ),
        ),
    ]
