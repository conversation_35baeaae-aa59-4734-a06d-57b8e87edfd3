# Generated by Django 5.0.6 on 2025-05-24 14:49

import core.utils.model_utils
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Project",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=255)),
                ("server_supported", models.BooleanField(default=False)),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("deployed", "Deployed"),
                            ("archived", "Archived"),
                            ("draft", "Draft"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("raw_data", models.JSONField(blank=True, default=dict, null=True)),
                ("deploy_data", models.JSONField(blank=True, default=dict, null=True)),
            ],
            options={
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="ProjectPlatform",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True)),
                (
                    "icon",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="project_type_icons/",
                        validators=[
                            core.utils.model_utils.validate_file_size,
                            django.core.validators.FileExtensionValidator(
                                allowed_extensions=[
                                    "jpg",
                                    "jpeg",
                                    "png",
                                    "gif",
                                    "webp",
                                    "svg",
                                ]
                            ),
                        ],
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "gtm_platform_account_id",
                    models.CharField(
                        blank=True,
                        help_text="GTM Platform Account ID for this template source.",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "gtm_platform_container_id",
                    models.CharField(
                        blank=True,
                        help_text="GTM Platform Container ID for this template source.",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "gtm_platform_workspace_id",
                    models.CharField(
                        blank=True,
                        help_text="GTM Platform Workspace ID to extract data from.",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "gtm_ad_network_account_id",
                    models.CharField(
                        blank=True,
                        help_text="GTM AdNetwork Account ID for this template source.",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "gtm_ad_network_container_id",
                    models.CharField(
                        blank=True,
                        help_text="GTM AdNetwork Container ID for this template source.",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "gtm_ad_network_workspace_id",
                    models.CharField(
                        blank=True,
                        help_text="GTM AdNetwork Workspace ID to extract data from.",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "origin_platform",
                    models.CharField(
                        help_text="Name of the origin platform in the notes field, this helps the locate the tags to parse",
                        max_length=100,
                    ),
                ),
                (
                    "latest_verification_status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("RAW_FETCHING", "Raw Data Fetching"),
                            ("RAW_FETCHED", "Raw Data Fetched"),
                            ("RAW_FETCH_FAILED", "Raw Data Fetch Failed"),
                            ("LOGICAL_PROCESSING", "Logical Model Processing"),
                            ("LOGICAL_FAILED", "Logical Model Failed"),
                            ("VERIFIED_OK", "Verified - OK"),
                            ("VERIFIED_WITH_WARNINGS", "Verified - With Warnings"),
                            ("VERIFICATION_FAILED", "Verification Failed"),
                        ],
                        help_text="Status of the latest verification run on the active_snapshot.",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "latest_verification_report",
                    models.JSONField(
                        blank=True,
                        help_text="Report from the latest verification run on the active_snapshot.",
                        null=True,
                    ),
                ),
            ],
            options={
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="ProjectPurpose",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True)),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="ProjectPurposeAssociation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="ProjectType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True)),
                (
                    "icon",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="project_type_icons/",
                        validators=[
                            core.utils.model_utils.validate_file_size,
                            django.core.validators.FileExtensionValidator(
                                allowed_extensions=[
                                    "jpg",
                                    "jpeg",
                                    "png",
                                    "gif",
                                    "webp",
                                    "svg",
                                ]
                            ),
                        ],
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="ProjectTypePlatformAssociation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="ProjectVersion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("version_number", models.CharField(max_length=20)),
                ("data", models.JSONField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("is_remote", models.BooleanField(default=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]
