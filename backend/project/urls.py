import project.views.project as project
import project.views.project_platform as project_platform
import project.views.project_purpose as project_purpose
import project.views.project_type as project_type
from django.urls import path

app_name = "project"
urlpatterns = [
    path(
        "project-purposes/",
        project_purpose.get_project_purposes,
        name="project_purposes",
    ),
    path("project-types/", project_type.get_project_types, name="project_types"),
    path(
        "project-platforms/",
        project_platform.get_project_platforms,
        name="project_platforms",
    ),
    path(
        "project-platform/by-project-type/<int:project_type_id>/",
        project_platform.get_project_platforms_by_project_type,
        name="project_platforms_by_project_type",
    ),
    path("<int:id>/", project.detail, name="detail_project"),
    path(
        "<int:id>/ad-networks/<int:ad_network_id>/",
        project.update_ad_network,
        name="update_project_ad_network",
    ),
    path("project/create", project.create, name="create_project"),
    path(
        "workspaces/<int:id>/projects/",
        project.get_projects_by_workspace,
        name="workspace_projects",
    ),
]
