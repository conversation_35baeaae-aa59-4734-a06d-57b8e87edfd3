import logging

from core.models import BaseModel
from core.utils.model_utils import validate_file_size
from django.conf import settings
from django.core.exceptions import SuspiciousFileOperation
from django.core.files.storage import default_storage
from django.core.validators import FileExtensionValidator
from django.db import models

logger = logging.getLogger(__name__)


class ProjectPlatform(BaseModel):
    """
    Model representing a project platform.
    """

    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)

    icon = models.FileField(
        upload_to="project_type_icons/",
        blank=True,
        null=True,
        validators=[
            validate_file_size,
            FileExtensionValidator(allowed_extensions=["jpg", "jpeg", "png", "gif", "webp", "svg"]),
        ],
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_project_platforms",
    )

    is_deleted = models.Bo<PERSON>anField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    deleted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="deleted_project_platforms",
        null=True,
        blank=True,
    )

    # platform info
    gtm_platform_account_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="GTM Platform Account ID for this template source.",
    )
    gtm_platform_container_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="GTM Platform Container ID for this template source.",
    )
    gtm_platform_workspace_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="GTM Platform Workspace ID to extract data from.",
    )

    # ad_network info
    gtm_ad_network_account_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="GTM AdNetwork Account ID for this template source.",
    )
    gtm_ad_network_container_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="GTM AdNetwork Container ID for this template source.",
    )
    gtm_ad_network_workspace_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="GTM AdNetwork Workspace ID to extract data from.",
    )

    origin_platform = models.CharField(
        max_length=100,
        help_text="Name of the origin platform in the notes field, this helps the locate the tags to parse",
    )

    active_snapshot = models.ForeignKey(
        "extractor.GTMSnapshot",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="active_for_project_platforms",
        help_text="The currently selected snapshot to be used as the base template.",
    )
    from extractor.enums import SnapshotStatus

    latest_verification_status = models.CharField(
        max_length=50,
        choices=SnapshotStatus.choices,
        null=True,
        blank=True,
        help_text="Status of the latest verification run on the active_snapshot.",
    )
    latest_verification_report = models.JSONField(
        null=True,
        blank=True,
        help_text="Report from the latest verification run on the active_snapshot.",
    )

    class Meta:
        ordering = ["created_at"]

    def __str__(self):
        return self.name

    def delete_icon_file(self):
        if self.icon:
            try:
                if default_storage.exists(self.icon.name):
                    default_storage.delete(self.icon.name)
            except SuspiciousFileOperation:
                logger.exception(f"Error deleting file: {self.icon.name}")

    def delete(self, *args, **kwargs):
        # Delete the icon file
        self.delete_icon_file()
        # Call the "real" delete() method
        super().delete(*args, **kwargs)

    def get_created_by(self):
        return self.created_by

    def get_deleted_by(self):
        return self.deleted_by
