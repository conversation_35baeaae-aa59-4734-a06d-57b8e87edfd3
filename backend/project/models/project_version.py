import logging

import semver
from django.conf import settings
from django.db import models
from project.models.project_model import Project

logger = logging.getLogger(__name__)


class ProjectVersion(models.Model):
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name="versions")
    version_number = models.CharField(max_length=20)  # Semantic versioning
    data = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_versions",
    )
    is_remote = models.BooleanField(default=True)
    parent_version = models.ForeignKey(
        "self",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="child_versions",
    )

    class Meta:
        ordering = ["-created_at"]
        unique_together = ["project", "version_number"]

    def __str__(self):
        return f"{self.project.name} - v{self.version_number}"

    @classmethod
    def increment_version(cls, current_version, increment_type="patch"):
        """
        Increment version number using semver
        increment_type can be 'major', 'minor', or 'patch'
        """
        version = semver.VersionInfo.parse(current_version)
        if increment_type == "major":
            return str(version.bump_major())
        elif increment_type == "minor":
            return str(version.bump_minor())
        return str(version.bump_patch())

    @classmethod
    def get_latest_version(cls, project):
        return cls.objects.filter(project=project).order_by("-created_at").first()
