import logging

from core.models import BaseModel
from django.db import models

logger = logging.getLogger(__name__)


class ProjectTypePlatformAssociation(BaseModel):
    project_type = models.ForeignKey(
        "ProjectType",
        on_delete=models.CASCADE,
        related_name="platform_associations",
    )
    project_platform = models.ForeignKey(
        "ProjectPlatform",
        on_delete=models.CASCADE,
        related_name="type_associations",
    )

    class Meta:
        unique_together = ("project_type", "project_platform")

    def __str__(self):
        return f"{self.project_type} - {self.project_platform}"
