import logging

from core.models import BaseModel
from django.conf import settings
from django.db import models

logger = logging.getLogger(__name__)


class ProjectPurpose(BaseModel):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_project_purposes",
    )

    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    deleted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="deleted_project_purposes",
        null=True,
        blank=True,
    )

    class Meta:
        ordering = ["created_at"]

    def __str__(self):
        return self.name

    def get_created_by(self):
        return self.created_by

    def get_deleted_by(self):
        return self.deleted_by
