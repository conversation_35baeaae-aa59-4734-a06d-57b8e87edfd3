import logging

from core.models import BaseModel
from django.db import models

logger = logging.getLogger(__name__)


class ProjectPurposeAssociation(BaseModel):
    project = models.ForeignKey(
        "Project",
        on_delete=models.CASCADE,
        related_name="purpose_associations",
    )
    project_purpose = models.ForeignKey(
        "ProjectPurpose",
        on_delete=models.CASCADE,
        related_name="project_associations",
    )

    class Meta:
        unique_together = ("project", "project_purpose")

    def __str__(self):
        return f"{self.project} - {self.project_purpose}"
