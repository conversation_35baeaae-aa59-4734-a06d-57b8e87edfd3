from core.models import BaseModel
from django.conf import settings
from django.db import models
from django.forms import ValidationError
from django.utils.translation import gettext_lazy as _
from domain.models import Subdomain
from project.models.project_purpose import ProjectPurpose
from project.models.project_purpose_association import ProjectPurposeAssociation
from project.models.project_type_platform_association import (
    ProjectTypePlatformAssociation,
)

# from workspaces.models import Workspace


class ProjectStatus(models.TextChoices):
    DEPLOYED = "deployed", _("Deployed")
    ARCHIVED = "archived", _("Archived")
    DRAFT = "draft", _("Draft")


class Project(BaseModel):
    name = models.CharField(max_length=255)
    workspace = models.ForeignKey(
        "workspaces.Workspace",
        on_delete=models.CASCADE,
        related_name="projects",
    )
    project_type = models.ForeignKey(
        "ProjectType",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="projects",
    )
    project_platform = models.ForeignKey(
        "ProjectPlatform",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="projects",
    )

    subdomain = models.ForeignKey(
        Subdomain,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="projects",
    )

    server_supported = models.BooleanField(default=False)

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_projects",
    )
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    deleted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="deleted_projects",
        null=True,
        blank=True,
    )

    status = models.CharField(max_length=20, choices=ProjectStatus.choices, default=ProjectStatus.DRAFT)

    raw_data = models.JSONField(null=True, blank=True, default=dict)

    deploy_data = models.JSONField(null=True, blank=True, default=dict)

    class Meta:
        ordering = ["created_at"]

    def __str__(self):
        return self.name

    def clean(self):
        project_type_and_platform = self.project_type and self.project_platform
        if (
            project_type_and_platform
            and not ProjectTypePlatformAssociation.objects.filter(
                project_type=self.project_type, project_platform=self.project_platform
            ).exists()
        ):
            raise ValidationError("Invalid project type and platform combination.")

    def get_workspace(self):
        return self.workspace

    def get_project_type(self):
        return self.project_type

    def get_domain(self):
        return self.subdomain.domain if self.subdomain else None

    def get_full_domain(self):
        return self.subdomain.full_name if self.subdomain else None

    def set_purpose(self, purpose):
        ProjectPurposeAssociation.objects.get_or_create(project=self, project_purpose=purpose)

    def get_purposes(self):
        return ProjectPurpose.objects.filter(project_associations__project=self)

    @property
    def project_purposes(self):
        if hasattr(self, "_prefetched_objects_cache") and "purpose_associations" in self._prefetched_objects_cache:
            return [assoc.project_purpose for assoc in self.purpose_associations.all()]

        return self.get_purposes()

    @property
    def ad_network(self):
        """
        Explicitly returns the list of prefetched ProjectAdNetwork objects.
        This provides a clear, unambiguous list for Pydantic to serialize.
        """
        if hasattr(self, "_prefetched_objects_cache") and "ad_networks" in self._prefetched_objects_cache:
            return self._prefetched_objects_cache["ad_networks"]

        # Fallback to a database query if not prefetched (less efficient but safe)
        return self.ad_networks.all()
