import logging

from core.models import BaseModel
from core.utils.model_utils import validate_file_size
from django.conf import settings
from django.core.exceptions import SuspiciousFileOperation
from django.core.files.storage import default_storage
from django.core.validators import FileExtensionValidator
from django.db import models

logger = logging.getLogger(__name__)


class ProjectType(BaseModel):
    """
    Model representing a project type.
    """

    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)

    icon = models.FileField(
        upload_to="project_type_icons/",
        blank=True,
        null=True,
        validators=[
            validate_file_size,
            FileExtensionValidator(allowed_extensions=["jpg", "jpeg", "png", "gif", "webp", "svg"]),
        ],
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_project_types",
    )

    is_deleted = models.Bo<PERSON>anField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    deleted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="deleted_project_types",
        null=True,
        blank=True,
    )

    class Meta:
        ordering = ["created_at"]

    def __str__(self):
        return self.name

    def delete_icon_file(self):
        if self.icon:
            try:
                if default_storage.exists(self.icon.name):
                    default_storage.delete(self.icon.name)
            except SuspiciousFileOperation:
                logger.exception(f"Error deleting file: {self.icon.name}")

    def delete(self, *args, **kwargs):
        # Delete the icon file
        self.delete_icon_file()
        # Call the "real" delete() method
        super().delete(*args, **kwargs)

    def get_created_by(self):
        return self.created_by

    def get_deleted_by(self):
        return self.deleted_by
