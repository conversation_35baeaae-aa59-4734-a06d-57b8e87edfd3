from ad_networks.models.ad_network_project import ProjectAdNetwork
from django import forms
from django.contrib import admin, messages
from django.http import HttpResponseRedirect
from django.urls import path, reverse
from django.utils.html import format_html
from project.admin_project_platform import ProjectPlatformAdmin
from project.models.project_model import Project
from project.models.project_platform_model import ProjectPlatform
from project.models.project_purpose import ProjectPurpose
from project.models.project_type_model import ProjectType
from project.models.project_type_platform_association import (
    ProjectTypePlatformAssociation,
)
from project.models.project_version import ProjectVersion

try:
    from extractor.tasks import PLATFORM_ORCHESTRATOR_MAP
except ImportError:
    PLATFORM_ORCHESTRATOR_MAP = {}


class BaseIconAdminForm(forms.ModelForm):
    def clean_icon(self):
        icon = self.cleaned_data.get("icon")
        if icon and icon.size > 200 * 1024:
            raise forms.ValidationError("The maximum file size that can be uploaded is 200KB")
        return icon


class BaseIconAdmin(admin.ModelAdmin):
    list_display = ["name", "description", "created_by", "is_deleted"]
    fields = ["name", "description", "icon", "created_by"]

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


class ProjectAdmin(admin.ModelAdmin):
    list_display = [
        "name",
        "workspace",
        "project_type",
        "project_platform",
        "initiate_build_logical_model_button_list_display",
    ]
    list_filter = ["workspace", "project_type", "project_platform"]
    # Add the new readonly field for the detail view button.
    # If ProjectAdmin had other readonly_fields, append to them: e.g., readonly_fields = ("other_field",) + ("initiate_build_logical_model_button_detail",)
    readonly_fields = ("initiate_build_logical_model_button_detail",)

    def _can_initiate_build_logical_model(self, obj: Project) -> tuple[bool, str]:
        if not obj.pk:
            return False, "Project must be saved first."
        if not obj.project_platform:
            return False, "Project does not have an associated Project Platform."
        if not obj.project_platform.origin_platform:
            return False, "Project Platform does not have an origin platform specified."

        platform_key = obj.project_platform.origin_platform.lower()
        if not PLATFORM_ORCHESTRATOR_MAP or platform_key not in PLATFORM_ORCHESTRATOR_MAP:
            return (
                False,
                f"No orchestrator configured for platform '{platform_key}' or orchestrator map is unavailable.",
            )

        if not obj.project_platform.active_snapshot:
            return (
                False,
                "Project Platform does not have an active GTM snapshot. Please extract GTM data first via Project Platform admin.",
            )
        return True, ""

    def initiate_build_logical_model_button_shared(self, obj: Project, context_type="list") -> str:
        can_build, reason = self._can_initiate_build_logical_model(obj)

        button_text = "Build Logical Model"
        if can_build:
            url = reverse("admin:workspaces_project_initiate_build_logical_model", args=[obj.pk])
            return format_html('<a class="button" href="{}">{}</a>', url, button_text)

        reason_html = (
            f'<p style="color: red; margin-top: 5px;">Cannot build: {reason}</p>'
            if context_type == "detail" and reason
            else ""
        )
        disabled_button_html = (
            f'<button class="button" disabled title="{reason if reason else "Build disabled"}">{button_text}</button>'
        )

        if context_type == "list" and reason:  # In list view, tooltip shows reason
            return format_html(disabled_button_html)
        return format_html(f"{disabled_button_html}{reason_html}")

    def initiate_build_logical_model_button_list_display(self, obj: Project) -> str:
        return self.initiate_build_logical_model_button_shared(obj, context_type="list")

    initiate_build_logical_model_button_list_display.short_description = "Build Model"

    def initiate_build_logical_model_button_detail(self, obj: Project) -> str:
        # This method is for the readonly_fields. It will display the button (or disabled state) in the form.
        return self.initiate_build_logical_model_button_shared(obj, context_type="detail")

    initiate_build_logical_model_button_detail.short_description = "Logical Model Actions"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                "<int:object_id>/initiate-build-logical-model/",
                self.admin_site.admin_view(self.initiate_build_logical_model_view),
                name="workspaces_project_initiate_build_logical_model",
            )
        ]
        return custom_urls + urls

    def initiate_build_logical_model_view(self, request, object_id: int):
        try:
            project = self.get_queryset(request).get(pk=object_id)
        except Project.DoesNotExist:
            self.message_user(request, "Project not found.", level=messages.ERROR)
            return HttpResponseRedirect(reverse("admin:workspaces_project_changelist"))

        can_build, reason = self._can_initiate_build_logical_model(project)
        if not can_build:
            self.message_user(
                request,
                f"Cannot initiate logical model build: {reason}",
                level=messages.ERROR,
            )
            return HttpResponseRedirect(reverse("admin:workspaces_project_change", args=[project.pk]))

        # Import task locally to avoid potential circular imports at module level
        from extractor.tasks import build_logical_model_task

        task = build_logical_model_task.delay(project.id, request.user.id)
        self.message_user(
            request,
            f"Logical model build task (ID: {task.id}) has been initiated for project '{project.name}'.",
            level=messages.SUCCESS,
        )

        return HttpResponseRedirect(reverse("admin:workspaces_project_change", args=[project.pk]))


class ProjectTypeAdminForm(BaseIconAdminForm):
    class Meta:
        model = ProjectType
        fields = "__all__"


class ProjectTypeAdmin(BaseIconAdmin):
    form = ProjectTypeAdminForm


class ProjectTypePlatformAssociationAdmin(admin.ModelAdmin):
    list_display = ["project_type", "project_platform"]
    list_filter = ["project_type", "project_platform"]


class ProjectAdNetworkAdmin(admin.ModelAdmin):
    list_display = ["project", "ad_network", "is_active", "created_by", "created_at"]
    list_filter = ["ad_network", "is_active", "created_at"]
    search_fields = ["project__name", "ad_network__name"]
    readonly_fields = ["created_by", "created_at"]

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


class ProjectVersionAdminForm(forms.ModelForm):
    class Meta:
        model = ProjectVersion
        fields = "__all__"


class ProjectVersionAdmin(admin.ModelAdmin):
    form = ProjectVersionAdminForm
    list_display = [
        "id",
        "project",
        "version_number",
        "created_at",
        "created_by",
    ]  # Added more fields for better display
    list_filter = ["project", "created_by"]
    search_fields = ["project__name", "version_number"]
    readonly_fields = ["created_at", "created_by"]


admin.site.register(Project, ProjectAdmin)
admin.site.register(ProjectType, ProjectTypeAdmin)
admin.site.register(ProjectTypePlatformAssociation, ProjectTypePlatformAssociationAdmin)
admin.site.register(ProjectPurpose)
admin.site.register(ProjectPlatform, ProjectPlatformAdmin)
admin.site.register(ProjectAdNetwork, ProjectAdNetworkAdmin)
admin.site.register(ProjectVersion, ProjectVersionAdmin)
