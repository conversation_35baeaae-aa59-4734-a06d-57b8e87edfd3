from core.drf_utils import create_success_response
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from project.models.project_type_model import ProjectType
from project.schemas.project_type_schema import PublicProjectTypeSerializer
from rest_framework.permissions import AllowAny
from rest_framework.views import APIView


@method_decorator(csrf_exempt, name="dispatch")
class ProjectTypeListView(APIView):
    """Get all project types"""

    permission_classes = [AllowAny]

    def get(self, request):
        """Get all project types"""
        q = ProjectType.objects.all().order_by("-id")
        serializer = PublicProjectTypeSerializer(q, many=True)
        return create_success_response(data=serializer.data, message="Project types retrieved successfully")


# Backward compatibility
get_project_types = ProjectTypeListView.as_view()
