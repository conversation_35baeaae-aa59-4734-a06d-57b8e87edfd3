from core.drf_utils import create_success_response
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from project.models.project_purpose import ProjectPurpose
from project.schemas.project_purpose_schema import PublicProjectPurposeSerializer
from rest_framework.permissions import AllowAny
from rest_framework.views import APIView


@method_decorator(csrf_exempt, name="dispatch")
class ProjectPurposeListView(APIView):
    """Get all project purposes"""

    permission_classes = [AllowAny]

    def get(self, request):
        """Get all project purposes"""
        q = ProjectPurpose.objects.all().order_by("-id")
        serializer = PublicProjectPurposeSerializer(q, many=True)
        return create_success_response(data=serializer.data, message="Project purposes retrieved successfully")


# Backward compatibility
get_project_purposes = ProjectPurposeListView.as_view()
