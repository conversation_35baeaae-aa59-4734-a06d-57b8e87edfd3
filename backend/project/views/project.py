from ad_networks.models.ad_network_project import ProjectAdNetwork
from ad_networks.schemas import ProjectAdNetworkSerializer
from core.authentication import GTMSessionAuthentication
from core.drf_utils import (
    create_error_response,
    create_paginated_response,
    create_success_response,
)
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from domain.models import Domain, Subdomain
from project.models.project_model import Project
from project.models.project_platform_model import ProjectPlatform
from project.models.project_purpose_association import ProjectPurposeAssociation
from project.models.project_type_model import ProjectType
from project.models.project_version import ProjectVersion
from project.schemas.project_schema import (
    CreateProjectSerializer,
    ProjectCreateDetailSerializer,
    ProjectPublicDetailSerializer,
    UpdateProjectSerializer,
)
from rest_framework import serializers, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from workspaces.models import Workspace


@method_decorator(csrf_exempt, name="dispatch")
class ProjectCreateView(APIView):
    """Create a new project"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    @transaction.atomic
    def post(self, request):
        """Create a new project"""
        serializer = CreateProjectSerializer(data=request.data)
        if not serializer.is_valid():
            return create_error_response(
                message="Invalid data",
                detail=serializer.errors,
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        data = serializer.validated_data
        user = request.user

        try:
            # Get workspace and check permissions
            workspace_obj = Workspace.objects.get(id=data["workspace"])

            # Check if user has permission to create projects in this workspace
            has_permission = False

            # Check direct ownership
            if workspace_obj.owner == user:
                has_permission = True

            # Check organization membership if workspace belongs to an organization
            elif workspace_obj.organization:
                has_permission = workspace_obj.organization.has_permission(user, "create")

            if not has_permission:
                return create_error_response(
                    message="You do not have permission to create projects in this workspace",
                    status_code=status.HTTP_403_FORBIDDEN,
                )

            ProjectType.objects.get(id=data["project_type"])
            ProjectPlatform.objects.get(id=data["project_platform"])
        except Workspace.DoesNotExist:
            return create_error_response(message="Workspace not found", status_code=status.HTTP_404_NOT_FOUND)
        except (ProjectType.DoesNotExist, ProjectPlatform.DoesNotExist):
            return create_error_response(
                message="Invalid project type or platform",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Handle Domain and Subdomain creation
        domain_obj, _ = Domain.objects.get_or_create(
            name=data["domain"], workspace=workspace_obj, defaults={"created_by": user}
        )

        subdomain_obj, created = Subdomain.objects.get_or_create(
            name=data["subdomain"], domain=domain_obj, defaults={"created_by": user}
        )
        if not created and Project.objects.filter(subdomain=subdomain_obj).exists():
            return create_error_response(
                message=f"Subdomain '{data['subdomain']}.{data['domain']}' is already in use",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Create the main Project object
        project = Project.objects.create(
            name=data["name"],
            workspace=workspace_obj,
            subdomain=subdomain_obj,
            project_type_id=data["project_type"],
            project_platform_id=data["project_platform"],
            server_supported=data.get("is_server_side", False),
            created_by=user,
            status="draft",
        )

        # Create ProjectPurpose associations
        if data.get("project_reasons"):
            associations = [
                ProjectPurposeAssociation(project=project, project_purpose_id=purpose_id)
                for purpose_id in data["project_reasons"]
            ]
            ProjectPurposeAssociation.objects.bulk_create(associations)

        # Create an initial ProjectVersion
        ProjectVersion.objects.create(
            project=project,
            version_number="0.1.0",
            created_by=user,
            is_remote=True,
            data={},
        )

        # Create AdNetwork associations
        if data.get("ad_networks"):
            ad_network_associations = [
                ProjectAdNetwork(project=project, ad_network_id=ad_network_id, created_by=user)
                for ad_network_id in data["ad_networks"]
            ]
            ProjectAdNetwork.objects.bulk_create(ad_network_associations)

        project_serializer = ProjectCreateDetailSerializer(project)
        return create_success_response(
            data=project_serializer.data,
            message="Project created successfully",
            status_code=status.HTTP_201_CREATED,
        )


@method_decorator(csrf_exempt, name="dispatch")
class ProjectsByWorkspaceView(APIView):
    """Get projects by workspace with filtering and search"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, id):
        """Get projects by workspace with filtering and search"""
        # Get workspace - this would typically be handled by WorkspaceModifierAuth
        try:
            from workspaces.models import Workspace

            workspace = Workspace.objects.get(id=id)
        except Workspace.DoesNotExist:
            return create_error_response(message="Workspace not found", status_code=status.HTTP_404_NOT_FOUND)

        # Check workspace permissions
        has_access = False
        if workspace.owner == request.user:
            has_access = True
        elif workspace.organization:
            has_access = workspace.organization.has_permission(request.user, "view")

        if not has_access:
            return create_error_response(
                message="You are not authorized to access this workspace",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        # Get pagination parameters
        offset = int(request.GET.get("offset", 0))
        limit = int(request.GET.get("limit", 10))

        # Get query parameters
        search = request.GET.get("search")
        status_filter = request.GET.get("status")

        # Build query
        q = Project.objects.filter(workspace=workspace)
        q = q.select_related("workspace", "subdomain__domain").prefetch_related(
            # Prefetch the ProjectAdNetwork objects, their related AdNetwork,
            # and THAT AdNetwork's required_fields all at once.
            "ad_networks__ad_network__required_fields"
        )

        if search:
            q = q.filter(name__icontains=search)
        if status_filter and status_filter in ["deployed", "archived", "draft"]:
            q = q.filter(status=status_filter)

        # Get total count before pagination
        total_items = q.count()

        # Apply pagination
        projects = q.order_by("-id")[offset : offset + limit]
        serializer = ProjectPublicDetailSerializer(projects, many=True)

        return create_paginated_response(
            items=serializer.data,
            offset=offset,
            limit=limit,
            total_items=total_items,
            message="Projects retrieved successfully",
        )


@method_decorator(csrf_exempt, name="dispatch")
class ProjectDetailView(APIView):
    """Get, Update, or Delete Project"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get_project(self, request, project_id):
        """Helper method to get project with permission check"""
        try:
            project = Project.objects.get(id=project_id)
        except Project.DoesNotExist:
            return None, create_error_response(message="Project not found", status_code=status.HTTP_404_NOT_FOUND)

        # Check permissions
        workspace = project.workspace
        has_access = False
        if workspace.owner == request.user:
            has_access = True
        elif workspace.organization:
            has_access = workspace.organization.has_permission(request.user, "view")

        if not has_access:
            return None, create_error_response(
                message="You do not have permission to access this project",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        return project, None

    def get(self, request, id):
        """Get project details"""
        project, error_response = self.get_project(request, id)
        if error_response:
            return error_response

        serializer = ProjectPublicDetailSerializer(project)
        return create_success_response(data=serializer.data, message="Project retrieved successfully")

    @transaction.atomic
    def put(self, request, id):
        """Update project (PUT)"""
        return self._update_project(request, id)

    @transaction.atomic
    def patch(self, request, id):
        """Update project (PATCH)"""
        return self._update_project(request, id)

    def _update_project(self, request, project_id):
        """Helper method for updating project"""
        project, error_response = self.get_project(request, project_id)
        if error_response:
            return error_response

        # Validate input data
        serializer = UpdateProjectSerializer(data=request.data)
        if not serializer.is_valid():
            return create_error_response(
                message="Invalid data",
                detail=serializer.errors,
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        data = serializer.validated_data
        if not data:
            return create_error_response(
                message="No data provided for update",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Handle project reasons (purposes)
        if "project_reasons" in data and data["project_reasons"] is not None:
            project.purpose_associations.all().delete()
            purpose_associations = [
                ProjectPurposeAssociation(project=project, project_purpose_id=purpose_id)
                for purpose_id in data["project_reasons"]
            ]
            ProjectPurposeAssociation.objects.bulk_create(purpose_associations)

        # Handle ad networks
        if "ad_networks" in data and data["ad_networks"] is not None:
            project.ad_networks.all().delete()
            ad_network_associations = [
                ProjectAdNetwork(
                    project=project,
                    ad_network_id=ad_network_id,
                    created_by=request.user,
                )
                for ad_network_id in data["ad_networks"]
            ]
            ProjectAdNetwork.objects.bulk_create(ad_network_associations)

        # Handle basic fields
        exclude_fields = {
            "project_reasons",
            "domain",
            "subdomain",
            "ad_networks",
            "raw_data",
        }
        foreign_key_fields = {"project_type", "project_platform", "workspace"}

        for field, value in data.items():
            if field not in exclude_fields and value is not None:
                if field in foreign_key_fields:
                    setattr(project, f"{field}_id", value)
                else:
                    setattr(project, field, value)

        project.save()
        project.refresh_from_db()

        serializer = ProjectPublicDetailSerializer(project)
        return create_success_response(data=serializer.data, message="Project updated successfully")

    def delete(self, request, id):
        """Delete project"""
        project, error_response = self.get_project(request, id)
        if error_response:
            return error_response

        project.delete()
        return create_success_response(
            message="Project deleted successfully",
            status_code=status.HTTP_204_NO_CONTENT,
        )


@method_decorator(csrf_exempt, name="dispatch")
class ProjectAdNetworkUpdateView(APIView):
    """Update project ad network field values"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    @transaction.atomic
    def patch(self, request, id, ad_network_id):
        """Update project ad network field values"""
        try:
            project = Project.objects.get(id=id)
        except Project.DoesNotExist:
            return create_error_response(message="Project not found", status_code=status.HTTP_404_NOT_FOUND)

        # Check permissions
        workspace = project.workspace
        has_access = False
        if workspace.owner == request.user:
            has_access = True
        elif workspace.organization:
            has_access = workspace.organization.has_permission(request.user, "edit")

        if not has_access:
            return create_error_response(
                message="You do not have permission to access this project",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        # Get project ad network
        try:
            project_ad_network = ProjectAdNetwork.objects.get(project=project, ad_network_id=ad_network_id)
        except ProjectAdNetwork.DoesNotExist:
            return create_error_response(
                message="Ad network not found for this project",
                status_code=status.HTTP_404_NOT_FOUND,
            )

        # Validate input data
        serializer = UpdateAdNetworkFieldValuesSerializer(data=request.data)
        if not serializer.is_valid():
            return create_error_response(
                message="Invalid data",
                detail=serializer.errors,
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        data = serializer.validated_data

        # Update field values
        project_ad_network.field_values = data["field_values"]

        try:
            # This will trigger the model's clean() method for validation
            project_ad_network.full_clean()
            project_ad_network.save()
        except ValidationError as e:
            return create_error_response(
                message="Validation failed",
                detail=e.message_dict if hasattr(e, "message_dict") else str(e),
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        ad_network_serializer = ProjectAdNetworkSerializer(project_ad_network)
        return create_success_response(
            data=ad_network_serializer.data,
            message="Ad network settings updated successfully",
        )


# Schema for updating ad network field values
class UpdateAdNetworkFieldValuesSerializer(serializers.Serializer):
    field_values = serializers.DictField()


# Backward compatibility functions for existing URL patterns
create = ProjectCreateView.as_view()
get_projects_by_workspace = ProjectsByWorkspaceView.as_view()
detail = ProjectDetailView.as_view()
update_ad_network = ProjectAdNetworkUpdateView.as_view()

# Backward compatibility for schemas
UpdateAdNetworkFieldValuesSchema = UpdateAdNetworkFieldValuesSerializer
