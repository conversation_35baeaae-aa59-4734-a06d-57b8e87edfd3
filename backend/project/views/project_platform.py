from core.drf_utils import create_success_response
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from project.models.project_platform_model import ProjectPlatform
from project.models.project_type_platform_association import (
    ProjectTypePlatformAssociation,
)
from project.schemas.project_platform_schema import PublicProjectPlatformSerializer
from rest_framework.permissions import AllowAny
from rest_framework.views import APIView


@method_decorator(csrf_exempt, name="dispatch")
class ProjectPlatformListView(APIView):
    """Get all project platforms"""

    permission_classes = [AllowAny]

    def get(self, request):
        """Get all project platforms"""
        q = ProjectPlatform.objects.all().order_by("-id")
        serializer = PublicProjectPlatformSerializer(q, many=True)
        return create_success_response(data=serializer.data, message="Project platforms retrieved successfully")


@method_decorator(csrf_exempt, name="dispatch")
class ProjectPlatformByTypeView(APIView):
    """Get project platforms by project type"""

    permission_classes = [AllowAny]

    def get(self, request, project_type_id):
        """Get project platforms by project type"""
        p = ProjectTypePlatformAssociation.objects.filter(project_type_id=project_type_id)
        if not p:
            return create_success_response(data=[], message="Project platforms retrieved successfully")
        new_list_of_project_platforms = [p.project_platform for p in p]
        serializer = PublicProjectPlatformSerializer(new_list_of_project_platforms, many=True)
        return create_success_response(data=serializer.data, message="Project platforms retrieved successfully")


# Backward compatibility
get_project_platforms = ProjectPlatformListView.as_view()
get_project_platforms_by_project_type = ProjectPlatformByTypeView.as_view()
