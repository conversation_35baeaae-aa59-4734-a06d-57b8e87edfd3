from django.urls import path
from organizations import views

app_name = "organizations"
urlpatterns = [
    # Organization management
    path(
        "organizations/personal-organizations/",
        views.list_personal_organizations,
        name="list_personal_organizations",
    ),
    path("organizations/", views.list_organizations, name="list_organizations"),
    path("organizations/create/", views.create_organization, name="create_organization"),
    path(
        "organizations/<str:organization_id>/delete/",
        views.delete_organization,
        name="delete_organization",
    ),
    path(
        "organizations/<str:organization_id>/members/",
        views.get_organization_members,
        name="get_organization_members",
    ),
    path(
        "organizations/<str:organization_id>/members/<str:member_id>/",
        views.manage_organization_member,
        name="manage_organization_member",
    ),
    path(
        "organizations/<str:organization_id>/invite/",
        views.invite_member,
        name="invite_member",
    ),
    path(
        "organizations/<str:organization_id>/invitations/",
        views.get_organization_invitations,
        name="get_organization_invitations",
    ),
    path(
        "organizations/<str:organization_id>/invitations/<str:invitation_id>/cancel/",
        views.cancel_invitation,
        name="cancel_invitation",
    ),
    path(
        "organizations/<str:organization_id>/invitations/<str:invitation_id>/resend/",
        views.resend_invitation,
        name="resend_invitation",
    ),
    # Invitation management
    path("invitations/accept/", views.accept_invitation, name="accept_invitation"),
    path("invitations/decline/", views.decline_invitation, name="decline_invitation"),
    # User organizations
    path(
        "user-organizations/",
        views.get_user_organizations,
        name="get_user_organizations",
    ),
    path(
        "user-pending-invitations/",
        views.get_user_pending_invitations,
        name="get_user_pending_invitations",
    ),
]
