import json
from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse
from organizations.models import (
    Organization,
    OrganizationInvitation,
    OrganizationMember,
    OrganizationType,
)
from workspaces.models import Workspace

User = get_user_model()


class OrganizationViewsTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
        )

        # Get the personal organization created by the signal
        self.personal_org = Organization.objects.get(owner=self.user, type=OrganizationType.PERSONAL)

        # Create team organization
        self.team_org = Organization.objects.create(name="Team Org", type=OrganizationType.TEAM, owner=self.user)

        # Create membership for the team organization owner
        OrganizationMember.objects.create(
            organization=self.team_org,
            user=self.user,
            role="owner",
            can_view=True,
            can_create=True,
            can_edit=True,
            can_delete=True,
            is_active=True,
        )

        # Login user
        self.client.force_login(self.user)

    def test_get_user_organizations(self):
        """Test retrieving user organizations"""
        url = reverse("organizations:get_user_organizations")
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        data = response.json()

        self.assertEqual(data["status_code"], 200)
        self.assertIn("data", data)
        # Check organization data - we expect 2 organizations: the signal-created personal org and the test team org
        org_names = [org["organization"]["name"] for org in data["data"]]
        self.assertEqual(len(data["data"]), 2)

        # The signal creates a personal org with format "{user.get_display_name()}'s Personal Account"
        # and we create a team org named "Team Org"
        expected_personal_name = f"{self.user.get_display_name()}'s Personal Account"
        self.assertIn(expected_personal_name, org_names)
        self.assertIn("Team Org", org_names)

    def test_create_team_organization(self):
        """Test creating a new team organization"""
        url = reverse("organizations:create_organization")
        data = {"name": "New Team", "type": "team"}

        response = self.client.post(url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, 201)
        response_data = response.json()

        self.assertEqual(response_data["status_code"], 201)
        self.assertIn("data", response_data)
        self.assertEqual(response_data["data"]["name"], "New Team")
        self.assertEqual(response_data["data"]["type"], "team")

        # Verify organization was created in database
        org = Organization.objects.get(name="New Team")
        self.assertEqual(org.type, OrganizationType.TEAM)
        self.assertEqual(org.owner, self.user)

    def test_create_personal_organization_forbidden(self):
        """Test that creating personal organizations is forbidden"""
        url = reverse("organizations:create_organization")
        data = {"name": "Another Personal", "type": "personal"}

        response = self.client.post(url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, 400)

    def test_get_organization_members(self):
        """Test retrieving organization members"""
        # Add a member to the team organization
        member_user = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
        )

        OrganizationMember.objects.create(organization=self.team_org, user=member_user, role="member")

        url = reverse(
            "organizations:get_organization_members",
            kwargs={"organization_id": str(self.team_org.id)},
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        data = response.json()

        self.assertEqual(data["status_code"], 200)
        self.assertIn("data", data)
        self.assertEqual(len(data["data"]), 2)  # Owner + member

        # Check that both the owner and member are in the results
        member_emails = [member["user"]["email"] for member in data["data"]]
        self.assertIn("<EMAIL>", member_emails)  # Owner
        self.assertIn("<EMAIL>", member_emails)  # Member

    def test_invitation_schema_includes_token(self):
        """Test that invitation responses include the token field"""
        # Create an invitation
        invitation = OrganizationInvitation.objects.create(
            organization=self.team_org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
        )

        url = reverse(
            "organizations:get_organization_invitations",
            kwargs={"organization_id": str(self.team_org.id)},
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        data = response.json()

        self.assertEqual(len(data["data"]), 1)
        invitation_data = data["data"][0]

        # Verify token field is present and is a valid UUID
        self.assertIn("token", invitation_data)
        self.assertEqual(invitation_data["token"], str(invitation.token))

        # Verify other required fields are present
        self.assertIn("id", invitation_data)
        self.assertIn("organization", invitation_data)
        self.assertIn("email", invitation_data)
        self.assertIn("role", invitation_data)
        self.assertIn("status", invitation_data)
        self.assertIn("invited_by", invitation_data)

    @patch("email_service.service.email_service.send_invitation_email")
    def test_invite_user(self, mock_send_email):
        """Test inviting a user to organization"""
        url = reverse(
            "organizations:invite_member",
            kwargs={"organization_id": str(self.team_org.id)},
        )
        data = {
            "email": "<EMAIL>",
            "role": "member",
            "message": "Join our team!",
        }

        response = self.client.post(url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, 201)
        response_data = response.json()

        self.assertEqual(response_data["status_code"], 201)
        self.assertIn("data", response_data)
        self.assertEqual(response_data["data"]["email"], "<EMAIL>")
        self.assertEqual(response_data["data"]["role"], "member")

        # Verify invitation was created
        invitation = OrganizationInvitation.objects.get(email="<EMAIL>")
        self.assertEqual(invitation.organization, self.team_org)
        self.assertEqual(invitation.invited_by, self.user)

        # Verify email was sent
        mock_send_email.assert_called_once_with(invitation)

    def test_invite_user_duplicate_email(self):
        """Test that duplicate invitations are not allowed"""
        # Create existing invitation
        OrganizationInvitation.objects.create(
            organization=self.team_org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
        )

        url = reverse(
            "organizations:invite_member",
            kwargs={"organization_id": str(self.team_org.id)},
        )
        data = {"email": "<EMAIL>", "role": "admin"}

        response = self.client.post(url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, 400)

    def test_invite_self_not_allowed(self):
        """Test that users cannot invite themselves"""
        url = reverse(
            "organizations:invite_member",
            kwargs={"organization_id": str(self.team_org.id)},
        )
        data = {
            "email": self.user.email,  # Same as the requesting user
            "role": "member",
        }

        response = self.client.post(url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertIn("cannot invite yourself", response_data["message"])

    def test_list_invitations_only_pending(self):
        """Test that only pending invitations are returned"""
        # Create invitations with different statuses
        OrganizationInvitation.objects.create(
            organization=self.team_org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
            status="pending",
        )

        OrganizationInvitation.objects.create(
            organization=self.team_org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
            status="cancelled",
        )

        OrganizationInvitation.objects.create(
            organization=self.team_org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
            status="accepted",
        )

        url = reverse(
            "organizations:get_organization_invitations",
            kwargs={"organization_id": str(self.team_org.id)},
        )

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        response_data = response.json()
        invitations = response_data["data"]

        # Should only return the pending invitation
        self.assertEqual(len(invitations), 1)
        self.assertEqual(invitations[0]["email"], "<EMAIL>")
        self.assertEqual(invitations[0]["status"], "pending")

    def test_list_invitations(self):
        """Test listing organization invitations"""
        # Create some invitations
        OrganizationInvitation.objects.create(
            organization=self.team_org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
        )

        OrganizationInvitation.objects.create(
            organization=self.team_org,
            email="<EMAIL>",
            role="admin",
            invited_by=self.user,
        )

        url = reverse(
            "organizations:get_organization_invitations",
            kwargs={"organization_id": str(self.team_org.id)},
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        data = response.json()

        self.assertEqual(data["status_code"], 200)
        self.assertIn("data", data)
        self.assertEqual(len(data["data"]), 2)

        emails = [inv["email"] for inv in data["data"]]
        self.assertIn("<EMAIL>", emails)
        self.assertIn("<EMAIL>", emails)

    # TODO: Implement cancel_invitation and resend_invitation views
    # def test_cancel_invitation(self):
    #     """Test canceling an invitation"""
    #     invitation = OrganizationInvitation.objects.create(
    #         organization=self.team_org,
    #         email="<EMAIL>",
    #         role="member",
    #         invited_by=self.user,
    #     )

    #     url = reverse(
    #         "cancel-invitation",
    #         kwargs={
    #             "organization_id": str(self.team_org.id),
    #             "invitation_id": str(invitation.id),
    #         },
    #     )

    #     response = self.client.post(url)

    #     self.assertEqual(response.status_code, 200)

    #     # Verify invitation was cancelled
    #     invitation.refresh_from_db()
    #     self.assertEqual(invitation.status, "cancelled")

    # def test_resend_invitation(self):
    #     """Test resending an invitation"""
    #     invitation = OrganizationInvitation.objects.create(
    #         organization=self.team_org,
    #         email="<EMAIL>",
    #         role="member",
    #         invited_by=self.user,
    #     )

    #     original_token = invitation.token

    #     url = reverse(
    #         "resend-invitation",
    #         kwargs={
    #             "organization_id": str(self.team_org.id),
    #             "invitation_id": str(invitation.id),
    #         },
    #     )

    #     with patch(
    #         "email_service.service.email_service.send_invitation_email"
    #     ) as mock_send_email:
    #         response = self.client.post(url)

    #     self.assertEqual(response.status_code, 200)

    #     # Verify invitation token was regenerated
    #     invitation.refresh_from_db()
    #     self.assertNotEqual(invitation.token, original_token)

    #     # Verify email was sent
    #     mock_send_email.assert_called_once_with(invitation)

    def test_delete_organization(self):
        """Test deleting an organization"""
        # Create some workspaces in the team organization
        workspace1 = Workspace.objects.create(title="Workspace 1", organization=self.team_org, owner=self.user)
        workspace2 = Workspace.objects.create(title="Workspace 2", organization=self.team_org, owner=self.user)

        url = reverse(
            "organizations:delete_organization",
            kwargs={"organization_id": str(self.team_org.id)},
        )
        response = self.client.delete(url)

        self.assertEqual(response.status_code, 200)
        response_data = response.json()

        self.assertEqual(response_data["status_code"], 200)
        self.assertIn("transferred_workspaces", response_data["data"])
        self.assertEqual(response_data["data"]["transferred_workspaces"], 2)

        # Verify organization was deleted
        self.assertFalse(Organization.objects.filter(id=self.team_org.id).exists())

        # Verify workspaces were transferred to personal organization
        workspace1.refresh_from_db()
        workspace2.refresh_from_db()

        self.assertEqual(workspace1.organization, self.personal_org)
        self.assertEqual(workspace2.organization, self.personal_org)

    def test_delete_personal_organization_forbidden(self):
        """Test that deleting personal organizations is forbidden"""
        url = reverse(
            "organizations:delete_organization",
            kwargs={"organization_id": str(self.personal_org.id)},
        )
        response = self.client.delete(url)

        self.assertEqual(response.status_code, 400)

    def test_unauthorized_access(self):
        """Test that unauthorized users cannot access organization endpoints"""
        self.client.logout()

        url = reverse("organizations:get_user_organizations")
        response = self.client.get(url)

        self.assertEqual(response.status_code, 403)

    def test_access_other_user_organization(self):
        """Test that users cannot access organizations they don't belong to"""
        other_user = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
        )

        other_org = Organization.objects.create(name="Other Org", type=OrganizationType.TEAM, owner=other_user)

        url = reverse(
            "organizations:get_organization_members",
            kwargs={"organization_id": str(other_org.id)},
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, 403)

    def test_get_user_pending_invitations_empty(self):
        """Test getting pending invitations when user has none"""
        url = reverse("organizations:get_user_pending_invitations")
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        data = response.json()

        self.assertEqual(data["status_code"], 200)
        self.assertIn("data", data)
        self.assertEqual(len(data["data"]), 0)

    def test_get_user_pending_invitations_with_invitations(self):
        """Test getting pending invitations when user has some"""
        # Create another user to send invitations
        other_user = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
        )

        other_org = Organization.objects.create(name="Other Org", type=OrganizationType.TEAM, owner=other_user)

        # Create pending invitation for our test user
        invitation1 = OrganizationInvitation.objects.create(
            organization=other_org,
            email=self.user.email,  # Invitation for our test user
            role="member",
            invited_by=other_user,
        )

        # Create another invitation for different email (should not appear)
        OrganizationInvitation.objects.create(
            organization=other_org,
            email="<EMAIL>",
            role="admin",
            invited_by=other_user,
        )

        # Create accepted invitation for a different organization (should not appear)
        # We need a different organization to avoid the unique constraint
        another_org = Organization.objects.create(name="Another Org", type=OrganizationType.TEAM, owner=other_user)
        accepted_invitation = OrganizationInvitation.objects.create(
            organization=another_org,  # Different organization
            email=self.user.email,
            role="viewer",
            invited_by=other_user,
        )
        accepted_invitation.status = "accepted"
        accepted_invitation.save()

        url = reverse("organizations:get_user_pending_invitations")
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        data = response.json()

        self.assertEqual(data["status_code"], 200)
        self.assertIn("data", data)
        self.assertEqual(len(data["data"]), 1)  # Only the pending invitation

        # Verify the invitation data
        invitation_data = data["data"][0]
        self.assertEqual(invitation_data["id"], str(invitation1.id))
        self.assertEqual(invitation_data["email"], self.user.email)
        self.assertEqual(invitation_data["role"], "member")
        self.assertEqual(invitation_data["status"], "pending")
        self.assertEqual(invitation_data["organization"]["name"], "Other Org")
        self.assertEqual(invitation_data["invited_by"]["email"], other_user.email)
        self.assertIn("token", invitation_data)

    def test_get_user_pending_invitations_expired_filtered(self):
        """Test that expired invitations are filtered out"""
        from datetime import timedelta

        from django.utils import timezone

        other_user = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
        )

        other_org = Organization.objects.create(name="Other Org", type=OrganizationType.TEAM, owner=other_user)

        # Create expired invitation
        expired_invitation = OrganizationInvitation.objects.create(
            organization=other_org,
            email=self.user.email,
            role="member",
            invited_by=other_user,
        )
        expired_invitation.expires_at = timezone.now() - timedelta(days=1)
        expired_invitation.save()

        # Create valid invitation in a different organization to avoid unique constraint
        another_org = Organization.objects.create(name="Another Org", type=OrganizationType.TEAM, owner=other_user)
        valid_invitation = OrganizationInvitation.objects.create(
            organization=another_org,  # Different organization
            email=self.user.email,
            role="admin",
            invited_by=other_user,
        )

        url = reverse("organizations:get_user_pending_invitations")
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        data = response.json()

        self.assertEqual(data["status_code"], 200)
        self.assertEqual(len(data["data"]), 1)  # Only the valid invitation
        self.assertEqual(data["data"][0]["id"], str(valid_invitation.id))

    def test_get_user_pending_invitations_unauthorized(self):
        """Test that unauthorized users cannot access pending invitations"""
        self.client.logout()

        url = reverse("organizations:get_user_pending_invitations")
        response = self.client.get(url)

        self.assertEqual(response.status_code, 403)


class InvitationAcceptanceTest(TestCase):
    def setUp(self):
        self.owner = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
        )

        self.invited_user = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
        )

        self.org = Organization.objects.create(name="Test Org", type=OrganizationType.TEAM, owner=self.owner)

        self.invitation = OrganizationInvitation.objects.create(
            organization=self.org,
            email="<EMAIL>",
            role="member",
            invited_by=self.owner,
        )

    @patch("email_service.service.email_service.send_invitation_accepted_email")
    def test_accept_invitation(self, mock_send_email):
        """Test accepting an invitation"""
        self.client.force_login(self.invited_user)

        url = reverse("organizations:accept_invitation")
        data = {"token": str(self.invitation.token)}

        response = self.client.post(url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, 200)
        response_data = response.json()

        self.assertEqual(response_data["status_code"], 200)
        self.assertIn("organization", response_data["data"])

        # Verify invitation was accepted
        self.invitation.refresh_from_db()
        self.assertEqual(self.invitation.status, "accepted")
        self.assertEqual(self.invitation.invited_user, self.invited_user)

        # Verify member was created
        member = OrganizationMember.objects.get(organization=self.org, user=self.invited_user)
        self.assertEqual(member.role, "member")

        # Verify email was sent
        mock_send_email.assert_called_once()

    def test_decline_invitation(self):
        """Test declining an invitation"""
        # Login as the invited user
        self.client.force_login(self.invited_user)

        url = reverse("organizations:decline_invitation")
        data = {"token": str(self.invitation.token)}

        response = self.client.post(url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, 200)

        # Verify invitation was declined
        self.invitation.refresh_from_db()
        self.assertEqual(self.invitation.status, "declined")

    def test_accept_expired_invitation(self):
        """Test that expired invitations cannot be accepted"""
        # Make invitation expired
        from datetime import timedelta

        from django.utils import timezone

        self.invitation.expires_at = timezone.now() - timedelta(days=1)
        self.invitation.save()

        self.client.force_login(self.invited_user)

        url = reverse("organizations:accept_invitation")
        data = {"token": str(self.invitation.token)}

        response = self.client.post(url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, 400)

    def test_accept_invalid_token(self):
        """Test that invalid tokens are rejected"""
        self.client.force_login(self.invited_user)

        url = reverse("organizations:accept_invitation")
        data = {"token": "invalid-token"}

        response = self.client.post(url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, 404)
