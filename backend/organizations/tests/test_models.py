import uuid
from datetime import timed<PERSON><PERSON>

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.test import TestCase
from django.utils import timezone
from organizations.models import (
    Organization,
    OrganizationInvitation,
    OrganizationMember,
    OrganizationType,
)
from workspaces.models import Workspace

User = get_user_model()


class OrganizationModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
        )

    def test_create_personal_organization(self):
        """Test creating a personal organization"""
        org = Organization.objects.create(name="Personal Workspace", type=OrganizationType.PERSONAL, owner=self.user)

        self.assertEqual(org.name, "Personal Workspace")
        self.assertEqual(org.type, OrganizationType.PERSONAL)
        self.assertEqual(org.owner, self.user)
        self.assertIsInstance(org.id, uuid.UUID)

    def test_create_team_organization(self):
        """Test creating a team organization"""
        org = Organization.objects.create(name="Team Workspace", type=OrganizationType.TEAM, owner=self.user)

        self.assertEqual(org.name, "Team Workspace")
        self.assertEqual(org.type, OrganizationType.TEAM)
        self.assertEqual(org.owner, self.user)

    def test_organization_str_representation(self):
        """Test string representation of organization"""
        org = Organization.objects.create(name="Test Org", type=OrganizationType.TEAM, owner=self.user)

        self.assertEqual(str(org), "Test Org (Team)")

    def test_organization_member_creation(self):
        """Test creating organization members"""
        org = Organization.objects.create(name="Test Org", type=OrganizationType.TEAM, owner=self.user)

        member_user = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
        )

        member = OrganizationMember.objects.create(organization=org, user=member_user, role="member")

        self.assertEqual(member.organization, org)
        self.assertEqual(member.user, member_user)
        self.assertEqual(member.role, "member")
        self.assertTrue(member.can_view)
        self.assertFalse(member.can_delete)

    def test_organization_member_permissions(self):
        """Test organization member permissions"""
        org = Organization.objects.create(name="Test Org", type=OrganizationType.TEAM, owner=self.user)

        admin_user = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
        )

        admin_member = OrganizationMember.objects.create(
            organization=org,
            user=admin_user,
            role="admin",
            can_view=True,
            can_create=True,
            can_edit=True,
            can_delete=True,
        )

        self.assertTrue(admin_member.can_view)
        self.assertTrue(admin_member.can_create)
        self.assertTrue(admin_member.can_edit)
        self.assertTrue(admin_member.can_delete)


class OrganizationInvitationModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
        )

        self.org = Organization.objects.create(name="Test Org", type=OrganizationType.TEAM, owner=self.user)

    def test_create_invitation(self):
        """Test creating an organization invitation"""
        invitation = OrganizationInvitation.objects.create(
            organization=self.org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
        )

        self.assertEqual(invitation.organization, self.org)
        self.assertEqual(invitation.email, "<EMAIL>")
        self.assertEqual(invitation.role, "member")
        self.assertEqual(invitation.invited_by, self.user)
        self.assertEqual(invitation.status, "pending")
        self.assertIsNotNone(invitation.token)
        self.assertIsNotNone(invitation.expires_at)

    def test_invitation_expiry(self):
        """Test invitation expiry logic"""
        invitation = OrganizationInvitation.objects.create(
            organization=self.org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
        )

        # Should not be expired initially
        self.assertFalse(invitation.is_expired())

        # Manually set expiry to past
        invitation.expires_at = timezone.now() - timedelta(days=1)
        invitation.save()

        self.assertTrue(invitation.is_expired())

    def test_invitation_acceptance(self):
        """Test accepting an invitation"""
        invited_user = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
        )

        invitation = OrganizationInvitation.objects.create(
            organization=self.org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
        )

        member = invitation.accept(invited_user)

        self.assertEqual(invitation.status, "accepted")
        self.assertEqual(invitation.invited_user, invited_user)
        self.assertIsNotNone(invitation.accepted_at)

        # Check that member was created
        self.assertEqual(member.organization, self.org)
        self.assertEqual(member.user, invited_user)
        self.assertEqual(member.role, "member")

    def test_invitation_decline(self):
        """Test declining an invitation"""
        invitation = OrganizationInvitation.objects.create(
            organization=self.org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
        )

        invitation.decline()

        self.assertEqual(invitation.status, "declined")
        self.assertIsNotNone(invitation.declined_at)

    def test_invitation_cancel(self):
        """Test canceling an invitation"""
        invitation = OrganizationInvitation.objects.create(
            organization=self.org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
        )

        invitation.cancel()

        self.assertEqual(invitation.status, "cancelled")

    def test_duplicate_pending_invitation_validation(self):
        """Test that duplicate pending invitations are not allowed"""
        OrganizationInvitation.objects.create(
            organization=self.org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
        )

        with self.assertRaises(ValidationError):
            duplicate_invitation = OrganizationInvitation(
                organization=self.org,
                email="<EMAIL>",
                role="admin",
                invited_by=self.user,
            )
            duplicate_invitation.full_clean()

    def test_invitation_str_representation(self):
        """Test string representation of invitation"""
        invitation = OrganizationInvitation.objects.create(
            organization=self.org,
            email="<EMAIL>",
            role="member",
            invited_by=self.user,
        )

        expected_str = f"<NAME_EMAIL> for {self.org.name}"
        self.assertEqual(str(invitation), expected_str)


class WorkspaceOrganizationIntegrationTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123",
        )

        self.org = Organization.objects.create(name="Test Org", type=OrganizationType.TEAM, owner=self.user)

    def test_workspace_organization_relationship(self):
        """Test workspace-organization relationship"""
        workspace = Workspace.objects.create(title="Test Workspace", organization=self.org, owner=self.user)

        self.assertEqual(workspace.organization, self.org)
        self.assertIn(workspace, self.org.workspaces.all())

    def test_organization_deletion_with_workspaces(self):
        """Test that organization deletion handles workspaces properly"""
        # Create workspaces in the organization
        workspace1 = Workspace.objects.create(title="Workspace 1", organization=self.org, owner=self.user)
        workspace2 = Workspace.objects.create(title="Workspace 2", organization=self.org, owner=self.user)

        # Create personal organization for transfer
        personal_org = Organization.objects.create(name="Personal", type=OrganizationType.PERSONAL, owner=self.user)

        # Transfer workspaces to personal organization
        workspace1.organization = personal_org
        workspace1.save()
        workspace2.organization = personal_org
        workspace2.save()

        # Now delete the team organization
        self.org.delete()

        # Workspaces should still exist in personal organization
        self.assertTrue(Workspace.objects.filter(id=workspace1.id).exists())
        self.assertTrue(Workspace.objects.filter(id=workspace2.id).exists())

        workspace1.refresh_from_db()
        workspace2.refresh_from_db()

        self.assertEqual(workspace1.organization, personal_org)
        self.assertEqual(workspace2.organization, personal_org)
