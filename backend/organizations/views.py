import contextlib
import logging

from core.authentication import GTMSessionAuthentication
from core.drf_utils import create_error_response, create_success_response
from django.db import models, transaction
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_exempt
from email_service.service import email_service
from organizations.models import (
    InvitationStatus,
    Organization,
    OrganizationInvitation,
    OrganizationMember,
    OrganizationType,
)
from organizations.schemas import (  # Backward compatibility imports
    CreateOrganizationSerializer,
    InvitationTokenSerializer,
    InviteMemberSerializer,
    OrganizationInvitationSerializer,
    OrganizationMemberSerializer,
    OrganizationSerializer,
    UpdateMemberSerializer,
    UserOrganizationSerializer,
)
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

logger = logging.getLogger(__name__)
# ErrorTuple = tuple[int, ErrorResponseSchema]  # Removed - no longer needed with DRF

# User = get_user_model()

# Response type aliases
# OrganizationListResponse = (
#     SuccessResponseSchema[list[OrganizationSchema]] | ErrorResponseSchema
# )
# OrganizationDetailResponse = (
#     SuccessResponseSchema[OrganizationSchema] | ErrorResponseSchema
# )
# OrganizationMemberListResponse = (
#     SuccessResponseSchema[list[OrganizationMemberSchema]] | ErrorResponseSchema
# )
# OrganizationInvitationListResponse = (
#     SuccessResponseSchema[list[OrganizationInvitationSchema]] | ErrorResponseSchema
# )
# OrganizationInvitationDetailResponse = (
#     SuccessResponseSchema[OrganizationInvitationSchema] | ErrorResponseSchema
# )
# UserOrganizationListResponse = (
#     SuccessResponseSchema[list[UserOrganizationSchema]] | ErrorResponseSchema
# )


# Organization management functions


@method_decorator(never_cache, name="dispatch")
@method_decorator(csrf_exempt, name="dispatch")
class PersonalOrganizationView(APIView):
    """List personal organizations"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        if not user:
            return create_error_response(message="Unauthorized", status_code=status.HTTP_401_UNAUTHORIZED)

        personal_org = Organization.objects.filter(owner=request.user, type=OrganizationType.PERSONAL).first()
        if not personal_org:
            return create_error_response(
                message="Personal organization not found",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        serializer = OrganizationSerializer(personal_org)
        return create_success_response(data=serializer.data, message="Personal organization retrieved successfully")


@method_decorator(never_cache, name="dispatch")
@method_decorator(csrf_exempt, name="dispatch")
class OrganizationListView(APIView):
    """List organizations where user is owner or member"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """List organizations where user is owner or member"""
        if not request.user.is_authenticated:
            return create_error_response(
                message="Authentication required",
                status_code=status.HTTP_401_UNAUTHORIZED,
            )

        user = request.user
        organizations = Organization.objects.filter(
            models.Q(owner=user) | models.Q(members__user=user, members__is_active=True)
        ).distinct()

        if not organizations:
            return create_error_response(message="No organizations found", status_code=status.HTTP_404_NOT_FOUND)

        serializer = OrganizationSerializer(organizations, many=True)
        return create_success_response(data=serializer.data, message="Organizations retrieved successfully")


@method_decorator(never_cache, name="dispatch")
@method_decorator(csrf_exempt, name="dispatch")
class OrganizationCreateView(APIView):
    """Create a new organization"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Create a new organization"""
        serializer = CreateOrganizationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        # Prevent creation of personal organizations
        if data.get("type") == OrganizationType.PERSONAL:
            return create_error_response(
                message="Personal organizations cannot be created manually",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        organization = Organization.objects.create(
            name=data["name"],
            type=data["type"],
            owner=request.user,
        )

        # Create owner membership
        OrganizationMember.objects.create(
            organization=organization,
            user=request.user,
            role="owner",
            can_view=True,
            can_create=True,
            can_edit=True,
            can_delete=True,
        )

        org_serializer = OrganizationSerializer(organization)
        return create_success_response(
            data=org_serializer.data,
            message="Organization created successfully",
            status_code=status.HTTP_201_CREATED,
        )


@method_decorator(never_cache, name="dispatch")
@method_decorator(csrf_exempt, name="dispatch")
class OrganizationMembersView(APIView):
    """Get organization members"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, organization_id):
        """Get organization members"""
        try:
            organization = Organization.objects.get(id=organization_id)
        except Organization.DoesNotExist:
            return create_error_response(message="Organization not found", status_code=status.HTTP_404_NOT_FOUND)

        # Check if user has access to this organization
        if not organization.has_permission(request.user, "view"):
            return create_error_response(
                message="You do not have permission to view this organization",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        members = OrganizationMember.objects.filter(organization=organization, is_active=True).select_related("user")

        serializer = OrganizationMemberSerializer(members, many=True)
        return create_success_response(data=serializer.data, message="Organization members retrieved successfully")


@method_decorator(never_cache, name="dispatch")
@method_decorator(csrf_exempt, name="dispatch")
class OrganizationMemberManageView(APIView):
    """Update or delete organization member"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get_organization_and_member(self, organization_id, member_id, user):
        """Helper method to get organization and member with permission checks"""
        try:
            organization = Organization.objects.get(id=organization_id)
        except Organization.DoesNotExist:
            return (
                None,
                None,
                create_error_response(
                    message="Organization not found",
                    status_code=status.HTTP_404_NOT_FOUND,
                ),
            )

        # Check if user has permission to edit members
        if not organization.has_permission(user, "edit"):
            return (
                None,
                None,
                create_error_response(
                    message="You do not have permission to edit organization members",
                    status_code=status.HTTP_403_FORBIDDEN,
                ),
            )

        try:
            member = OrganizationMember.objects.get(id=member_id, organization=organization)
        except OrganizationMember.DoesNotExist:
            return (
                None,
                None,
                create_error_response(
                    message="Organization member not found",
                    status_code=status.HTTP_404_NOT_FOUND,
                ),
            )

        return organization, member, None

    def delete(self, request, organization_id, member_id):
        """Delete organization member"""
        organization, member, error_response = self.get_organization_and_member(
            organization_id, member_id, request.user
        )
        if error_response:
            return error_response

        # Check if user has permission to delete members
        if not organization.has_permission(request.user, "delete"):
            return create_error_response(
                message="You do not have permission to delete organization members",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        # Prevent deleting owner
        if member.role == "owner":
            return create_error_response(
                message="Cannot delete organization owner",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Prevent self-deletion
        if member.user == request.user:
            return create_error_response(
                message="Cannot delete yourself",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Delete member
        member.delete()

        return create_success_response(message="Member removed successfully", data=None)

    def patch(self, request, organization_id, member_id):
        """Update organization member permissions"""
        return self._update_member(request, organization_id, member_id)

    def put(self, request, organization_id, member_id):
        """Update organization member permissions"""
        return self._update_member(request, organization_id, member_id)

    def _update_member(self, request, organization_id, member_id):
        """Helper method for updating member permissions"""
        organization, member, error_response = self.get_organization_and_member(
            organization_id, member_id, request.user
        )
        if error_response:
            return error_response

        # Prevent editing owner permissions
        if member.role == "owner":
            return create_error_response(
                message="Cannot modify owner permissions",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        serializer = UpdateMemberSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        # Update member permissions
        if data.get("role") is not None:
            member.role = data["role"]
        if data.get("can_view") is not None:
            member.can_view = data["can_view"]
        if data.get("can_create") is not None:
            member.can_create = data["can_create"]
        if data.get("can_edit") is not None:
            member.can_edit = data["can_edit"]
        if data.get("can_delete") is not None:
            member.can_delete = data["can_delete"]

        member.save()

        member_serializer = OrganizationMemberSerializer(member)
        return create_success_response(
            data=member_serializer.data,
            message="Member permissions updated successfully",
        )


@method_decorator(never_cache, name="dispatch")
@method_decorator(csrf_exempt, name="dispatch")
class InviteMemberView(APIView):
    """Invite a user to the organization"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, organization_id):
        """Invite a user to the organization"""
        try:
            organization = Organization.objects.get(id=organization_id)
        except Organization.DoesNotExist:
            return create_error_response(message="Organization not found", status_code=status.HTTP_404_NOT_FOUND)

        # Check if user has permission to invite
        if not organization.has_permission(request.user, "edit"):
            return create_error_response(
                message="You do not have permission to invite users",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        serializer = InviteMemberSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        # Check if user is trying to invite themselves
        if data["email"].lower() == request.user.email.lower():
            return create_error_response(
                message="You cannot invite yourself to the organization",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Check if user is already a member or has pending invitation
        existing_member = OrganizationMember.objects.filter(
            organization=organization, user__email=data["email"], is_active=True
        ).first()
        if existing_member:
            return create_error_response(
                message="User is already a member of this organization",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        existing_invitation = OrganizationInvitation.objects.filter(
            organization=organization, email=data["email"], status="pending"
        ).first()
        if existing_invitation:
            return create_error_response(
                message="User already has a pending invitation",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Create invitation
        invitation = OrganizationInvitation.objects.create(
            organization=organization,
            email=data["email"],
            role=data["role"],
            can_view=data["can_view"],
            can_create=data["can_create"],
            can_edit=data["can_edit"],
            can_delete=data["can_delete"],
            invited_by=request.user,
        )

        # Send invitation email
        with contextlib.suppress(Exception):
            email_service.send_invitation_email(invitation)

        invitation_serializer = OrganizationInvitationSerializer(invitation)
        return create_success_response(
            data=invitation_serializer.data,
            message="Invitation sent successfully",
            status_code=status.HTTP_201_CREATED,
        )


@method_decorator(never_cache, name="dispatch")
@method_decorator(csrf_exempt, name="dispatch")
class OrganizationInvitationsView(APIView):
    """Get organization invitations"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, organization_id):
        """Get organization invitations"""
        try:
            organization = Organization.objects.get(id=organization_id)
        except Organization.DoesNotExist:
            return create_error_response(message="Organization not found", status_code=status.HTTP_404_NOT_FOUND)

        # Check if user has permission to view invitations
        if not organization.has_permission(request.user, "view"):
            return create_error_response(
                message="You do not have permission to view invitations",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        # Only show pending invitations (not cancelled, declined, accepted, or expired)
        invitations = OrganizationInvitation.objects.filter(organization=organization, status="pending").order_by(
            "-created_at"
        )

        serializer = OrganizationInvitationSerializer(invitations, many=True)
        return create_success_response(
            data=serializer.data,
            message="Organization invitations retrieved successfully",
        )


# Invitation management functions


@method_decorator(never_cache, name="dispatch")
@method_decorator(csrf_exempt, name="dispatch")
class AcceptInvitationView(APIView):
    """Accept an invitation"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Accept an invitation"""
        serializer = InvitationTokenSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        try:
            invitation = OrganizationInvitation.objects.get(token=data["token"], status="pending")
        except (OrganizationInvitation.DoesNotExist, ValueError, Exception):
            return create_error_response(
                message="Invitation not found or already processed",
                status_code=status.HTTP_404_NOT_FOUND,
            )

        # Check if invitation is expired
        if invitation.is_expired():
            return create_error_response(
                message="Invitation has expired",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Check if user email matches invitation
        if invitation.email != request.user.email:
            return create_error_response(
                message="You can only accept invitations sent to your email",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        with transaction.atomic():
            # Accept the invitation
            member = invitation.accept(request.user)

            # Send acceptance notification
            with contextlib.suppress(Exception):
                email_service.send_invitation_accepted_email(invitation, request.user)

        org_serializer = OrganizationSerializer(member.organization)
        return create_success_response(
            data={"organization": org_serializer.data},
            message="Invitation accepted successfully",
        )


@method_decorator(never_cache, name="dispatch")
@method_decorator(csrf_exempt, name="dispatch")
class DeclineInvitationView(APIView):
    """Decline an invitation"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Decline an invitation"""
        serializer = InvitationTokenSerializer(data=request.data)
        if not serializer.is_valid():
            return create_error_response(
                message="Invalid data",
                detail=serializer.errors,
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        token = serializer.validated_data.get("token")
        if not token:
            return create_error_response(message="Token is required", status_code=status.HTTP_400_BAD_REQUEST)

        try:
            invitation = OrganizationInvitation.objects.get(token=token, status="pending")
        except (OrganizationInvitation.DoesNotExist, ValueError, Exception):
            return create_error_response(
                message="Invitation not found or already processed",
                status_code=status.HTTP_404_NOT_FOUND,
            )

        # Check if user email matches invitation
        if invitation.email != request.user.email:
            return create_error_response(
                message="You can only decline invitations sent to your email",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        invitation.decline()

        return create_success_response(message="Invitation declined successfully")


@method_decorator(never_cache, name="dispatch")
@method_decorator(csrf_exempt, name="dispatch")
class CancelInvitationView(APIView):
    """Cancel an invitation"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, organization_id, invitation_id):
        """Cancel an invitation"""
        try:
            organization = Organization.objects.get(id=organization_id)
        except Organization.DoesNotExist:
            return create_error_response(message="Organization not found", status_code=status.HTTP_404_NOT_FOUND)

        # Check if user has permission to cancel invitations
        if not organization.has_permission(request.user, "edit"):
            return create_error_response(
                message="You do not have permission to cancel invitations",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        try:
            invitation = OrganizationInvitation.objects.get(
                id=invitation_id, organization=organization, status="pending"
            )
        except OrganizationInvitation.DoesNotExist:
            return create_error_response(
                message="Invitation not found or already processed",
                status_code=status.HTTP_404_NOT_FOUND,
            )

        invitation.cancel()

        return create_success_response(message="Invitation cancelled successfully")


@method_decorator(never_cache, name="dispatch")
@method_decorator(csrf_exempt, name="dispatch")
class ResendInvitationView(APIView):
    """Resend an invitation"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, organization_id, invitation_id):
        """Resend an invitation"""
        try:
            organization = Organization.objects.get(id=organization_id)
        except Organization.DoesNotExist:
            return create_error_response(message="Organization not found", status_code=status.HTTP_404_NOT_FOUND)

        # Check if user has permission to resend invitations
        if not organization.has_permission(request.user, "edit"):
            return create_error_response(
                message="You do not have permission to resend invitations",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        try:
            invitation = OrganizationInvitation.objects.get(
                id=invitation_id, organization=organization, status="pending"
            )
        except OrganizationInvitation.DoesNotExist:
            return create_error_response(
                message="Invitation not found or already processed",
                status_code=status.HTTP_404_NOT_FOUND,
            )

        # Check if invitation is expired
        if invitation.is_expired():
            return create_error_response(
                message="Cannot resend expired invitation",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Regenerate token and extend expiry
        invitation.regenerate_token()

        # Send email
        try:
            email_service.send_invitation_email(invitation)
        except Exception as e:
            return create_error_response(
                message=f"Failed to send invitation email: {str(e)}",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        invitation_serializer = OrganizationInvitationSerializer(invitation)
        return create_success_response(data=invitation_serializer.data, message="Invitation resent successfully")


# User organization functions


@method_decorator(never_cache, name="dispatch")
@method_decorator(csrf_exempt, name="dispatch")
class UserOrganizationsView(APIView):
    """Get user's organization memberships"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get user's organization memberships"""
        memberships = OrganizationMember.objects.filter(user=request.user, is_active=True).select_related(
            "organization", "user"
        )

        serializer = UserOrganizationSerializer(memberships, many=True)
        logger.info(f"User organizations retrieved successfully for user: {request.user.email}")

        return create_success_response(data=serializer.data, message="User organizations retrieved successfully")


@method_decorator(never_cache, name="dispatch")
@method_decorator(csrf_exempt, name="dispatch")
class UserPendingInvitationsView(APIView):
    """Get pending invitations for the current user"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get pending invitations for the current user"""
        # Get pending invitations for the user's email that haven't expired
        invitations = OrganizationInvitation.objects.filter(
            email=request.user.email, status=InvitationStatus.PENDING
        ).select_related("organization", "organization__owner", "invited_by")

        # Filter out expired invitations
        valid_invitations = [inv for inv in invitations if not inv.is_expired()]

        serializer = OrganizationInvitationSerializer(valid_invitations, many=True)
        return create_success_response(data=serializer.data, message="Pending invitations retrieved successfully")


@method_decorator(never_cache, name="dispatch")
@method_decorator(csrf_exempt, name="dispatch")
class OrganizationDeleteView(APIView):
    """Delete an organization with proper validation and workspace transfer"""

    authentication_classes = [GTMSessionAuthentication]
    permission_classes = [IsAuthenticated]

    def delete(self, request, organization_id):
        """Delete an organization with proper validation and workspace transfer"""
        try:
            organization = Organization.objects.get(id=organization_id)
        except Organization.DoesNotExist:
            return create_error_response(message="Organization not found", status_code=status.HTTP_404_NOT_FOUND)

        # Check if user is the owner
        if organization.owner != request.user:
            return create_error_response(
                message="Only the organization owner can delete the organization",
                status_code=status.HTTP_403_FORBIDDEN,
            )

        # Prevent deletion of personal organizations
        if organization.type == OrganizationType.PERSONAL:
            return create_error_response(
                message="Personal organizations cannot be deleted",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Get workspace count for logging/response
        workspace_count = organization.workspaces.count()

        with transaction.atomic():
            # Transfer workspaces to owner's personal organization if any exist
            if workspace_count > 0:
                personal_org = Organization.objects.filter(owner=request.user, type=OrganizationType.PERSONAL).first()

                if not personal_org:
                    return create_error_response(
                        message="Cannot transfer workspaces: Personal organization not found",
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    )

                # Transfer all workspaces to personal organization
                organization.workspaces.update(organization=personal_org)

            # Delete the organization (this will cascade delete members and invitations)
            organization.delete()

        message = "Organization deleted successfully"
        if workspace_count > 0:
            message += f". {workspace_count} workspace(s) transferred to your personal account"

        return create_success_response(data={"transferred_workspaces": workspace_count}, message=message)


# Backward compatibility functions for existing URL patterns
list_personal_organizations = PersonalOrganizationView.as_view()
list_organizations = OrganizationListView.as_view()
create_organization = OrganizationCreateView.as_view()
get_organization_members = OrganizationMembersView.as_view()
manage_organization_member = OrganizationMemberManageView.as_view()
invite_member = InviteMemberView.as_view()
get_organization_invitations = OrganizationInvitationsView.as_view()
accept_invitation = AcceptInvitationView.as_view()
decline_invitation = DeclineInvitationView.as_view()
cancel_invitation = CancelInvitationView.as_view()
resend_invitation = ResendInvitationView.as_view()
get_user_organizations = UserOrganizationsView.as_view()
get_user_pending_invitations = UserPendingInvitationsView.as_view()
delete_organization = OrganizationDeleteView.as_view()
