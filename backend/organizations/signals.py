from django.contrib.auth import get_user_model
from django.db.models.signals import post_save
from django.dispatch import receiver
from organizations.models import Organization, OrganizationMember, OrganizationType

User = get_user_model()


@receiver(post_save, sender=User)
def create_personal_organization(sender, instance, created, **kwargs):
    """
    Create a personal organization for each new user.
    This ensures every user has a default organization for their workspaces.
    """
    if created:
        organization = Organization.objects.create(
            name=f"{instance.get_display_name()}'s Personal Account",
            description="Personal account for individual workspaces",
            type=OrganizationType.PERSONAL,
            owner=instance,
        )

        # Create owner membership
        OrganizationMember.objects.create(
            organization=organization,
            user=instance,
            role="owner",
            can_view=True,
            can_create=True,
            can_edit=True,
            can_delete=True,
            is_active=True,
        )
