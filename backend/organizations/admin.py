from django.contrib import admin
from organizations.models import (
    Organization,
    OrganizationInvitation,
    OrganizationMember,
)


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ["name", "type", "owner", "is_active", "created_at"]
    list_filter = ["type", "is_active", "created_at"]
    search_fields = ["name", "owner__email", "owner__username"]
    readonly_fields = ["id", "created_at", "updated_at"]

    fieldsets = (
        (None, {"fields": ("name", "description", "type", "owner", "is_active")}),
        (
            "Timestamps",
            {"fields": ("id", "created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(OrganizationMember)
class OrganizationMemberAdmin(admin.ModelAdmin):
    list_display = ["user", "organization", "role", "is_active", "joined_at"]
    list_filter = ["role", "is_active", "joined_at"]
    search_fields = ["user__email", "user__username", "organization__name"]
    readonly_fields = ["id", "joined_at", "created_at", "updated_at"]

    fieldsets = (
        (None, {"fields": ("organization", "user", "role", "is_active")}),
        (
            "Permissions",
            {"fields": ("can_view", "can_create", "can_edit", "can_delete")},
        ),
        (
            "Timestamps",
            {
                "fields": ("id", "joined_at", "created_at", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )


@admin.register(OrganizationInvitation)
class OrganizationInvitationAdmin(admin.ModelAdmin):
    list_display = [
        "email",
        "organization",
        "invited_by",
        "status",
        "created_at",
        "expires_at",
    ]
    list_filter = ["status", "role", "created_at", "expires_at"]
    search_fields = ["email", "organization__name", "invited_by__email"]
    readonly_fields = [
        "id",
        "token",
        "created_at",
        "updated_at",
        "accepted_at",
        "declined_at",
    ]

    fieldsets = (
        (None, {"fields": ("organization", "email", "invited_by", "invited_user")}),
        ("Invitation Details", {"fields": ("role", "message", "status", "expires_at")}),
        (
            "Permissions",
            {"fields": ("can_view", "can_create", "can_edit", "can_delete")},
        ),
        (
            "Timestamps",
            {
                "fields": (
                    "id",
                    "token",
                    "created_at",
                    "updated_at",
                    "accepted_at",
                    "declined_at",
                ),
                "classes": ("collapse",),
            },
        ),
    )
