from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from organizations.models import Organization, OrganizationMember, OrganizationType

User = get_user_model()


class Command(BaseCommand):
    help = "Create missing personal organizations and memberships for existing users"

    def handle(self, *args, **options):
        users = User.objects.all()
        created_orgs = 0
        created_memberships = 0

        for user in users:
            # Check if user has a personal organization
            personal_org = Organization.objects.filter(owner=user, type=OrganizationType.PERSONAL).first()

            if not personal_org:
                # Create personal organization
                personal_org = Organization.objects.create(
                    name=f"{user.get_display_name()}'s Personal Account",
                    description="Personal account for individual workspaces",
                    type=OrganizationType.PERSONAL,
                    owner=user,
                )
                created_orgs += 1
                self.stdout.write(
                    self.style.SUCCESS(f"Created personal organization for {user.email}: {personal_org.name}")
                )

            # Check if user has membership in their personal organization
            membership = OrganizationMember.objects.filter(organization=personal_org, user=user, is_active=True).first()

            if not membership:
                # Create owner membership
                OrganizationMember.objects.create(
                    organization=personal_org,
                    user=user,
                    role="owner",
                    can_view=True,
                    can_create=True,
                    can_edit=True,
                    can_delete=True,
                    is_active=True,
                )
                created_memberships += 1
                self.stdout.write(
                    self.style.SUCCESS(f"Created owner membership for {user.email} in {personal_org.name}")
                )

        self.stdout.write(
            self.style.SUCCESS(f"Summary: Created {created_orgs} organizations and {created_memberships} memberships")
        )
