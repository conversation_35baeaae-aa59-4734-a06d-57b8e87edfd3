from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from django.db import transaction
from organizations.models import Organization, OrganizationType
from workspaces.models import Workspace

User = get_user_model()


class Command(BaseCommand):
    help = "Migrate existing workspaces to personal organizations"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be done without making changes",
        )

    def handle(self, *args, **options):
        dry_run = options["dry_run"]

        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No changes will be made"))

        # Get all users who own workspaces but don't have personal organizations
        users_without_personal_orgs = (
            User.objects.filter(workspaces__isnull=False)
            .exclude(owned_organizations__type=OrganizationType.PERSONAL)
            .distinct()
        )

        self.stdout.write(f"Found {users_without_personal_orgs.count()} users who need personal organizations")

        # Get workspaces without organizations
        workspaces_without_orgs = Workspace.objects.filter(organization__isnull=True)

        self.stdout.write(f"Found {workspaces_without_orgs.count()} workspaces without organizations")

        if dry_run:
            for user in users_without_personal_orgs:
                user_workspaces = workspaces_without_orgs.filter(owner=user)
                self.stdout.write(
                    f"Would create personal organization for {user.get_display_name()} "
                    f"({user.email}) with {user_workspaces.count()} workspaces"
                )
            return

        # Perform the migration
        created_orgs = 0
        migrated_workspaces = 0

        with transaction.atomic():
            for user in users_without_personal_orgs:
                # Create personal organization for user
                personal_org, created = Organization.objects.get_or_create(
                    owner=user,
                    type=OrganizationType.PERSONAL,
                    defaults={
                        "name": f"{user.get_display_name()}'s Personal Account",
                        "description": "Personal account for individual workspaces",
                    },
                )

                if created:
                    created_orgs += 1
                    self.stdout.write(f"Created personal organization for {user.get_display_name()}")

                # Migrate user's workspaces to their personal organization
                user_workspaces = workspaces_without_orgs.filter(owner=user)
                updated_count = user_workspaces.update(organization=personal_org)
                migrated_workspaces += updated_count

                if updated_count > 0:
                    self.stdout.write(f"Migrated {updated_count} workspaces to {personal_org.name}")

        self.stdout.write(
            self.style.SUCCESS(
                f"Migration completed successfully!\n"
                f"Created {created_orgs} personal organizations\n"
                f"Migrated {migrated_workspaces} workspaces"
            )
        )

        # Check for any remaining workspaces without organizations
        remaining_workspaces = Workspace.objects.filter(organization__isnull=True)
        if remaining_workspaces.exists():
            self.stdout.write(
                self.style.WARNING(
                    f"Warning: {remaining_workspaces.count()} workspaces still "
                    f"without organizations. These may need manual review."
                )
            )

            for workspace in remaining_workspaces[:5]:  # Show first 5
                self.stdout.write(f'  - Workspace "{workspace.title}" owned by {workspace.owner.email}')
