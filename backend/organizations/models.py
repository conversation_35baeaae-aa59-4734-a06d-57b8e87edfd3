import uuid
from datetime import <PERSON><PERSON><PERSON>

from core.models import BaseModel
from django.conf import settings
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _


class OrganizationType(models.TextChoices):
    PERSONAL = "personal", _("Personal")
    TEAM = "team", _("Team")


class OrganizationRole(models.TextChoices):
    OWNER = "owner", _("Owner")
    ADMIN = "admin", _("Admin")
    MEMBER = "member", _("Member")
    VIEWER = "viewer", _("Viewer")


class Permission(models.TextChoices):
    VIEW = "view", _("View")
    CREATE = "create", _("Create")
    EDIT = "edit", _("Edit")
    DELETE = "delete", _("Delete")


class Organization(BaseModel):
    """
    Model representing an organization/team.
    All workspaces belong to an organization.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.Char<PERSON>ield(max_length=255)
    description = models.TextField(blank=True)
    type = models.CharField(
        max_length=20,
        choices=OrganizationType.choices,
        default=OrganizationType.PERSONAL,
    )

    # Owner is always the creator of the organization
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="owned_organizations",
    )

    # Organization settings
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ["created_at"]

    def __str__(self):
        return f"{self.name} ({self.get_type_display()})"

    def get_member_count(self):
        return self.members.filter(is_active=True).count()

    def get_workspace_count(self):
        return self.workspaces.count()

    def has_permission(self, user, permission):
        """Check if user has specific permission in this organization"""
        if not user.is_authenticated:
            return False

        if user == self.owner:
            return True

        try:
            member = self.members.get(user=user, is_active=True)
            return member.has_permission(permission)
        except OrganizationMember.DoesNotExist:
            return False


class OrganizationMember(BaseModel):
    """
    Model representing a user's membership in an organization.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="members",
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="organization_memberships",
    )
    role = models.CharField(
        max_length=20,
        choices=OrganizationRole.choices,
        default=OrganizationRole.MEMBER,
    )

    # Permissions - can be customized per member
    can_view = models.BooleanField(default=True)
    can_create = models.BooleanField(default=False)
    can_edit = models.BooleanField(default=False)
    can_delete = models.BooleanField(default=False)

    is_active = models.BooleanField(default=True)
    joined_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ["organization", "user"]
        ordering = ["joined_at"]

    def __str__(self):
        return f"{self.user.get_display_name()} in {self.organization.name}"

    def has_permission(self, permission):
        """Check if this member has a specific permission"""
        permission_map = {
            Permission.VIEW: self.can_view,
            Permission.CREATE: self.can_create,
            Permission.EDIT: self.can_edit,
            Permission.DELETE: self.can_delete,
        }
        return permission_map.get(permission, False)

    def get_role_permissions(self):
        """Get default permissions based on role"""
        role_permissions = {
            OrganizationRole.OWNER: {
                "can_view": True,
                "can_create": True,
                "can_edit": True,
                "can_delete": True,
            },
            OrganizationRole.ADMIN: {
                "can_view": True,
                "can_create": True,
                "can_edit": True,
                "can_delete": True,
            },
            OrganizationRole.MEMBER: {
                "can_view": True,
                "can_create": True,
                "can_edit": True,
                "can_delete": False,
            },
            OrganizationRole.VIEWER: {
                "can_view": True,
                "can_create": False,
                "can_edit": False,
                "can_delete": False,
            },
        }
        return role_permissions.get(self.role, {})


class InvitationStatus(models.TextChoices):
    PENDING = "pending", _("Pending")
    ACCEPTED = "accepted", _("Accepted")
    DECLINED = "declined", _("Declined")
    CANCELLED = "cancelled", _("Cancelled")
    EXPIRED = "expired", _("Expired")


class OrganizationInvitation(BaseModel):
    """
    Model representing an invitation to join an organization.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="invitations",
    )

    # Inviter information
    invited_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="sent_invitations",
    )

    # Invitee information
    email = models.EmailField()
    invited_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="received_invitations",
        null=True,
        blank=True,
    )

    # Invitation details
    role = models.CharField(
        max_length=20,
        choices=OrganizationRole.choices,
        default=OrganizationRole.MEMBER,
    )
    message = models.TextField(blank=True)

    # Permissions for the invited user
    can_view = models.BooleanField(default=True)
    can_create = models.BooleanField(default=False)
    can_edit = models.BooleanField(default=False)
    can_delete = models.BooleanField(default=False)

    # Status and timing
    status = models.CharField(
        max_length=20,
        choices=InvitationStatus.choices,
        default=InvitationStatus.PENDING,
    )
    expires_at = models.DateTimeField()
    accepted_at = models.DateTimeField(null=True, blank=True)
    declined_at = models.DateTimeField(null=True, blank=True)

    # Unique token for invitation links
    token = models.UUIDField(default=uuid.uuid4, unique=True)

    class Meta:
        ordering = ["-created_at"]
        # Note: We don't use unique_together here because we want to allow
        # multiple cancelled/declined invitations for the same email.
        # The business logic in views.py prevents multiple pending invitations.

    def __str__(self):
        return f"Invitation to {self.email} for {self.organization.name}"

    def save(self, *args, **kwargs):
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(days=7)
        super().save(*args, **kwargs)

    def is_expired(self):
        return timezone.now() > self.expires_at

    def can_be_accepted(self):
        return self.status == InvitationStatus.PENDING and not self.is_expired()

    def accept(self, user):
        """Accept the invitation and create organization membership"""
        if not self.can_be_accepted():
            raise ValueError("Invitation cannot be accepted")

        # Create or update membership
        member, created = OrganizationMember.objects.get_or_create(
            organization=self.organization,
            user=user,
            defaults={
                "role": self.role,
                "can_view": self.can_view,
                "can_create": self.can_create,
                "can_edit": self.can_edit,
                "can_delete": self.can_delete,
            },
        )

        if not created:
            # Update existing membership
            member.role = self.role
            member.can_view = self.can_view
            member.can_create = self.can_create
            member.can_edit = self.can_edit
            member.can_delete = self.can_delete
            member.is_active = True
            member.save()

        # Update invitation status
        self.status = InvitationStatus.ACCEPTED
        self.accepted_at = timezone.now()
        self.invited_user = user
        self.save()

        return member

    def decline(self):
        """Decline the invitation"""
        if self.status != InvitationStatus.PENDING:
            raise ValueError("Only pending invitations can be declined")

        self.status = InvitationStatus.DECLINED
        self.declined_at = timezone.now()
        self.save()

    def cancel(self):
        """Cancel the invitation"""
        if self.status != InvitationStatus.PENDING:
            raise ValueError("Only pending invitations can be cancelled")

        self.status = InvitationStatus.CANCELLED
        self.save()

    def regenerate_token(self):
        """Regenerate the invitation token and extend expiry"""
        if self.status != InvitationStatus.PENDING:
            raise ValueError("Only pending invitations can have their token regenerated")

        self.token = uuid.uuid4()
        self.expires_at = timezone.now() + timedelta(days=7)
        self.save()
