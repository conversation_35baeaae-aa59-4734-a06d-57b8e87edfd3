# Generated manually to add partial unique constraint for pending invitations

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("organizations", "0003_remove_invitation_unique_constraint"),
    ]

    operations = [
        migrations.RunSQL(
            # Add a partial unique constraint that only applies to pending invitations
            sql="""
                CREATE UNIQUE INDEX organizations_organizationinvitation_pending_unique
                ON organizations_organizationinvitation (organization_id, email)
                WHERE status = 'pending';
            """,
            reverse_sql="""
                DROP INDEX IF EXISTS organizations_organizationinvitation_pending_unique;
            """,
        ),
    ]
