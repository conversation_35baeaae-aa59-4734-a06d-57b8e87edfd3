from rest_framework import serializers
from users.schemas import PublicUserSerializer


class OrganizationSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    name = serializers.CharField()
    type = serializers.CharField()
    owner = PublicUserSerializer()
    created_at = serializers.DateTimeField()
    updated_at = serializers.DateTimeField()


class OrganizationMemberSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    user = PublicUserSerializer()
    organization = OrganizationSerializer()
    role = serializers.CharField()
    can_view = serializers.BooleanField()
    can_create = serializers.BooleanField()
    can_edit = serializers.BooleanField()
    can_delete = serializers.BooleanField()
    is_active = serializers.BooleanField()
    joined_at = serializers.DateTimeField()


class OrganizationInvitationSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    organization = OrganizationSerializer()
    email = serializers.EmailField()
    role = serializers.Char<PERSON>ield()
    can_view = serializers.BooleanField()
    can_create = serializers.BooleanField()
    can_edit = serializers.BooleanField()
    can_delete = serializers.BooleanField()
    status = serializers.CharField()
    invited_by = PublicUserSerializer()
    expires_at = serializers.DateTimeField()
    created_at = serializers.DateTimeField()
    updated_at = serializers.DateTimeField()
    token = serializers.UUIDField()


class CreateOrganizationSerializer(serializers.Serializer):
    name = serializers.CharField()
    type = serializers.CharField(default="team")


class InviteMemberSerializer(serializers.Serializer):
    email = serializers.EmailField()
    role = serializers.CharField(default="member")
    can_view = serializers.BooleanField(default=True)
    can_create = serializers.BooleanField(default=False)
    can_edit = serializers.BooleanField(default=False)
    can_delete = serializers.BooleanField(default=False)


class UpdateMemberPermissionsSerializer(serializers.Serializer):
    member_id = serializers.UUIDField()
    role = serializers.CharField(required=False, allow_null=True)
    can_view = serializers.BooleanField(required=False, allow_null=True)
    can_create = serializers.BooleanField(required=False, allow_null=True)
    can_edit = serializers.BooleanField(required=False, allow_null=True)
    can_delete = serializers.BooleanField(required=False, allow_null=True)


class UpdateMemberSerializer(serializers.Serializer):
    role = serializers.CharField(required=False, allow_null=True)
    can_view = serializers.BooleanField(required=False, allow_null=True)
    can_create = serializers.BooleanField(required=False, allow_null=True)
    can_edit = serializers.BooleanField(required=False, allow_null=True)
    can_delete = serializers.BooleanField(required=False, allow_null=True)


class InvitationTokenSerializer(serializers.Serializer):
    token = serializers.CharField()


class UserOrganizationSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    organization = OrganizationSerializer()
    role = serializers.CharField()
    can_view = serializers.BooleanField()
    can_create = serializers.BooleanField()
    can_edit = serializers.BooleanField()
    can_delete = serializers.BooleanField()
    is_active = serializers.BooleanField()
    joined_at = serializers.DateTimeField()


# For backward compatibility
OrganizationSchema = OrganizationSerializer
OrganizationMemberSchema = OrganizationMemberSerializer
OrganizationInvitationSchema = OrganizationInvitationSerializer
CreateOrganizationSchema = CreateOrganizationSerializer
InviteMemberSchema = InviteMemberSerializer
UpdateMemberPermissionsSchema = UpdateMemberPermissionsSerializer
UpdateMemberSchema = UpdateMemberSerializer
InvitationTokenSchema = InvitationTokenSerializer
UserOrganizationSchema = UserOrganizationSerializer
