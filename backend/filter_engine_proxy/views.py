"""
Filter Engine Proxy Views

Forwards requests to the Filter Engine microservice and handles responses.
"""

import json
import logging
from typing import Any

import requests
from django.conf import settings
from django.http import JsonResponse
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.csrf import csrf_exempt

logger = logging.getLogger(__name__)

# Filter Engine Service Configuration
FILTER_ENGINE_BASE_URL = getattr(settings, "FILTER_ENGINE_SERVICE_URL", "http://localhost:3001")
FILTER_ENGINE_TIMEOUT = getattr(settings, "FILTER_ENGINE_TIMEOUT", 30)


class FilterEngineProxyView(View):
    """Base proxy view for Filter Engine requests"""

    def get_service_url(self, endpoint: str) -> str:
        """Construct the full service URL"""
        return f"{FILTER_ENGINE_BASE_URL}/api/v1/filter/{endpoint}"

    def forward_request(self, endpoint: str, data: dict[str, Any]) -> dict[str, Any]:
        """Forward request to Filter Engine service"""
        url = self.get_service_url(endpoint)

        try:
            response = requests.post(
                url,
                json=data,
                headers={
                    "Content-Type": "application/json",
                    "User-Agent": "GTM-Backend-Proxy/1.0",
                },
                timeout=FILTER_ENGINE_TIMEOUT,
            )

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Filter Engine service error: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": "Service Error",
                    "message": f"Filter Engine service returned {response.status_code}",
                    "details": response.text if response.status_code < 500 else None,
                }

        except requests.exceptions.Timeout:
            logger.error(f"Filter Engine service timeout for endpoint: {endpoint}")
            return {
                "success": False,
                "error": "Timeout Error",
                "message": "Filter Engine service is not responding",
            }
        except requests.exceptions.RequestException as e:
            logger.error(f"Filter Engine service connection error: {str(e)}")
            return {
                "success": False,
                "error": "Connection Error",
                "message": "Unable to connect to Filter Engine service",
            }
        except Exception as e:
            logger.error(f"Unexpected error in filter engine proxy: {str(e)}")
            return {
                "success": False,
                "error": "Internal Error",
                "message": "An unexpected error occurred",
            }


@method_decorator(csrf_exempt, name="dispatch")
class ValidateConfigurationView(FilterEngineProxyView):
    """Proxy for configuration validation"""

    def post(self, request):
        try:
            data = json.loads(request.body)
            result = self.forward_request("validate-configuration", data)

            status_code = 200 if result.get("success", False) else 400
            return JsonResponse(result, status=status_code)

        except json.JSONDecodeError:
            return JsonResponse(
                {
                    "success": False,
                    "error": "Invalid JSON",
                    "message": "Request body must be valid JSON",
                },
                status=400,
            )


@method_decorator(csrf_exempt, name="dispatch")
class ValidatePagesView(FilterEngineProxyView):
    """Proxy for pages validation"""

    def post(self, request):
        try:
            data = json.loads(request.body)
            result = self.forward_request("validate-pages", data)

            status_code = 200 if result.get("success", False) else 400
            return JsonResponse(result, status=status_code)

        except json.JSONDecodeError:
            return JsonResponse(
                {
                    "success": False,
                    "error": "Invalid JSON",
                    "message": "Request body must be valid JSON",
                },
                status=400,
            )


@method_decorator(csrf_exempt, name="dispatch")
class ValidateEventsView(FilterEngineProxyView):
    """Proxy for events validation"""

    def post(self, request):
        try:
            data = json.loads(request.body)
            result = self.forward_request("validate-events", data)

            status_code = 200 if result.get("success", False) else 400
            return JsonResponse(result, status=status_code)

        except json.JSONDecodeError:
            return JsonResponse(
                {
                    "success": False,
                    "error": "Invalid JSON",
                    "message": "Request body must be valid JSON",
                },
                status=400,
            )


@method_decorator(csrf_exempt, name="dispatch")
class ValidateSingleRuleView(FilterEngineProxyView):
    """Proxy for single rule validation"""

    def post(self, request):
        try:
            data = json.loads(request.body)
            result = self.forward_request("validate-single-rule", data)

            status_code = 200 if result.get("success", False) else 400
            return JsonResponse(result, status=status_code)

        except json.JSONDecodeError:
            return JsonResponse(
                {
                    "success": False,
                    "error": "Invalid JSON",
                    "message": "Request body must be valid JSON",
                },
                status=400,
            )


@method_decorator(csrf_exempt, name="dispatch")
class GenerateFiringLogicView(FilterEngineProxyView):
    """Proxy for firing logic generation"""

    def post(self, request):
        try:
            data = json.loads(request.body)
            result = self.forward_request("generate-firing-logic", data)

            status_code = 200 if result.get("success", False) else 400
            return JsonResponse(result, status=status_code)

        except json.JSONDecodeError:
            return JsonResponse(
                {
                    "success": False,
                    "error": "Invalid JSON",
                    "message": "Request body must be valid JSON",
                },
                status=400,
            )


@method_decorator(csrf_exempt, name="dispatch")
class GetInputHintsView(FilterEngineProxyView):
    """Proxy for input hints"""

    def post(self, request):
        try:
            data = json.loads(request.body)
            result = self.forward_request("get-input-hints", data)

            status_code = 200 if result.get("success", False) else 400
            return JsonResponse(result, status=status_code)

        except json.JSONDecodeError:
            return JsonResponse(
                {
                    "success": False,
                    "error": "Invalid JSON",
                    "message": "Request body must be valid JSON",
                },
                status=400,
            )


# Health check for the Filter Engine service
def filter_engine_health_check(request):
    """Check if Filter Engine service is healthy"""
    try:
        response = requests.get(f"{FILTER_ENGINE_BASE_URL}/health", timeout=5)

        if response.status_code == 200:
            service_health = response.json()
            return JsonResponse(
                {
                    "status": "healthy",
                    "service": service_health,
                    "proxy_timestamp": json.dumps(None, default=str),
                }
            )
        else:
            return JsonResponse(
                {
                    "status": "unhealthy",
                    "message": f"Service returned {response.status_code}",
                },
                status=503,
            )

    except Exception as e:
        return JsonResponse(
            {"status": "unhealthy", "message": f"Service unavailable: {str(e)}"},
            status=503,
        )
