"""
Filter Engine Proxy URL Configuration
"""

from django.urls import path

from . import views

app_name = "filter_engine_proxy"

urlpatterns = [
    # Main validation endpoints
    path(
        "validate-configuration/",
        views.ValidateConfigurationView.as_view(),
        name="validate_configuration",
    ),
    path(
        "validate-pages/",
        views.ValidatePagesView.as_view(),
        name="validate_pages",
    ),
    path(
        "validate-events/",
        views.ValidateEventsView.as_view(),
        name="validate_events",
    ),
    path(
        "validate-single-rule/",
        views.ValidateSingleRuleView.as_view(),
        name="validate_single_rule",
    ),
    path(
        "generate-firing-logic/",
        views.GenerateFiringLogicView.as_view(),
        name="generate_firing_logic",
    ),
    path(
        "get-input-hints/",
        views.GetInputHintsView.as_view(),
        name="get_input_hints",
    ),
    # Health check
    path(
        "health/",
        views.filter_engine_health_check,
        name="health_check",
    ),
]
