# Sparkle Backend

This is the Django backend for the Sparkle SaaS platform that helps entrepreneurs set up Google Tag Manager (GTM) tracking for their websites, funnels, and online shops.

## 🏗️ Architecture

- **Framework**: Django 5.0+ with Django REST Framework
- **Database**: PostgreSQL with Redis for caching
- **Package Management**: uv (fast Python package manager)
- **Code Quality**: <PERSON><PERSON> (fast Python linter and formatter)
- **Authentication**: Django Allauth with 2FA support
- **Task Queue**: Celery with Redis broker
- **API Integration**: Google Tag Manager API, Stape API

## 🚀 Quick Start

### Prerequisites
- Python 3.12+
- uv (install with: `curl -LsSf https://astral.sh/uv/install.sh | sh`)
- PostgreSQL and Redis (via Docker)

### 1. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
# Set database credentials, API keys, etc.
```

### 2. Database Setup
```bash
# Start PostgreSQL and Redis
docker compose -f ../docker-compose-dev.yml up -d

# Install dependencies
uv pip install -e .[dev]

# Run migrations
uv run python manage.py migrate

# Create cache table
uv run python manage.py createcachetable

# Update search index
uv run python manage.py update_index
```

### 3. Development Server
```bash
# Run development server
uv run python manage.py runserver

# Or use the Makefile
make runserver
```

## 🛠️ Development Commands

### Package Management
```bash
# Install dependencies
uv pip install -e .

# Install with dev dependencies
uv pip install -e .[dev]

# Add new package
uv pip install package-name

# Add dev dependency
uv pip install package-name
# Then manually add to pyproject.toml [project.optional-dependencies.dev]
```

### Code Quality
```bash
# Format code
make format
# or: uv run ruff check --fix . && uv run ruff format .

# Lint code
make lint
# or: uv run ruff check .

# Fix linting issues
make lint-fix
# or: uv run ruff check --fix .
```

### Testing
```bash
# Run all tests
make test
# or: uv run pytest

# Run with coverage
uv run pytest --cov=.

# Run specific test file
uv run pytest apps/core/tests.py

# Run specific test
uv run pytest apps/core/tests.py::TestClassName::test_method
```

### Database Management
```bash
# Create migrations
uv run python manage.py makemigrations

# Apply migrations
uv run python manage.py migrate

# Create superuser
uv run python manage.py createsuperuser

# Load fixtures
uv run python manage.py loaddata fixture_name

# Database shell
uv run python manage.py dbshell
```

### Django Management
```bash
# Django shell
uv run python manage.py shell

# Collect static files
uv run python manage.py collectstatic

# Clear cache
uv run python manage.py clear_cache

# Update search index
uv run python manage.py update_index
```

## 📁 Project Structure

```
backend/
├── apps/                   # Django applications
│   ├── core/              # Core functionality
│   ├── accounts/          # User management
│   ├── projects/          # Project management
│   ├── gtm/              # GTM integration
│   └── stape/            # Stape integration
├── gtm/                   # Main Django project
│   ├── settings/         # Environment-specific settings
│   ├── urls.py           # URL configuration
│   └── wsgi.py           # WSGI application
├── static/               # Static files
├── media/                # User uploads
├── templates/            # Django templates
├── requirements/         # Legacy requirements (use pyproject.toml)
├── manage.py            # Django management script
├── pyproject.toml       # Project configuration and dependencies
├── gunicorn_config.py   # Gunicorn configuration
└── .env.example         # Environment template
```

## 🔧 Configuration

### Environment Variables
Key environment variables in `.env`:

```bash
# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/gtm
REDIS_URL=redis://localhost:6379/0

# Django
SECRET_KEY=your-secret-key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Google APIs
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Stape API
STAPE_API_TOKEN=your-stape-token

# Email (optional for development)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
```

### Settings Structure
- `gtm/settings/base.py` - Base settings
- `gtm/settings/development.py` - Development settings
- `gtm/settings/production.py` - Production settings
- `gtm/settings/test.py` - Test settings

## 🧪 Testing

### Test Structure
```bash
apps/
├── core/
│   ├── tests/
│   │   ├── __init__.py
│   │   ├── test_models.py
│   │   ├── test_views.py
│   │   └── test_utils.py
│   └── tests.py          # Legacy test file
```

### Test Configuration
Tests use pytest with Django integration:
- Configuration in `pyproject.toml`
- Test database: SQLite (faster for tests)
- Coverage reports generated automatically

### Running Specific Tests
```bash
# Test specific app
uv run pytest apps/core/

# Test with verbose output
uv run pytest -v

# Test with coverage
uv run pytest --cov=apps --cov-report=html

# Test matching pattern
uv run pytest -k "test_user"
```

## 🚢 Deployment

### Docker
```bash
# Build image
docker build -t sparkle-backend .

# Run container
docker run -p 8000:8000 sparkle-backend
```

### Production Checklist
- [ ] Set `DEBUG=False`
- [ ] Configure proper `ALLOWED_HOSTS`
- [ ] Set up proper database (not SQLite)
- [ ] Configure email backend
- [ ] Set up static file serving
- [ ] Configure logging
- [ ] Set up monitoring
- [ ] Configure backup strategy

## 🔍 API Documentation

### Authentication
- Session-based authentication for web client
- API key authentication for external clients
- Google OAuth2 integration

### Key Endpoints
- `/api/projects/` - Project management
- `/api/gtm/` - GTM container operations
- `/api/stape/` - Stape integration
- `/api/auth/` - Authentication endpoints

### API Testing
Use the Django admin or tools like Postman:
```bash
# Get API key from admin
X-GTM-API-KEY: your-api-key

# Or use session authentication
# Login via /admin/ first
```

## 🐛 Troubleshooting

### Common Issues

1. **Import errors**: Ensure you're in the backend directory
2. **Database connection**: Check PostgreSQL is running
3. **Redis connection**: Check Redis is running
4. **Migration errors**: Try `uv run python manage.py migrate --fake-initial`
5. **Static files**: Run `uv run python manage.py collectstatic`

### Debug Mode
```bash
# Enable debug toolbar
DEBUG_TOOLBAR=True

# Verbose logging
LOGGING_LEVEL=DEBUG

# Django shell with IPython
uv run python manage.py shell_plus
```

### Performance Monitoring
```bash
# Query count middleware (development)
DJANGO_QUERYCOUNT=True

# Profile views
uv run python manage.py runserver --profile
```

## 📚 Resources

- [Django Documentation](https://docs.djangoproject.com/)
- [Django REST Framework](https://www.django-rest-framework.org/)
- [uv Documentation](https://docs.astral.sh/uv/)
- [Ruff Documentation](https://docs.astral.sh/ruff/)
- [Celery Documentation](https://docs.celeryproject.org/)

## 🤝 Contributing

1. Follow the existing code style (enforced by Ruff)
2. Write tests for new features
3. Update documentation as needed
4. Use conventional commit messages
5. Ensure all tests pass before submitting PR

### Code Style
- Line length: 120 characters
- Use Ruff for formatting and linting
- Follow Django best practices
- Write docstrings for public methods
