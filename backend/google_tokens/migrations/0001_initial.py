# Generated by Django 5.0.6 on 2025-05-24 14:49

import core.utils.model_utils
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="GoogleAPITokens",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "code",
                    core.utils.model_utils.EncryptedTextField(
                        blank=True,
                        null=True,
                        validators=[django.core.validators.MinLengthValidator(1)],
                    ),
                ),
                (
                    "access_token",
                    core.utils.model_utils.EncryptedTextField(
                        validators=[django.core.validators.MinLengthValidator(1)]
                    ),
                ),
                (
                    "refresh_token",
                    core.utils.model_utils.EncryptedTextField(
                        validators=[django.core.validators.MinLengthValidator(1)]
                    ),
                ),
                ("expires_at", models.DateTimeField()),
            ],
            options={
                "verbose_name": "Google API Token",
                "verbose_name_plural": "Google API Tokens",
                "db_table": "google_tokens",
            },
        ),
    ]
