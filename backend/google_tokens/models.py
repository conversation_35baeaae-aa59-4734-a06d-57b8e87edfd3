from core.models import BaseModel
from core.utils.model_utils import EncryptedTextField
from django.conf import settings
from django.core.validators import MinLengthValidator
from django.db import models


class GoogleAPITokens(BaseModel):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="google_tokens")
    code = EncryptedTextField(null=True, blank=True, validators=[MinLengthValidator(1)])
    access_token = EncryptedTextField(validators=[MinLengthValidator(1)])
    refresh_token = EncryptedTextField(validators=[MinLengthValidator(1)])
    expires_at = models.DateTimeField()

    class Meta:
        db_table = "google_tokens"
        verbose_name = "Google API Token"
        verbose_name_plural = "Google API Tokens"
