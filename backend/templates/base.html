<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}GTM{% endblock %}</title>
    {% load static %}
    <link rel="preconnect" href="{% static 'client/' %}">

    <link rel="icon" href="{% static 'favicon/favicon.ico' %}" type="image/x-icon">
    <link rel="preconnect" href="{% static 'bundle.css' %}">
    <link rel="stylesheet" href="{% static 'bundle.css' %}">
    <link rel="apple-touch-icon" sizes="57x57" href=" {% static 'apple-icon-57x57.png' %}">
    <link rel="apple-touch-icon" sizes="60x60" href=" {% static 'apple-icon-60x60.png' %}">
    <link rel="apple-touch-icon" sizes="72x72" href=" {% static 'apple-icon-72x72.png' %}">
    <link rel="apple-touch-icon" sizes="76x76" href=" {% static 'apple-icon-76x76.png' %}">
    <link rel="apple-touch-icon" sizes="114x114" href=" {% static 'apple-icon-114x114.png' %}">
    <link rel="apple-touch-icon" sizes="120x120" href=" {% static 'apple-icon-120x120.png' %}">
    <link rel="apple-touch-icon" sizes="144x144" href=" {% static 'apple-icon-144x144.png' %}">
    <link rel="apple-touch-icon" sizes="152x152" href=" {% static 'apple-icon-152x152.png' %}">
    <link rel="apple-touch-icon" sizes="180x180" href=" {% static 'apple-icon-180x180.png' %}">
    <link rel="icon" type="image/png" sizes="192x192" href=" {% static 'android-icon-192x192.png' %}">
    <link rel="icon" type="image/png" sizes="32x32" href=" {% static 'favicon-32x32.png' %}">
    <link rel="icon" type="image/png" sizes="96x96" href=" {% static 'favicon-96x96.png' %}">
    <link rel="icon" type="image/png" sizes="16x16" href=" {% static 'favicon-16x16.png' %}">
    <link rel="manifest" href=" {% static 'manifest.json' %}">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="/ms-icon-144x144.png">
    <meta name="theme-color" content="#ffffff">
    <meta name="description" content="This SaaS platform revolutionises the way entrepreneurs with websites, funnels (leads or sales), or online shops set up tracking using Google Tag Manager (GTM).">

</head>

<body>
{% block content %}{% endblock %}

{% if request.django_messages %}
{{ request.django_messages|json_script:"django_messages" }}
{% endif %}

{% if request.gtm_user %}
{{ request.gtm_user|json_script:"gtm_user" }}
{% endif %}

{% if request.gtm_config %}
{{ request.gtm_config|json_script:"gtm_config" }}
{% endif %}



{% if messages %}
{% for message in messages %}

{% endfor %}
{% endif %}

{% csrf_token %}


<link rel="preconnect" href="{% static 'fonts/font.css' %}">
<link rel="stylesheet" href="{% static 'fonts/font.css' %}">


</body>

</html>
