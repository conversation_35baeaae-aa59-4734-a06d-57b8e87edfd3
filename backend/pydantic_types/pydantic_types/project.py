"""
Project-related Pydantic models for shared use across services.
"""

from __future__ import annotations

from typing import Any, Optional

from pydantic import BaseModel, Field


class ProjectCreateInput(BaseModel):
    name: str = Field(..., min_length=3, max_length=100)
    workspace_id: int = Field(..., gt=0)
    description: Optional[str] = None


class ProjectUpdateInput(BaseModel):
    # Allow partial updates; accept arbitrary fields for forward-compat
    model_config = dict(extra="allow")
    updates: dict[str, Any]


class ProjectListFilters(BaseModel):
    workspace_id: Optional[int] = None
    # Accept arbitrary filter keys
    model_config = dict(extra="allow")


class Project(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    workspace_id: int


class ProjectCreateResult(BaseModel):
    status: str
    project: Project | None = None
    raw: Any | None = None
    error: str | None = None
