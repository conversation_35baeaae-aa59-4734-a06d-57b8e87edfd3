from __future__ import annotations

from typing import Any, Optional

from pydantic import BaseModel, Field


class WorkspaceCreateInput(BaseModel):
    name: str = Field(..., min_length=3, max_length=50)
    organization_id: Optional[str] = None
    description: Optional[str] = None


class WorkspaceUpdateInput(BaseModel):
    # Allow partial updates; accept arbitrary fields for forward-compat
    model_config = dict(extra="allow")
    updates: dict[str, Any]


class WorkspaceListFilters(BaseModel):
    organization_id: Optional[str] = None
    # Accept arbitrary filter keys
    model_config = dict(extra="allow")


class Workspace(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    organization_id: Optional[str] = None


class ApiResponse(BaseModel):
    message: str | None = None
    data: Any | None = None


class WorkspaceCreateResult(BaseModel):
    status: str
    workspace: Workspace | None = None
    raw: Any | None = None
    error: str | None = None
