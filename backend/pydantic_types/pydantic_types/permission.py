"""
Permission-related Pydantic models for shared use across services.
"""

from __future__ import annotations

from typing import Any, Optional

from pydantic import BaseModel, Field


class PermissionCheckInput(BaseModel):
    resource_type: str = Field(..., min_length=1)
    resource_id: int = Field(..., gt=0)
    action: str = Field(..., min_length=1)


class UserPermissionsInput(BaseModel):
    user_id: Optional[int] = None


class Permission(BaseModel):
    resource_type: str
    resource_id: int
    action: str
    allowed: bool


class PermissionCheckResult(BaseModel):
    status: str
    permission: Permission | None = None
    raw: Any | None = None
    error: str | None = None


class UserPermissionsResult(BaseModel):
    status: str
    permissions: list[dict[str, Any]] | None = None
    raw: Any | None = None
    error: str | None = None
