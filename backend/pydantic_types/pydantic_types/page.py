"""
Page-related Pydantic models for shared use across services.
"""

from __future__ import annotations

from typing import Any, Optional

from pydantic import BaseModel, Field


class PageCreateInput(BaseModel):
    name: str = Field(..., min_length=3, max_length=100)
    project_id: int = Field(..., gt=0)
    url_rules: Optional[list[dict[str, Any]]] = None


class PageUpdateInput(BaseModel):
    # Allow partial updates; accept arbitrary fields for forward-compat
    model_config = dict(extra="allow")
    updates: dict[str, Any]


class PageListFilters(BaseModel):
    project_id: Optional[int] = None
    # Accept arbitrary filter keys
    model_config = dict(extra="allow")


class Page(BaseModel):
    id: int
    name: str
    project_id: int
    url_rules: Optional[list[dict[str, Any]]] = None


class PageCreateResult(BaseModel):
    status: str
    page: Page | None = None
    raw: Any | None = None
    error: str | None = None
