# Sparkle Pydantic Types

Shared Pydantic models for Sparkle/GTMMixer services.

## Installation

```bash
pip install sparkle-pydantic-types
```

## Usage

```python
from pydantic_types.workspace import WorkspaceCreateInput, WorkspaceCreateResult

# Create input model
workspace_input = WorkspaceCreateInput(
    name="My Workspace",
    organization_id="org_123",
    description="A sample workspace"
)
```

## Available Models

### Workspace Models
- `WorkspaceCreateInput` - Input for creating workspaces
- `WorkspaceUpdateInput` - Input for updating workspaces
- `WorkspaceListFilters` - Filters for listing workspaces
- `Workspace` - Workspace data model
- `WorkspaceCreateResult` - Result from workspace creation
- `ApiResponse` - Generic API response model

## Version History

- 0.3.1 - Fixed package build (Python files now included)
- 0.3.0 - Initial release with workspace models# Sparkle Pydantic Types

Shared Pydantic models for Sparkle/GTMMixer services.

## Installation

```bash
pip install sparkle-pydantic-types
```

## Usage

```python
from pydantic_types.workspace import WorkspaceCreateInput, WorkspaceCreateResult

# Create input model
workspace_input = WorkspaceCreateInput(
    name="My Workspace",
    organization_id="org_123",
    description="A sample workspace"
)
```

## Available Models

### Workspace Models
- `WorkspaceCreateInput` - Input for creating workspaces
- `WorkspaceUpdateInput` - Input for updating workspaces
- `WorkspaceListFilters` - Filters for listing workspaces
- `Workspace` - Workspace data model
- `WorkspaceCreateResult` - Result from workspace creation
- `ApiResponse` - Generic API response model

## Version History

- 0.3.1 - Fixed package build (Python files now included)
- 0.3.0 - Initial release with workspace models
