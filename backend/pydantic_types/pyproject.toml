[build-system]
requires = ["hatchling>=1.24.0"]
build-backend = "hatchling.build"

[project]
name = "sparkle-pydantic-types"
version = "0.4.0"
description = "Shared Pydantic models for Sparkle/GTMMixer services"
readme = "README.md"
authors = [
  { name = "Sparkle Team" }
]
requires-python = ">=3.10"
dependencies = [
  "pydantic>=2.6",
]

[project.urls]
Homepage = "https://gitlab.example.com/sparkle/backend/pydantic_types"

[tool.hatch.build.targets.wheel]
packages = ["pydantic_types"]

[tool.hatch.version]
path = "pydantic_types/__init__.py"
