import json
import logging
from urllib.parse import urljoin

import requests
from django.conf import settings
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse, StreamingHttpResponse
from django.utils import timezone
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_tokens.models import GoogleAPITokens
from stape.models import StapeUser

logger = logging.getLogger(__name__)


def refresh_google_token(google_tokens):
    """
    Attempt to refresh the Google access token using the refresh token.

    Args:
        google_tokens (GoogleAPITokens): The token model instance to refresh

    Returns:
        bool: True if refresh was successful, False otherwise
    """
    try:
        # Create credentials object from stored tokens
        credentials = Credentials(
            token=google_tokens.access_token,
            refresh_token=google_tokens.refresh_token,
            token_uri="https://oauth2.googleapis.com/token",
            client_id=settings.GOOGLE_CLIENT_ID,
            client_secret=settings.GOOGLE_CLIENT_SECRET,
        )

        # Refresh the token
        credentials.refresh(Request())

        # Update the stored tokens
        google_tokens.access_token = credentials.token
        google_tokens.expires_at = timezone.now() + timezone.timedelta(
            seconds=credentials.expiry.timestamp() - timezone.now().timestamp()
        )
        google_tokens.save()

        return True

    except Exception as e:
        logger.error(f"Token refresh failed: {str(e)}", exc_info=True)
        return False


class GTM_WrapperProxyView(View):
    """
    Proxy view that forwards requests to GTM_WRAPPER while handling authentication.
    """

    GTM_WRAPPER_BASE_URL = settings.GTM_WRAPPER_BASE_URL

    @csrf_exempt
    def dispatch(self, request, *args, **kwargs):
        """Override dispatch to make the entire view CSRF exempt."""
        return super().dispatch(request, *args, **kwargs)

    def get_auth_headers(self, user):
        """Get authentication headers for the GTM_WRAPPER server."""
        try:
            google_tokens = GoogleAPITokens.objects.get(user=user)
            return {
                "Authorization": f"Bearer {google_tokens.access_token}",
                "Refresh-Token": google_tokens.refresh_token,
                "Admin-Api-Key": settings.ADMIN_API_KEY,
            }

        except GoogleAPITokens.DoesNotExist:
            logger.warning(f"No Google tokens found for user {user.id}")
            return None

    def proxy_request(self, request, path):
        """Forward the request to the GTM_WRAPPER server with proper authentication."""
        # Build the target URL
        print("path", path)
        clean_path = path.lstrip("/")
        target_url = urljoin(self.GTM_WRAPPER_BASE_URL, f"/api/v1/{clean_path}")

        # Get authentication headers
        auth_headers = self.get_auth_headers(request.user)
        if not auth_headers:
            return JsonResponse({"error": "Unauthorized"}, status=401)

        # Prepare headers by combining original headers with auth headers
        headers = dict(request.headers)
        headers.update(auth_headers)

        try:
            # Forward the request to the GTM_WRAPPER server
            response = requests.request(
                method=request.method,
                url=target_url,
                headers=headers,
                data=(request.body if request.method in ["POST", "PUT", "PATCH"] else None),
                params=request.GET,
                stream=True,
            )

            # Create Django response from the GTM_WRAPPER response
            django_response = StreamingHttpResponse(
                streaming_content=response.iter_content(chunk_size=8192),
                content_type=response.headers.get("Content-Type"),
                status=response.status_code,
            )

            # Forward relevant headers from the GTM_WRAPPER response
            for header, value in response.headers.items():
                if header.lower() not in [
                    "content-encoding",
                    "transfer-encoding",
                    "content-length",
                    "connection",
                ]:
                    django_response[header] = value

            return django_response

        except requests.RequestException as e:
            logger.error(f"Error forwarding request to GTM_WRAPPER: {str(e)}", exc_info=True)
            return JsonResponse(
                {
                    "status": "error",
                    "message": "Failed to forward request",
                    "details": str(e),
                },
                status=502,
            )

    def get(self, request, path=""):
        return self.proxy_request(request, path)

    def post(self, request, path=""):
        return self.proxy_request(request, path)

    def put(self, request, path=""):
        return self.proxy_request(request, path)

    def patch(self, request, path=""):
        return self.proxy_request(request, path)

    def delete(self, request, path=""):
        return self.proxy_request(request, path)


class StapeWrapperProxyView(LoginRequiredMixin, View):
    """
    Proxy view that forwards POST requests to STAPE_WRAPPER.
    """

    STAPE_WRAPPER_BASE_URL = settings.STAPE_WRAPPER_BASE_URL

    @csrf_exempt
    def dispatch(self, request, *args, **kwargs):
        """Override dispatch to make the entire view CSRF exempt."""
        return super().dispatch(request, *args, **kwargs)

    def proxy_request(self, request, path):
        """Forward the request to the GTM_WRAPPER server with proper authentication."""
        # Build the target URL
        clean_path = path.lstrip("/")
        target_url = urljoin(self.STAPE_WRAPPER_BASE_URL, clean_path)

        # Prepare headers by combining original headers with auth headers
        headers = dict(request.headers)
        headers["admin-access-token"] = settings.ADMIN_API_KEY

        # check if the user Identifier is present in the request
        user_identifier = request.headers.get("X-Stape-User-Identifier")
        user = None
        if user_identifier:
            user = StapeUser.get_stape_user_by_identifier(user_identifier)
            if not user:
                return JsonResponse({"error": "Invalid Identifier"}, status=404)

            # ensure that the request.user is the same on the stapeuser
            if request.user != user.localUser:
                print("Unauthorized", request.user, user.localUser)
                return JsonResponse({"error": "Unauthorized"}, status=400)

            auth_headers = {
                "X-User-Identifier": user_identifier,
                "api-location": user.token_type,
            }

            # ensure that the user has an API token, if the request is not get_user
            if not user.api_token:
                logger.error(f"Token is Absent {user_identifier}")
            else:
                auth_headers["X-Client-Api-Key"] = user.api_token

            headers.update(auth_headers)

        try:
            # Forward the request to the GTM_WRAPPER server
            response = requests.request(
                method=request.method,
                url=target_url,
                headers=headers,
                data=(request.body if request.method in ["POST", "PUT", "PATCH"] else None),
                params=request.GET,
                stream=True,
            )

            stape_api_key = response.headers.get("X-Stape-User-ApiKey")
            if stape_api_key and user_identifier and user:
                # This is probably a get_user request so we need to update the user's API token
                user.api_token = stape_api_key
                user.save()

            # Create Django response from the GTM_WRAPPER response
            django_response = StreamingHttpResponse(
                streaming_content=response.iter_content(chunk_size=8192),
                content_type=response.headers.get("Content-Type"),
                status=response.status_code,
            )

            # Forward relevant headers from the GTM_WRAPPER response
            for header, value in response.headers.items():
                if header.lower() not in [
                    "content-encoding",
                    "transfer-encoding",
                    "content-length",
                    "X-Stape-User-ApiKey",
                    "connection",
                ]:
                    django_response[header] = value

            return django_response

        except requests.RequestException as e:
            print(f"Error forwarding request to GTM_WRAPPER: {str(e)}")
            logger.error(f"Error forwarding request to GTM_WRAPPER: {str(e)}", exc_info=True)
            return JsonResponse(
                {
                    "status": "error",
                    "message": "Failed to forward request",
                    "details": str(e),
                },
                status=502,
            )

    def post(self, request, path=""):
        """Handle POST requests."""
        return self.proxy_request(request, path)

    def get(self, request, path=""):
        """Handle GET requests."""
        return JsonResponse({"error": "Method Not Allowed"}, status=405)

    def put(self, request, path=""):
        """Handle PUT requests."""
        return JsonResponse({"error": "Method Not Allowed"}, status=405)

    def patch(self, request, path=""):
        """Handle PATCH requests."""
        return JsonResponse({"error": "Method Not Allowed"}, status=405)

    def delete(self, request, path=""):
        """Handle DELETE requests."""
        return JsonResponse({"error": "Method Not Allowed"}, status=405)


class MCPProxyView(View):
    """
    Proxy view that forwards AI agent requests to gtm-mixer-mcp-server.
    Similar to GTM_WrapperProxyView but for MCP communication.
    """

    @csrf_exempt
    def dispatch(self, request, *args, **kwargs):
        """Override dispatch to make the entire view CSRF exempt."""
        return super().dispatch(request, *args, **kwargs)

    def get_mcp_server_url(self):
        """Get MCP server base URL from settings."""
        return getattr(settings, "MCP_SERVER_BASE_URL", "http://localhost:8006")

    def get_auth_headers(self, user):
        """Build authentication headers for MCP server."""
        headers = {
            "X-Admin-API-Key": getattr(settings, "ADMIN_API_KEY", ""),
            "X-User-Id": str(user.id),
            "Content-Type": "application/json",
            "X-Requested-With": "XMLHttpRequest",
        }

        # Add organization ID if user has one
        if hasattr(user, "organization") and user.organization:
            headers["X-Org-Id"] = str(user.organization.id)

        # Add comprehensive user context as JSON
        user_context = {
            "id": user.id,
            "username": user.username,
            # "email": user.email,
            "is_superuser": user.is_superuser,
            "is_staff": user.is_staff,
            "organization_id": (user.organization.id if hasattr(user, "organization") and user.organization else None),
        }
        headers["X-User-Json"] = json.dumps(user_context)

        return headers

    def get_csrf_headers(self, request):
        """Extract CSRF token from request for forwarding."""
        csrf_headers = {}

        # Get CSRF token from cookies
        csrf_token = request.COOKIES.get("csrftoken")
        if csrf_token:
            csrf_headers["X-CSRFToken"] = csrf_token

        return csrf_headers

    def proxy_request(self, request, path):
        """Forward request to MCP server with proper authentication."""
        try:
            # Build target URL for MCP server
            mcp_base_url = self.get_mcp_server_url()
            # Remove leading slash from path to avoid double slashes
            clean_path = path.lstrip("/") if path else ""
            target_url = urljoin(mcp_base_url, f"/{clean_path}")

            logger.info(f"🤖 MCP Proxy Request: {request.method} {path} → {target_url}")

            # Build headers
            headers = dict(request.headers)

            # Add authentication headers
            auth_headers = self.get_auth_headers(request.user)
            headers.update(auth_headers)

            # Add CSRF headers
            csrf_headers = self.get_csrf_headers(request)
            headers.update(csrf_headers)

            # Remove problematic headers that might interfere
            headers_to_remove = ["host", "content-length", "connection"]
            for header in headers_to_remove:
                headers.pop(header, None)
                headers.pop(header.title(), None)

            # Prepare request body
            request_body = None
            if request.method in ["POST", "PUT", "PATCH"]:
                request_body = request.body

            # Forward request to MCP server
            response = requests.request(
                method=request.method,
                url=target_url,
                headers=headers,
                data=request_body,
                params=request.GET,
                stream=True,
                timeout=30,  # 30 second timeout
            )

            logger.info(f"✅ MCP Proxy Response: {response.status_code} {response.reason}")

            # Handle Server-Sent Events
            if response.headers.get("content-type", "").startswith("text/event-stream"):
                django_response = StreamingHttpResponse(
                    streaming_content=response.iter_content(chunk_size=1024),
                    content_type="text/event-stream",
                    status=response.status_code,
                )
                django_response["Cache-Control"] = "no-cache"
                django_response["Connection"] = "keep-alive"
                return django_response

            # Handle regular responses
            content_type = response.headers.get("Content-Type", "application/json")
            django_response = StreamingHttpResponse(
                streaming_content=response.iter_content(
                    chunk_size=8192,
                ),
                content_type=content_type,
                status=response.status_code,
            )

            # Forward relevant headers from MCP server response
            headers_to_forward = [
                "content-type",
                "cache-control",
                "etag",
                "last-modified",
                "access-control-allow-origin",
                "access-control-allow-methods",
                "access-control-allow-headers",
                "access-control-expose-headers",
            ]

            for header, value in response.headers.items():
                if header.lower() in headers_to_forward:
                    django_response[header] = value

            return django_response

        except requests.Timeout:
            logger.error(f"MCP server timeout for {request.method} {path}")
            return JsonResponse(
                {
                    "error": "MCP server timeout",
                    "message": "The AI agent server took too long to respond",
                    "status": "timeout",
                },
                status=504,
            )

        except requests.ConnectionError:
            logger.error(f"MCP server connection error for {request.method} {path}")
            return JsonResponse(
                {
                    "error": "MCP server unavailable",
                    "message": "Could not connect to the AI agent server",
                    "status": "connection_error",
                },
                status=503,
            )

        except requests.RequestException as e:
            logger.error(f"MCP proxy request failed: {str(e)}", exc_info=True)
            return JsonResponse(
                {
                    "error": "MCP proxy request failed",
                    "message": str(e),
                    "status": "proxy_error",
                },
                status=502,
            )

        except Exception as e:
            logger.error(f"Unexpected error in MCP proxy: {str(e)}", exc_info=True)
            return JsonResponse(
                {
                    "error": "Internal proxy error",
                    "message": "An unexpected error occurred in the proxy",
                    "status": "internal_error",
                },
                status=500,
            )

    def get(self, request, path=""):
        """Handle GET requests."""
        return self.proxy_request(request, path)

    def post(self, request, path=""):
        """Handle POST requests."""
        return self.proxy_request(request, path)

    def put(self, request, path=""):
        """Handle PUT requests."""
        return self.proxy_request(request, path)

    def patch(self, request, path=""):
        """Handle PATCH requests."""
        return self.proxy_request(request, path)

    def delete(self, request, path=""):
        """Handle DELETE requests."""
        return self.proxy_request(request, path)

    def options(self, request, path=""):
        """Handle OPTIONS requests for CORS preflight."""
        return self.proxy_request(request, path)


class AppConfigView(View):
    def get(self, request):
        config = {
            "STAPE_EMAIL_GLOBAL": settings.STAPE_EMAIL_GLOBAL,
            "STAPE_EMAIL_EU": settings.STAPE_EMAIL_EU,
        }
        return JsonResponse(config)
