"""

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/stable/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from core.docs_views import (
    SimpleProtectedSpectacularAPIView,
    SimpleProtectedSpectacularRedocView,
    SimpleProtectedSpectacularSwaggerView,
)
from core.health import health_check, simple_health_check
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.contrib.staticfiles.urls import staticfiles_urlpatterns
from django.shortcuts import redirect
from django.urls import include, path, re_path
from gtm.views import (
    AppConfigView,
    GTM_WrapperProxyView,
    MCPProxyView,
    StapeWrapperProxyView,
)

# from .api_urls import urlpatterns as api_urls
from .kit import sveltekit_actions_handler


def redirect_static(request, path):
    # return redirect(f"https://gtmixter.storage.googleapis.com/static/client/dist/assets/{path}")
    return redirect(f"{settings.STATIC_URL}assets/{path}")


urlpatterns = [
    # Health check endpoints
    path("health/", health_check, name="health-check"),
    path("ping/", simple_health_check, name="simple-health-check"),
    path("action/", sveltekit_actions_handler),
    # API Documentation (protected in production)
    # Choose one of the two approaches below:
    # Option 1: User authentication-based protection (requires Django user accounts)
    # path(
    #     "docs/",
    #     ProtectedSpectacularSwaggerView.as_view(url_name="docs-schema"),
    #     name="docs-swagger",
    # ),
    # path(
    #     "docs/redoc/",
    #     ProtectedSpectacularRedocView.as_view(url_name="docs-schema"),
    #     name="docs-redoc",
    # ),
    # path("docs/schema/", ProtectedSpectacularAPIView.as_view(), name="docs-schema"),
    # Option 2: Simple password protection (currently active)
    path(
        "docs/",
        SimpleProtectedSpectacularSwaggerView.as_view(url_name="docs-schema"),
        name="docs-swagger",
    ),
    path(
        "docs/redoc/",
        SimpleProtectedSpectacularRedocView.as_view(url_name="docs-schema"),
        name="docs-redoc",
    ),
    path("docs/schema/", SimpleProtectedSpectacularAPIView.as_view(), name="docs-schema"),
    # API endpoints
    path("api/v1/user/", include("users.urls")),
    path("api/v1/workspaces/", include("workspaces.urls")),
    path("api/v1/projects/", include("project.urls")),
    path("api/v1/", include("organizations.urls")),
    path("api/v1/audit/", include("audit.urls")),
    path("auth/", include("authentication.urls")),
    path("api/v1/networks/", include("ad_networks.urls")),
    path("api/v1/filter-engine/", include("filter_engine_proxy.urls")),
    path("api/v1/conversations/", include("conversations.urls")),
    # Django admin and auth
    path("accounts/", include("allauth.urls")),
    path("admin/", admin.site.urls),
    path("hijack/", include("hijack.urls", namespace="hijack")),
    # Static files
    path("static/assets/<path:path>", redirect_static),  # Redirect to GCS
]


urlpatterns += staticfiles_urlpatterns()
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

if settings.ENABLE_DEBUG_TOOLBAR:
    urlpatterns.append(path("__debug__/", include("debug_toolbar.urls")))


# urlpatterns += api_urls

urlpatterns += [
    # This will catch all paths under gtm/api/v1/ and forward them to the FastAPI server
    re_path(r"^gtm/api/v1/(?P<path>.*)$", GTM_WrapperProxyView.as_view(), name="gtm-proxy"),
    path("config/", AppConfigView.as_view(), name="config"),
]

urlpatterns += [
    path("stape/<path:path>", StapeWrapperProxyView.as_view(), name="stape-proxy"),
    path("stape/", StapeWrapperProxyView.as_view(), name="stape-proxy"),  # Handle the root path
]

# MCP Proxy - AI Agent Service replacement
urlpatterns += [
    path("ai-agent/<path:path>", MCPProxyView.as_view(), name="mcp-agent"),
    path("ai-agent/", MCPProxyView.as_view(), name="mcp-agent-root"),  # Handle the root path
]
