# """
# This file is used to define the API URLs for the GTM app.
# We will define the API URLs for the GTM app in this file.
# This is to help us keep the code organized, maintainable, and namespaced.
# """

# from django.urls import include, path
# from rest_framework import routers

# from workspaces.api_urls import project_router
# from workspaces.api_urls import router as workspaces_router

# app_name = "gtm"

# # Define the router
# router = routers.DefaultRouter()

# # Define the API URLs
# router.registry.extend(workspaces_router.registry)

# urlpatterns = [
#     path("api/v1/", include(router.urls)),
#     path("api/v1/", include(project_router.urls)),
# ]
