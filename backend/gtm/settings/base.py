import logging
from pathlib import Path

import environ
from django.utils.translation import gettext_lazy

logger = logging.getLogger(__name__)

BASE_DIR = Path(__file__).resolve().parent.parent.parent
ENV_FILE = BASE_DIR / ".env"

env = environ.Env()

if ENV_FILE.is_file():
    env.read_env(str(ENV_FILE))


SECRET_KEY = env.str("SECRET_KEY", default="1234")
ENCRYPTION_KEY = env.str("ENCRYPTION_KEY", default="1234")

# SECURITY WARNING: don"t run with debug turned on in production!
DEBUG = env.bool("DEBUG", default=True)  # type: ignore
ENABLE_DEBUG_TOOLBAR = env.bool("ENABLE_DEBUG_TOOLBAR", default=False)  # type: ignore

# STAPE
STAPE_AUTH_TOKEN_EU = env.str("STAPE_AUTH_TOKEN_EU", default="")
STAPE_AUTH_TOKEN_EU_PARTNER = env.str("STAPE_AUTH_TOKEN_EU_PARTNER", default="")

# Filter Engine Service Configuration
FILTER_ENGINE_SERVICE_URL = env.str("FILTER_ENGINE_SERVICE_URL", default="http://localhost:3001")
FILTER_ENGINE_TIMEOUT = env.int("FILTER_ENGINE_TIMEOUT", default=30)
STAPE_AUTH_TOKEN_GLOBAL = env.str("STAPE_AUTH_TOKEN_GLOBAL", default="")
STAPE_AUTH_TOKEN_GLOBAL_PARTNER = env.str("STAPE_AUTH_TOKEN_GLOBAL_PARTNER", default="")

STAPE_EMAIL_GLOBAL = env.str("STAPE_EMAIL_GLOBAL", default="")
STAPE_EMAIL_EU = env.str("STAPE_EMAIL_EU, ", default="")

STAPE_WRAPPER_BASE_URL = env.str("STAPE_WRAPPER_BASE_URL", default="")  # type: ignore

# Note: It is not recommended to set ALLOWED_HOSTS to "*" in production
ALLOWED_HOSTS = env.list("ALLOWED_HOSTS", default=["*"])  # type: ignore

SITE_ID = 1

SITE_URL = env("SITE_URL", default="http://localhost:8000")  # type: ignore

# Application definition

DJANGO_APPS = [
    "django.contrib.admin",
    "django.contrib.admindocs",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.sitemaps",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.sites",
    "django.forms",
]


# Put your third-party apps here
THIRD_PARTY_APPS = [
    "corsheaders",
    "allauth",  # allauth account/registration management
    "allauth.account",
    "allauth.socialaccount",
    "allauth.socialaccount.providers.google",
    "hijack",  # "login as" functionality
    "hijack.contrib.admin",  # hijack buttons in the admin
    "whitenoise.runserver_nostatic",  # whitenoise runserver
    "waffle",
    "collectfast",
    "rest_framework",
    "drf_spectacular",  # API documentation
    "django_filters",
]

PROJECT_APPS = [
    "ad_networks",
    "audit",
    "authentication",
    "conversations",
    "core",
    "domain",
    "email_service",
    "filter_engine_proxy",
    "google_tokens",
    "organizations",
    "page",
    "project",
    "stape",
    "users",
    "workspaces",
    "extractor",
]

INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + PROJECT_APPS

AUTHENTICATION_BACKENDS = [
    "allauth.account.auth_backends.AuthenticationBackend",  # allauth backend
    "django.contrib.auth.backends.ModelBackend",  # Django default
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "audit.middleware.AuditMiddleware",  # Add audit middleware after auth
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "hijack.middleware.HijackUserMiddleware",
    "waffle.middleware.WaffleMiddleware",
    "allauth.account.middleware.AccountMiddleware",
]

if ENABLE_DEBUG_TOOLBAR:
    MIDDLEWARE.insert(0, "debug_toolbar.middleware.DebugToolbarMiddleware")
    INSTALLED_APPS.append("debug_toolbar")
    INTERNAL_IPS = ["127.0.0.1"]
    try:
        import socket

        # get hostname for Docker environments
        # See https://django-debug-toolbar.readthedocs.io/en/latest/installation.html#configure-internal-ips
        hostname, _, ips = socket.gethostbyname_ex(socket.gethostname())
        # add discovered IPs plus some common defaults
        INTERNAL_IPS += [ip[: ip.rfind(".")] + ".1" for ip in ips] + [
            "************",
            "********",
        ]
    except OSError as e:
        print(f"{e} while attempting to resolve system hostname. Using INTERNAL_IPS={INTERNAL_IPS}")

MESSAGE_STORAGE = "django.contrib.messages.storage.session.SessionStorage"


ROOT_URLCONF = "gtm.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [
            BASE_DIR / "templates",
        ],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]


WSGI_APPLICATION = "gtm.wsgi.application"
SILENCED_SYSTEM_CHECKS = ["models.W036"]


GOOGLE_CLIENT_ID = env("GOOGLE_CLIENT_ID", default="")
GOOGLE_CLIENT_SECRET = env("GOOGLE_CLIENT_SECRET", default="")


# Database
# https://docs.djangoproject.com/en/stable/ref/settings/#databases
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": env("DJANGO_DATABASE_NAME", default="gtm"),
        "USER": env("DJANGO_DATABASE_USER", default="postgres"),
        "PASSWORD": env("DJANGO_DATABASE_PASSWORD", default="postgres"),
        "HOST": env("DJANGO_DATABASE_HOST", default="localhost"),
        "PORT": env("DJANGO_DATABASE_PORT", default="5432"),
        "ATOMIC_REQUESTS": False,
        "CONN_MAX_AGE": 0,
    },
}

# Cache Configuration
_CACHE_URL = None
CACHES = {}

try:
    # Check if CACHE_URL is available
    _CACHE_URL = env.cache_url("CACHE_URL", default=None)

    if _CACHE_URL:
        # Redis cache is available
        CACHES = {"default": env.cache(default=_CACHE_URL)}

        if CACHES["default"]["BACKEND"] == "django_redis.cache.RedisCache":
            # Use cached_db sessions with Redis resilience
            SESSION_ENGINE = "django.contrib.sessions.backends.cached_db"

            if "OPTIONS" not in CACHES["default"]:
                CACHES["default"]["OPTIONS"] = {}

            # Add Redis connection resilience
            CACHES["default"]["OPTIONS"].update(
                {
                    "CONNECTION_POOL_KWARGS": {
                        "socket_connect_timeout": 5,
                        "socket_timeout": 5,
                        "retry_on_timeout": True,
                        "health_check_interval": 30,
                    },
                    "IGNORE_EXCEPTIONS": True,  # Fallback to database if Redis fails
                }
            )
            logger.info("Redis cache enabled with resilient configuration")
        else:
            # Non-Redis cache backend
            SESSION_ENGINE = "django.contrib.sessions.backends.cached_db"
            logger.info(f"Cache enabled with backend: {CACHES['default']['BACKEND']}")
    else:
        # No CACHE_URL provided, use dummy cache
        CACHES = {
            "default": {
                "BACKEND": "django.core.cache.backends.dummy.DummyCache",
            }
        }
        SESSION_ENGINE = "django.contrib.sessions.backends.db"
        logger.info("No CACHE_URL provided, using dummy cache")

except Exception as e:
    logger.error(f"Error parsing cache URL: {e}")
    # Fallback to dummy cache if configuration fails
    CACHES = {
        "default": {
            "BACKEND": "django.core.cache.backends.dummy.DummyCache",
        }
    }
    SESSION_ENGINE = "django.contrib.sessions.backends.db"
    logger.warning("Cache configuration failed, falling back to dummy cache")


# Auth / login stuff

# Django recommends overriding the user model even if you don"t think you need to because it makes
# future changes much easier.
AUTH_USER_MODEL = "users.CustomUser"

# Password validation
# https://docs.djangoproject.com/en/stable/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# enable social login
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_USERNAME_REQUIRED = False
ACCOUNT_AUTHENTICATION_METHOD = "email"
ACCOUNT_EMAIL_VERIFICATION = "optional"
ACCOUNT_ADAPTER = "users.adapter.AccountAdapter"  # Use email as username
SOCIALACCOUNT_ADAPTER = "users.adapter.SocialAccountAdapter"  # Use email as username for social accounts
LOGIN_REDIRECT_URL = "/"
LOGIN_URL = "/"
SOCIALACCOUNT_PROVIDERS = {
    "google": {
        "APP": {
            "client_id": GOOGLE_CLIENT_ID,
            "secret": GOOGLE_CLIENT_SECRET,
            "key": "",
        },
        "SCOPE": [
            "profile",
            "email",
            # Google Tag Manager Scopes
            "https://www.googleapis.com/auth/tagmanager.readonly",
            "https://www.googleapis.com/auth/tagmanager.edit.containers",
            "https://www.googleapis.com/auth/tagmanager.delete.containers",
            "https://www.googleapis.com/auth/tagmanager.edit.containerversions",
            "https://www.googleapis.com/auth/tagmanager.publish",
            "https://www.googleapis.com/auth/tagmanager.manage.users",
            "https://www.googleapis.com/auth/tagmanager.manage.accounts",
        ],
        "AUTH_PARAMS": {"access_type": "offline", "prompt": "consent"},
        "CALLBACK_PROTOCOL": "https",
    }
}

CLIENT_URL = env("CLIENT_URL", default="http://localhost:3000")  # type: ignore


SOCIALACCOUNT_AUTO_SIGNUP = True
SOCIALACCOUNT_EMAIL_REQUIRED = True
GOOGLE_OAUTH_API_SESSION_CALLBACK_URL = f"{CLIENT_URL}/auth/login/callback/"

# Internationalization
# https://docs.djangoproject.com/en/stable/topics/i18n/

LANGUAGE_CODE = "en-us"
LANGUAGE_COOKIE_NAME = "gtm_language"
LANGUAGES = [
    ("en", gettext_lazy("English")),
    ("de", gettext_lazy("German")),
]
LOCALE_PATHS = (BASE_DIR / "locale",)

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True

# rest_framework
REST_FRAMEWORK = {
    "FORM_METHOD_OVERRIDE": None,
    "FORM_CONTENT_OVERRIDE": None,
    "FORM_CONTENTTYPE_OVERRIDE": None,
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
        "rest_framework.renderers.BrowsableAPIRenderer",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.SessionAuthentication",
    ],
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.AllowAny",
    ],
    "DEFAULT_FILTER_BACKENDS": [
        "django_filters.rest_framework.DjangoFilterBackend",
    ],
    "DEFAULT_THROTTLE_RATES": {
        "burst": "60/minute",
        "page_submit": "5/hour",
    },
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    "PAGE_SIZE": 10,
    "COERCE_DECIMAL_TO_STRING": False,
    "LOGIN_URL": "/",
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
}

# drf-spectacular settings
SPECTACULAR_SETTINGS = {
    "TITLE": "Sparkle GTM API",
    "DESCRIPTION": "API documentation for Sparkle GTM platform - Google Tag Manager automation and analytics platform",
    "VERSION": "1.0.0",
    "SERVE_INCLUDE_SCHEMA": False,
    "COMPONENT_SPLIT_REQUEST": True,
    "COMPONENT_NO_READ_ONLY_REQUIRED": True,
    "SCHEMA_PATH_PREFIX": "/api/v1/",
    "SCHEMA_PATH_PREFIX_TRIM": True,
    "SERVERS": [
        {"url": "http://localhost:8000", "description": "Development server"},
        {"url": "https://mixer.gettracktion.io", "description": "Production server"},
    ],
    # "TAGS": [
    #     {
    #         "name": "Authentication",
    #         "description": "User authentication and session management",
    #     },
    #     {"name": "Users", "description": "User management and profiles"},
    #     {"name": "Organizations", "description": "Organization management"},
    #     {"name": "Workspaces", "description": "Workspace management"},
    #     {"name": "Projects", "description": "Project management"},
    #     {"name": "GTM", "description": "Google Tag Manager integration"},
    #     {"name": "Networks", "description": "Ad network management"},
    #     {"name": "Audit", "description": "Audit trail and logging"},
    # ],
    # "CONTACT": {
    #     "name": "Sparkle GTM Support",
    #     "email": "<EMAIL>",
    # },
    "LICENSE": {
        "name": "Proprietary",
    },
    # Security schemes for authentication
    "SECURITY": [
        {
            "type": "apiKey",
            "in": "header",
            "name": "X-GTM-API-KEY",
            "description": "API Key for external integrations (development only)",
        },
        {
            "type": "http",
            "scheme": "bearer",
            "description": "Session-based authentication via cookies",
        },
    ],
    "APPEND_COMPONENTS": {
        "securitySchemes": {
            "ApiKeyAuth": {
                "type": "apiKey",
                "in": "header",
                "name": "X-GTM-API-KEY",
                "description": "API Key for external integrations (development only)",
            },
            "SessionAuth": {
                "type": "http",
                "scheme": "bearer",
                "description": "Session-based authentication via cookies",
            },
        }
    },
}

# API Documentation Password Protection
# Set DOCS_PASSWORD environment variable to enable password protection for /docs/ in production
# Example: DOCS_PASSWORD=your_secure_password_here

#  Logging
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": '[{asctime}] {levelname} "{name}" {message}',
            "style": "{",
            "datefmt": "%d/%b/%Y %H:%M:%S",  # match Django server time format
        },
    },
    "handlers": {
        "null": {
            "class": "logging.NullHandler",
        },
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "verbose",
        },
        "mail_admins": {
            "level": "ERROR",
            "class": "django.utils.log.AdminEmailHandler",
        },
    },
    "loggers": {
        "django.security.DisallowedHost": {
            "handlers": ["null"],
            "propagate": False,
        },
        "django": {
            "handlers": ["console"],
            "level": "INFO",
        },
        "gtm": {
            "handlers": ["console"],
            "level": "INFO",
        },
    },
}

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/stable/howto/static-files/

# Static files configuration
STATIC_LOCATION = "static"
STATIC_URL = f"/{STATIC_LOCATION}/"
STATIC_ROOT = env("STATIC_ROOT", default=BASE_DIR / "public/static")

STATICFILES_DIRS = [BASE_DIR / "static"]

STATICFILES_FINDERS = [
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
]

STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"

# Media files configuration
MEDIA_LOCATION = "media"
MEDIA_URL = f"/{MEDIA_LOCATION}/"
# Use local media directory for development, persistent volume for production
MEDIA_ROOT = env("MEDIA_ROOT", default=BASE_DIR / "media")

# WhiteNoise Configuration for Production
WHITENOISE_USE_FINDERS = True
WHITENOISE_AUTOREFRESH = DEBUG  # Only auto-refresh in development
WHITENOISE_MAX_AGE = 31536000  # 1 year cache for static files
WHITENOISE_SKIP_COMPRESS_EXTENSIONS = [
    "jpg",
    "jpeg",
    "png",
    "gif",
    "webp",
    "zip",
    "gz",
    "tgz",
    "bz2",
    "tbz",
    "xz",
    "br",
]

IGNORE_PATTERNS = [
    "node_modules/*",  # Ignore node_modules directory when collecting static files
    ".vscode",
    "src/*",
    ".dockerignore",
    ".gitignore",
    "index.html",
    "package.json",
    "package-lock.json",
    "pnpm-lock.yaml",
    "vite.config.js",
    "yarn.lock",
    "tsconfig.json",
    "tsconfig.node.json",
    "README.md",
    "svelte.config.js",
]


# Default primary key field type
# https://docs.djangoproject.com/en/stable/ref/settings/#default-auto-field

# future versions of Django will use BigAutoField as the default, but it can result in unwanted library
# migration files being generated, so we stick with AutoField for now.
# change this to BigAutoField if you"re sure you want to use it and aren"t worried about migrations.
DEFAULT_AUTO_FIELD = "django.db.models.AutoField"


# use in development
EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"
# use in production
# see https://github.com/anymail/django-anymail for more details/examples
# EMAIL_BACKEND = "anymail.backends.mailgun.EmailBackend"


# Celery setup (using redis)
if "CACHE_URL" in env:
    CACHE_URL = env("CACHE_URL")
elif "REDIS_TLS_URL" in env:
    CACHE_URL = env("REDIS_TLS_URL")
else:
    REDIS_HOST = env("REDIS_HOST", default="localhost")
    REDIS_PORT = env("REDIS_PORT", default="6379")
    CACHE_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/0"

if CACHE_URL.startswith("rediss"):
    CACHE_URL = f"{CACHE_URL}?ssl_cert_reqs=none"


CELERY_BROKER_URL = CELERY_RESULT_BACKEND = CACHE_URL
CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TIMEZONE = "UTC"

# CELERY_BEAT_SCHEDULE = {
#     "sync-subscriptions-every-day": {
#         "task": "apps.subscriptions.tasks.sync_subscriptions_task",
#         "schedule": timedelta(hours=24),
#         "options": {
#             "expires": 60 * 60,  # cancel this task after an hour if it hasn"t started
#         },
#     },
# }


# set this to True in production to have URLs generated with https instead of http
USE_HTTPS_IN_ABSOLUTE_URLS = env.bool("USE_HTTPS_IN_ABSOLUTE_URLS", default=True)


# populate this to configure sentry. should take the form: "https://****@sentry.io/12345"
SENTRY_DSN = env("SENTRY_DSN", default="")


if SENTRY_DSN:
    import sentry_sdk
    from sentry_sdk.integrations.django import DjangoIntegration

    sentry_sdk.init(dsn=SENTRY_DSN, integrations=[DjangoIntegration()])


ITEMS_PER_PAGE = env.int("ITEMS_PER_PAGE", default=10)

LOGOUT_REDIRECT_URL = env("LOGOUT_REDIRECT_URL", default="/")

# Email Service Configuration
EMAIL_SERVICE = {
    "PROVIDER": env("EMAIL_PROVIDER", default="resend"),
    "CONFIG": {
        "api_key": env("RESEND_API_KEY", default=""),
        "from_email": env("FROM_EMAIL", default="<EMAIL>"),
        "from_name": env("FROM_NAME", default="GTM Platform"),
    },
}

# Frontend URL for invitation links
FRONTEND_URL = env("FRONTEND_URL", default="http://localhost:3000")

GTM_WRAPPER_BASE_URL = env("GTM_WRAPPER_BASE_URL", default="http://*************:8000")

ADMIN_API_KEY = env("ADMIN_API_KEY", default="1234")

# MCP Server Configuration (FastMCP with streamable-http)
MCP_SERVER_BASE_URL = env("MCP_SERVER_BASE_URL", default="http://localhost:8006")

GTM_CREDENTIAL_USER_USERNAME = env("GTM_CREDENTIAL_USER_USERNAME", default="ayomipo")
