from .dev import *  # noqa
from .dev import DATABASES, DEBUG_TOOLBAR, INSTALLED_APPS, MIDD<PERSON>WARE

if DEBUG_TOOLBAR:
    INSTALLED_APPS.remove("debug_toolbar")
    MIDDLEWARE.remove("debug_toolbar.middleware.DebugToolbarMiddleware")

DATABASE_ROUTERS = []

if "replica1" in DATABASES:
    del DATABASES["replica1"]

# use sqlite for tests
DATABASES["default"] = {"ENGINE": "django.db.backends.sqlite3", "NAME": ":memory:"}

# Use in-memory cache for tests
CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
    }
}


TEST_RUNNER = "pytest_django.runner.PyTestRunner"
