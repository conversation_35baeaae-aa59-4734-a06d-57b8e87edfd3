from .base import *  # noqa
from .base import env

# # Setup logging for GCP
# try:
#     google_logging_client = google.cloud.logging.Client()
#     google_logging_client.setup_logging()
# except GoogleAuthError:
#     pass


DEBUG = env.bool("DEBUG", default=False)
ALLOWED_HOSTS = [
    "gettracktion.io",
    ".gettracktion.io",
    "mixer.gettracktion.io",
    "localhost",
    "127.0.0.1",
]

# corsheaders
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

# Static files, storages
# GS_BUCKET_NAME = "gtmixter"
# GS_DEFAULT_ACL = "publicRead"
# GS_FILE_OVERWRITE = True
# GS_IS_GZIPPED = True
# GZIP_CONTENT_TYPES = [
#     "text/css",
#     "text/javascript",
#     "application/javascript",
#     "application/x-javascript",
#     "image/svg+xml",
#     "image/jpeg",
#     "image/jpg",
#     "font/ttf",
# ]
# GS_OBJECT_PARAMETERS = {
#     "cache_control": 86400,
# }

# DEFAULT_FILE_STORAGE = "core.storages.GoogleCloudMediaStorage"
# STATICFILES_STORAGE = "core.storages.GoogleCloudStaticStorage"
# STORAGE_URL = f"https://{GS_BUCKET_NAME}.storage.googleapis.com"
# MEDIA_URL = f"{STORAGE_URL}/{MEDIA_LOCATION}/"
# STATIC_URL = f"{STORAGE_URL}/{STATIC_LOCATION}/"

# # collectfast
# CACHES["collectfast"] = env.cache(
#     "CACHE_URL_COLLECTFAST",
#     default="dbcache://cache_collectfast?timeout=None&max_entries=1000",
# )
# COLLECTFAST_STRATEGY = "collectfast.strategies.gcloud.GoogleCloudStrategy"
# COLLECTFAST_CACHE = "collectfast"

# Security
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_SAMESITE = "None"
CSRF_COOKIE_SECURE = True
CSRF_TRUSTED_ORIGINS = ["*"]
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_SECONDS = 31536000
SECURE_REDIRECT_EXEMPT: list = []
SECURE_SSL_HOST: str | bool | None = None
SECURE_SSL_REDIRECT = True
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")
