import os
from pathlib import Path

from .base import REST_FRAMEWORK
from .staging import *  # noqa

# Django security checklist settings.
# More details here: https://docs.djangoproject.com/en/stable/howto/deployment/checklist/
SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True

# HTTP Strict Transport Security settings
# Without uncommenting the lines below, you will get security warnings when running ./manage.py check --deploy
# https://docs.djangoproject.com/en/stable/ref/middleware/#http-strict-transport-security

# # Increase this number once you're confident everything works https://stackoverflow.com/a/49168623/8207
# SECURE_HSTS_SECONDS = 60
# # Uncomment these two lines if you are sure that you don't host any subdomains over HTTP.
# # You will get security warnings if you don't do this.
# SECURE_HSTS_INCLUDE_SUBDOMAINS = True
# SECURE_HSTS_PRELOAD = True

USE_HTTPS_IN_ABSOLUTE_URLS = True

CORS_ALLOW_ALL_ORIGINS = False
CORS_ALLOWED_ORIGINS = [
    "https://gettracktion.io",
    "https://www.gettracktion.io",
    "https://app.gettracktion.io",
    "https://*.gettracktion.io",
]


# rest_framework
if "rest_framework.renderers.BrowsableAPIRenderer" in REST_FRAMEWORK["DEFAULT_RENDERER_CLASSES"]:
    REST_FRAMEWORK["DEFAULT_RENDERER_CLASSES"].remove("rest_framework.renderers.BrowsableAPIRenderer")


# Security
CSRF_TRUSTED_ORIGINS = CORS_ALLOWED_ORIGINS

# Production media configuration
# Use persistent volume for media files in production
MEDIA_ROOT = Path(os.environ.get("MEDIA_ROOT", "/app/media"))

# Disable WhiteNoise auto-refresh in production for performance
WHITENOISE_AUTOREFRESH = False
