from .base import *  # noqa
from .base import BASE_DIR, DATABASES

DEBUG = True
INTERNAL_IPS = ["127.0.0.1"]
ALLOWED_HOSTS = ["*"]
EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

# corsheaders
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

CSRF_TRUSTED_ORIGINS = [
    "https://*.ngrok-free.app",  # Trust all ngrok-free.app subdomains
    "http://localhost:8000",
    "http://127.0.0.1:8000",
    "http://localhost:5173",
    "http://mixer.gettracktion.io",
    "https://mixer.gettracktion.io",
    "https://app.gettracktion.io",
    "https://www.gettracktion.io",
    "https://gettracktion.io",
    "https://*.gettracktion.io",
]

# Replace CORS_ALLOW_ALL_ORIGINS with specific origins
CORS_ALLOWED_ORIGINS = [
    "https://*.ngrok-free.app",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
    "http://localhost:5173",
    "http://mixer.gettracktion.io",
    "https://mixer.gettracktion.io",
    "https://app.gettracktion.io",
    "https://www.gettracktion.io",
    "https://gettracktion.io",
    "https://*.gettracktion.io",
]
CORS_ALLOW_CREDENTIALS = True

ALLOWED_HOSTS = [
    "localhost",
    "127.0.0.1",
    "mixer.gettracktion.io",
    "*.gettracktion.io",
    ".ngrok-free.app",  # Allows all subdomains of ngrok-free.app
]

# SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")
# USE_X_FORWARDED_HOST = True
# USE_X_FORWARDED_PORT = True
# SECURE_SSL_REDIRECT = True


# Reset databases
DATABASE_ROUTERS = []

if "replica1" in DATABASES:
    del DATABASES["replica1"]


DEBUG_TOOLBAR = False

# Development-specific media/static configuration
# Override MEDIA_ROOT to use local development directory
MEDIA_ROOT = BASE_DIR / "media"

# Enable WhiteNoise auto-refresh in development
WHITENOISE_AUTOREFRESH = True
