"""SvelteKit actions handler."""

import logging

from django.http import HttpRe<PERSON>, JsonResponse
from django.urls import NoReverseMatch, resolve, reverse
from django.views.decorators.csrf import csrf_exempt

logger = logging.getLogger(__name__)


@csrf_exempt
def sveltekit_actions_handler(request: HttpRequest):
    """Proxy view to handle SvelteKit actions.

    @param request: HttpRequest
    @return: HttpResponse
    """
    url_name = request.GET.get("url_name")
    namespace = request.GET.get("namespace")
    if namespace:
        url_name = f"{namespace}:{url_name}"

    try:
        url = reverse(url_name)
        logger.info(f"[PROXY:{request.method}] {url}")
        view_func = resolve(url).func

    except NoReverseMatch:
        return JsonResponse({"message": f"URL '{url_name}' does not exist."}, status=404)

    if getattr(view_func, "remove_trigger__", False):
        return JsonResponse({"message": f"ViewFunc '{view_func.__name__}' not triggerable."}, status=403)

    return view_func(request)
