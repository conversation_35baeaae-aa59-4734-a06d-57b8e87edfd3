# Generated by Django 5.0.6 on 2025-05-24 14:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("page", "0001_initial"),
        ("projects", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="suggestedpage",
            name="project_platform",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="suggested_pages",
                to="projects.projectplatform",
            ),
        ),
        migrations.AddField(
            model_name="suggestedpage",
            name="project_type",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="suggested_pages",
                to="projects.projecttype",
            ),
        ),
        migrations.AddField(
            model_name="suggestedpageassociation",
            name="project_type_platform",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="suggested_pages",
                to="projects.projecttypeplatformassociation",
            ),
        ),
        migrations.AddField(
            model_name="suggestedpageassociation",
            name="suggested_page",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="associations",
                to="page.suggestedpage",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="suggestedpageassociation",
            unique_together={("suggested_page", "project_type_platform")},
        ),
    ]
