# Generated by Django 5.0.6 on 2025-05-24 14:49

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="SuggestedPage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=255)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("checkout", "Checkout Page"),
                            ("sales_copy", "Sales Copy Page"),
                            ("product_page", "Product Details Page"),
                            ("cart", "Cart Page"),
                            ("category", "Category Page"),
                            ("purchase_confirmation", "Purchase Confirmation Page"),
                            ("opt_in_confirmation", "Opt-in Confirmation Page"),
                            ("opt_in_form", "Opt-in Form Page"),
                            ("other", "Other"),
                        ],
                        max_length=255,
                    ),
                ),
                ("data", models.JSONField(default=dict)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="SuggestedPageAssociation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
