from core.models import BaseModel
from django.db import models
from project.models.project_platform_model import ProjectPlatform
from project.models.project_type_model import ProjectType
from project.models.project_type_platform_association import (
    ProjectTypePlatformAssociation,
)

PAGE_TYPES = [
    ("checkout", "Checkout Page"),
    ("sales_copy", "Sales Copy Page"),
    ("product_page", "Product Details Page"),
    ("cart", "Cart Page"),
    ("category", "Category Page"),
    ("purchase_confirmation", "Purchase Confirmation Page"),
    ("opt_in_confirmation", "Opt-in Confirmation Page"),
    ("opt_in_form", "Opt-in Form Page"),
    ("other", "Other"),
]


class SuggestedPage(BaseModel):
    name = models.CharField(max_length=255)
    type = models.CharField(max_length=255, choices=PAGE_TYPES)
    project_type = models.ForeignKey(
        ProjectType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="suggested_pages",
    )
    project_platform = models.ForeignKey(
        ProjectPlatform,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="suggested_pages",
    )
    data = models.JSONField(default=dict)

    def __str__(self):
        return self.name


class SuggestedPageAssociation(BaseModel):
    suggested_page = models.ForeignKey(SuggestedPage, on_delete=models.CASCADE, related_name="associations")
    project_type_platform = models.ForeignKey(
        ProjectTypePlatformAssociation,
        on_delete=models.CASCADE,
        related_name="suggested_pages",
    )

    class Meta:
        unique_together = ("suggested_page", "project_type_platform")

    def __str__(self):
        return f"{self.suggested_page} - {self.project_type_platform}"
