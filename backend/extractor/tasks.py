import logging

import requests
from celery import shared_task
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone
from extractor.processors.shopify.processor import ShopifyOrchestrator
from google_tokens.models import GoogleAPITokens

# Import Project model
from project.models.project_model import Project
from project.models.project_platform_model import ProjectPlatform

from .client import MyGTMDataExtractor
from .models import GTMSnapshot, SnapshotStatus

User = get_user_model()
logger = logging.getLogger(__name__)


PLATFORM_ORCHESTRATOR_MAP = {
    "shopify": ShopifyOrchestrator,
}


@shared_task(bind=True, max_retries=3, default_retry_delay=5 * 60)
def extract_gtm_data_task(self, project_platform_id: int, gtm_credential_user_id: int, initiating_user_id: int):
    """
    Celery task to extract GTM data (tags, variables, triggers) for a ProjectPlatform.
    Stores the raw data in a GTMSnapshot.
    """
    try:
        project_platform = ProjectPlatform.objects.get(pk=project_platform_id)
        gtm_credential_user = User.objects.get(pk=gtm_credential_user_id)
        initiating_user = User.objects.get(pk=initiating_user_id)
    except (ProjectPlatform.DoesNotExist, User.DoesNotExist) as e:
        logger.error(
            f"Task Aborted: Prerequisite model not found. ProjectPlatform ID: {project_platform_id}, GTM User ID: {gtm_credential_user_id}, Initiating User ID: {initiating_user_id}. Error: {str(e)}"
        )
        return f"Failed: Prerequisite model not found - {str(e)}"

    logger.info(
        f"Starting GTM extraction for ProjectPlatform '{project_platform.name}' (ID: {project_platform.id}). "
        f"Initiated by: {initiating_user.username}. Using GTM credentials of: {gtm_credential_user.username}."
    )

    platform_configured = (
        project_platform.gtm_platform_account_id
        and project_platform.gtm_platform_container_id
        and project_platform.gtm_platform_workspace_id
    )
    ad_network_configured = (
        project_platform.gtm_ad_network_account_id
        and project_platform.gtm_ad_network_container_id
        and project_platform.gtm_ad_network_workspace_id
    )

    if not (platform_configured and ad_network_configured):
        logger.error(
            f"Task Aborted: GTM Account/Container/Workspace IDs for both Platform and AdNetworks are not configured for ProjectPlatform '{project_platform.name}'."
        )
        return f"Failed: GTM IDs not configured for ProjectPlatform '{project_platform.name}'"

    try:
        google_tokens = GoogleAPITokens.objects.get(user=gtm_credential_user)
        # TODO: Add token refresh logic if necessary, or ensure tokens are fresh before task execution.
        # if not client.is_token_valid(google_tokens.access_token) : client.refresh_token() etc.
    except GoogleAPITokens.DoesNotExist:
        logger.error(f"Task Aborted: CRITICAL - GoogleAPITokens not found for {gtm_credential_user.username}.")
        return "Failed: Tokens unavailable."

    try:
        client = MyGTMDataExtractor(
            access_token=google_tokens.access_token,  # type: ignore
            refresh_token=google_tokens.refresh_token,  # type: ignore
        )
    except Exception as e:
        logger.error(
            f"Task Aborted: Failed to initialize GTM client. Error: {str(e)}",
            exc_info=True,
        )
        return f"Failed: Client initialization error - {str(e)}"

    snapshot_data = {
        "project_platform": project_platform,
        "version_tag": f"auto_{timezone.now().strftime('%Y%m%d_%H%M%S_%f')}",
        "extracted_by": initiating_user,
        "source_gtm_platform_account_id": project_platform.gtm_platform_account_id,
        "source_gtm_ad_network_account_id": project_platform.gtm_ad_network_account_id,
        "platform_container_id": project_platform.gtm_platform_container_id,
        "platform_workspace_id": project_platform.gtm_platform_workspace_id,
        "ad_network_container_id": project_platform.gtm_ad_network_container_id,
        "ad_network_workspace_id": project_platform.gtm_ad_network_workspace_id,
        "status": SnapshotStatus.RAW_FETCHING,  # Initial status during fetch
    }

    # Create snapshot early to record attempt
    snapshot = GTMSnapshot.objects.create(**snapshot_data)
    logger.info(f"Created GTMSnapshot (ID: {snapshot.id}) for PP ID {project_platform.id} in RAW_FETCHING state.")

    error_occured_during_fetch = False
    fetch_error_details = {}  # type: ignore

    try:
        if platform_configured:
            logger.info(f"Fetching PLATFORM GTM data for PP ID {project_platform.id}")
            snapshot.platform_tags = client.fetch_tags(
                project_platform.gtm_platform_account_id,
                project_platform.gtm_platform_container_id,
                project_platform.gtm_platform_workspace_id,
            )
            snapshot.platform_variables = client.fetch_variables(
                project_platform.gtm_platform_account_id,
                project_platform.gtm_platform_container_id,
                project_platform.gtm_platform_workspace_id,
            )
            snapshot.platform_triggers = client.fetch_triggers(
                project_platform.gtm_platform_account_id,
                project_platform.gtm_platform_container_id,
                project_platform.gtm_platform_workspace_id,
            )
        if ad_network_configured:
            logger.info(f"Fetching AD NETWORK GTM data for PP ID {project_platform.id}")
            snapshot.ad_network_tags = client.fetch_tags(
                project_platform.gtm_ad_network_account_id,
                project_platform.gtm_ad_network_container_id,
                project_platform.gtm_ad_network_workspace_id,
            )
            snapshot.ad_network_variables = client.fetch_variables(
                project_platform.gtm_ad_network_account_id,
                project_platform.gtm_ad_network_container_id,
                project_platform.gtm_ad_network_workspace_id,
            )
            snapshot.ad_network_triggers = client.fetch_triggers(
                project_platform.gtm_ad_network_account_id,
                project_platform.gtm_ad_network_container_id,
                project_platform.gtm_ad_network_workspace_id,
            )
        snapshot.status = SnapshotStatus.RAW_FETCHED  # Mark as fetched if no HTTP errors
        snapshot.save()

    except requests.exceptions.HTTPError as http_err:
        logger.error(
            f"HTTPError during GTM fetch for PP ID {project_platform.id}: {http_err}",
            exc_info=True,
        )
        error_occured_during_fetch = True
        fetch_error_details = {
            "type": "HTTPError",
            "message": str(http_err),
            "status_code": http_err.response.status_code if http_err.response else None,
        }
        snapshot.status = SnapshotStatus.RAW_FETCH_FAILED
        snapshot.fetch_error_report = fetch_error_details  # Assuming GTMSnapshot has this field
        snapshot.save(update_fields=["status", "fetch_error_report"])
        if http_err.response is not None and (
            http_err.response.status_code == 429 or http_err.response.status_code >= 500
        ):
            self.retry(exc=http_err)
        return f"Failed: HTTPError during GTM fetch - {str(http_err)}"  # End task here for HTTP errors not retried
    except Exception as e:
        logger.error(
            f"Error during GTM fetch for PP ID {project_platform.id}: {e}",
            exc_info=True,
        )
        error_occured_during_fetch = True
        fetch_error_details = {"type": str(type(e).__name__), "message": str(e)}
        snapshot.status = SnapshotStatus.RAW_FETCH_FAILED
        snapshot.fetch_error_report = fetch_error_details  # Assuming GTMSnapshot has this field
        snapshot.save(update_fields=["status", "fetch_error_report"])
        self.retry(exc=e)
        return f"Failed: Error during GTM fetch - {str(e)}"  # End task here for other errors not retried

    logger.info(
        f"GTMSnapshot (ID: {snapshot.id}) data fetched for PP ID {project_platform.id}. Fetch errors: {error_occured_during_fetch}"
    )

    if error_occured_during_fetch:  # Should have returned or retried already if this is true
        logger.warning(f"Fetch errors occurred for Snapshot ID {snapshot.id}. Logical processing might be impacted.")

    # Proceed to logical processing only if fetch was successful
    process_and_validate_snapshot_task.delay(snapshot.id)
    logger.info(f"Dispatched logical model processing for GTMSnapshot ID: {snapshot.id}")

    return f"Extraction task for PP ID {project_platform.id} completed. Snapshot ID: {snapshot.id}. Fetch errors: {error_occured_during_fetch}"


@shared_task(
    bind=True, max_retries=0, default_retry_delay=2 * 60
)  # max_retries=0 means it won't retry unless explicitly told
def process_and_validate_snapshot_task(self, snapshot_id: int):
    try:
        snapshot = GTMSnapshot.objects.get(pk=snapshot_id)
    except GTMSnapshot.DoesNotExist:
        logger.error(f"Task Aborted: GTMSnapshot with ID {snapshot_id} not found.")
        return f"Failed: GTMSnapshot {snapshot_id} not found."

    # If fetch failed, do not proceed with processing
    if snapshot.status == SnapshotStatus.RAW_FETCH_FAILED:
        logger.warning(f"Skipping processing for Snapshot ID {snapshot_id} due to RAW_FETCH_FAILED status.")
        return f"Skipped processing for Snapshot {snapshot_id} due to fetch failure."

    with transaction.atomic():
        snapshot.status = SnapshotStatus.LOGICAL_PROCESSING
        snapshot.save(update_fields=["status"])

    logger.info(f"Starting platform orchestration for Snapshot ID: {snapshot.id}")

    try:
        if not snapshot.project_platform or not snapshot.project_platform.origin_platform:
            err_msg = f"Snapshot ID {snapshot.id} is missing project_platform or origin_platform."
            logger.error(err_msg)
            snapshot.status = SnapshotStatus.LOGICAL_FAILED
            snapshot.verification_report = {"error": err_msg}
            snapshot.save(update_fields=["status", "verification_report"])
            return f"Failed: {err_msg}"

        platform_key_from_model = snapshot.project_platform.origin_platform.lower()
        OrchestratorClass = PLATFORM_ORCHESTRATOR_MAP.get(platform_key_from_model)

        if not OrchestratorClass:
            logger.error(
                f"No PlatformOrchestrator found for platform '{platform_key_from_model}' (Snapshot ID: {snapshot.id})"
            )
            snapshot.status = SnapshotStatus.LOGICAL_FAILED
            snapshot.verification_report = {"error": f"Unsupported platform: {platform_key_from_model}"}
            snapshot.save(update_fields=["status", "verification_report"])
            return f"Failed: No orchestrator for platform '{platform_key_from_model}'."

        orchestrator = OrchestratorClass(snapshot)
        final_status, final_report, final_nodes = orchestrator.process_and_validate()

        with transaction.atomic():
            snapshot.status = final_status
            snapshot.verification_report = final_report
            snapshot.nodes = final_nodes
            snapshot.platform_processing_report = None
            snapshot.ad_network_processing_report = None
            snapshot.save(
                update_fields=[
                    "status",
                    "verification_report",
                    "nodes",
                    "platform_processing_report",
                    "ad_network_processing_report",
                ]
            )

            if snapshot.project_platform:
                snapshot.project_platform.latest_verification_status = snapshot.status
                snapshot.project_platform.latest_verification_report = snapshot.verification_report
                snapshot.project_platform.save(
                    update_fields=[
                        "latest_verification_status",
                        "latest_verification_report",
                    ]
                )

        logger.info(
            f"Platform orchestration for Snapshot ID {snapshot.id} completed. Final Status: {snapshot.status.label}"
        )

    except Exception as e:
        logger.error(
            f"Critical error in platform orchestration for Snapshot ID {snapshot_id}: {e}",
            exc_info=True,
        )
        with transaction.atomic():
            snapshot.status = SnapshotStatus.LOGICAL_FAILED
            snapshot.verification_report = {
                "task_error": str(e),
                "details": "Check Celery logs for full traceback.",
            }
            snapshot.save(update_fields=["status", "verification_report"])
        # Do not retry indefinitely for processing errors, as they might be data-related.
        # self.retry(exc=e) # Consider if retry is appropriate here.
        return f"Failed: Error during platform orchestration for snapshot {snapshot_id}."

    return f"Platform orchestration for Snapshot {snapshot.id} completed. Status: {snapshot.status.label}"


@shared_task(bind=True, max_retries=3, default_retry_delay=5 * 60)
def build_logical_model_task(self, project_id: int, initiating_user_id: int):
    """
    Celery task to build a logical model for a Project using its platform orchestrator
    and active GTM snapshot.
    """
    try:
        project = Project.objects.get(pk=project_id)
        initiating_user = User.objects.get(pk=initiating_user_id)
    except (Project.DoesNotExist, User.DoesNotExist) as e:
        logger.error(
            f"BuildLogicalModel Task Aborted: Prerequisite model not found. Project ID: {project_id}, Initiating User ID: {initiating_user_id}. Error: {str(e)}"
        )
        return f"Failed: Prerequisite model not found - {str(e)}"

    logger.info(
        f"Starting logical model build for Project '{project.name}' (ID: {project.id}). "
        f"Initiated by: {initiating_user.username}."
    )

    if not project.project_platform:
        logger.error(f"BuildLogicalModel Task Aborted: Project '{project.name}' has no associated Project Platform.")
        # Optionally, update a status field on the Project model here
        return f"Failed: Project '{project.name}' has no Project Platform."

    project_platform = project.project_platform
    if not project_platform.origin_platform:
        logger.error(
            f"BuildLogicalModel Task Aborted: Project Platform '{project_platform.name}' for Project '{project.name}' has no origin platform specified."
        )
        return f"Failed: Project Platform '{project_platform.name}' has no origin platform."

    platform_key = project_platform.origin_platform.lower()
    OrchestratorClass = PLATFORM_ORCHESTRATOR_MAP.get(platform_key)

    if not OrchestratorClass:
        logger.error(
            f"BuildLogicalModel Task Aborted: No PlatformOrchestrator found for platform '{platform_key}' (Project ID: {project.id})"
        )
        return f"Failed: No orchestrator for platform '{platform_key}'."

    active_snapshot = project_platform.active_snapshot
    if not active_snapshot:
        logger.error(
            f"BuildLogicalModel Task Aborted: Project Platform '{project_platform.name}' for Project '{project.name}' has no active GTM snapshot. "
            "Please ensure GTM data has been extracted and a snapshot is active."
        )
        return f"Failed: No active GTM snapshot for Project Platform '{project_platform.name}'."

    # Ensure the active snapshot has been successfully processed (e.g. VERIFIED_OK)
    # This check depends on your workflow. If build_logical_model can work with any snapshot, this might not be needed.
    # For robustness, it's good if the snapshot is in a good state.
    if active_snapshot.status not in [
        SnapshotStatus.VERIFIED_OK,
        SnapshotStatus.VERIFIED_WITH_WARNINGS,
    ]:
        logger.warning(
            f"BuildLogicalModel Task: Active snapshot ID {active_snapshot.id} for project '{project.name}' is not in a VERIFIED_OK or VERIFIED_WITH_WARNINGS state (current: {active_snapshot.status.label}). "
            "Proceeding, but the logical model quality might be affected."
        )
        # You could choose to fail here if a verified snapshot is strictly required:
        # return f"Failed: Active snapshot {active_snapshot.id} is not verified (status: {active_snapshot.status.label})."

    logger.info(
        f"Using active snapshot ID: {active_snapshot.id} (Status: {active_snapshot.status.label}) for Project '{project.name}' to build logical model."
    )

    try:
        orchestrator = OrchestratorClass(snapshot=active_snapshot)
        logger.info(f"[{OrchestratorClass.__name__}] Calling build_logical_model for project ID {project.id}...")

        # The build_logical_model method in the orchestrator is responsible for its own logic,
        # including how and where it persists the resulting logical model.
        # It might update the Project model, create/update ProjectVersionData, etc.
        logical_model_data = orchestrator.build_logical_model(
            project_id=str(project.id)
        )  # Ensure project_id is passed as str if expected

        # The task itself doesn't persist logical_model_data unless specified.
        # Logging the outcome or type of data returned.
        result_type = type(logical_model_data).__name__
        result_summary = ""
        if isinstance(logical_model_data, dict):
            result_summary = f"Returned a dict with keys: {list(logical_model_data.keys())}"
        elif isinstance(logical_model_data, list):
            result_summary = f"Returned a list with {len(logical_model_data)} items."
        elif logical_model_data is None:
            result_summary = "Returned None."
        else:
            result_summary = f"Returned data of type {result_type}."

        logger.info(
            f"Logical model build process completed by {OrchestratorClass.__name__} for project '{project.name}'. {result_summary}"
        )
        # Example: Update project status after successful build (if project model has such fields)
        # project.last_logical_model_build_at = timezone.now()
        # project.last_logical_model_build_status = "SUCCESS"
        # project.save(update_fields=['last_logical_model_build_at', 'last_logical_model_build_status'])

        return f"Logical model build for project '{project.name}' initiated successfully by {OrchestratorClass.__name__}. {result_summary}"

    except NotImplementedError:
        logger.error(
            f"BuildLogicalModel Task Failed: The 'build_logical_model' method is not implemented in {OrchestratorClass.__name__} for project '{project.name}'.",
            exc_info=True,
        )
        # project.last_logical_model_build_status = "FAILED_NOT_IMPLEMENTED"
        # project.save(...)
        return f"Failed: 'build_logical_model' not implemented in {OrchestratorClass.__name__}."
    except Exception as e:
        logger.error(
            f"Critical error during logical model build for project '{project.name}' by {OrchestratorClass.__name__}: {e}",
            exc_info=True,
        )
        # project.last_logical_model_build_status = "FAILED_EXCEPTION"
        # project.save(...)
        # self.retry(exc=e) # Decide if retry is appropriate for such failures.
        return f"Failed: Error during logical model build for project '{project.name}' by {OrchestratorClass.__name__} - {str(e)}."
