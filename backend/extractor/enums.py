from django.db import models
from django.utils.translation import gettext_lazy as _


class SnapshotStatus(models.TextChoices):
    RAW_FETCHING = "RAW_FETCHING", _("Raw Data Fetching")
    RAW_FETCHED = "RAW_FETCHED", _("Raw Data Fetched")
    RAW_FETCH_FAILED = "RAW_FETCH_FAILED", _("Raw Data Fetch Failed")
    LOGICAL_PROCESSING = "LOGICAL_PROCESSING", _("Logical Model Processing")
    LOGICAL_FAILED = "LOGICAL_FAILED", _("Logical Model Failed")
    VERIFIED_OK = "VERIFIED_OK", _("Verified - OK")
    VERIFIED_WITH_WARNINGS = "VERIFIED_WITH_WARNINGS", _("Verified - With Warnings")
    VERIFICATION_FAILED = "VERIFICATION_FAILED", _("Verification Failed")
