# apps/extractor/admin.py
import json

from django.contrib import admin
from django.utils.html import format_html, format_html_join
from django.utils.safestring import mark_safe
from extractor.models import GTMSnapshot


@admin.register(GTMSnapshot)
class GTMSnapshotAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "project_platform_link",
        "version_tag",
        "status",
        "extracted_by",
        "created_at",
        "display_verification_status_label",
    )
    list_filter = (
        "project_platform__name",
        "status",
        "extracted_by",
        "created_at",
    )  # Filter by name
    search_fields = (
        "version_tag",
        "project_platform__name",
        "project_platform__origin_platform",
        "status",
    )
    readonly_fields = (
        "id",
        "project_platform_link_detail",
        "version_tag",
        "status",  # Make sure this uses get_status_display or similar if needed
        "extracted_by",
        "created_at",
        "updated_at",
        "source_gtm_platform_account_id",
        "source_gtm_ad_network_account_id",
        "platform_container_id",
        "platform_workspace_id",
        "display_formatted_nodes",
        "display_verification_report",
    )

    fieldsets = (
        (
            None,
            {"fields": ("project_platform", "version_tag", "status", "extracted_by")},
        ),
        (
            "Source GTM Info",
            {
                "classes": ("collapse",),
                "fields": (
                    "source_gtm_platform_account_id",
                    "platform_container_id",
                    "platform_workspace_id",
                    "source_gtm_ad_network_account_id",
                    "ad_network_container_id",
                    "ad_network_workspace_id",
                ),
            },
        ),
        (
            "Verification & Processed Nodes",
            {
                "description": "Processed data and verification results. Click 'Full JSON Content' or section titles to expand/collapse.",
                "fields": ("display_verification_report", "display_formatted_nodes"),
            },
        ),
        (
            "Raw Platform Data (JSON)",
            {
                "classes": ("collapse",),
                "fields": ("platform_tags", "platform_variables", "platform_triggers"),
                "description": "Raw JSON data fetched from the platform GTM container. Displayed as is.",
            },
        ),
        (
            "Raw Ad Network Data (JSON)",
            {
                "classes": ("collapse",),
                "fields": (
                    "ad_network_tags",
                    "ad_network_variables",
                    "ad_network_triggers",
                ),
                "description": "Raw JSON data fetched from the ad network GTM container. Displayed as is.",
            },
        ),
        (
            "Timestamps",
            {
                "classes": ("collapse",),
                "fields": ("created_at", "updated_at"),
            },
        ),
    )

    def get_status_display(self, obj):  # Helper for status display
        return obj.get_status_display()

    get_status_display.short_description = "Status"

    def project_platform_link(self, obj):
        from django.urls import reverse

        if obj.project_platform:
            try:
                link = reverse(
                    "admin:workspaces_projectplatform_change",
                    args=[obj.project_platform.id],
                )
                return format_html('<a href="{}">{}</a>', link, obj.project_platform.name)
            except Exception:  # Catch if reverse fails for any reason
                return obj.project_platform.name  # Fallback to just name
        return "N/A"

    project_platform_link.short_description = "Project Platform"
    project_platform_link.admin_order_field = "project_platform"

    def project_platform_link_detail(self, obj):
        return self.project_platform_link(obj)

    project_platform_link_detail.short_description = "Project Platform"

    def display_verification_status_label(self, obj):
        if obj.verification_report and isinstance(obj.verification_report, dict):
            return obj.verification_report.get("status_label", obj.get_status_display())
        return obj.get_status_display()

    display_verification_status_label.short_description = "Verification Status"
    display_verification_status_label.admin_order_field = "status"  # Allows sorting by the actual status field

    def _make_collapsible_script(self):
        return mark_safe(
            """
            <script>
            function toggleAdminReportVisibility(elementId) {
                var el = document.getElementById(elementId);
                if (el) {
                    el.style.display = el.style.display === 'none' ? 'block' : 'none';
                }
            }
            </script>
        """
        )

    def _format_json_display(self, data, title="Data", unique_id_prefix=""):
        # Include the script once per page load ideally, or ensure it's idempotent
        # For readonly_fields, this might render multiple times.
        # A better approach for the script is via Admin Media if it gets complex.
        # For now, this will work but might output the script multiple times.

        if data is None:
            return mark_safe("<em>N/A</em>")
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                return format_html("<pre>{}</pre>", data)  # data is a simple string here

        html_segments = []
        # Use a unique prefix for element IDs to avoid conflicts if this function is called multiple times on one page
        base_id = f"{unique_id_prefix}_{title.replace(' ', '_').lower()}"

        if isinstance(data, dict):
            html_segments.append(format_html("<h4>{}</h4>", title))

            # Enhanced Summary Display
            if "summary" in data and isinstance(data["summary"], dict):
                summary_items = []
                for key, value in data["summary"].items():
                    summary_items.append(
                        format_html(
                            "<li><strong>{}:</strong> {}</li>",
                            str(key).replace("_", " ").title(),
                            str(value),
                        )
                    )
                if summary_items:
                    summary_id = f"{base_id}_summary_details"
                    html_segments.append(
                        format_html(
                            '<h5><a href="javascript:void(0);" onclick="toggleAdminReportVisibility(\'{}\');">Summary (Click to toggle)</a></h5>',
                            summary_id,
                        )
                    )
                    html_segments.append(
                        format_html(
                            "<ul id='{}' style='display:none;'>{}</ul><hr>",
                            summary_id,
                            format_html_join("", "{}", ((item,) for item in summary_items)),
                        )
                    )

            # Enhanced Issues Display
            if "issues" in data and isinstance(data["issues"], list) and data["issues"]:
                issue_rows = []
                for issue_idx, issue in enumerate(data["issues"]):
                    if isinstance(issue, dict):
                        message_id = f"{base_id}_issue_msg_{issue_idx}"
                        message_content = str(issue.get("message", "N/A"))
                        message_display = message_content

                        if len(message_content) > 150:  # Arbitrary length for truncation
                            message_display = format_html(
                                '{}... <a href="javascript:void(0);" onclick="toggleAdminReportVisibility(\'{}\');">(details)</a><div id="{}" style="display:none; white-space: pre-wrap; background-color: #f0f0f0; border: 1px solid #ddd; padding: 5px; margin-top: 5px;">{}</div>',
                                message_content[:150],
                                message_id,
                                message_id,
                                message_content,
                            )

                        issue_rows.append(
                            format_html(
                                "<tr><td>{}</td><td>{}</td><td>{}</td></tr>",
                                str(issue.get("severity", "N/A")),
                                message_display,  # Already safe or made safe
                                str(issue.get("code", "N/A")),
                            )
                        )
                if issue_rows:
                    issues_id = f"{base_id}_issues_details"
                    html_segments.append(
                        format_html(
                            '<h5><a href="javascript:void(0);" onclick="toggleAdminReportVisibility(\'{}\');">Issues (Click to toggle)</a></h5>',
                            issues_id,
                        )
                    )
                    html_segments.append(
                        format_html(
                            "<div id='{}' style='display:none;'><table class='report-table'><thead><tr><th>Severity</th><th>Message</th><th>Code</th></tr></thead><tbody>{}</tbody></table></div><hr>",
                            issues_id,
                            format_html_join("", "{}", ((row,) for row in issue_rows)),
                        )
                    )

            # Collapsible Full JSON Content
            detail_id = f"{base_id}_full_json"
            html_segments.append(
                format_html(
                    '<h5><a href="javascript:void(0);" onclick="toggleAdminReportVisibility(\'{}\');">Full JSON Content (Click to expand)</a></h5>',
                    detail_id,
                )
            )
            formatted_json_str = json.dumps(data, indent=2, sort_keys=True)
            html_segments.append(
                format_html(
                    '<div id="{}" style="display:none;"><pre>{}</pre></div>',
                    detail_id,
                    formatted_json_str,
                )
            )

        elif isinstance(data, list):
            html_segments.append(format_html("<h4>{} (List)</h4>", title))
            formatted_json_str = json.dumps(data, indent=2)
            html_segments.append(format_html("<pre>{}</pre>", formatted_json_str))
        else:
            html_segments.append(format_html("<pre>{}</pre>", str(data)))

        # Prepend the script once if it's not already part of the page
        # This is a simplified way; a proper Admin Media approach is better for scripts
        final_html = self._make_collapsible_script() + mark_safe("".join(html_segments))
        return final_html

    def display_verification_report(self, obj):
        # Pass a unique prefix for element IDs
        return self._format_json_display(
            obj.verification_report,
            title="Verification Report",
            unique_id_prefix=f"snap{obj.pk or 'new'}",
        )

    display_verification_report.short_description = "Verification Report"

    def display_formatted_nodes(self, obj):
        return self._format_json_display(
            obj.nodes,
            title="Processed Nodes",
            unique_id_prefix=f"snap{obj.pk or 'new'}",
        )

    display_formatted_nodes.short_description = "Processed Nodes (JSON)"

    class Media:
        # js = ("admin/js/gtm_snapshot_admin.js",)
        css = {"all": ("admin/css/custom_admin_reports.css",)}
