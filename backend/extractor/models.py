from core.models import BaseModel
from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _
from project.models.project_platform_model import ProjectPlatform


class SnapshotStatus(models.TextChoices):
    RAW_FETCHED = "RAW_FETCHED", _("Raw Data Fetched")
    LOGICAL_PROCESSING = "LOGICAL_PROCESSING", _("Logical Model Processing")
    LOGICAL_PROCESSED = "LOGICAL_PROCESSED", _("Logical Model Processed")
    LOGICAL_FAILED = "LOGICAL_PROCESSING_FAILED", _("Logical Model Processing Failed")
    VERIFICATION_PENDING = "VERIFICATION_PENDING", _("Verification Pending")
    VERIFYING = "VERIFYING", _("Verifying Template")
    VERIFIED_OK = "VERIFIED_OK", _("Template Verified - OK")
    VERIFIED_WITH_WARNINGS = "VERIFIED_WITH_WARNINGS", _("Template Verified - Warnings")
    VERIFICATION_FAILED = "VERIFICATION_FAILED", _("Template Verification Failed")


class GTMSnapshot(BaseModel):
    project_platform = models.ForeignKey(
        ProjectPlatform,
        on_delete=models.CASCADE,
        related_name="gtm_snapshots",
    )
    version_tag = models.CharField(
        max_length=100,
        help_text="A descriptive tag for this snapshot (e.g., 'auto_YYYYMMDD_HHMMSS', 'v1.2')",
    )

    # Account Information from source ProjectPlatform
    source_gtm_platform_account_id = models.CharField(max_length=100, null=True, blank=True)
    source_gtm_ad_network_account_id = models.CharField(max_length=100, null=True, blank=True)

    # Platform Data (Raw from GTM)
    platform_container_id = models.CharField(max_length=100, null=True, blank=True)
    platform_workspace_id = models.CharField(max_length=100, null=True, blank=True)
    platform_tags = models.JSONField(default=list, null=True, blank=True)
    platform_variables = models.JSONField(default=list, null=True, blank=True)
    platform_triggers = models.JSONField(default=list, null=True, blank=True)

    # Ad Network Data (Raw from GTM)
    ad_network_container_id = models.CharField(max_length=100, null=True, blank=True)
    ad_network_workspace_id = models.CharField(max_length=100, null=True, blank=True)
    ad_network_tags = models.JSONField(default=list, null=True, blank=True)
    ad_network_variables = models.JSONField(default=list, null=True, blank=True)
    ad_network_triggers = models.JSONField(default=list, null=True, blank=True)

    extracted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="gtm_extractions_initiated",
    )

    # New Fields for Processing and Verification
    status = models.CharField(
        max_length=30,  # Adjusted max_length to fit longer choice values
        choices=SnapshotStatus.choices,
        default=SnapshotStatus.RAW_FETCHED,
        db_index=True,
    )
    platform_processing_report = models.JSONField(
        null=True,
        blank=True,
        help_text="Report from parsing platform GTM data into logical nodes.",
    )
    ad_network_processing_report = models.JSONField(
        null=True,
        blank=True,
        help_text="Report from parsing ad network GTM data into logical nodes.",
    )
    verification_report = models.JSONField(
        null=True,
        blank=True,
        help_text="Report from the internal template verification run.",
    )

    nodes = models.JSONField(null=True, blank=True, default=list)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "GTM Snapshot"
        verbose_name_plural = "GTM Snapshots"
        unique_together = [["project_platform", "version_tag"]]

    def __str__(self):
        project_platform_name = self.project_platform.name if self.project_platform else "N/A"
        return f"Snapshot for {project_platform_name} - {self.version_tag} ({self.status})"
