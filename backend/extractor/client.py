# /apps/extractor/client.py

import logging

from extractor.shared.crawler import GTMWorkspaceClient

logger = logging.getLogger(__name__)


class MyGTMDataExtractor(GTMWorkspaceClient):
    def _parse_response(self, response_data: dict, entity_type: str) -> list:
        logger.debug(f"Attempting to parse GTM Wrapper response for entity type: {entity_type}")

        if not isinstance(response_data, dict):
            logger.error(
                f"Expected a dict for parsing, got {type(response_data)}. Response: {str(response_data)[:500]}"
            )
            return []

        # Based on the logs, the data is nested under a 'data' key,
        # and then under a key matching the entity_type.
        # Example: {'status': 'success', 'message': 'tags retrieved successfully', 'data': {'tag': [...]}}

        if (
            response_data.get("status") == "success"
            and "data" in response_data
            and isinstance(response_data["data"], dict)
        ):
            data_payload = response_data["data"]
            if entity_type in data_payload and isinstance(data_payload[entity_type], list):
                entities = data_payload[entity_type]
                logger.info(
                    f"Successfully parsed {len(entities)} entities for '{entity_type}' from response_data['data']['{entity_type}']."
                )
                return entities
            else:
                logger.warning(
                    f"Key '{entity_type}' not found in 'data' sub-dictionary or is not a list. "
                    f"'data' keys: {list(data_payload.keys()) if isinstance(data_payload, dict) else 'Not a dict'}. "
                    f"Data payload snippet: {str(data_payload)[:300]}"
                )
        else:
            # Log if the primary 'data' key or expected success status is missing
            logger.warning(
                f"Response does not match expected success structure or 'data' key is missing/invalid for '{entity_type}'. "
                f"Response status: {response_data.get('status')}. Top-level keys: {list(response_data.keys())}. "
                f"Response snippet: {str(response_data)[:500]}"
            )

        # Fallback to original possible_keys check if the nested structure isn't found,
        # though logs indicate the nested structure is the correct one.
        possible_keys = [
            entity_type,
            f"{entity_type}s",
            "items",
            # "data", # Removed 'data' from here as we now look for entity_type *within* 'data'
            "result",
        ]

        for key in possible_keys:
            if key in response_data and isinstance(response_data[key], list):
                logger.info(
                    f"Fallback: Successfully parsed {len(response_data[key])} entities for '{entity_type}' from top-level key '{key}'."
                )
                return response_data[key]

        if isinstance(response_data, list):
            logger.info(
                f"Fallback: Parsed response directly as a list of {len(response_data)} entities for '{entity_type}'."
            )
            return response_data

        logger.error(  # Changed to error as this means parsing completely failed
            f"Could not find or parse list for '{entity_type}' in GTM Wrapper response using known structures. "
            f"Response snippet: {str(response_data)[:500]}"
        )

        # Check for error messages from the wrapper if parsing failed
        if "error" in response_data:  # General error key
            logger.error(f"Error explicitly found in GTM Wrapper response: {response_data['error']}")
        elif "message" in response_data and response_data.get("status_code", 200) >= 400:
            logger.error(f"Error message found in GTM Wrapper response: {response_data['message']}")
        elif (
            response_data.get("status") != "success" and "message" in response_data
        ):  # Non-success status with a message
            logger.error(
                f"Non-success status from GTM Wrapper: {response_data.get('status')}, Message: {response_data['message']}"
            )

        return []
