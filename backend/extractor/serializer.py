from extractor.models import GTMSnapshot
from rest_framework import serializers


class GTMSnapshotSerializer(serializers.ModelSerializer):
    """
    Serializer for the GTMSnapshot model.
    """

    status_display = serializers.CharField(source="get_status_display", read_only=True)
    project_platform_name = serializers.CharField(source="project_platform.name", read_only=True, allow_null=True)
    # extracted_by_identifier = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = GTMSnapshot
        fields = [
            "id",
            "project_platform_name",
            "version_tag",
            "status",
            "status_display",
            # "platform_tags",
            # "platform_variables",
            # "platform_triggers",
            # "ad_network_tags",
            # "ad_network_variables",
            # "ad_network_triggers",
            "verification_report",
            "nodes",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "status_display",
            "project_platform_name",
            "extracted_by_identifier",
            "platform_tags",
            "platform_variables",
            "platform_triggers",
            "ad_network_tags",
            "ad_network_variables",
            "ad_network_triggers",
            "platform_processing_report",
            "ad_network_processing_report",
            "verification_report",
            "nodes",
            "created_at",
            "updated_at",
        ]
