import json
import logging
from abc import ABC, abstractmethod
from typing import Any

# from apps.extractor.models import GTMSnapshot
from extractor.enums import SnapshotStatus

logger = logging.getLogger(__name__)


class BaseGTMEntityProcessor(ABC):
    def __init__(self, snapshot):
        self.snapshot = snapshot
        self.processing_errors: list[dict[str, Any]] = []
        self.processed_logical_objects: list[dict[str, Any]] = []
        self.nodes_processed_successfully_count = 0
        self.entities_inspected_count = 0

    @property
    @abstractmethod
    def processor_name(self) -> str:
        """The name of this specific entity processor (e.g., 'GTMTagProcessor')."""
        pass

    @abstractmethod
    def _get_logical_data_from_notes(
        self,
        parsed_notes: dict[str, Any] | None,
        raw_entity_data: dict[str, Any],
        gtm_entity_type: str,
    ) -> dict[str, Any]:
        """
        Extracts specific logical fields from parsed GTM notes and raw_entity_data.
        Subclasses MUST implement this. If parsed_notes is None, implementation should
        rely solely on raw_entity_data to construct the logical representation.

        Returns:
            A dictionary of fields (e.g., node_identifier, node_category, custom fields)
            to be merged into the final logical object. Can include 'additional_params'.
        """
        pass

    def _extract_common_gtm_fields(self, raw_entity_data: dict[str, Any], gtm_entity_type: str) -> dict[str, Any]:
        gtm_id_field_map = {
            "tag": "tagId",
            "variable": "variableId",
            "trigger": "triggerId",
        }
        snapshot_id_val = self.snapshot.id if hasattr(self.snapshot, "id") else str(self.snapshot)

        common_data = {
            "snapshot_id_ref": snapshot_id_val,
            "gtm_id": raw_entity_data.get(gtm_id_field_map.get(gtm_entity_type)),
            "gtm_name": raw_entity_data.get("name"),
            "gtm_type": raw_entity_data.get("type"),
            "raw_gtm_fingerprint": raw_entity_data.get("fingerprint"),
            "additional_params": {"original_gtm_entity": raw_entity_data.copy()},
        }
        return {k: v for k, v in common_data.items() if v is not None}

    def _parse_notes_string_to_dict(
        self, notes_content_str: str | None, gtm_entity_identifier: str
    ) -> dict[str, Any] | None:
        if not isinstance(notes_content_str, str) or not notes_content_str.strip():
            if notes_content_str is None:
                logger.debug(f"Notes field is missing for GTM entity: {gtm_entity_identifier}.")
            elif isinstance(notes_content_str, str) and not notes_content_str.strip():
                logger.debug(f"Notes field is an empty string for GTM entity: {gtm_entity_identifier}.")
            else:
                logger.warning(
                    f"Notes field is not a string (type: {type(notes_content_str)}) for GTM entity: {gtm_entity_identifier}. Content: '{str(notes_content_str)[:100]}'."
                )
            return None
        try:
            parsed = json.loads(notes_content_str)
            if not isinstance(parsed, dict):
                logger.warning(
                    f"Notes field for GTM entity {gtm_entity_identifier} parsed to non-dict type: {type(parsed)}. Notes: '{notes_content_str[:200]}'"
                )
                self.processing_errors.append(
                    {
                        "gtm_identifier": gtm_entity_identifier,
                        "warning": "Notes parsed to non-dictionary type.",
                        "notes_preview": notes_content_str[:200],
                    }
                )
                return None
            return parsed
        except json.JSONDecodeError as e:
            logger.warning(
                f"Failed to parse JSON from notes for GTM entity {gtm_entity_identifier}. Error: {e}. Notes: '{notes_content_str[:200]}'"
            )
            self.processing_errors.append(
                {
                    "gtm_identifier": gtm_entity_identifier,
                    "warning": "Notes JSON parsing failed.",
                    "error_detail": str(e),
                    "notes_preview": notes_content_str[:200],
                }
            )
            return None

    def _prepare_logical_data_object(
        self,
        raw_entity_data: dict[str, Any],
        gtm_entity_type: str,
        logical_object_source_type: str,
    ) -> dict[str, Any]:
        logical_data_object = self._extract_common_gtm_fields(raw_entity_data, gtm_entity_type)
        logical_data_object["source_type"] = logical_object_source_type  # e.g., PLATFORM_TAG

        gtm_entity_identifier = (
            f"ID {logical_data_object.get('gtm_id', 'N/A')}, Name {logical_data_object.get('gtm_name', 'N/A')}"
        )

        notes_content_str = raw_entity_data.get("notes")
        parsed_notes_dict = self._parse_notes_string_to_dict(notes_content_str, gtm_entity_identifier)

        note_derived_data = self._get_logical_data_from_notes(parsed_notes_dict, raw_entity_data, gtm_entity_type)
        logical_data_object.update(note_derived_data)

        if "additional_params" in note_derived_data and isinstance(note_derived_data["additional_params"], dict):
            if "additional_params" not in logical_data_object or not isinstance(
                logical_data_object["additional_params"], dict
            ):
                logical_data_object["additional_params"] = {}

            original_gtm_entity_backup = logical_data_object["additional_params"].get("original_gtm_entity")
            # Subclass's additional_params can overwrite common ones, except original_gtm_entity
            common_additional_params = logical_data_object["additional_params"]
            subclass_additional_params = note_derived_data["additional_params"]

            merged_additional_params = {
                **common_additional_params,
                **subclass_additional_params,
            }
            if original_gtm_entity_backup:  # Ensure it's preserved
                merged_additional_params["original_gtm_entity"] = original_gtm_entity_backup
            logical_data_object["additional_params"] = merged_additional_params

        if not logical_data_object.get("node_identifier"):
            default_identifier = f"unparsed_{logical_data_object.get('gtm_type', 'unknown_type')}_{logical_data_object.get('gtm_id', raw_entity_data.get('name', 'unknown_id'))}"
            logical_data_object["node_identifier"] = default_identifier
            warn_msg = f"node_identifier not set, defaulted to '{default_identifier}' for {gtm_entity_identifier}."
            logger.warning(warn_msg)
            self.processing_errors.append({"gtm_identifier": gtm_entity_identifier, "warning": warn_msg})

        if not logical_data_object.get("node_category"):
            default_category = logical_data_object.get("gtm_type", "unknown_category")  # Use gtm_type as a fallback
            logical_data_object["node_category"] = default_category
            warn_msg = f"node_category not set, defaulted to '{default_category}' for {gtm_entity_identifier}."
            logger.warning(warn_msg)
            self.processing_errors.append({"gtm_identifier": gtm_entity_identifier, "warning": warn_msg})

        if not logical_data_object.get("node_identifier") or not logical_data_object.get("node_category"):
            error_msg = (
                f"CRITICAL: Missing node_identifier or node_category after defaults for "
                f"{gtm_entity_identifier}. Entity will be skipped."
            )
            logger.error(error_msg)
            self.processing_errors.append(
                {
                    "gtm_identifier": gtm_entity_identifier,
                    "error": error_msg,
                    "skipped": True,
                }
            )
            raise ValueError(error_msg)
        return logical_data_object

    def process_raw_entities(
        self,
        raw_entities_list: list[dict[str, Any]],
        gtm_entity_type: str,
        logical_object_source_type: str,
    ) -> None:
        """
        Processes a list of raw GTM entities (tags, triggers, or variables) into logical objects.
        The results are stored in self.processed_logical_objects and errors in self.processing_errors.
        Counters are updated. This method resets state for each call.
        """
        self.entities_inspected_count = 0
        self.nodes_processed_successfully_count = 0
        self.processed_logical_objects = []  # Reset for this specific list processing run
        self.processing_errors = []  # Reset errors for this specific list processing run

        for raw_entity in raw_entities_list:
            self.entities_inspected_count += 1
            # Use a consistent error flag from your data fetching, e.g., "error_flag_from_fetch"
            # or check if essential keys are missing, making it unusable.
            if not raw_entity or not isinstance(raw_entity, dict) or raw_entity.get("error_flag_from_fetch"):
                fetch_error_msg = (
                    raw_entity.get(
                        "error_message",
                        "Missing, invalid, or error-flagged entity data",
                    )
                    if isinstance(raw_entity, dict)
                    else "Missing or invalid entity data"
                )
                logger.warning(
                    f"Skipping entity due to fetch error/invalid data: {fetch_error_msg} Data preview: {str(raw_entity)[:200]}"
                )
                self.processing_errors.append(
                    {
                        "entity_data_preview": str(raw_entity)[:200],
                        "error": fetch_error_msg,
                        "skipped": True,
                    }
                )
                continue

            try:
                logical_object = self._prepare_logical_data_object(
                    raw_entity, gtm_entity_type, logical_object_source_type
                )
                self.processed_logical_objects.append(logical_object)
                self.nodes_processed_successfully_count += 1
            except ValueError as ve:
                # Error already logged and added to self.processing_errors by _prepare_logical_data_object
                logger.warning(
                    f"ValueError processing entity, skipped during _prepare_logical_data_object: {ve}. Data preview: {str(raw_entity)[:200]}"
                )
            except Exception as e:
                gtm_id_val = raw_entity.get(self._get_gtm_id_field_name(gtm_entity_type))
                name_val = raw_entity.get("name", gtm_id_val)
                logger.error(
                    f"Unexpected error processing GTM {gtm_entity_type} into a logical object: {name_val}. Error: {e}",
                    exc_info=True,
                )
                self.processing_errors.append(
                    {
                        "gtm_name": name_val,
                        "gtm_id": gtm_id_val,
                        "error": str(e),
                        "type": str(type(e).__name__),
                        "skipped": True,
                    }
                )

    def _get_gtm_id_field_name(self, gtm_entity_type: str) -> str:
        """Helper to get the correct GTM ID field name."""
        return {"tag": "tagId", "variable": "variableId", "trigger": "triggerId"}.get(gtm_entity_type, "unknownId")

    def generate_report(self) -> dict[str, Any]:
        snapshot_id_val = self.snapshot.id if hasattr(self.snapshot, "id") else str(self.snapshot)
        has_critical_errors = any(issue.get("skipped", False) or "error" in issue for issue in self.processing_errors)

        status = "Completed"
        if has_critical_errors:
            status = "Completed with critical errors"  # Implies some entities might have been skipped
        elif self.processing_errors:  # Only warnings left if no critical errors
            status = "Completed with warnings"

        return {
            "processor": self.processor_name,  # Specific processor like GTMTagProcessor
            "snapshot_id_ref": snapshot_id_val,  # To know which snapshot this sub-report pertains to
            "status": status,
            "entities_inspected": self.entities_inspected_count,
            "logical_objects_created": self.nodes_processed_successfully_count,
            "processing_issues": self.processing_errors,
            "issue_count": len(self.processing_errors),
        }


class GTMTagProcessor(BaseGTMEntityProcessor):
    @property
    def processor_name(self) -> str:
        return "GTMTagProcessor"

    def _get_logical_data_from_notes(
        self,
        parsed_notes: dict[str, Any] | None,
        raw_entity_data: dict[str, Any],
        gtm_entity_type: str,
    ) -> dict[str, Any]:
        data: dict[str, Any] = {"additional_params": {}}  # Initialize to merge later

        # Start with defaults or data derived from raw_entity if notes are absent
        data["node_identifier"] = raw_entity_data.get("name")  # Default to GTM name
        data["node_category"] = gtm_entity_type  # Default to "tag"

        if parsed_notes:
            # Override with more specific info from notes if available
            data["node_identifier"] = parsed_notes.get("node_identifier", data["node_identifier"])
            data["node_category"] = parsed_notes.get(
                "entity_type", data["node_category"]
            )  # e.g., "conversion_pixel", "remarketing"
            data["ad_network"] = parsed_notes.get("ad_network")  # e.g., "bing"
            data["entity_group_key"] = parsed_notes.get("entity_group_key")
            data["is_linkable"] = parsed_notes.get("is_linkable", False)

            # Store other relevant notes fields in additional_params from notes
            # Be careful not to overwrite existing keys in data["additional_params"] unless intended
            notes_additional_params = parsed_notes.get("additional_params", {})
            if isinstance(notes_additional_params, dict):
                data["additional_params"].update(notes_additional_params)

            # Specific examples from your notes
            if "fires_on_event_signal" in parsed_notes:
                data["additional_params"]["fires_on_event_signal"] = parsed_notes.get("fires_on_event_signal")
            if "source_description" in parsed_notes:
                data["additional_params"]["notes_source_description"] = parsed_notes.get("source_description")
            if "api_target_entity_id" in parsed_notes:
                data["additional_params"]["notes_api_target_entity_id"] = parsed_notes.get("api_target_entity_id")

        # Fallback for node_identifier if it's still None or empty after notes
        if not data.get("node_identifier"):
            data["node_identifier"] = f"tag_unidentified_{raw_entity_data.get('tagId', 'unknown')}"

        return data


class GTMTriggerProcessor(BaseGTMEntityProcessor):
    @property
    def processor_name(self) -> str:
        return "GTMTriggerProcessor"

    def _get_logical_data_from_notes(
        self,
        parsed_notes: dict[str, Any] | None,
        raw_entity_data: dict[str, Any],
        gtm_entity_type: str,
    ) -> dict[str, Any]:
        data: dict[str, Any] = {"additional_params": {}}
        data["node_identifier"] = raw_entity_data.get("name")
        data["node_category"] = gtm_entity_type  # Default to "trigger"

        if parsed_notes:
            data["node_identifier"] = parsed_notes.get("event_signal_name", data["node_identifier"])
            data["node_category"] = parsed_notes.get(
                "entity_type", data["node_category"]
            )  # e.g. 'custom_event_listener'
            data["template_name"] = parsed_notes.get("origin_platform")
            data["entity_group_key"] = parsed_notes.get("entity_group_key")
            data["is_linkable"] = parsed_notes.get("is_linkable", False)

            notes_additional_params = parsed_notes.get("additional_params", {})
            if isinstance(notes_additional_params, dict):
                data["additional_params"].update(notes_additional_params)

            if "source_description" in parsed_notes:
                data["additional_params"]["notes_source_description"] = parsed_notes.get("source_description")
            if "api_target_entity_id" in parsed_notes:
                data["additional_params"]["notes_api_target_entity_id"] = parsed_notes.get("api_target_entity_id")

        if not data.get("node_identifier"):
            data["node_identifier"] = f"trigger_unidentified_{raw_entity_data.get('triggerId', 'unknown')}"
        return data


class GTMVariableProcessor(BaseGTMEntityProcessor):
    @property
    def processor_name(self) -> str:
        return "GTMVariableProcessor"

    def _get_logical_data_from_notes(
        self,
        parsed_notes: dict[str, Any] | None,
        raw_entity_data: dict[str, Any],
        gtm_entity_type: str,
    ) -> dict[str, Any]:
        data: dict[str, Any] = {"additional_params": {}}
        data["node_identifier"] = raw_entity_data.get("name")
        data["node_category"] = gtm_entity_type  # Default to "variable"

        if parsed_notes:
            data["node_identifier"] = parsed_notes.get(
                "variable_name_override", data["node_identifier"]
            )  # Example custom field from notes
            data["node_category"] = parsed_notes.get(
                "entity_type", data["node_category"]
            )  # e.g. "dataLayer_variable", "lookup_table"
            data["entity_group_key"] = parsed_notes.get("entity_group_key")
            # Variables are typically not "linkable" in the same way as tags/triggers might be to UI elements
            data["is_linkable"] = parsed_notes.get("is_linkable", False)

            notes_additional_params = parsed_notes.get("additional_params", {})
            if isinstance(notes_additional_params, dict):
                data["additional_params"].update(notes_additional_params)

            if "source_description" in parsed_notes:
                data["additional_params"]["notes_source_description"] = parsed_notes.get("source_description")

        if not data.get("node_identifier"):  # Fallback if name was None and notes didn't provide
            data["node_identifier"] = f"var_unidentified_{raw_entity_data.get('variableId', 'unknown')}"
        return data


class BasePlatformOrchestrator(ABC):
    def __init__(self, snapshot):
        self.snapshot = snapshot
        # Instantiate generic entity processors, passing the snapshot for context (e.g., snapshot_id_ref)
        self.tag_processor = GTMTagProcessor(self.snapshot)
        self.trigger_processor = GTMTriggerProcessor(self.snapshot)
        self.variable_processor = GTMVariableProcessor(self.snapshot)

        # These will hold the *results* from the generic processors
        self._generic_reports: list[dict[str, Any]] = []
        self._all_generically_processed_tags: list[dict[str, Any]] = []
        self._all_generically_processed_triggers: list[dict[str, Any]] = []
        self._all_generically_processed_variables: list[dict[str, Any]] = []

    @property
    @abstractmethod
    def platform_name_key(self) -> str:
        """
        A unique string key for the platform, e.g., 'shopify', 'magento'.
        Used for constructing logical_object_source_type.
        """
        pass

    def _run_generic_entity_processing(self):
        """
        Internal method to run the generic GTM entity processors on all relevant
        data arrays within the snapshot (platform and ad_network).
        Populates internal lists like self._all_generically_processed_tags.
        """
        self._generic_reports = []
        self._all_generically_processed_tags = []
        self._all_generically_processed_triggers = []
        self._all_generically_processed_variables = []

        # --- Tags ---
        if self.snapshot.platform_tags:
            self.tag_processor.process_raw_entities(
                self.snapshot.platform_tags,
                "tag",
                f"{self.platform_name_key.upper()}_PLATFORM_TAG",
            )
            self._all_generically_processed_tags.extend(self.tag_processor.processed_logical_objects)
            self._generic_reports.append(self.tag_processor.generate_report())
        if self.snapshot.ad_network_tags:
            self.tag_processor.process_raw_entities(
                self.snapshot.ad_network_tags,
                "tag",
                f"{self.platform_name_key.upper()}_ADNETWORK_TAG",
            )
            self._all_generically_processed_tags.extend(self.tag_processor.processed_logical_objects)
            self._generic_reports.append(self.tag_processor.generate_report())

        # --- Triggers ---
        if self.snapshot.platform_triggers:
            self.trigger_processor.process_raw_entities(
                self.snapshot.platform_triggers,
                "trigger",
                f"{self.platform_name_key.upper()}_PLATFORM_TRIGGER",
            )
            self._all_generically_processed_triggers.extend(self.trigger_processor.processed_logical_objects)
            self._generic_reports.append(self.trigger_processor.generate_report())
        if self.snapshot.ad_network_triggers:
            self.trigger_processor.process_raw_entities(
                self.snapshot.ad_network_triggers,
                "trigger",
                f"{self.platform_name_key.upper()}_ADNETWORK_TRIGGER",
            )
            self._all_generically_processed_triggers.extend(self.trigger_processor.processed_logical_objects)
            self._generic_reports.append(self.trigger_processor.generate_report())

        # --- Variables ---
        if self.snapshot.platform_variables:
            self.variable_processor.process_raw_entities(
                self.snapshot.platform_variables,
                "variable",
                f"{self.platform_name_key.upper()}_PLATFORM_VARIABLE",
            )
            self._all_generically_processed_variables.extend(self.variable_processor.processed_logical_objects)
            self._generic_reports.append(self.variable_processor.generate_report())
        if self.snapshot.ad_network_variables:
            self.variable_processor.process_raw_entities(
                self.snapshot.ad_network_variables,
                "variable",
                f"{self.platform_name_key.upper()}_ADNETWORK_VARIABLE",
            )
            self._all_generically_processed_variables.extend(self.variable_processor.processed_logical_objects)
            self._generic_reports.append(self.variable_processor.generate_report())

    @abstractmethod
    def _apply_platform_specific_logic(
        self,
        tags: list[dict[str, Any]],
        triggers: list[dict[str, Any]],
        variables: list[dict[str, Any]],
    ) -> tuple[SnapshotStatus, dict[str, Any], dict[str, list[dict[str, Any]]]]:
        """
        This is where the concrete platform orchestrator (e.g., ShopifyOrchestrator)
        implements its unique logic. It receives all generically processed entities.

        It should:
        1. Perform platform-specific merging/filtering of tags, triggers, variables.
        2. Link entities (e.g., tags to triggers).
        3. Perform platform-specific validations.
        4. Enrich entities with platform-specific insights.
        5. Construct the final `snapshot.nodes` structure.
        6. Generate a comprehensive `verification_report`.
        7. Determine the final `SnapshotStatus`.

        Returns:
            final_status (SnapshotStatus enum value),
            final_verification_report (dict),
            final_consolidated_nodes (dict for snapshot.nodes)
        """
        pass

    def process_and_validate(
        self,
    ) -> tuple[SnapshotStatus, dict[str, Any], dict[str, list[dict[str, Any]]]]:
        """
        Main public method to orchestrate the full processing and validation for the platform.
        """
        logger.info(
            f"[{self.__class__.__name__}] Starting generic entity processing for snapshot {self.snapshot.id}..."
        )
        self._run_generic_entity_processing()
        logger.info(
            f"[{self.__class__.__name__}] Generic entity processing complete. Found {len(self._all_generically_processed_tags)} tags, {len(self._all_generically_processed_triggers)} triggers, {len(self._all_generically_processed_variables)} variables."
        )

        logger.info(
            f"[{self.__class__.__name__}] Applying platform-specific logic for {self.platform_name_key.upper()}..."
        )
        final_status, verification_report, consolidated_nodes = self._apply_platform_specific_logic(
            self._all_generically_processed_tags,
            self._all_generically_processed_triggers,
            self._all_generically_processed_variables,
        )

        # Optionally, add generic processing summaries to the final verification report
        if "generic_processing_summary" not in verification_report:
            verification_report["generic_processing_summary"] = self._generic_reports

        logger.info(f"[{self.__class__.__name__}] Platform-specific logic complete. Final status: {final_status.label}")
        return final_status, verification_report, consolidated_nodes

    @abstractmethod
    def build_logical_model(self, project_id: str) -> dict[str, Any]:
        pass
