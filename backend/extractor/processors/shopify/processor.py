import logging
from typing import Any

from extractor.enums import SnapshotStatus
from extractor.processors.base import BasePlatformOrchestrator

logger = logging.getLogger(__name__)


class ShopifyOrchestrator(BasePlatformOrchestrator):
    @property
    def platform_name_key(self) -> str:
        return "shopify"

    def _get_all_shopify_triggers(self, consolidated_triggers: list[dict[str, Any]]) -> list[str]:
        """
        We need to get all valid triggers for Shopify.
        This allows us to have a key, to extract only the needed entity_group_key for Shopify.
        """

        # extract all group entity keys
        def only_linkable_triggers(trigger):
            return trigger.get("is_linkable", False)

        def entity_group_key_reducer(trigger):
            return trigger.get("entity_group_key")

        linkable_triggers = map(
            entity_group_key_reducer,
            filter(only_linkable_triggers, consolidated_triggers),
        )
        return list(set(linkable_triggers))

    def _apply_platform_specific_logic(
        self,
        tags: list[dict[str, Any]],
        triggers: list[dict[str, Any]],
        variables: list[dict[str, Any]],
    ) -> tuple[SnapshotStatus, dict[str, Any], dict[str, list[dict[str, Any]]]]:
        validation_issues: list[dict[str, Any]] = []

        consolidated_tags = []
        consolidated_triggers = list(triggers)
        consolidated_variables = list(variables)

        valid_trigger_entities = self._get_all_shopify_triggers(consolidated_triggers)
        logger.info(f"[ShopifyOrchestrator] extracted {len(valid_trigger_entities)} valid Shopify triggers.")

        # for all the valid entities, we only fetch the linkable tags that match
        for entity_group_key in valid_trigger_entities:
            for tag in tags:
                if tag.get("is_linkable", False) and tag.get("entity_group_key") == entity_group_key:
                    consolidated_tags.append(tag)

        # 3. Determine final status and create report
        final_status_enum = SnapshotStatus.VERIFIED_OK
        if any(
            issue.get("severity") == "error" for issue in validation_issues
        ):  # Assume "error" is a possible severity
            final_status_enum = SnapshotStatus.VERIFICATION_FAILED
        elif validation_issues:
            final_status_enum = SnapshotStatus.VERIFIED_WITH_WARNINGS

        final_verification_report = {
            "platform": self.platform_name_key,
            "status_label": str(final_status_enum.label),
            "issues": validation_issues,
            "summary": {
                "total_tags_evaluated": len(consolidated_tags),
                "total_triggers_evaluated": len(consolidated_triggers),
                "total_variables_evaluated": len(consolidated_variables),
            },
        }
        final_consolidated_nodes = {
            "tags": consolidated_tags,
            "triggers": consolidated_triggers,
            "variables": consolidated_variables,
        }
        return final_status_enum, final_verification_report, final_consolidated_nodes

    def build_logical_model(self, project_id: str) -> dict[str, Any]:
        # validate project id

        # get the latest ProjectVersion for the project to get access
        # to the ProjectVersionData

        # get all the linked adNetwork information for the Project

        # get the snapshot information

        # The eventual goal is to first check the Project version data
        # for all

        # check the networks that we are required to build for
        return super().build_logical_model(project_id)
