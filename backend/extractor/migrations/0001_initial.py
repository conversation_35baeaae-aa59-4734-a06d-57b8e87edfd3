# Generated by Django 5.0.6 on 2025-05-24 14:49

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="GTMSnapshot",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "version_tag",
                    models.Char<PERSON>ield(
                        help_text="A descriptive tag for this snapshot (e.g., 'auto_YYYYMMDD_HHMMSS', 'v1.2')",
                        max_length=100,
                    ),
                ),
                (
                    "source_gtm_platform_account_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "source_gtm_ad_network_account_id",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                (
                    "platform_container_id",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                (
                    "platform_workspace_id",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                (
                    "platform_tags",
                    models.JSONField(blank=True, default=list, null=True),
                ),
                (
                    "platform_variables",
                    models.JSONField(blank=True, default=list, null=True),
                ),
                (
                    "platform_triggers",
                    models.JSONField(blank=True, default=list, null=True),
                ),
                (
                    "ad_network_container_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "ad_network_workspace_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "ad_network_tags",
                    models.JSONField(blank=True, default=list, null=True),
                ),
                (
                    "ad_network_variables",
                    models.JSONField(blank=True, default=list, null=True),
                ),
                (
                    "ad_network_triggers",
                    models.JSONField(blank=True, default=list, null=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("RAW_FETCHED", "Raw Data Fetched"),
                            ("LOGICAL_PROCESSING", "Logical Model Processing"),
                            ("LOGICAL_PROCESSED", "Logical Model Processed"),
                            (
                                "LOGICAL_PROCESSING_FAILED",
                                "Logical Model Processing Failed",
                            ),
                            ("VERIFICATION_PENDING", "Verification Pending"),
                            ("VERIFYING", "Verifying Template"),
                            ("VERIFIED_OK", "Template Verified - OK"),
                            ("VERIFIED_WITH_WARNINGS", "Template Verified - Warnings"),
                            ("VERIFICATION_FAILED", "Template Verification Failed"),
                        ],
                        db_index=True,
                        default="RAW_FETCHED",
                        max_length=30,
                    ),
                ),
                (
                    "platform_processing_report",
                    models.JSONField(
                        blank=True,
                        help_text="Report from parsing platform GTM data into logical nodes.",
                        null=True,
                    ),
                ),
                (
                    "ad_network_processing_report",
                    models.JSONField(
                        blank=True,
                        help_text="Report from parsing ad network GTM data into logical nodes.",
                        null=True,
                    ),
                ),
                (
                    "verification_report",
                    models.JSONField(
                        blank=True,
                        help_text="Report from the internal template verification run.",
                        null=True,
                    ),
                ),
                ("nodes", models.JSONField(blank=True, default=list, null=True)),
            ],
            options={
                "verbose_name": "GTM Snapshot",
                "verbose_name_plural": "GTM Snapshots",
                "ordering": ["-created_at"],
            },
        ),
    ]
