# Generated by Django 5.0.6 on 2025-05-24 14:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("extractor", "0001_initial"),
        ("projects", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="gtmsnapshot",
            name="extracted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="gtm_extractions_initiated",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="gtmsnapshot",
            name="project_platform",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="gtm_snapshots",
                to="projects.projectplatform",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="gtmsnapshot",
            unique_together={("project_platform", "version_tag")},
        ),
    ]
