import builtins
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Any, TypedDict

from extractor.shared.exceptions import GTMEntityError


@dataclass
class Parameter:
    type: str
    key: str
    value: Any | None = None
    list: list[dict[str, Any]] | None = field(default_factory=list)
    map: builtins.list[dict[str, Any]] | None = field(default_factory=builtins.list)

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "Parameter":
        return cls(
            type=data["type"],
            key=data["key"],
            value=data.get("value"),
            list=data.get("list", []),  # Ensure default is list if key exists but is None
            map=data.get("map", []),  # Ensure default is list if key exists but is None
        )


@dataclass
class ConsentSettings:
    consent_status: str
    consent_type: dict[str, Any] | None = None

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "ConsentSettings":
        return cls(consent_status=data["consentStatus"], consent_type=data.get("consentType"))


@dataclass
class MonitoringMetadata:
    type: str
    map: list[dict[str, Any]] | None = field(default_factory=list)

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "MonitoringMetadata":
        return cls(type=data["type"], map=data.get("map", []))


@dataclass
class SetupTag:
    tag_name: str

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "SetupTag":
        return cls(tag_name=data["tagName"])


@dataclass
class FilterCondition:  # Renamed from Filter to avoid conflict with built-in filter
    type: str
    parameter: list[Parameter] = field(default_factory=list)

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "FilterCondition":
        return cls(
            type=data["type"],
            parameter=[Parameter.from_dict(p) for p in data.get("parameter", [])],
        )


@dataclass
class AutoEventFilterCondition(FilterCondition):
    pass


@dataclass
class WaitForTags:
    type: str
    value: str

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "WaitForTags":
        return cls(type=data["type"], value=str(data["value"]))  # value can be bool


@dataclass
class CheckValidation:
    type: str
    value: str

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "CheckValidation":
        return cls(type=data["type"], value=str(data["value"]))  # value can be bool


@dataclass
class WaitForTagsTimeout:
    type: str
    value: str | None = None

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "WaitForTagsTimeout":
        return cls(type=data["type"], value=data.get("value"))


@dataclass
class UniqueTriggerId:
    type: str
    value: str | None = None

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "UniqueTriggerId":
        return cls(type=data["type"], value=data.get("value"))


@dataclass
class FormatValue:
    case_conversion_type: str | None = None

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "FormatValue":
        return cls(case_conversion_type=data.get("caseConversionType"))


class BaseGTMEntityABC(ABC):
    REQUIRED_FIELDS = [
        "accountId",
        "containerId",
        "name",
    ]

    def __init__(
        self,
        account_id: str,
        container_id: str,
        name: str,
        fingerprint: str,
        notes: str | None = None,
        raw_data: dict[str, Any] | None = None,
    ):
        self.account_id = account_id
        self.container_id = container_id
        self.name = name
        self.fingerprint = fingerprint
        self.notes = notes
        self._raw_data = raw_data  # Optional: for debugging or accessing unmapped fields

    @classmethod
    def _validate_required_fields(cls, data: dict[str, Any], entity_name: str | None = None):
        """Checks if all required fields are present in the data."""
        missing_fields = [field for field in cls.REQUIRED_FIELDS if field not in data]
        if missing_fields:
            entity_identifier = entity_name or data.get("name", "Unknown Entity")
            raise GTMEntityError(
                f"Missing required fields for {entity_identifier} ({cls.__name__}): {', '.join(missing_fields)}"
            )

    @classmethod
    @abstractmethod
    def from_json_data(cls, data: dict[str, Any]) -> "BaseGTMEntityABC":
        """Abstract class method to create an instance from JSON data."""
        pass

    @abstractmethod
    def get_summary(self) -> str:
        """Returns a string summary of the entity."""
        pass

    def update_notes(self, new_notes: str):
        """A common method to update notes."""
        self.notes = new_notes
        print(f"Notes updated for {self.name}")


class UrlInfo(TypedDict):
    id: str
    url: str
    contains: str


class Modifier(TypedDict):
    id: str
    path: str
    value: str
    condition: str  # e.g., "contains"


class DroppedPage(TypedDict):
    id: str
    urls: list[UrlInfo]
    title: str
    events: list[Any]
    sourceId: str


class DroppedEvent(TypedDict):
    id: str
    type: str
    title: str
    pageId: str
    sourceId: str
    modifiers: list[Modifier]


class ProjectVersionData(TypedDict):
    droppedPages: list[DroppedPage]
    droppedEvents: list[DroppedEvent]
    userAddedPages: list[DroppedPage]
    userAddedEvents: list[DroppedEvent]
