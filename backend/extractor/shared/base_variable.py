from abc import abstractmethod
from typing import Any

from extractor.shared.schemas import BaseGTMEntityABC, FormatValue, Parameter


class VariableABC(BaseGTMEntityABC):
    REQUIRED_FIELDS = BaseGTMEntityABC.REQUIRED_FIELDS + ["variableId", "type"]

    def __init__(
        self,
        account_id: str,
        container_id: str,
        name: str,
        fingerprint: str,
        variable_id: str,
        type: str,
        parameters: list[Parameter],
        format_value: FormatValue | None = None,
        notes: str | None = None,
        raw_data: dict[str, Any] | None = None,
    ):
        super().__init__(account_id, container_id, name, fingerprint, notes, raw_data)
        self.variable_id = variable_id
        self.type = type
        self.parameters = parameters
        self.format_value = format_value

    @classmethod
    def from_json_data(cls, data: dict[str, Any]) -> "VariableABC":
        cls._validate_required_fields(data, data.get("name"))

        params = [Parameter.from_dict(p) for p in data.get("parameter", [])]
        fv_data = data.get("formatValue")
        fv = FormatValue.from_dict(fv_data) if fv_data else None

        return cls(
            account_id=data["accountId"],
            container_id=data["containerId"],
            name=data["name"],
            fingerprint=data["fingerprint"],
            variable_id=data["variableId"],
            type=data["type"],
            parameters=params,
            format_value=fv,
            notes=data.get("notes"),
            raw_data=data,
        )

    def get_summary(self) -> str:
        return f"Variable: {self.name} (ID: {self.variable_id}, Type: {self.type})"

    @abstractmethod
    def get_referenced_value(self) -> Any | None:
        """
        Abstract method to get the value this variable typically resolves to
        (might involve looking up other variables, etc. - simplified here).
        """
        pass
