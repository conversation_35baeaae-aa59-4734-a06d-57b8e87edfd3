from abc import abstractmethod
from dataclasses import field
from typing import Any

from extractor.shared.schemas import (
    BaseGTMEntityABC,
    ConsentSettings,
    MonitoringMetadata,
    Parameter,
    SetupTag,
)


class TagABC(BaseGTMEntityABC):
    REQUIRED_FIELDS = BaseGTMEntityABC.REQUIRED_FIELDS + [
        "tagId",
        "type",
        "tagFiringOption",
    ]

    def __init__(
        self,
        account_id: str,
        container_id: str,
        name: str,
        fingerprint: str,
        tag_id: str,
        type: str,
        parameters: list[Parameter],
        firing_trigger_id: list[str],
        tag_firing_option: str,
        consent_settings: ConsentSettings | None = None,
        monitoring_metadata: MonitoringMetadata | None = None,
        setup_tag: list[SetupTag] | None = field(default_factory=list),
        notes: str | None = None,
        raw_data: dict[str, Any] | None = None,
    ):
        super().__init__(account_id, container_id, name, fingerprint, notes, raw_data)
        self.tag_id = tag_id
        self.type = type
        self.parameters = parameters
        self.firing_trigger_id = firing_trigger_id
        self.tag_firing_option = tag_firing_option
        self.consent_settings = consent_settings
        self.monitoring_metadata = monitoring_metadata
        self.setup_tag = setup_tag if setup_tag is not None else []

    @classmethod
    def from_json_data(cls, data: dict[str, Any]) -> "TagABC":
        cls._validate_required_fields(data, data.get("name"))  # Pass entity name for better error messages

        params = [Parameter.from_dict(p) for p in data.get("parameter", [])]
        consent_data = data.get("consentSettings")
        consent = ConsentSettings.from_dict(consent_data) if consent_data else None
        monitor_data = data.get("monitoringMetadata")
        monitor = MonitoringMetadata.from_dict(monitor_data) if monitor_data else None
        setup_tags_data = data.get("setupTag", [])
        setup_tags = [SetupTag.from_dict(st) for st in setup_tags_data]

        # For subclasses, you might check 'type' and instantiate a specific subclass
        # For now, we instantiate the base ABC or a generic Tag implementation
        return cls(
            account_id=data["accountId"],
            container_id=data["containerId"],
            name=data["name"],
            fingerprint=data["fingerprint"],
            tag_id=data["tagId"],
            type=data["type"],
            parameters=params,
            firing_trigger_id=data.get("firingTriggerId", []),
            tag_firing_option=data["tagFiringOption"],
            consent_settings=consent,
            monitoring_metadata=monitor,
            setup_tag=setup_tags,
            notes=data.get("notes"),
            raw_data=data,
        )

    def get_summary(self) -> str:
        return f"Tag: {self.name} (ID: {self.tag_id}, Type: {self.type})"

    @abstractmethod
    def get_parameter_value(self, key: str) -> Any | None:
        """Abstract method to get a specific parameter's value."""
        pass
