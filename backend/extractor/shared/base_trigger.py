from abc import abstractmethod
from typing import Any

from extractor.shared.schemas import (
    AutoEventFilterCondition,
    BaseGTMEntityABC,
    CheckValidation,
    FilterCondition,
    Parameter,
    UniqueTriggerId,
    WaitForTags,
    WaitForTagsTimeout,
)


class TriggerABC(BaseGTMEntityABC):
    REQUIRED_FIELDS = BaseGTMEntityABC.REQUIRED_FIELDS + ["triggerId", "type"]

    def __init__(
        self,
        account_id: str,
        container_id: str,
        name: str,
        fingerprint: str,
        trigger_id: str,
        type: str,
        custom_event_filter: list[FilterCondition] | None = None,
        filter_conditions: list[FilterCondition] | None = None,  # Renamed from 'filter'
        auto_event_filter: list[AutoEventFilterCondition] | None = None,
        wait_for_tags: WaitForTags | None = None,
        check_validation: CheckValidation | None = None,
        wait_for_tags_timeout: WaitForTagsTimeout | None = None,
        unique_trigger_id: UniqueTriggerId | None = None,
        trigger_group_parameters: (list[Parameter] | None) = None,  # For TRIGGER_GROUP type
        notes: str | None = None,
        raw_data: dict[str, Any] | None = None,
    ):
        super().__init__(account_id, container_id, name, fingerprint, notes, raw_data)
        self.trigger_id = trigger_id
        self.type = type
        self.custom_event_filter = custom_event_filter if custom_event_filter is not None else []
        self.filter_conditions = filter_conditions if filter_conditions is not None else []
        self.auto_event_filter = auto_event_filter if auto_event_filter is not None else []
        self.wait_for_tags = wait_for_tags
        self.check_validation = check_validation
        self.wait_for_tags_timeout = wait_for_tags_timeout
        self.unique_trigger_id = unique_trigger_id
        self.trigger_group_parameters = trigger_group_parameters if trigger_group_parameters is not None else []

    @classmethod
    def from_json_data(cls, data: dict[str, Any]) -> "TriggerABC":
        cls._validate_required_fields(data, data.get("name"))

        cef_data = data.get("customEventFilter", [])
        ce_filters = [FilterCondition.from_dict(f) for f in cef_data]

        f_data_list = data.get("filter", [])  # JSON uses 'filter'
        filters = [FilterCondition.from_dict(f) for f in f_data_list]

        aef_data = data.get("autoEventFilter", [])
        ae_filters = [AutoEventFilterCondition.from_dict(f) for f in aef_data]

        wft_data = data.get("waitForTags")
        wft = WaitForTags.from_dict(wft_data) if wft_data else None

        cv_data = data.get("checkValidation")
        cv = CheckValidation.from_dict(cv_data) if cv_data else None

        wftt_data = data.get("waitForTagsTimeout")
        wftt = WaitForTagsTimeout.from_dict(wftt_data) if wftt_data else None

        uti_data = data.get("uniqueTriggerId")
        uti = UniqueTriggerId.from_dict(uti_data) if uti_data else None

        tgp_data = data.get("parameter", [])  # For TRIGGER_GROUP
        tg_params = [Parameter.from_dict(p) for p in tgp_data]

        return cls(
            account_id=data["accountId"],
            container_id=data["containerId"],
            name=data["name"],
            fingerprint=data["fingerprint"],
            trigger_id=data["triggerId"],
            type=data["type"],
            custom_event_filter=ce_filters,
            filter_conditions=filters,  # attribute name is filter_conditions
            auto_event_filter=ae_filters,
            wait_for_tags=wft,
            check_validation=cv,
            wait_for_tags_timeout=wftt,
            unique_trigger_id=uti,
            trigger_group_parameters=tg_params,
            notes=data.get("notes"),
            raw_data=data,
        )

    def get_summary(self) -> str:
        return f"Trigger: {self.name} (ID: {self.trigger_id}, Type: {self.type})"

    @abstractmethod
    def list_conditions(self) -> list[str]:
        """Abstract method to list all conditions in a human-readable format."""
        pass
