import abc
import json
import logging

import requests
from django.conf import settings

logger = logging.getLogger(__name__)


class GTMWorkspaceClient(abc.ABC):
    def __init__(self, access_token: str, refresh_token: str | None = None):
        self.base_url = settings.GTM_WRAPPER_BASE_URL.rstrip("/")
        self.api_endpoint_path = "/api/v1/process-gtm-request/"
        self.full_api_url = f"{self.base_url}{self.api_endpoint_path}"

        self.access_token = access_token
        self.refresh_token = refresh_token
        # Using ADMIN_API_KEY as per your provided client code.
        # If it should be GTM_WRAPPER_ADMIN_KEY, adjust settings access here.
        self.admin_api_key = settings.ADMIN_API_KEY

        self.headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Admin-Api-Key": self.admin_api_key,
            "Content-Type": "application/json",
        }

        if self.refresh_token:
            self.headers["Refresh-Token"] = self.refresh_token

    def _make_request(self, resource: str, action: str, item_payload_list: list) -> dict:
        request_body = {
            "resource": resource,
            "action": action,
            "payload": item_payload_list,
        }
        logger.debug(f"Requesting URL: {self.full_api_url}")
        logger.debug(f"Request Body: {json.dumps(request_body, indent=2)}")
        logger.debug(
            f"Request Headers: Authorization: Bearer ****, Admin-Api-Key: ****, Refresh-Token: {'****' if self.refresh_token else 'N/A'}"
        )

        try:
            response = requests.post(
                self.full_api_url,
                headers=self.headers,
                json=request_body,
                timeout=(settings.GTM_API_TIMEOUT_SECONDS if hasattr(settings, "GTM_API_TIMEOUT_SECONDS") else 60),
            )
            logger.debug(f"Response Status: {response.status_code}")
            logger.debug(f"Response Headers: {response.headers}")

            if response.status_code >= 400:
                logger.error(f"HTTP error {response.status_code} from GTM Wrapper: {response.text[:500]}")
                response.raise_for_status()

            if response.status_code == 202:
                task_info = response.json()
                logger.info(
                    f"GTM Wrapper returned 202 Accepted. Task ID: {task_info.get('task_id')}. Further polling needed."
                )
                if "task_id" in task_info:
                    raise NotImplementedError(
                        "Asynchronous task polling for list operations is not yet implemented in this client."
                    )

            if "application/json" in response.headers.get("Content-Type", "").lower():
                response_json = response.json()
                logger.debug(f"Response JSON: {json.dumps(response_json, indent=2, ensure_ascii=False)[:1000]}...")
                return response_json
            else:
                logger.warning(
                    f"Non-JSON response received from GTM Wrapper. Status: {response.status_code}, Content-Type: {response.headers.get('Content-Type')}, Body: {response.text[:200]}..."
                )
                return {
                    "error": "Non-JSON response",
                    "status_code": response.status_code,
                    "body": response.text,
                }

        except requests.exceptions.HTTPError as http_err:
            logger.error(
                f"HTTP error occurred calling GTM Wrapper: {http_err} - Response: {http_err.response.text if http_err.response else 'No response body'}",
                exc_info=True,
            )
            raise
        except requests.exceptions.RequestException as req_err:
            logger.error(
                f"Request exception occurred calling GTM Wrapper: {req_err}",
                exc_info=True,
            )
            raise
        except json.JSONDecodeError as json_err:
            logger.error(  # noqa: F821
                f"Failed to decode JSON response from GTM Wrapper: {json_err} - Raw Response: {response.text[:500]}",  # type: ignore
                exc_info=True,
            )
            raise

    @abc.abstractmethod
    def _parse_response(self, response_data: dict, entity_type: str) -> list:
        pass

    def fetch_entities(
        self,
        resource_name: str,
        action_name: str,
        entity_type_for_parser: str,
        account_id: str,
        container_id: str,
        workspace_id: str,
    ) -> list:
        item_payload = {
            "accountId": account_id,
            "containerId": container_id,
            "workspaceId": workspace_id,
        }
        # ensure cache is cleared, to avoid getting stale data.
        self._make_request(
            resource="cache",
            action="clear_cache",
            item_payload_list=[{"gtm_resource": resource_name}],
        )
        raw_data = self._make_request(resource=resource_name, action=action_name, item_payload_list=[item_payload])
        return self._parse_response(raw_data, entity_type_for_parser)

    def fetch_tags(self, account_id: str, container_id: str, workspace_id: str) -> list:
        return self.fetch_entities("tags", "list_tags", "tag", account_id, container_id, workspace_id)

    def fetch_variables(self, account_id: str, container_id: str, workspace_id: str) -> list:
        return self.fetch_entities(
            "variables",
            "list_variables",
            "variable",
            account_id,
            container_id,
            workspace_id,
        )

    def fetch_triggers(self, account_id: str, container_id: str, workspace_id: str) -> list:
        return self.fetch_entities(
            "triggers",
            "list_triggers",
            "trigger",
            account_id,
            container_id,
            workspace_id,
        )
