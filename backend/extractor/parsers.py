import json


def parse_gtm_notes_to_logical_model(gtm_entity_data: dict):
    notes_str = gtm_entity_data.get("notes", "")
    if not notes_str:
        return None
    try:
        notes_json = json.loads(notes_str)
        # Validate expected keys for your logical model structure
        if all(k in notes_json for k in ["node_name", "node_type"]):  # Add other required keys
            return {
                "node_identifier": notes_json.get("node_name"),
                "node_category": notes_json.get("node_type"),
                "template_type": notes_json.get("template_type"),
                "template_name": notes_json.get("template_name"),
                "is_linkable": notes_json.get("is_linkable", False),
                # You might want to store other GTM fields too
                "gtm_id": gtm_entity_data.get("tagId")
                or gtm_entity_data.get("variableId")
                or gtm_entity_data.get("triggerId"),
                "gtm_name": gtm_entity_data.get("name"),
                "gtm_type": gtm_entity_data.get("type"),
                # "raw_gtm_entity_data": gtm_entity_data # Store the whole original entity
            }
        return None
    except json.JSONDecodeError:
        return None  # Notes are not valid JSON
