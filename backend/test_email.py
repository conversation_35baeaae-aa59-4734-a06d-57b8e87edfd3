#!/usr/bin/env python
"""
Test script to verify email service configuration.
"""

import pytest
from django.conf import settings
from email_service.base import create_simple_email
from email_service.service import email_service


@pytest.mark.django_db
def test_email_service():
    """Test the email service configuration"""
    print("🧪 Testing Email Service Configuration...")

    try:
        # Test simple email
        test_email = "<EMAIL>"
        subject = "Test Email from GTM Platform"
        html_content = """
        <h1>Email Service Test</h1>
        <p>This is a test email to verify that your email service is working correctly.</p>
        <p>If you receive this email, your configuration is working! 🎉</p>
        """
        text_content = "Email Service Test - If you receive this email, your configuration is working!"

        print(f"📧 Sending test email to: {test_email}")

        # Create and send email
        message = create_simple_email(
            to_email=test_email,
            subject=subject,
            html_content=html_content,
            text_content=text_content,
        )

        result = email_service.send_email(message)

        print("✅ Email sent successfully!")
        print(f"📋 Result: {result}")

        # Assert that the result indicates success
        assert result is not None, "Email service should return a result"

    except Exception as e:
        print(f"❌ Email service test failed: {str(e)}")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure RESEND_API_KEY is set in your .env file")
        print("2. Verify FROM_EMAIL is a valid email address")
        print("3. Check that your Resend account is active")
        print("4. For production, ensure your domain is verified with Resend")
        # Re-raise the exception so pytest can catch it
        pytest.fail(f"Email service test failed: {str(e)}")


def test_email_configuration():
    """Test email service configuration"""
    print("🔍 Checking Email Service Configuration...")

    email_config = getattr(settings, "EMAIL_SERVICE", {})

    assert email_config, "EMAIL_SERVICE should be configured in settings"

    provider = email_config.get("PROVIDER")
    config = email_config.get("CONFIG", {})

    print(f"📦 Provider: {provider}")
    print(f"📧 From Email: {config.get('from_email', 'Not set')}")
    print(f"👤 From Name: {config.get('from_name', 'Not set')}")
    print(f"🔑 API Key: {'Set' if config.get('api_key') else 'Not set'}")

    assert provider, "Email provider should be configured"
    assert config.get("from_email"), "From email should be configured"
    assert config.get("from_name"), "From name should be configured"

    if not config.get("api_key"):
        print("⚠️  Warning: API key is not set. Email service will use mock mode.")
    else:
        print("✅ Configuration looks good!")


# Tests are run by pytest, no main execution needed
