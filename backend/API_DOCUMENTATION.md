# API Documentation Setup

This document describes the API documentation setup for the Sparkle GTM platform using drf-spectacular and Swagger UI.

## Overview

The API documentation is automatically generated from your Django REST Framework views and serializers using **drf-spectacular**, which creates OpenAPI 3.0 compliant documentation.

## Access URLs

- **Swagger UI**: `http://localhost:8000/docs/` (Development) / `https://your-domain.com/docs/` (Production)
- **ReDoc**: `http://localhost:8000/docs/redoc/` (Alternative documentation UI)
- **OpenAPI Schema**: `http://localhost:8000/docs/schema/` (Raw OpenAPI YAML)

## Authentication & Security

### Development Environment
- **No authentication required** - Documentation is freely accessible
- All endpoints are visible and testable

### Production Environment
- **Password protection enabled** - Simple password prompt
- Set the `DOCS_PASSWORD` environment variable to enable protection
- Users enter password once per session

## Configuration

### Environment Variables

```bash
# Required for production password protection
DOCS_PASSWORD=your_secure_password_here
```

### Settings Configuration

The documentation is configured in `backend/gtm/settings/base.py`:

```python
# drf-spectacular settings
SPECTACULAR_SETTINGS = {
    "TITLE": "Sparkle GTM API",
    "DESCRIPTION": "API documentation for Sparkle GTM platform",
    "VERSION": "1.0.0",
    # ... other settings
}
```

## Protection Methods

### Current: Simple Password Protection

**Pros:**
- No user accounts required
- Simple setup and maintenance
- Single password for all authorized users
- Session-based (password entered once)

**Cons:**
- Shared password (less secure)
- No user-specific access control

**Usage:**
1. Set `DOCS_PASSWORD` environment variable
2. Users visit `/docs/`
3. Enter password when prompted
4. Access granted for session duration

### Alternative: User Authentication Protection

**Pros:**
- Individual user accounts
- Granular permission control
- Audit trail of access
- Integration with existing Django auth

**Cons:**
- Requires user account management
- More complex setup
- Users need Django accounts

**To Enable:**
1. Comment out simple password protection URLs
2. Uncomment user authentication URLs in `backend/gtm/urls.py`
3. Users must be logged in and have staff/superuser permissions

## API Features

### Automatic Documentation Generation
- **Endpoints**: All DRF views automatically documented
- **Schemas**: Request/response schemas from serializers
- **Authentication**: Security schemes documented
- **Tags**: Endpoints grouped by app/functionality

### Interactive Testing
- **Try it out**: Test endpoints directly from the UI
- **Authentication**: Support for API keys and session auth
- **Request/Response**: See real request/response examples

### Multiple Formats
- **Swagger UI**: Interactive, modern interface
- **ReDoc**: Clean, three-panel documentation
- **OpenAPI YAML**: Machine-readable schema

## Customization

### Adding Descriptions to Views

```python
from drf_spectacular.utils import extend_schema

class MyAPIView(APIView):
    @extend_schema(
        description="Detailed description of what this endpoint does",
        tags=["Custom Tag"],
        responses={200: MySerializer}
    )
    def get(self, request):
        # Your view logic
        pass
```

### Adding Examples

```python
from drf_spectacular.utils import extend_schema, OpenApiExample

@extend_schema(
    examples=[
        OpenApiExample(
            'Example 1',
            value={'field': 'value'},
            request_only=True,
        ),
    ]
)
def my_view(request):
    pass
```

### Custom Tags and Grouping

Tags are automatically assigned based on the app name, but you can customize them:

```python
@extend_schema(tags=['Custom Group'])
def my_view(request):
    pass
```

## Maintenance

### Updating Documentation
- Documentation updates automatically when you:
  - Add new DRF views
  - Modify serializers
  - Change view descriptions
  - Update API responses

### Schema Validation
- Run `python manage.py spectacular --validate` to check for issues
- Generate static schema: `python manage.py spectacular --file schema.yml`

## Security Best Practices

### Production Deployment
1. **Always set DOCS_PASSWORD** for production environments
2. **Use HTTPS** to protect password transmission
3. **Regular password rotation** for shared passwords
4. **Monitor access logs** for unauthorized attempts

### Password Requirements
- Minimum 12 characters
- Mix of letters, numbers, symbols
- Avoid common passwords
- Store securely (environment variables, not code)

## Troubleshooting

### Common Issues

**Template Not Found Error:**
- Ensure `drf_spectacular` is in `INSTALLED_APPS`
- Check that templates are not overridden

**Schema Generation Errors:**
- Run `python manage.py spectacular --validate`
- Check serializer definitions
- Ensure proper DRF view inheritance

**Authentication Issues:**
- Verify `DOCS_PASSWORD` environment variable
- Check session configuration
- Clear browser cookies if needed

### Debug Mode
In development, set `DEBUG = True` to see detailed error messages.

## Integration with SvelteKit

The documentation is accessible through your SvelteKit app via the `/spark/docs/` proxy route, which forwards to the Django backend.

## Future Enhancements

Potential improvements:
- **API versioning** support
- **Rate limiting** documentation
- **Webhook** documentation
- **SDK generation** from OpenAPI schema
- **Custom themes** for Swagger UI
