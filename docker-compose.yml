version: '3.8'

services:
  db:
    image: postgres
    # persist data beyond lifetime of container
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=gtm
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    healthcheck:
      test: pg_isready -d $${POSTGRES_DB} -U $${POSTGRES_USER}
      interval: 2s
      retries: 10
  redis:
    image: redis
    # persistent storage
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: bash -c 'exec 6<>/dev/tcp/redis/6379'
      interval: 2s
      retries: 10
  web:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/code
    ports:
      - '8000:8000'
    env_file:
      - ./.env
    environment:
      - DJANGO_SETTINGS_MODULE=gtm.settings.production
    command: bash -c ' python manage.py migrate && python manage.py createcachetable && python manage.py update_index && uv run gunicorn --reload -c python:gunicorn_config project.wsgi:application'
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
  celery:
    build:
      context: .
      dockerfile: Dockerfile.dev
    command: celery -A gtm worker -l INFO --beat --concurrency 2
    volumes:
      - .:/code
    env_file:
      - ./.env
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy

volumes:
  postgres_data:
  redis_data:
