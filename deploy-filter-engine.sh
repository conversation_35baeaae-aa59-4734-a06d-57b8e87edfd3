#!/bin/bash

# Filter Engine Microservice Deployment Script
# Deploys the filter engine service to Google Cloud Run

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
PROJECT_ID=${PROJECT_ID:-"gtm-mixer-v3"}
REGION=${REGION:-"us-central1"}
SERVICE_NAME=${SERVICE_NAME:-"filter-engine-service"}
IMAGE_NAME=${IMAGE_NAME:-"filter-engine"}

print_status "🚀 Deploying Filter Engine Microservice to Cloud Run"
print_status "Project: $PROJECT_ID"
print_status "Region: $REGION"
print_status "Service: $SERVICE_NAME"

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    print_error "gcloud CLI is not installed. Please install it first."
    exit 1
fi

# Check if logged in to gcloud
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    print_error "Not logged in to gcloud. Please run 'gcloud auth login' first."
    exit 1
fi

# Set the project
print_status "Setting project to $PROJECT_ID..."
gcloud config set project $PROJECT_ID

# Enable required APIs
print_status "Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Navigate to filter engine service directory
if [ ! -d "filter-engine-service" ]; then
    print_error "filter-engine-service directory not found. Please run this script from the project root."
    exit 1
fi

cd filter-engine-service

# Check if Dockerfile exists
if [ ! -f "Dockerfile" ]; then
    print_error "Dockerfile not found in filter-engine-service directory."
    exit 1
fi

# Build and push the image
COMMIT_SHA=$(git rev-parse HEAD 2>/dev/null || echo "latest")
IMAGE_URL="gcr.io/$PROJECT_ID/$IMAGE_NAME:$COMMIT_SHA"

print_status "Building Docker image: $IMAGE_URL"
docker build --no-cache -t $IMAGE_URL .

print_status "Pushing image to Container Registry..."
docker push $IMAGE_URL

# Deploy to Cloud Run
print_status "Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
    --platform=managed \
    --region=$REGION \
    --image=$IMAGE_URL \
    --cpu=1 \
    --memory=512Mi \
    --min-instances=0 \
    --max-instances=10 \
    --timeout=300 \
    --concurrency=80 \
    --allow-unauthenticated \
    --set-env-vars="NODE_ENV=production" \
    --set-env-vars="NODE_OPTIONS=--max-old-space-size=256"

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")

print_success "✅ Filter Engine service deployed successfully!"
print_status "🌐 Service URL: $SERVICE_URL"

# Test the health endpoint
print_status "Testing service health..."
sleep 5

if curl -s "$SERVICE_URL/health" > /dev/null; then
    print_success "✅ Health check passed"

    # Show service info
    echo ""
    print_status "📋 Service Information:"
    curl -s "$SERVICE_URL/health" | jq . 2>/dev/null || curl -s "$SERVICE_URL/health"

else
    print_warning "⚠️ Health check failed. Service might still be starting up."
fi

echo ""
print_status "📝 Next Steps:"
echo "  1. Update your Django backend's FILTER_ENGINE_SERVICE_URL to: $SERVICE_URL"
echo "  2. Redeploy your Django backend with the new environment variable"
echo "  3. Test the integration from your frontend"

echo ""
print_status "🔧 To update Django backend environment:"
echo "  gcloud run services update your-backend-service \\"
echo "    --region=$REGION \\"
echo "    --set-env-vars=\"FILTER_ENGINE_SERVICE_URL=$SERVICE_URL\""

echo ""
print_success "🎉 Deployment complete!"
