#!/bin/bash

# Filter Engine Microservice Setup Script
# This script sets up and starts the Filter Engine microservice

set -e

echo "🚀 Setting up Filter Engine Microservice..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ is required. Current version: $(node --version)"
    exit 1
fi

print_success "Node.js $(node --version) detected"

# Navigate to filter engine service directory
cd filter-engine-service

# Check if package.json exists
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Make sure you're in the correct directory."
    exit 1
fi

# Install dependencies
print_status "Installing dependencies..."
if command -v npm &> /dev/null; then
    npm install
else
    print_error "npm not found. Please install npm."
    exit 1
fi

print_success "Dependencies installed"

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    print_status "Creating .env file from template..."
    cp .env.example .env
    print_warning "Please review and update .env file with your configuration"
fi

# Build the service
print_status "Building the service..."
npm run build

print_success "Service built successfully"

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        return 1
    else
        return 0
    fi
}

# Check if port 3001 is available
if ! check_port 3001; then
    print_warning "Port 3001 is already in use. The service might already be running."
    print_status "Checking if it's our service..."

    # Try to hit the health endpoint
    if curl -s http://localhost:3001/health > /dev/null; then
        print_success "Filter Engine service is already running on port 3001"
        print_status "Health check: $(curl -s http://localhost:3001/health | jq -r '.status' 2>/dev/null || echo 'OK')"
        exit 0
    else
        print_error "Port 3001 is occupied by another service. Please stop it or change the port."
        exit 1
    fi
fi

# Start the service
print_status "Starting Filter Engine service..."

# Check if we should run in development or production mode
if [ "${NODE_ENV:-development}" = "production" ]; then
    print_status "Starting in production mode..."
    npm start &
else
    print_status "Starting in development mode..."
    npm run dev &
fi

SERVICE_PID=$!

# Wait a moment for the service to start
sleep 3

# Check if service is running
if kill -0 $SERVICE_PID 2>/dev/null; then
    print_success "Filter Engine service started successfully (PID: $SERVICE_PID)"

    # Test the health endpoint
    print_status "Testing service health..."
    sleep 2

    if curl -s http://localhost:3001/health > /dev/null; then
        print_success "Service is healthy and responding"
        print_status "🌐 Service URL: http://localhost:3001"
        print_status "📊 Health check: http://localhost:3001/health"
        print_status "📚 API base: http://localhost:3001/api/v1/filter"

        # Show service info
        echo ""
        echo "📋 Service Information:"
        curl -s http://localhost:3001/health | jq . 2>/dev/null || curl -s http://localhost:3001/health

    else
        print_warning "Service started but health check failed. It might still be initializing..."
    fi

    echo ""
    print_status "To stop the service, run: kill $SERVICE_PID"
    print_status "Or use: pkill -f 'filter-engine'"

else
    print_error "Failed to start the service"
    exit 1
fi

echo ""
print_success "✅ Filter Engine Microservice setup complete!"
echo ""
print_status "Next steps:"
echo "  1. Update Django settings to point to the service"
echo "  2. Test the integration with your frontend"
echo "  3. Check the logs if you encounter any issues"
echo ""
print_status "For logs, run: npm run dev (in filter-engine-service directory)"
