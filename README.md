---
title: Readme
slug: rADO-read
createdAt: 2024-05-26T21:48:06.095Z
updatedAt: 2024-05-26T21:49:40.977Z
---

# Welcome to Sparkle.

Sparkle is our internal codename for this SaaS platform that revolutionizes how entrepreneurs with websites, funnels (leads or sales), or online shops set up tracking using Google Tag Manager (GTM). See [Overview](docId:fEBRrrKVDQj_eEc9r5GmW)

## Code Overview.

This project is currently being developed with [Django](https://www.djangoproject.com/) handling business logic and [Svelte](https://svelte.dev/) being used for the presentation and the UI. For the database, we use [Postgres](https://www.postgresql.org/) and a replica, with [Redis](https://redis.io/) as our fast memory cache.

We have currently taken the decision to avoid having Svelte running as a Single Page App (SPA) with API calls to a DjangoRestFramework, rather we have Django views serving compiled Svelte components.&#x20;

This has led to an interesting [Architecture.](docId:hPLLryqv8riW-aaN087MH)&#x20;

## Local Development.

Locally, we use the `docker-compose-dev.yml` to run the database and Redis, running the client and the backend in other terminals.&#x20;

1. Clone the project from the repository
2. Open 3 terminals.&#x20;
3. In the first terminal, `docker compose -f docker-compose-dev.yml up --build` to run the database and redis
4. In the second terminal, navigate to the backend folder and create your `.env` file from the `env.example` file then run the following commands.
5. `poetry install` This installs the dependencies for the backend&#x20;
6. `poetry run python manage.py migrate` This runs the migrations required to set up the database tables and other necessary setup processes.&#x20;
7. `poetry run python manage.py runserver` This runs the backend server in development mode.&#x20;
8. For the client, we navigate to the client folder using the third terminal, the `/backend/static/client` is the location of the client.
9. This project uses [pnpm ](https://pnpm.io/)for dependency management in the client, this is installed by running `npm install -g pnpm`
10. To get started run `pnpm install && pnpm watch` This installs the dependencies and rebuilds the client whenever the code changes in the client.&#x20;
