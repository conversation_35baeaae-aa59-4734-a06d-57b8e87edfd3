version: '3.8'

networks:
  default:
    external: true
    name: coolify

services:
  # Django Backend Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      # Persistent storage for media files (1GB)
      - media_files:/app/media
    environment:
      # Core Django Settings
      - SECRET_KEY=${SECRET_KEY}
      - ENC<PERSON><PERSON>TION_KEY=${ENCR<PERSON>TION_KEY}
      - DJANGO_SETTINGS_MODULE=${DJANGO_SETTINGS_MODULE}
      - DEBUG=${DEBUG}
      - ENABLE_DEBUG_TOOLBAR=${ENABLE_DEBUG_TOOLBAR}

      # Database Configuration (External)
      - DJANGO_DATABASE_NAME=${DJANGO_DATABASE_NAME}
      - DJANGO_DATABASE_USER=${DJANGO_DATABASE_USER}
      - DJANGO_DATABASE_PASSWORD=${DJANGO_DATABASE_PASSWORD}
      - DJANGO_DATABASE_HOST=${DJANGO_DATABASE_HOST}
      - DJANGO_DATABASE_PORT=${DJANGO_DATABASE_PORT}

      # Cache Configuration (External)
      - CACHE_URL=${CACHE_URL}

      # Google OAuth Configuration
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}

      # URLs and API Configuration
      - SITE_URL=${SITE_URL}
      - CLIENT_URL=${CLIENT_URL}
      - GTM_WRAPPER_BASE_URL=${GTM_WRAPPER_BASE_URL}
      - ADMIN_API_KEY=${ADMIN_API_KEY}

      # Google Cloud Configuration
      - PROJECT_ID=${PROJECT_ID}
      - GOOGLE_APPLICATION_CREDENTIALS=${GOOGLE_APPLICATION_CREDENTIALS}

      # Analytics and Monitoring
      - GOOGLE_ANALYTICS_ID=${GOOGLE_ANALYTICS_ID}
      - SENTRY_DSN=${SENTRY_DSN}

      # Stape Configuration
      - STAPE_AUTH_TOKEN_EU=${STAPE_AUTH_TOKEN_EU}
      - STAPE_AUTH_TOKEN_EU_PARTNER=${STAPE_AUTH_TOKEN_EU_PARTNER}
      - STAPE_AUTH_TOKEN_GLOBAL=${STAPE_AUTH_TOKEN_GLOBAL}
      - STAPE_AUTH_TOKEN_GLOBAL_PARTNER=${STAPE_AUTH_TOKEN_GLOBAL_PARTNER}
      - STAPE_EMAIL_GLOBAL=${STAPE_EMAIL_GLOBAL}
      - STAPE_EMAIL_EU=${STAPE_EMAIL_EU}

      # Email Service Configuration
      - EMAIL_PROVIDER=${EMAIL_PROVIDER}
      - RESEND_API_KEY=${RESEND_API_KEY}
      - FROM_EMAIL=${FROM_EMAIL}
      - FROM_NAME=${FROM_NAME}
      - FRONTEND_URL=${FRONTEND_URL}

      # Runtime Configuration
      - HOST=${HOST}
      - PORT=${PORT}
      - WORKERS=${WORKERS}
      - WORKER_CONNECTIONS=${WORKER_CONNECTIONS}
      - REDIS_MAX_CONNS=${REDIS_MAX_CONNS}
      - PYTHONUNBUFFERED=${PYTHONUNBUFFERED}
    command: >
      bash -c "
        python manage.py migrate &&
        python manage.py createcachetable &&
        python manage.py collectstatic --noinput &&
        gunicorn -c python:gunicorn_config gtm.wsgi:application
      "
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:${PORT:-8000}/ping/']

      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: unless-stopped

volumes:
  # Persistent storage for media files (1GB)
  media_files:
    driver: local
