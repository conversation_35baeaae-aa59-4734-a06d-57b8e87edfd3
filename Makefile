.PHONY: runserver runclient runagent runall runall-bg stopall stopall-bg runall-tmux stopall-tmux status status-simple logs restart health docker docker-down docker-build format lint lint-fix test install publish major minor help

runserver:
	make docker && cd backend && uv run python manage.py runserver

runclient:
	cd gtm_client && pnpm install && pnpm dev

runagent:
	cd ai-agent-service && uv run python main.py

# Run all services together (backend, frontend, and AI agent) - Enhanced version
runall:
	@./dev-server.sh start

# Alternative: Run all services in background (simple version)
runall-bg:
	@echo "🚀 Starting all services..."
	@echo "📦 Starting Docker services..."
	@make docker
	@echo "⏳ Waiting for Docker services to be ready..."
	@sleep 5
	@echo "🔧 Starting Django backend..."
	@cd backend && uv run python manage.py runserver &
	@echo "⏳ Waiting for backend to start..."
	@sleep 3
	@echo "🤖 Starting AI Agent service..."
	@cd ai-agent-service && uv run python main.py &
	@echo "⏳ Waiting for AI agent to start..."
	@sleep 3
	@echo "🌐 Starting frontend client..."
	@cd gtm_client && pnpm install && pnpm dev
	@echo "✅ All services started!"

# Stop all services (enhanced version)
stopall:
	@./dev-server.sh stop

# Alternative: Stop all background services (simple version)
stopall-bg:
	@echo "🛑 Stopping all services..."
	@pkill -f "python manage.py runserver" || true
	@pkill -f "python main.py" || true
	@pkill -f "pnpm dev" || true
	@make docker-down
	@echo "✅ All services stopped!"

# Alternative: Run all services in parallel using tmux (if available)
runall-tmux:
	@echo "🚀 Starting all services in tmux sessions..."
	@make docker
	@sleep 5
	@tmux new-session -d -s backend 'cd backend && uv run python manage.py runserver'
	@tmux new-session -d -s agent 'cd ai-agent-service && uv run python main.py'
	@tmux new-session -d -s frontend 'cd gtm_client && pnpm install && pnpm dev'
	@echo "✅ All services started in tmux sessions!"
	@echo "📋 Use 'tmux list-sessions' to see running sessions"
	@echo "📋 Use 'tmux attach -t <session-name>' to attach to a session"
	@echo "📋 Use 'make stopall-tmux' to stop all sessions"

# Stop all tmux sessions
stopall-tmux:
	@echo "🛑 Stopping all tmux sessions..."
	@tmux kill-session -t backend 2>/dev/null || true
	@tmux kill-session -t agent 2>/dev/null || true
	@tmux kill-session -t frontend 2>/dev/null || true
	@make docker-down
	@echo "✅ All tmux sessions stopped!"

# Check status of all services (enhanced version)
status:
	@./dev-server.sh status

# Show logs from all services
logs:
	@./dev-server.sh logs

# Restart all services
restart:
	@./dev-server.sh restart

# Alternative: Check status of all services (simple version)
status-simple:
	@echo "📊 Service Status Check:"
	@echo "🐳 Docker Services:"
	@docker compose -f docker-compose-dev.yml ps || echo "❌ Docker not running"
	@echo ""
	@echo "🔧 Backend (Django):"
	@curl -s http://localhost:8000/health > /dev/null && echo "✅ Backend running on :8000" || echo "❌ Backend not responding"
	@echo ""
	@echo "🤖 AI Agent Service:"
	@curl -s http://localhost:3002/health > /dev/null && echo "✅ AI Agent running on :3002" || echo "❌ AI Agent not responding"
	@echo ""
	@echo "🌐 Frontend:"
	@curl -s http://localhost:5173 > /dev/null && echo "✅ Frontend running on :5173" || echo "❌ Frontend not responding"

# Quick health check for all services
health:
	@echo "🏥 Health Check:"
	@curl -s http://localhost:8000/health | jq '.status' 2>/dev/null || echo "❌ Backend health check failed"
	@curl -s http://localhost:3002/health | jq '.status' 2>/dev/null || echo "❌ AI Agent health check failed"

docker:
	docker compose -f docker-compose-dev.yml up -d

docker-down:
	docker compose -f docker-compose-dev.yml down

docker-build:
	docker compose -f docker-compose-dev.yml build

format:
	cd backend && uv run ruff format .

lint:
	cd backend && uv run ruff check .

lint-fix:
	cd backend && uv run ruff check --fix .

test:
	cd backend && uv run pytest

install:
	cd backend && uv pip install -e .

publish:
	@./scripts/publish.py $(filter major minor,$(MAKECMDGOALS))

major:
	@:

minor:
	@:

# Show help information
help:
	@echo "🚀 Sparkle GTM Project - Available Commands:"
	@echo ""
	@echo "🏃 Running Services:"
	@echo "  make runserver    - Run Django backend only"
	@echo "  make runclient    - Run SvelteKit frontend only"
	@echo "  make runagent     - Run AI Agent service only"
	@echo "  make runall       - 🌟 Run all services (enhanced with process management)"
	@echo "  make runall-bg    - Run all services (simple background version)"
	@echo "  make runall-tmux  - Run all services in separate tmux sessions"
	@echo ""
	@echo "🛑 Stopping Services:"
	@echo "  make stopall      - 🌟 Stop all services (enhanced)"
	@echo "  make stopall-bg   - Stop all background services (simple)"
	@echo "  make stopall-tmux - Stop all tmux sessions"
	@echo "  make restart      - Restart all services"
	@echo ""
	@echo "🐳 Docker Management:"
	@echo "  make docker       - Start Docker services (PostgreSQL, Redis)"
	@echo "  make docker-down  - Stop Docker services"
	@echo "  make docker-build - Build Docker images"
	@echo ""
	@echo "🔍 Monitoring:"
	@echo "  make status       - 🌟 Check status of all services (enhanced)"
	@echo "  make logs         - Show recent logs from all services"
	@echo "  make health       - Quick health check for APIs"
	@echo "  make status-simple- Check status (simple version)"
	@echo ""
	@echo "🛠️ Development:"
	@echo "  make format       - Format backend code with ruff"
	@echo "  make lint         - Lint backend code"
	@echo "  make lint-fix     - Fix linting issues automatically"
	@echo "  make test         - Run backend tests"
	@echo "  make install      - Install backend dependencies"
	@echo "  make publish major|minor - Bump & release pydantic types package"
	@echo ""
	@echo "💡 Quick Start:"
	@echo "  1. make runall    - Start everything at once (enhanced)"
	@echo "  2. make status    - Check if services are running"
	@echo "  3. make logs      - View recent logs if issues"
	@echo "  4. make stopall   - Stop everything when done"
	@echo ""
	@echo "🚀 Alternative: Use the dev-server.sh script directly:"
	@echo "  ./dev-server.sh start   - Start all services"
	@echo "  ./dev-server.sh status  - Check service status"
	@echo "  ./dev-server.sh logs    - View logs"
	@echo "  ./dev-server.sh stop    - Stop all services"
