# Filter Engine Microservice Integration Guide

## 🎯 Overview

This guide explains how to integrate the Filter Engine as a standalone microservice with your GTM application.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GTM Client    │    │  Django Backend │    │ Filter Engine   │
│   (Frontend)    │    │   (Main API)    │    │  Microservice   │
│                 │    │                 │    │                 │
│  Components ────┼───▶│  Proxy Routes ──┼───▶│   Node.js API   │
│                 │    │                 │    │   Port: 3001    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
     Port: 5173             Port: 8001             Port: 3001
```

### Benefits of Microservice Approach:

- ✅ **Clean Separation**: Filter logic is completely independent
- ✅ **Technology Choice**: Use Node.js/TypeScript for optimal performance
- ✅ **Scalability**: Can scale filter validation independently
- ✅ **Maintainability**: No code duplication or complex integration
- ✅ **Reusability**: Other services can use the same filter engine

## 🚀 Quick Start

### 1. Start the Filter Engine Service

```bash
# Run the setup script
./setup-filter-engine.sh

# Or manually:
cd filter-engine-service
npm install
npm run dev
```

### 2. Verify Service is Running

```bash
# Health check
curl http://localhost:3001/health

# Test validation
curl -X POST http://localhost:3001/api/v1/filter/validate-configuration \
  -H "Content-Type: application/json" \
  -d '{"pages": [], "events": []}'
```

### 3. Start Django Backend

```bash
cd backend
python manage.py runserver 8001
```

### 4. Test Django Proxy

```bash
# Test through Django proxy
curl -X POST http://localhost:8001/api/v1/filter-engine/validate-configuration/ \
  -H "Content-Type: application/json" \
  -d '{"pages": [], "events": []}'
```

### 5. Start Frontend

```bash
cd gtm_client
npm run dev
```

## 📡 API Integration

### Frontend Usage

```typescript
import { useFilterEngineService } from '$lib/hooks/useFilterEngineService'

// In your Svelte component
const filterEngineService = useFilterEngineService()
const { hasIssues, errors, warnings, firingLogic } = filterEngineService

// Validate pages
await filterEngineService.validatePages(pages)

// Check results
if ($hasIssues) {
  console.log('Validation errors:', $errors)
  console.log('Validation warnings:', $warnings)
}

// Get firing logic
console.log('Firing logic:', $firingLogic)
```

### Direct API Usage

```typescript
import { filterEngineService } from '$lib/api/filterEngineService'

// Validate configuration
const response = await filterEngineService.validateConfiguration({
  pages: [
    /* your pages */
  ],
  events: [
    /* your events */
  ],
})

if (response.success) {
  console.log('Validation result:', response.result)
}
```

## 🔧 Configuration

### Environment Variables

Create `filter-engine-service/.env`:

```bash
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:5173
BACKEND_URL=http://localhost:8001
```

### Django Settings

Already configured in `backend/gtm/settings/base.py`:

```python
# Filter Engine Service Configuration
FILTER_ENGINE_SERVICE_URL = env.str("FILTER_ENGINE_SERVICE_URL", default="http://localhost:3001")
FILTER_ENGINE_TIMEOUT = env.int("FILTER_ENGINE_TIMEOUT", default=30)
```

## 🧪 Testing the Integration

### 1. Test Service Health

```bash
# Direct service
curl http://localhost:3001/health

# Through Django proxy
curl http://localhost:8001/api/v1/filter-engine/health/
```

### 2. Test Validation

```bash
# Test with sample data
curl -X POST http://localhost:8001/api/v1/filter-engine/validate-configuration/ \
  -H "Content-Type: application/json" \
  -d '{
    "pages": [
      {
        "id": "page1",
        "name": "Home Page",
        "rules": [
          {
            "id": "rule1",
            "type": "url",
            "condition": "Contains",
            "value": "/home",
            "isActive": true
          }
        ],
        "isActive": true
      }
    ],
    "events": []
  }'
```

Expected response:

```json
{
  "success": true,
  "result": {
    "is_valid": true,
    "issues": [],
    "rule_effects": [],
    "firing_logic": "1 page(s) with 1 rule(s)"
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 🔌 Frontend Integration

### Update PagesMain.svelte

```typescript
// Add to the top of the script section
import { useFilterEngineService } from '$lib/hooks/useFilterEngineService'

// Initialize the hook
const filterEngineService = useFilterEngineService()
const { hasIssues, errors, warnings, firingLogic } = filterEngineService

// Convert your pages to GTM format
const gtmPages = $derived.by(() => {
  return selectedPages.map((page) => ({
    id: page.instanceId,
    name: page.name,
    rules: page.rules.map((rule) => ({
      id: rule.id.toString(),
      type: rule.type as 'url' | 'datalayer',
      condition: rule.condition as 'Contains' | 'Equals' | 'StartsWith' | 'EndsWith',
      value: rule.value,
      isActive: true,
    })),
    isActive: true,
  }))
})

// Reactive validation
$effect(() => {
  if (gtmPages.length > 0) {
    filterEngineService.validatePages(gtmPages)
  } else {
    filterEngineService.clearValidation()
  }
})
```

### Add Validation UI

```svelte
<!-- Add to your template -->
{#if $hasIssues}
  <div class="space-y-2">
    {#each $errors as error}
      <div class="rounded-lg border border-red-200 bg-red-50 p-3 text-red-800">
        <p class="text-sm font-medium">Error</p>
        <p class="text-xs">{error.message}</p>
      </div>
    {/each}

    {#each $warnings as warning}
      <div class="rounded-lg border border-amber-200 bg-amber-50 p-3 text-amber-800">
        <p class="text-sm font-medium">Warning</p>
        <p class="text-xs">{warning.message}</p>
      </div>
    {/each}
  </div>
{/if}

{#if $firingLogic && $firingLogic !== 'No firing conditions defined'}
  <div class="rounded-lg border border-blue-200 bg-blue-50 p-3 text-blue-800">
    <p class="text-sm font-medium">Firing Logic</p>
    <p class="text-xs">{$firingLogic}</p>
  </div>
{/if}
```

## 🚢 Production Deployment

### Docker Deployment

```bash
cd filter-engine-service
docker build -t filter-engine-service .
docker run -p 3001:3001 filter-engine-service
```

### Docker Compose

```yaml
# Add to your docker-compose.yml
services:
  filter-engine:
    build: ./filter-engine-service
    ports:
      - '3001:3001'
    environment:
      - NODE_ENV=production
      - PORT=3001
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3001/health']
      interval: 30s
      timeout: 10s
      retries: 3
```

### Environment Variables for Production

```bash
# filter-engine-service/.env
NODE_ENV=production
PORT=3001
FRONTEND_URL=https://your-frontend-domain.com
BACKEND_URL=https://your-backend-domain.com

# backend/.env
FILTER_ENGINE_SERVICE_URL=http://filter-engine:3001
FILTER_ENGINE_TIMEOUT=30
```

## 🔍 Monitoring & Debugging

### Service Logs

```bash
# Development
cd filter-engine-service
npm run dev

# Production
docker logs filter-engine-container
```

### Health Monitoring

```bash
# Check service health
curl http://localhost:3001/health

# Check through Django proxy
curl http://localhost:8001/api/v1/filter-engine/health/
```

### Common Issues

1. **Service not starting**: Check port 3001 availability
2. **Connection refused**: Verify service is running and accessible
3. **CORS errors**: Check CORS configuration in service
4. **Timeout errors**: Increase `FILTER_ENGINE_TIMEOUT` setting

## 📈 Performance Considerations

- Service handles async requests efficiently
- Connection pooling for database-like operations
- Request timeout handling (30s default)
- Rate limiting (1000 requests per 15 minutes per IP)
- Memory-efficient processing

## 🔄 Development Workflow

1. Make changes to filter engine logic in `filter-engine-service/src/engine/`
2. Test locally: `npm run dev`
3. Run tests: `npm test`
4. Build: `npm run build`
5. Deploy to staging/production

## ✅ Verification Checklist

- [ ] Filter Engine service starts on port 3001
- [ ] Health check responds: `curl http://localhost:3001/health`
- [ ] Django proxy works: `curl http://localhost:8001/api/v1/filter-engine/health/`
- [ ] Frontend can validate pages/events
- [ ] Validation results display in UI
- [ ] Error handling works correctly
- [ ] Service restarts automatically in production

## 🆘 Troubleshooting

### Service Won't Start

```bash
# Check if port is in use
lsof -i :3001

# Check logs
cd filter-engine-service
npm run dev
```

### Django Proxy Issues

```bash
# Test Django directly
python manage.py shell
>>> from filter_engine_proxy.views import filter_engine_health_check
```

### Frontend Integration Issues

```bash
# Check browser console for errors
# Verify API calls in Network tab
# Test API endpoints directly with curl
```
