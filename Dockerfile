FROM python:3.12-bookworm

ENV PYTHONUNBUFFERED=1
ENV HOST=0.0.0.0
ENV PORT=8000
ENV WORKERS=1
ENV WORKER_CONNECTIONS=80
ENV REDIS_MAX_CONNS=80
ENV DJANGO_SETTINGS_MODULE=gtm.settings.production

EXPOSE $PORT


# Install gettext for translations
RUN apt-get update && apt-get -yqq install gettext

# Create code directory
RUN mkdir /code
WORKDIR /code

# Install uv
RUN pip install --no-cache-dir uv

# Copy only dependency files first
COPY ./backend/pyproject.toml ./

# Install dependencies
RUN uv pip install --system -r pyproject.toml

# Copy the rest of the application
COPY ./backend ./


WORKDIR /code

CMD ["gunicorn", "-c", "python:gunicorn_config", "gtm.wsgi:application"]
