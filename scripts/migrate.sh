#!/bin/bash

# Simple Migration Runner
# Quick script to run migrations using Cloud Run job

set -e

# Use Cloud Build substitution variables
REGION="${_REGION:-europe-west1}"
SERVICE_NAME="${_SERVICE_NAME:-gtm-mixer}"

echo "🚀 Running Django migrations..."

# Check if gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ Error: Please authenticate with gcloud first:"
    echo "   gcloud auth login"
    exit 1
fi

# Execute the migration job
gcloud run jobs execute $SERVICE_NAME-migrate-job \
    --region=$REGION \
    --wait

echo "✅ Migrations completed!"
