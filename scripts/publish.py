#!/usr/bin/env python3
"""Utility for bumping the pydantic_types package version and creating a release tag.

Usage:
    scripts/publish.py <major|minor>

The script assumes it is run from within the repository (typically via `make`).
It requires a clean git worktree before executing and will:

1. Bump the version in `backend/pydantic_types/pyproject.toml` and
   `backend/pydantic_types/__init__.py` using the requested bump strategy.
2. Commit the change with a conventional commit message.
3. Create a git tag (`pydantic-types-v<version>`).
4. Push both the commit and tag to the configured remote (defaults to `origin`).
"""

from __future__ import annotations

import re
import subprocess
import sys
from dataclasses import dataclass
from pathlib import Path

REPO_ROOT = Path(__file__).resolve().parents[1]
PYPROJECT_PATH = REPO_ROOT / "backend" / "pydantic_types" / "pyproject.toml"
INIT_PATH = REPO_ROOT / "backend" / "pydantic_types" / "__init__.py"
REMOTE_NAME = "origin"

VERSION_PATTERN = re.compile(
    r'^version\s*=\s*"(?P<version>\d+\.\d+\.\d+)"', re.MULTILINE
)
INIT_VERSION_PATTERN = re.compile(
    r'^__version__\s*=\s*"(?P<version>.+?)"', re.MULTILINE
)


@dataclass(frozen=True)
class Version:
    major: int
    minor: int
    patch: int

    @classmethod
    def parse(cls, raw: str) -> "Version":
        parts = raw.split(".")
        if len(parts) != 3 or not all(part.isdigit() for part in parts):
            raise ValueError(f"Unsupported version format: {raw}")
        return cls(*(int(part) for part in parts))

    def bump(self, bump_type: str) -> "Version":
        if bump_type == "major":
            return Version(self.major + 1, 0, 0)
        if bump_type == "minor":
            return Version(self.major, self.minor + 1, 0)
        raise ValueError(f"Unsupported bump type: {bump_type}")

    def __str__(self) -> str:
        return f"{self.major}.{self.minor}.{self.patch}"


def run(*args: str, check: bool = True) -> subprocess.CompletedProcess[str]:
    return subprocess.run(
        args, cwd=REPO_ROOT, text=True, check=check, capture_output=True
    )


def ensure_clean_worktree() -> None:
    result = run("git", "status", "--porcelain")
    if result.stdout.strip():
        raise SystemExit(
            "Repository has uncommitted changes. Please commit or stash them before publishing."
        )


def read_current_version() -> Version:
    content = PYPROJECT_PATH.read_text()
    match = VERSION_PATTERN.search(content)
    if not match:
        raise SystemExit(
            "Could not locate version entry in backend/pydantic_types/pyproject.toml"
        )
    return Version.parse(match.group("version"))


def write_new_versions(new_version: Version) -> None:
    version_str = str(new_version)

    pyproject_content = PYPROJECT_PATH.read_text()
    updated_pyproject = VERSION_PATTERN.sub(
        f'version = "{version_str}"', pyproject_content, count=1
    )
    PYPROJECT_PATH.write_text(updated_pyproject)

    init_content = INIT_PATH.read_text()
    if INIT_VERSION_PATTERN.search(init_content):
        updated_init = INIT_VERSION_PATTERN.sub(
            f'__version__ = "{version_str}"', init_content, count=1
        )
    else:
        updated_init = f'__version__ = "{version_str}"\n\n{init_content}'
    INIT_PATH.write_text(updated_init)


def git_commit_tag_push(new_version: Version) -> None:
    version_str = str(new_version)
    tag_name = f"pydantic-types-v{version_str}"

    existing_tags = run("git", "tag", "-l", tag_name).stdout.splitlines()
    if tag_name in existing_tags:
        raise SystemExit(f"Tag {tag_name} already exists. Aborting.")

    run(
        "git",
        "add",
        str(PYPROJECT_PATH.relative_to(REPO_ROOT)),
        str(INIT_PATH.relative_to(REPO_ROOT)),
    )
    commit_message = f"chore: release pydantic types {version_str}"
    run("git", "commit", "-m", commit_message)
    run("git", "tag", tag_name)

    # Push commit and tag
    push_commit = run("git", "push", REMOTE_NAME, "HEAD", check=False)
    if push_commit.returncode != 0:
        raise SystemExit(push_commit.stderr or "Failed to push commit to remote.")

    push_tag = run("git", "push", REMOTE_NAME, tag_name, check=False)
    if push_tag.returncode != 0:
        raise SystemExit(push_tag.stderr or "Failed to push tag to remote.")


def main(args: list[str]) -> None:
    if len(args) != 1 or args[0] not in {"major", "minor"}:
        raise SystemExit("Usage: scripts/publish.py <major|minor>")

    bump_type = args[0]
    ensure_clean_worktree()
    current_version = read_current_version()
    new_version = current_version.bump(bump_type)
    write_new_versions(new_version)
    git_commit_tag_push(new_version)
    print(f"Published pydantic types version {new_version}")


if __name__ == "__main__":
    main(sys.argv[1:])
