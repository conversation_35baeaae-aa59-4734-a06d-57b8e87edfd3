#!/bin/bash

# Cloud Run Migration Script
# This script runs Django migrations using a Cloud Run job

set -e  # Exit on any error

# Configuration - Use Cloud Build substitution variables
PROJECT_ID="${_PROJECT_ID:-gtm-mixer-v3}"
REGION="${_REGION:-europe-west1}"
SERVICE_NAME="${_SERVICE_NAME:-gtm-mixer}"
JOB_NAME="$SERVICE_NAME-migrate-job"
IMAGE_TAG="${COMMIT_SHA:-latest}"
DJANGO_SETTINGS_MODULE="${_DJANGO_SETTINGS_MODULE:-gtm.settings.prod}"
DJANGO_SETTINGS_SECRET="${_DJANGO_SETTINGS_SECRET}"
DEBUG_TOOLBAR="${_DEBUG_TOOLBAR:-False}"
REDIS_MAX_CONNS="${_REDIS_MAX_CONNS:-50}"
CPU="${_CPU:-1}"
MEMORY="${_MEMORY:-256Mi}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🚀 Starting Django Migration Job...${NC}"

# Check if required environment variables are set
if [ -z "$DJANGO_SETTINGS_SECRET" ]; then
    echo -e "${RED}❌ Error: DJANGO_SETTINGS_SECRET environment variable is required${NC}"
    exit 1
fi

# Build image URL using Cloud Build variables
IMAGE_URL="gcr.io/$PROJECT_ID/gitlab.com/o534/$SERVICE_NAME:$IMAGE_TAG"
echo -e "${YELLOW}📦 Using image: $IMAGE_URL${NC}"

echo -e "${YELLOW}🔧 Creating/updating migration job...${NC}"

# Create or update the Cloud Run job
gcloud run jobs replace <(cat <<EOF
apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: $JOB_NAME
  annotations:
    run.googleapis.com/launch-stage: BETA
spec:
  template:
    spec:
      parallelism: 1
      completions: 1
      backoffLimit: 1
      template:
        spec:
          restartPolicy: Never
          timeoutSeconds: 600
          containers:
          - name: migrate
            image: $IMAGE_URL
            command: ["python"]
            args: ["manage.py", "migrate", "--no-input"]
            env:
            - name: DJANGO_SETTINGS_MODULE
              value: "$DJANGO_SETTINGS_MODULE"
            - name: DJANGO_SETTINGS_SECRET
              value: "$DJANGO_SETTINGS_SECRET"
            - name: DEBUG_TOOLBAR
              value: "$DEBUG_TOOLBAR"
            - name: REDIS_MAX_CONNS
              value: "$REDIS_MAX_CONNS"
            resources:
              limits:
                cpu: "$CPU"
                memory: "$MEMORY"
              requests:
                cpu: "0.5"
                memory: "128Mi"
EOF
) --region=$REGION

echo -e "${YELLOW}▶️  Executing migration job...${NC}"

# Execute the job
EXECUTION_NAME=$(gcloud run jobs execute $JOB_NAME --region=$REGION --format="value(metadata.name)")

echo -e "${YELLOW}📋 Job execution: $EXECUTION_NAME${NC}"
echo -e "${YELLOW}⏳ Waiting for migration to complete...${NC}"

# Wait for the job to complete and get the status
while true; do
    STATUS=$(gcloud run jobs executions describe $EXECUTION_NAME --region=$REGION --format="value(status.conditions[0].type)")

    if [ "$STATUS" = "Completed" ]; then
        echo -e "${GREEN}✅ Migration completed successfully!${NC}"
        break
    elif [ "$STATUS" = "Failed" ]; then
        echo -e "${RED}❌ Migration failed!${NC}"
        echo -e "${YELLOW}📋 Checking logs...${NC}"
        gcloud logging read "resource.type=cloud_run_job AND resource.labels.job_name=$JOB_NAME" --limit=50 --format="value(textPayload)"
        exit 1
    else
        echo -e "${YELLOW}⏳ Status: $STATUS - waiting...${NC}"
        sleep 10
    fi
done

echo -e "${GREEN}🎉 Django migrations completed successfully!${NC}"

# Optional: Show recent logs
echo -e "${YELLOW}📋 Recent migration logs:${NC}"
gcloud logging read "resource.type=cloud_run_job AND resource.labels.job_name=$JOB_NAME" --limit=20 --format="value(textPayload)"
