#!/bin/bash

# Sparkle GTM Development Server Manager
# This script manages all services for the Sparkle GTM project

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# PID file locations
BACKEND_PID_FILE=".backend.pid"
AGENT_PID_FILE=".agent.pid"
FRONTEND_PID_FILE=".frontend.pid"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to check if a service is running
check_service() {
    local url=$1
    local name=$2

    if curl -s "$url" > /dev/null 2>&1; then
        print_success "$name is running"
        return 0
    else
        print_error "$name is not responding"
        return 1
    fi
}

# Function to start Docker services
start_docker() {
    print_status "Starting Docker services..."
    if docker compose -f docker-compose-dev.yml up -d; then
        print_success "Docker services started"
        sleep 3
    else
        print_error "Failed to start Docker services"
        exit 1
    fi
}

# Function to start backend
start_backend() {
    print_status "Starting Django backend..."
    cd backend
    nohup uv run python manage.py runserver > ../backend.log 2>&1 &
    echo $! > "../$BACKEND_PID_FILE"
    cd ..
    sleep 3

    if check_service "http://localhost:8000/health" "Backend"; then
        print_success "Backend started successfully (PID: $(cat $BACKEND_PID_FILE))"
    else
        print_warning "Backend may still be starting up..."
    fi
}

# Function to start AI agent
start_agent() {
    print_status "Starting AI Agent service..."
    cd ai-agent-service
    nohup uv run python main.py > ../agent.log 2>&1 &
    echo $! > "../$AGENT_PID_FILE"
    cd ..
    sleep 3

    if check_service "http://localhost:3002/health" "AI Agent"; then
        print_success "AI Agent started successfully (PID: $(cat $AGENT_PID_FILE))"
    else
        print_warning "AI Agent may still be starting up..."
    fi
}

# Function to start frontend
start_frontend() {
    print_status "Starting SvelteKit frontend..."
    cd gtm_client
    pnpm install > /dev/null 2>&1
    nohup pnpm dev > ../frontend.log 2>&1 &
    echo $! > "../$FRONTEND_PID_FILE"
    cd ..
    sleep 5

    if check_service "http://localhost:5173" "Frontend"; then
        print_success "Frontend started successfully (PID: $(cat $FRONTEND_PID_FILE))"
    else
        print_warning "Frontend may still be starting up..."
    fi
}

# Function to stop all services
stop_all() {
    print_status "Stopping all services..."

    # Stop processes using PID files
    for pid_file in "$BACKEND_PID_FILE" "$AGENT_PID_FILE" "$FRONTEND_PID_FILE"; do
        if [ -f "$pid_file" ]; then
            pid=$(cat "$pid_file")
            if kill -0 "$pid" 2>/dev/null; then
                kill "$pid"
                print_success "Stopped process $pid"
            fi
            rm -f "$pid_file"
        fi
    done

    # Fallback: kill by process name
    pkill -f "python manage.py runserver" 2>/dev/null || true
    pkill -f "python main.py" 2>/dev/null || true
    pkill -f "pnpm dev" 2>/dev/null || true

    # Stop Docker
    docker compose -f docker-compose-dev.yml down > /dev/null 2>&1 || true

    print_success "All services stopped"
}

# Function to show status
show_status() {
    echo -e "${CYAN}📊 Service Status:${NC}"
    echo ""

    echo -e "${PURPLE}🐳 Docker Services:${NC}"
    docker compose -f docker-compose-dev.yml ps 2>/dev/null || echo "❌ Docker not running"
    echo ""

    echo -e "${PURPLE}🔧 Backend (Django):${NC}"
    check_service "http://localhost:8000/health" "Backend"
    echo ""

    echo -e "${PURPLE}🤖 AI Agent Service:${NC}"
    check_service "http://localhost:3002/health" "AI Agent"
    echo ""

    echo -e "${PURPLE}🌐 Frontend (SvelteKit):${NC}"
    check_service "http://localhost:5173" "Frontend"
}

# Function to show logs
show_logs() {
    echo -e "${CYAN}📋 Recent Logs:${NC}"
    echo ""

    if [ -f "backend.log" ]; then
        echo -e "${PURPLE}🔧 Backend Logs:${NC}"
        tail -n 10 backend.log
        echo ""
    fi

    if [ -f "agent.log" ]; then
        echo -e "${PURPLE}🤖 AI Agent Logs:${NC}"
        tail -n 10 agent.log
        echo ""
    fi

    if [ -f "frontend.log" ]; then
        echo -e "${PURPLE}🌐 Frontend Logs:${NC}"
        tail -n 10 frontend.log
        echo ""
    fi
}

# Main script logic
case "$1" in
    "start")
        echo -e "${CYAN}🚀 Starting Sparkle GTM Development Environment${NC}"
        echo ""
        start_docker
        start_backend
        start_agent
        start_frontend
        echo ""
        echo -e "${GREEN}🎉 All services started!${NC}"
        echo ""
        echo -e "${CYAN}📋 Service URLs:${NC}"
        echo "  🔧 Backend:   http://localhost:8000"
        echo "  🤖 AI Agent: http://localhost:3002"
        echo "  🌐 Frontend: http://localhost:5173"
        echo ""
        echo -e "${YELLOW}💡 Use './dev-server.sh status' to check service health${NC}"
        echo -e "${YELLOW}💡 Use './dev-server.sh logs' to view recent logs${NC}"
        echo -e "${YELLOW}💡 Use './dev-server.sh stop' to stop all services${NC}"
        ;;
    "stop")
        stop_all
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "restart")
        stop_all
        sleep 2
        $0 start
        ;;
    *)
        echo -e "${CYAN}🚀 Sparkle GTM Development Server Manager${NC}"
        echo ""
        echo "Usage: $0 {start|stop|status|logs|restart}"
        echo ""
        echo "Commands:"
        echo "  start   - Start all services (Docker + Backend + AI Agent + Frontend)"
        echo "  stop    - Stop all services"
        echo "  status  - Check status of all services"
        echo "  logs    - Show recent logs from all services"
        echo "  restart - Stop and start all services"
        echo ""
        echo "Quick start: $0 start"
        ;;
esac
