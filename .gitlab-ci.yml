image: python:3.12-slim

stages:
  - test
  - build

variables:
  PIP_CACHE_DIR: '$CI_PROJECT_DIR/.cache'
  UV_SYSTEM_PYTHON: '1'

cache:
  key: ${CI_JOB_NAME}
  paths:
    - .cache/

# This script runs before each job
before_script:
  - pip install -U pip uv
  - apt-get update -qq && apt-get install -yqq curl

# --- Test Stage ---
# This job runs tests on all branches and merge requests, but not on tags
test_backend:
  stage: test
  script:
    - cd backend
    - uv sync --all-groups --frozen
    - uv run pytest
    # - uv run ruff check .
    # - uv run ruff format --check .
  rules:
    - if: $CI_COMMIT_TAG
      when: never
    - when: on_success

# --- Build Stage ---
build_and_publish_pydantic_types:
  stage: build
  before_script:
    # Install build tools
    - pip install uv build twine
  script:
    - 'echo "Building and publishing package from tag: ${CI_COMMIT_TAG}"'
    - cd backend/pydantic_types
    - python -m build
    # Upload to GitLab's PyPI registry using the secure CI_JOB_TOKEN
    - python -m twine upload --non-interactive --repository-url "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/pypi" --username "gitlab-ci-token" --password "$CI_JOB_TOKEN" dist/*
  rules:
    # This rule ensures the job only runs when a tag is pushed
    - if: $CI_COMMIT_TAG
