version: '3.8'
services:
  db:
    image: postgres:16.3
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=gtm
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - '5432:5432'
    healthcheck:
      test: pg_isready -d $${POSTGRES_DB} -U $${POSTGRES_USER}
      interval: 2s
      retries: 10

  redis:
    image: redis:7
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - '6379:6379'
    healthcheck:
      test: bash -c 'exec 6<>/dev/tcp/redis/6379'
      interval: 2s
      retries: 10

  # web:
  #   build:
  #     context: .
  #     dockerfile: ./Dockerfile.dev
  #   volumes:
  #     - ./backend:/code/backend
  #     - ./backend/static/client/dist:/code/backend/static/client/dist
  #   ports:
  #     - '8080:8080'
  #   env_file:
  #     - ./backend/.env
  #   restart: unless-stopped
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #     redis:
  #       condition: service_healthy

  # celery:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile.dev
  #   command: celery -A gtm worker -l INFO --beat --concurrency 2
  #   volumes:
  #     - ./backend:/code
  #   env_file:
  #     - ./backend/.env
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #     redis:
  #       condition: service_healthy

volumes:
  postgres_data:
  redis_data:
