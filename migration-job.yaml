apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: ${_SERVICE_NAME}-migrate-job
  annotations:
    run.googleapis.com/launch-stage: BETA
spec:
  template:
    spec:
      parallelism: 1
      completions: 1
      backoffLimit: 1
      template:
        spec:
          restartPolicy: Never
          timeoutSeconds: 600 # 10 minutes timeout
          containers:
            - name: migrate
              image: gcr.io/${_PROJECT_ID}/gitlab.com/o534/${_SERVICE_NAME}:${COMMIT_SHA}
              command: ['python']
              args: ['manage.py', 'migrate', '--no-input']
              env:
                - name: DJANGO_SETTINGS_MODULE
                  value: '${_DJANGO_SETTINGS_MODULE}'
                - name: DJ<PERSON>GO_SETTINGS_SECRET
                  value: '${_DJANGO_SETTINGS_SECRET}'
                - name: DEBUG_TOOLBAR
                  value: '${_DEBUG_TOOLBAR}'
                - name: REDIS_MAX_CONNS
                  value: '${_REDIS_MAX_CONNS}'
              resources:
                limits:
                  cpu: '${_CPU}'
                  memory: '${_MEMORY}'
                requests:
                  cpu: '0.5'
                  memory: '128Mi'
