# Mastra Chat Agent Testing Guide

## 🎯 Overview

This guide will help you test the new Mastra-based chat agent with Google Gemini integration.

## ✅ Prerequisites

### 1. Google API Key

You need a Google Gemini API key. Get one from:
https://aistudio.google.com/app/apikey

### 2. Update .env File

Edit `/Users/<USER>/Documents/projects/upwork/sascha/sparkle/gtm_client/.env`:

```bash
# Replace 'your-google-api-key-here' with your actual API key
VITE_GOOGLE_API_KEY='AIza...'  # Your actual Google API key
VITE_DEFAULT_MODEL='gemini-1.5-pro'
```

### 3. Django Backend Running

Make sure your Django backend is running on `http://localhost:8000`:

```bash
# In the Django project directory
python manage.py runserver
```

## 🚀 Running the Test

### Step 1: Build the Frontend

```bash
cd /Users/<USER>/Documents/projects/upwork/sascha/sparkle/gtm_client
pnpm run build
```

### Step 2: Start the Development Server

```bash
pnpm run dev
```

### Step 3: Open the Test Page

Navigate to: **http://localhost:5173/test-mastra-workspace**

## 🧪 Test Scenarios

### Test 1: List Workspaces

**Input:** "List all my workspaces"

**Expected Result:**
- Agent calls `listWorkspacesTool`
- Returns list of workspaces from Django backend
- Shows workspace names, IDs, and descriptions

**What to Check:**
- ✅ User context is passed correctly (user_id, organization_id)
- ✅ Authentication headers are sent to Django
- ✅ Response is formatted nicely by the agent

### Test 2: Create Workspace

**Input:** "Create a workspace called Marketing with description 'Q1 2024 campaigns'"

**Expected Result:**
- Agent calls `createWorkspaceTool`
- Creates new workspace in Django
- Returns success message with workspace details

**What to Check:**
- ✅ Workspace is created in Django database
- ✅ Title and description are set correctly
- ✅ Organization ID is associated correctly

### Test 3: Get Specific Workspace

**Input:** "Show me workspace 5"

**Expected Result:**
- Agent calls `getWorkspaceTool` with workspace_id=5
- Returns details of workspace #5

**What to Check:**
- ✅ Correct workspace is retrieved
- ✅ All workspace fields are displayed

### Test 4: Update Workspace

**Input:** "Update workspace 3 to have title 'Sales Team'"

**Expected Result:**
- Agent calls `updateWorkspaceTool`
- Updates workspace #3 title
- Returns confirmation

**What to Check:**
- ✅ Workspace title is updated in Django
- ✅ Other fields remain unchanged

### Test 5: Delete Workspace

**Input:** "Delete workspace 7"

**Expected Result:**
- Agent calls `deleteWorkspaceTool`
- Deletes workspace #7
- Returns confirmation

**What to Check:**
- ✅ Workspace is deleted from Django
- ✅ Confirmation message is clear

### Test 6: Streaming Response

**Input:** Any of the above queries

**Expected Result:**
- Response streams in real-time
- You see the agent "thinking" with animated cursor
- Tool calls are shown in expandable sections

**What to Check:**
- ✅ Streaming works smoothly
- ✅ No lag or stuttering
- ✅ Final response is complete

## 🔍 Debugging

### Check Browser Console

Open browser DevTools (F12) and check:

1. **Network Tab**
   - Look for POST requests to `/api/mastra/workspace`
   - Check request payload includes `message` and `context`
   - Check response status (should be 200)

2. **Console Tab**
   - Look for any JavaScript errors
   - Check for Mastra-related logs

### Check Server Logs

In your terminal where `pnpm run dev` is running:

1. **Look for:**
   - "Step finished:" logs showing tool calls
   - Any error messages from Mastra or Google Gemini

2. **Common Issues:**
   - **"Chat agent not found"** → Mastra index.ts not exporting chatAgent correctly
   - **"Invalid API key"** → Google API key not set or incorrect
   - **403 Forbidden** → Django authentication issue
   - **500 Internal Server Error** → Check Django logs

### Check Django Logs

In your Django terminal:

1. **Look for:**
   - API requests to `/api/v1/workspaces/`
   - Authentication headers (`X-User-Id`, `X-Org-Id`)
   - Any permission errors

2. **Common Issues:**
   - **"Authentication credentials were not provided"** → Headers not being sent
   - **"Permission denied"** → User doesn't have workspace permissions
   - **404 Not Found** → Workspace doesn't exist

## 📊 Expected Flow

```
User Input
    ↓
MastraChatAgent.svelte
    ↓
POST /api/mastra/workspace
    ↓
Chat Agent (Google Gemini)
    ↓
Workspace Tool (e.g., listWorkspacesTool)
    ↓
Backend Client (backend-client.ts)
    ↓
Django API (/api/v1/workspaces/)
    ↓
Response back through chain
    ↓
Display in UI
```

## 🎨 UI Features to Test

### 1. Message Display
- User messages appear on the right (light background)
- Assistant messages appear on the left (darker background)

### 2. Tool Calls
- Expandable "Tool Calls" section under assistant messages
- Shows JSON of tool calls made

### 3. Streaming
- Animated cursor (▊) while streaming
- Smooth text appearance

### 4. Error Handling
- Red alert box for errors
- Clear error messages

### 5. Loading States
- Loading spinner while waiting for response
- Input disabled during processing

## 🐛 Troubleshooting

### Issue: "Chat agent not found"

**Solution:**
```bash
# Rebuild the project
cd /Users/<USER>/Documents/projects/upwork/sascha/sparkle/gtm_client
pnpm run build
pnpm run dev
```

### Issue: "Invalid API key"

**Solution:**
1. Check `.env` file has correct `VITE_GOOGLE_API_KEY`
2. Restart dev server after changing .env
3. Verify API key at https://aistudio.google.com/app/apikey

### Issue: 403 Forbidden from Django

**Solution:**
1. Check you're logged in to the SvelteKit app
2. Verify user has organization access
3. Check Django logs for permission errors

### Issue: No response from agent

**Solution:**
1. Check browser console for errors
2. Check server logs for Mastra errors
3. Verify Google Gemini API quota not exceeded

## ✨ Success Criteria

You'll know it's working when:

1. ✅ You can send messages and get responses
2. ✅ Agent correctly interprets natural language requests
3. ✅ Workspace operations work (create, list, update, delete, get)
4. ✅ Responses stream in real-time
5. ✅ Tool calls are visible and correct
6. ✅ Django backend receives authenticated requests
7. ✅ No errors in browser or server console

## 📝 Next Steps After Testing

Once testing is successful:

1. **Add More Tools** - Project, Page, Permission tools
2. **Update AI Agent Sidebar** - Replace old MCP client with Mastra
3. **Add Conversation History** - Store chat history
4. **Improve Streaming** - Better UX for streaming responses
5. **Add Suggestions** - Quick action buttons

## 🎉 Celebrate!

If all tests pass, you've successfully migrated from FastMCP to Mastra with:
- ✅ Google Gemini integration
- ✅ Streaming responses
- ✅ Natural language interface
- ✅ Full workspace management
- ✅ Proper authentication

Great work! 🚀

