{"name": "gtm-client", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev --host 0.0.0.0 --port 3000", "dev:mastra": "<PERSON>ra dev", "build": "vite build", "build:mastra": "mastra build", "start": "node build/index.js", "preview": "vite preview --host 0.0.0.0 --port 3000", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test:unit": "vitest", "test": "npm run test:unit -- --run", "circular-dependency-check": "dpdm --no-warning --no-tree --exit-code circular:1 src/**/*.svelte src/**/*.js src/**/*.ts", "machine-translate": "inlang machine translate --project project.inlang"}, "dependencies": {"@ai-sdk/google": "^2.0.17", "@ai-sdk/openai": "^2.0.42", "@mastra/core": "^0.18.0", "@mastra/libsql": "^0.14.3", "@sveltejs/adapter-node": "^5.3.1", "@sveltejs/kit": "^2.37.0", "cookie": "^1.0.2", "js-cookie": "^3.0.5", "mastra": "^0.13.2", "set-cookie-parser": "^2.7.1"}, "devDependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.7.4", "@eslint/compat": "^1.3.2", "@eslint/js": "^9.34.0", "@inlang/cli": "^3.0.12", "@inlang/paraglide-js": "2.2.0", "@internationalized/date": "^3.9.0", "@lucide/svelte": "^0.542.0", "@midudev/tailwind-animations": "^0.2.0", "@paralleldrive/cuid2": "^2.2.2", "@sveltejs/vite-plugin-svelte": "^6.1.3", "@sveltelegos-blue/svelte-legos": "^0.5.1", "@tailwindcss/postcss": "^4.1.12", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.12", "@testing-library/jest-dom": "^6.8.0", "@testing-library/svelte": "^5.2.8", "@types/set-cookie-parser": "^2.4.10", "adapter": "link:@atlaskit/pragmatic-drag-and-drop/element/adapter", "bits-ui": "^2.9.5", "clsx": "^2.1.1", "daisyui": "^5.1.5", "dayjs": "^1.11.18", "dpdm": "^3.14.0", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-svelte": "^3.11.0", "formsnap": "^2.0.1", "globals": "^16.3.0", "js-cookie": "^3.0.5", "jsdom": "^26.1.0", "lint-staged": "^16.1.6", "mode-watcher": "1.1.0", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "runed": "^0.31.1", "svelte": "^5.38.6", "svelte-check": "^4.3.1", "svelte-sonner": "^1.0.5", "sveltekit-superforms": "^2.27.1", "tailwind-merge": "^3.3.1", "tailwind-scrollbar": "^4.0.2", "tailwind-variants": "^3.1.0", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.7", "typescript": "^5.9.2", "typescript-eslint": "^8.42.0", "vaul-svelte": "1.0.0-next.7", "vite": "^7.1.4", "vitest": "^3.2.4", "zod": "^4.1.5"}}