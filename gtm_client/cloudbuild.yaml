steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '--no-cache'
      - '-t'
      - 'gcr.io/$_PROJECT_ID/$_IMAGE_NAME:$COMMIT_SHA' # Using custom PROJECT_ID variable
      - '.'
    id: 'Build'
    dir: gtm_client

  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$_PROJECT_ID/$_IMAGE_NAME:$COMMIT_SHA']
    id: 'Push'

  # Debug: Print all variables
  - name: 'ubuntu'
    args:
      - 'echo'
      - 'SERVICE_NAME: $_SERVICE_NAME, IMAGE_NAME: $_IMAGE_NAME, REGION: $_REGION, PROJECT_ID: $_PROJECT_ID'
    id: 'Debug Variables'

  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - '$_SERVICE_NAME'
      - '--platform=managed'
      - '--region=$_REGION'
      - '--image=gcr.io/$_PROJECT_ID/$_IMAGE_NAME:$COMMIT_SHA'
      - '--cpu=$_CPU'
      - '--memory=$_MEMORY'
      - '--min-instances=$_MIN_INSTANCES'
      - '--max-instances=$_MAX_INSTANCES'
      - '--timeout=$_TIMEOUT'
      - '--concurrency=$_CONCURRENCY'
      - '--allow-unauthenticated'
      - '--port=$_PORT'
      - '--set-env-vars'
      - 'NODE_ENV=$_NODE_ENV'
      - '--set-env-vars'
      - 'HOST=$_HOST'
      - '--set-env-vars'
      - 'NODE_OPTIONS=--max-old-space-size=384'
      - '--set-env-vars'
      - 'API_URL=$_API_URL'
    id: 'Deploy'

  # Route traffic to the new revision
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'services'
      - 'update-traffic'
      - '$_SERVICE_NAME'
      - '--region=$_REGION'
      - '--to-latest'
    id: 'Update Traffic'

options:
  logging: CLOUD_LOGGING_ONLY
