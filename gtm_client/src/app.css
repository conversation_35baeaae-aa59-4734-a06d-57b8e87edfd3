body {
	font-family: 'Inter', sans-serif;
}

@import 'tailwindcss';
@import 'tw-animate-css';
@plugin "@tailwindcss/typography";
@import '$fonts/styles/inter.css';
@plugin '@midudev/tailwind-animations';

/* @custom-variant dark (&:is(.dark *)); */

:root {
	--radius: 0.625rem;
	--background: oklch(1 0 0);
	--foreground: oklch(0.145 0 0);
	--card: oklch(1 0 0);
	--card-foreground: oklch(0.145 0 0);
	--popover: oklch(1 0 0);
	--popover-foreground: oklch(0.145 0 0);
	--primary: oklch(0.205 0 0);
	--primary-foreground: oklch(0.985 0 0);
	--secondary: oklch(0.97 0 0);
	--secondary-foreground: oklch(0.205 0 0);
	--muted: oklch(0.97 0 0);
	--muted-foreground: oklch(0.556 0 0);
	--accent: oklch(0.97 0 0);
	--accent-foreground: oklch(0.205 0 0);
	--destructive: oklch(0.577 0.245 27.325);
	--border: oklch(0.922 0 0);
	--input: oklch(0.922 0 0);
	--ring: oklch(0.708 0 0);
	--chart-1: oklch(0.646 0.222 41.116);
	--chart-2: oklch(0.6 0.118 184.704);
	--chart-3: oklch(0.398 0.07 227.392);
	--chart-4: oklch(0.828 0.189 84.429);
	--chart-5: oklch(0.769 0.188 70.08);
	--sidebar: oklch(0.985 0 0);
	--sidebar-foreground: oklch(0.145 0 0);
	--sidebar-primary: oklch(0.205 0 0);
	--sidebar-primary-foreground: oklch(0.985 0 0);
	--sidebar-accent: oklch(0.97 0 0);
	--sidebar-accent-foreground: oklch(0.205 0 0);
	--sidebar-border: oklch(0.922 0 0);
	--sidebar-ring: oklch(0.708 0 0);
}

@theme inline {
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);
	--font-sans: 'Inter', sans-serif;
	--font-mono: 'Inter', sans-serif;
	--animate-accordion-up: accordion-up 0.2s ease-out;
	--animate-accordion-down: accordion-down 0.2s ease-out;
	--animate-caret-blink: caret-blink 1.25s ease-out infinite;
}

.dark {
	--background: oklch(0.145 0 0);
	--foreground: oklch(0.985 0 0);
	--card: oklch(0.205 0 0);
	--card-foreground: oklch(0.985 0 0);
	--popover: oklch(0.269 0 0);
	--popover-foreground: oklch(0.985 0 0);
	--primary: oklch(0.922 0 0);
	--primary-foreground: oklch(0.205 0 0);
	--secondary: oklch(0.269 0 0);
	--secondary-foreground: oklch(0.985 0 0);
	--muted: oklch(0.269 0 0);
	--muted-foreground: oklch(0.708 0 0);
	--accent: oklch(0.371 0 0);
	--accent-foreground: oklch(0.985 0 0);
	--destructive: oklch(0.704 0.191 22.216);
	--border: oklch(1 0 0 / 10%);
	--input: oklch(1 0 0 / 15%);
	--ring: oklch(0.556 0 0);
	--chart-1: oklch(0.488 0.243 264.376);
	--chart-2: oklch(0.696 0.17 162.48);
	--chart-3: oklch(0.769 0.188 70.08);
	--chart-4: oklch(0.627 0.265 303.9);
	--chart-5: oklch(0.645 0.246 16.439);
	--sidebar: oklch(0.205 0 0);
	--sidebar-foreground: oklch(0.985 0 0);
	--sidebar-primary: oklch(0.488 0.243 264.376);
	--sidebar-primary-foreground: oklch(0.985 0 0);
	--sidebar-accent: oklch(0.269 0 0);
	--sidebar-accent-foreground: oklch(0.985 0 0);
	--sidebar-border: oklch(1 0 0 / 10%);
	--sidebar-ring: oklch(0.439 0 0);

	/* Explicit dark overrides for @theme inline variables */
	/* These will be the same as your main dark theme variables by design */
	--radius-sm: calc(
		var(--radius) - 4px
	); /* Stays the same or recalculates based on dark --radius if it changed */
	--radius-md: calc(var(--radius) - 2px); /* Stays the same or recalculates */
	--radius-lg: var(--radius); /* Stays the same or recalculates */
	--radius-xl: calc(var(--radius) + 4px); /* Stays the same or recalculates */
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);
	/* Font variables likely don't change for dark mode, but included for completeness if they did */
	--font-sans: 'Inter', sans-serif;
	--font-mono: 'Inter', sans-serif;
	/* Animation variables also likely don't change */
	--animate-accordion-up: accordion-up 0.2s ease-out;
	--animate-accordion-down: accordion-down 0.2s ease-out;
	--animate-caret-blink: caret-blink 1.25s ease-out infinite;
}
@layer base {
	* {
		@apply border-border outline-ring/50;
	}

	html {
		@apply scroll-smooth;
	}

	body {
		@apply bg-background text-foreground overscroll-none;
		/* font-feature-settings: "rlig" 1, "calt" 1; */
		font-synthesis-weight: none;
		text-rendering: optimizeLegibility;
	}

	@supports (font: -apple-system-body) and (-webkit-appearance: none) {
		[data-wrapper] {
			@apply min-[1800px]:border-t;
		}
	}

	/* Custom scrollbar styling. Thanks @pranathiperii. */
	::-webkit-scrollbar {
		width: 5px;
	}

	::-webkit-scrollbar-track {
		background: transparent;
	}

	::-webkit-scrollbar-thumb {
		background: var(--border);
		border-radius: 5px;
	}

	* {
		scrollbar-width: thin;
		scrollbar-color: var(--border) transparent;
	}
}

@utility step {
	@apply before:border-background before:bg-muted before:absolute before:mt-[-4px] before:ml-[-50px] before:inline-flex before:size-9 before:items-center before:justify-center before:rounded-full before:border-4 before:text-center before:-indent-px before:font-mono before:text-base before:font-medium before:content-[counter(step)];
	counter-increment: step;
}

@utility no-scrollbar {
	@apply [-ms-overflow-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden;
}

@utility border-grid {
	@apply border-border/50 dark:border-border border-dashed;
}

@utility container-wrapper {
	@apply border-border/70 dark:border-border mx-auto w-full max-w-[1400px] border-dashed min-[1400px]:border-x min-[1800px]:max-w-screen-2xl;
}

@utility container {
	@apply mx-auto max-w-screen-2xl px-4 xl:px-6;
}

@keyframes accordion-down {
	from {
		height: 0;
	}

	to {
		height: var(--bits-accordion-content-height);
	}
}

@keyframes accordion-up {
	from {
		height: var(--bits-accordion-content-height);
	}

	to {
		height: 0;
	}
}

@keyframes caret-blink {
	0%,
	70%,
	100% {
		opacity: 1;
	}

	20%,
	50% {
		opacity: 0;
	}
}

.markdown code:not(pre code) {
	background-color: var(--muted);
	font-family: var(--font-mono);
	border-radius: var(--radius);
	padding: 0.2rem 0.4rem;
	font-size: 0.875rem;
}

[data-theme='default'] {
	display: flex;
}

[data-style='new-york'] [data-style='default'] {
	display: none;
}

[data-style='default'] [data-style='default'] {
	display: flex;
	flex-direction: column;
}

[data-style='new-york'] [data-style='new-york'] {
	display: flex;
	flex-direction: column;
}

[data-style='default'] [data-style='new-york'] {
	display: none;
}

[data-rehype-pretty-code-fragment] {
	position: relative;
}

[data-rehype-pretty-code-fragment] code {
	display: grid;
	min-width: 100%;
	overflow-wrap: break-word;
	border-radius: 0;
	border: 0;
	background: transparent;
	padding: 0;
	counter-reset: line;
	box-decoration-break: clone;
}

[data-rehype-pretty-code-fragment] .line {
	display: inline-block;
	min-height: 1rem;
	width: 100%;
	padding: 0.2rem 1rem;
}

[data-rehype-pretty-code-fragment] [data-line-numbers] .line {
	padding: 0 calc(var(--spacing) * 2);
}

[data-rehype-pretty-code-fragment] [data-line-numbers] > .line::before {
	font-size: 0.875rem;
	color: color-mix(in oklab, var(--color-zinc-50) 40%, transparent);
	counter-increment: line;
	content: counter(line);
	display: inline-block;
	width: 1.8rem;
	margin-right: 1.4rem;
	text-align: right;
}

[data-metadata] .pre-copy-btn {
	top: calc(var(--spacing) * 16) !important;
}

[data-rehype-pretty-code-fragment] .line--highlighted {
	background-color: color-mix(in oklab, var(--color-zinc-700) 50%, transparent);
}

[data-rehype-pretty-code-fragment] .with--meta {
	top: calc(var(--spacing) * 16) !important;
}

[data-rehype-pretty-code-fragment] .line-highlighted span {
	position: relative;
}

[data-rehype-pretty-code-fragment] .word--highlighted {
	border-radius: var(--radius-md);
	background-color: color-mix(in oklab, var(--color-zinc-700) 50%, transparent);
	border-color: color-mix(in oklab, var(--color-zinc-700) 70%, transparent);
	padding: var(--spacing);
}

.dark [data-rehype-pretty-code-fragment] .word--highlighted {
	background-color: var(--color-zinc-900);
}

[data-rehype-pretty-code-title] {
	margin-top: calc(var(--spacing) * 2);
	padding: 0 calc(var(--spacing) * 2);
	padding-top: calc(var(--spacing) * 6);
	font-weight: 500;
	font-size: 0.875rem;
}

[data-rehype-pretty-code-title] + pre {
	margin-top: calc(var(--spacing) * 2);
}

.super-debug {
	background-color: var(--color-zinc-950) !important;
	color: #fff8 !important;
}

.super-debug--pre {
	background-color: transparent !important;
	color: #fff8 !important;
}

.super-debug--code {
	background-color: transparent !important;
}

.super-debug--code .key {
	color: #fff8 !important;
}

.super-debug--code .undefined {
	color: #fff8 !important;
}

.super-debug--code .string {
	color: #fff8 !important;
}

.super-debug--code .number {
	color: #fff8 !important;
}

.super-debug--code .boolean {
	color: #fff8 !important;
}

.super-debug--code .boolean {
	color: #fff8 !important;
}

/* Custom styling for tab triggers to ensure consistency */
.tab-trigger {
	/* Add consistent padding and border-radius for all triggers */
	padding: 0.5rem 1rem; /* Adjust as needed */
	border-radius: var(--radius); /* Uses your ShadCN border radius variable */
	transition:
		background-color 0.15s ease-in-out,
		color 0.15s ease-in-out;
}

/* Specific styling for the active state of the trigger */
.tab-trigger[data-state='active'] {
	/* These colors should match your ShadCN active tab/accent styling */
	background-color: var(--accent);
	color: var(--accent-foreground);
}

/* Specific styling for the inactive state of the trigger */
.tab-trigger[data-state='inactive'] {
	/* Ensures inactive tabs have no background */
	background-color: transparent;
	color: var(--muted-foreground); /* Or whatever your default text color is */
}
