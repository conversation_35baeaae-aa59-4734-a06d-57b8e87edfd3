import { sequence } from '@sveltejs/kit/hooks';
import { handleOrganization } from './hooks/organization';
import { paraglideHandle } from './hooks/paraglide';
import { handleMessagesCookies } from './hooks/message';
import { handleUser } from './hooks/user';
import { handleApi } from './hooks/api';

export const handle = sequence(
	paraglideHandle,
	handleApi,
	handleUser,
	handleMessagesCookies,
	handleOrganization
);
