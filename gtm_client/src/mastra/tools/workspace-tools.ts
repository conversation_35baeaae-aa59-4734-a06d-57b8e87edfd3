/**
 * Workspace Tools for Mastra
 *
 * Individual tools for workspace management operations.
 * These tools are used by the master chat agent.
 */

import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { post<PERSON><PERSON>, get<PERSON><PERSON>, put<PERSON><PERSON> } from '$mastra/lib/backend-client';
import { normalizeWorkspacePayload } from '$mastra/lib/tool-helpers';
import type { UserContext } from '$mastra/lib/types';

/**
 * Create a new workspace
 */
export const createWorkspaceTool = createTool({
	id: 'create-workspace',
	description:
		'Create a new workspace in the organization. The organization ID will be automatically taken from the user context.',
	inputSchema: z.object({
		title: z.string().min(3).max(50).describe('The workspace title (3-50 characters)'),
		description: z.string().optional().describe('Optional workspace description'),
		organization_id: z
			.string()
			.optional()
			.describe('Organization ID (automatically provided from user context)'),
		user_context: z.any().optional().describe('User context for authentication')
	}),
	outputSchema: z.object({
		success: z.boolean(),
		status: z.string(),
		workspace: z.any().optional(),
		error: z.string().optional(),
		ui_actions: z
			.array(
				z.object({
					type: z.string(),
					label: z.string(),
					action: z.string(),
					data: z.any().optional()
				})
			)
			.optional()
	}),
	execute: async (context) => {
		try {
			const { title, description = '', organization_id, user_context } = context.context || context;

			const payload: any = { title };
			if (description) payload.description = description;

			// Use organization_id from parameter or user context
			const orgId = organization_id || user_context?.organization_id;

			if (orgId) {
				payload.organization_id = orgId;
			} else {
				return {
					success: false,
					status: 'error',
					error: 'Organization ID is required but not found in user context'
				};
			}

			const result = await postJson<any>('/api/v1/workspaces/new/', payload, user_context);

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);
				const workspace = normalized.workspace || normalized;
				console.log('🔍 createWorkspace DEBUG: Normalized workspace:', workspace);
				console.log('🔍 createWorkspace DEBUG: Workspace keys:', Object.keys(workspace || {}));

				return {
					success: true,
					status: 'success',
					workspace: workspace,
					ui_actions: [
						{
							type: 'navigate',
							label: `Go to ${workspace?.title || workspace?.name || 'workspace'}`,
							action: 'navigate',
							data: {
								url: `/workspaces/${workspace?.id}`,
								workspace_id: workspace?.id,
								workspace_title: workspace?.title || workspace?.name
							}
						}
					]
				};
			} else {
				return {
					success: false,
					status: 'error',
					error: result.error
				};
			}
		} catch (error) {
			return {
				success: false,
				status: 'error',
				error: error instanceof Error ? error.message : 'Unknown error'
			};
		}
	}
});

/**
 * List workspaces
 */
export const listWorkspacesTool = createTool({
	id: 'list-workspaces',
	description: 'List workspaces with optional filtering by organization or title',
	inputSchema: z.object({
		organization_id: z.string().optional().describe('Optional organization ID filter'),
		title: z.string().optional().describe('Optional title filter'),
		user_context: z.any().optional().describe('User context for authentication')
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z.any().optional(),
		ui_actions: z
			.array(
				z.object({
					type: z.string(),
					label: z.string(),
					action: z.string(),
					data: z.any().optional()
				})
			)
			.optional()
	}),
	execute: async (context) => {
		try {
			// In newer Mastra versions, parameters are passed directly in context
			const { organization_id, title, user_context } = context.context || context;

			let path = '/api/v1/workspaces/';
			const params: string[] = [];

			// Try to get organization_id from multiple sources
			const orgId = organization_id || user_context?.organization_id;

			if (orgId) params.push(`organization_id=${orgId}`);
			if (title) params.push(`title=${encodeURIComponent(title)}`);
			if (params.length > 0) path += '?' + params.join('&');

			const result = await getJson<any>(path, user_context);

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);

				// Create UI actions for each workspace
				const ui_actions: any[] = [];
				// Check for workspaces in different possible locations
				let workspaces = null;
				if (normalized.data && normalized.data.items && Array.isArray(normalized.data.items)) {
					workspaces = normalized.data.items;
				} else if (normalized.workspaces && Array.isArray(normalized.workspaces)) {
					workspaces = normalized.workspaces;
				} else if (Array.isArray(normalized)) {
					workspaces = normalized;
				}

				if (workspaces && Array.isArray(workspaces)) {
					for (const workspace of workspaces) {
						ui_actions.push({
							type: 'navigate',
							label: `Go to ${workspace.title}`,
							action: 'navigate',
							data: {
								url: `/workspaces/${workspace.id}`,
								workspace_id: workspace.id,
								workspace_title: workspace.title
							}
						});
					}
				}

				return {
					success: true,
					message: 'Workspaces retrieved successfully',
					data: normalized,
					ui_actions: ui_actions
				};
			} else {
				return {
					success: false,
					message: `Error listing workspaces: ${result.error}`,
					data: result.data
				};
			}
		} catch (error) {
			return {
				success: false,
				message: `Error listing workspaces: ${error instanceof Error ? error.message : 'Unknown error'}`
			};
		}
	}
});

/**
 * Update workspace
 */
export const updateWorkspaceTool = createTool({
	id: 'update-workspace',
	description: 'Update an existing workspace by ID',
	inputSchema: z.object({
		workspace_id: z.number().describe('The ID of the workspace to update'),
		updates: z.record(z.any()).describe('Dictionary of fields to update'),
		user_context: z.any().optional().describe('User context for authentication')
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z.any().optional()
	}),
	execute: async (context) => {
		const { workspace_id, updates, user_context } = context.context || context;
		try {
			if (!updates || Object.keys(updates).length === 0) {
				return {
					success: false,
					message: 'Error: No updates provided'
				};
			}

			const result = await putJson<any>(
				`/api/v1/workspaces/${workspace_id}/`,
				updates,
				user_context
			);

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);
				return {
					success: true,
					message: 'Workspace updated successfully',
					data: normalized
				};
			} else {
				return {
					success: false,
					message: `Error updating workspace: ${result.error}`,
					data: result.data
				};
			}
		} catch (error) {
			return {
				success: false,
				message: `Error updating workspace: ${error instanceof Error ? error.message : 'Unknown error'}`
			};
		}
	}
});

/**
 * Delete workspace
 */
export const deleteWorkspaceTool = createTool({
	id: 'delete-workspace',
	description: 'Delete a workspace by ID',
	inputSchema: z.object({
		workspace_id: z.number().describe('The ID of the workspace to delete'),
		user_context: z.any().optional().describe('User context for authentication')
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z.any().optional()
	}),
	execute: async (context) => {
		const { workspace_id, user_context } = context.context || context;
		try {
			const result = await postJson<any>(
				`/api/v1/workspaces/${workspace_id}/delete/`,
				{},
				user_context
			);

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);
				return {
					success: true,
					message: 'Workspace deleted successfully',
					data: normalized
				};
			} else {
				return {
					success: false,
					message: `Error deleting workspace: ${result.error}`,
					data: result.data
				};
			}
		} catch (error) {
			return {
				success: false,
				message: `Error deleting workspace: ${error instanceof Error ? error.message : 'Unknown error'}`
			};
		}
	}
});

/**
 * Get workspace
 */
export const getWorkspaceTool = createTool({
	id: 'get-workspace',
	description: 'Get a specific workspace by ID',
	inputSchema: z.object({
		workspace_id: z.number().describe('The ID of the workspace to retrieve'),
		user_context: z.any().optional().describe('User context for authentication')
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z.any().optional()
	}),
	execute: async (context) => {
		const { workspace_id, user_context } = context.context || context;
		try {
			const result = await postJson<any>(
				`/api/v1/workspaces/${workspace_id}/get/`,
				{},
				user_context
			);

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);
				return {
					success: true,
					message: 'Workspace retrieved successfully',
					data: normalized
				};
			} else {
				return {
					success: false,
					message: `Error retrieving workspace: ${result.error}`,
					data: result.data
				};
			}
		} catch (error) {
			return {
				success: false,
				message: `Error retrieving workspace: ${error instanceof Error ? error.message : 'Unknown error'}`
			};
		}
	}
});
