/**
 * Ma<PERSON> Tools for Page Management
 * 
 * These tools provide page CRUD operations and integrate with the Django backend.
 * Each tool handles authentication via user context and returns structured responses.
 */

import { createTool } from '@mastra/core';
import { z } from 'zod';
import { post<PERSON><PERSON>, get<PERSON><PERSON>, put<PERSON><PERSON>, delete<PERSON><PERSON> } from '../lib/backend-client';

/**
 * Create a new page
 */
export const createPageTool = createTool({
	id: 'createPage',
	description: 'Create a new page in a project',
	inputSchema: z.object({
		name: z.string().min(3).max(100),
		description: z.string().optional(),
		project_id: z.number(),
		page_type: z.enum([
			'checkout',
			'sales_copy', 
			'product_page',
			'cart',
			'category',
			'purchase_confirmation',
			'opt_in_confirmation',
			'opt_in_form',
			'other'
		]),
		url: z.string().url().optional(),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z.object({
			id: z.number(),
			name: z.string(),
			description: z.string().optional(),
			project_id: z.number(),
			page_type: z.string(),
			url: z.string().optional(),
			created_at: z.string(),
			updated_at: z.string()
		}).optional(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, ...pageData } = input;
			
			const [status, data] = await postJson(
				user_context,
				'/api/v1/pages/',
				pageData
			);

			if (status && status < 400) {
				return {
					success: true,
					message: 'Page created successfully',
					data: data
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to create page';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error creating page: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});

/**
 * List pages in a project
 */
export const listPagesTool = createTool({
	id: 'listPages',
	description: 'List all pages in a project',
	inputSchema: z.object({
		project_id: z.number(),
		search: z.string().optional(),
		page_type: z.string().optional(),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z.array(z.object({
			id: z.number(),
			name: z.string(),
			description: z.string().optional(),
			project_id: z.number(),
			page_type: z.string(),
			url: z.string().optional(),
			created_at: z.string(),
			updated_at: z.string()
		})).optional(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, project_id, search, page_type } = input;
			
			// Build query parameters
			const params = new URLSearchParams();
			if (search) params.append('search', search);
			if (page_type) params.append('page_type', page_type);
			
			const url = `/api/v1/projects/${project_id}/pages/${params.toString() ? '?' + params.toString() : ''}`;
			
			const [status, data] = await getJson(user_context, url);

			if (status && status < 400) {
				return {
					success: true,
					message: 'Pages retrieved successfully',
					data: data?.results || data || []
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to retrieve pages';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error retrieving pages: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});

/**
 * Get a specific page
 */
export const getPageTool = createTool({
	id: 'getPage',
	description: 'Get details of a specific page',
	inputSchema: z.object({
		page_id: z.number(),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z.object({
			id: z.number(),
			name: z.string(),
			description: z.string().optional(),
			project_id: z.number(),
			page_type: z.string(),
			url: z.string().optional(),
			created_at: z.string(),
			updated_at: z.string()
		}).optional(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, page_id } = input;
			
			const [status, data] = await getJson(
				user_context,
				`/api/v1/pages/${page_id}/`
			);

			if (status && status < 400) {
				return {
					success: true,
					message: 'Page retrieved successfully',
					data: data
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to retrieve page';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error retrieving page: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});

/**
 * Update a page
 */
export const updatePageTool = createTool({
	id: 'updatePage',
	description: 'Update an existing page',
	inputSchema: z.object({
		page_id: z.number(),
		updates: z.record(z.string(), z.any()),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z.object({
			id: z.number(),
			name: z.string(),
			description: z.string().optional(),
			project_id: z.number(),
			page_type: z.string(),
			url: z.string().optional(),
			created_at: z.string(),
			updated_at: z.string()
		}).optional(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, page_id, updates } = input;
			
			const [status, data] = await putJson(
				user_context,
				`/api/v1/pages/${page_id}/`,
				updates
			);

			if (status && status < 400) {
				return {
					success: true,
					message: 'Page updated successfully',
					data: data
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to update page';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error updating page: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});

/**
 * Delete a page
 */
export const deletePageTool = createTool({
	id: 'deletePage',
	description: 'Delete a page',
	inputSchema: z.object({
		page_id: z.number(),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, page_id } = input;
			
			const [status, data] = await deleteJson(
				user_context,
				`/api/v1/pages/${page_id}/`
			);

			if (status && status < 400) {
				return {
					success: true,
					message: 'Page deleted successfully'
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to delete page';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error deleting page: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});

/**
 * Get suggested pages for a project
 */
export const getSuggestedPagesTool = createTool({
	id: 'getSuggestedPages',
	description: 'Get suggested pages for a project based on its type and platform',
	inputSchema: z.object({
		project_id: z.number(),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z.array(z.object({
			id: z.number(),
			name: z.string(),
			type: z.string(),
			data: z.record(z.string(), z.any())
		})).optional(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, project_id } = input;
			
			const [status, data] = await getJson(
				user_context,
				`/api/v1/projects/${project_id}/suggested-pages/`
			);

			if (status && status < 400) {
				return {
					success: true,
					message: 'Suggested pages retrieved successfully',
					data: data?.results || data || []
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to retrieve suggested pages';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error retrieving suggested pages: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});
