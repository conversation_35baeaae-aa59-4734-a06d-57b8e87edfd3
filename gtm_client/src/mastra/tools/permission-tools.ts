/**
 * Ma<PERSON> Tools for Permission Management
 *
 * These tools provide permission checking and user permission management.
 * Each tool handles authentication via user context and returns structured responses.
 */

import { createTool } from '@mastra/core';
import { z } from 'zod';
import { postJson, getJson } from '../lib/backend-client';

/**
 * Check if user has permission for a specific action on a resource
 */
export const checkPermissionTool = createTool({
	id: 'checkPermission',
	description: 'Check if user has permission for a specific action on a resource',
	inputSchema: z.object({
		resource_type: z.string().min(1),
		resource_id: z.number().positive(),
		action: z.string().min(1),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z
			.object({
				resource_type: z.string(),
				resource_id: z.number(),
				action: z.string(),
				allowed: z.boolean()
			})
			.optional(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, resource_type, resource_id, action } = input;

			const payload = {
				resource_type,
				resource_id,
				action
			};

			const [status, data] = await postJson(user_context, '/api/v1/permissions/check/', payload);

			if (status && status < 400) {
				return {
					success: true,
					message: 'Permission check completed',
					data: data
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to check permission';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error checking permission: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});

/**
 * Get all permissions for a user
 */
export const getUserPermissionsTool = createTool({
	id: 'getUserPermissions',
	description: 'Get all permissions for a user',
	inputSchema: z.object({
		user_id: z.number().optional(),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z
			.array(
				z.object({
					resource_type: z.string(),
					resource_id: z.number(),
					action: z.string(),
					allowed: z.boolean()
				})
			)
			.optional(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, user_id } = input;

			const params: Record<string, any> = {};
			if (user_id) {
				params.user_id = user_id;
			}

			const [status, data] = await postJson(user_context, '/api/v1/permissions/user/', params);

			if (status && status < 400) {
				return {
					success: true,
					message: 'User permissions retrieved successfully',
					data: data?.permissions || data || []
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to get user permissions';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error getting user permissions: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});

/**
 * Check workspace permissions for current user
 */
export const checkWorkspacePermissionTool = createTool({
	id: 'checkWorkspacePermission',
	description: 'Check if user has permission for a specific action on a workspace',
	inputSchema: z.object({
		workspace_id: z.number().positive(),
		action: z.enum(['read', 'write', 'delete', 'manage']),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z
			.object({
				workspace_id: z.number(),
				action: z.string(),
				allowed: z.boolean()
			})
			.optional(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, workspace_id, action } = input;

			const payload = {
				resource_type: 'workspace',
				resource_id: workspace_id,
				action
			};

			const [status, data] = await postJson(user_context, '/api/v1/permissions/check/', payload);

			if (status && status < 400) {
				return {
					success: true,
					message: 'Workspace permission check completed',
					data: {
						workspace_id,
						action,
						allowed: data?.allowed || false
					}
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to check workspace permission';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error checking workspace permission: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});

/**
 * Check project permissions for current user
 */
export const checkProjectPermissionTool = createTool({
	id: 'checkProjectPermission',
	description: 'Check if user has permission for a specific action on a project',
	inputSchema: z.object({
		project_id: z.number().positive(),
		action: z.enum(['read', 'write', 'delete', 'manage']),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z
			.object({
				project_id: z.number(),
				action: z.string(),
				allowed: z.boolean()
			})
			.optional(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, project_id, action } = input;

			const payload = {
				resource_type: 'project',
				resource_id: project_id,
				action
			};

			const [status, data] = await postJson(user_context, '/api/v1/permissions/check/', payload);

			if (status && status < 400) {
				return {
					success: true,
					message: 'Project permission check completed',
					data: {
						project_id,
						action,
						allowed: data?.allowed || false
					}
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to check project permission';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error checking project permission: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});

/**
 * Get user's role in an organization
 */
export const getUserOrganizationRoleTool = createTool({
	id: 'getUserOrganizationRole',
	description: "Get user's role in an organization",
	inputSchema: z.object({
		organization_id: z.number().positive(),
		user_id: z.number().optional(),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z
			.object({
				organization_id: z.number(),
				user_id: z.number(),
				role: z.string(),
				permissions: z.array(z.string())
			})
			.optional(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, organization_id, user_id } = input;

			const url = user_id
				? `/api/v1/organizations/${organization_id}/users/${user_id}/role/`
				: `/api/v1/organizations/${organization_id}/my-role/`;

			const [status, data] = await getJson(user_context, url);

			if (status && status < 400) {
				return {
					success: true,
					message: 'User organization role retrieved successfully',
					data: data
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to get user organization role';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error getting user organization role: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});
