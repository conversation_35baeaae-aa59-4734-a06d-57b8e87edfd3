/**
 * Ma<PERSON> Tools for Project Management
 *
 * These tools provide project CRUD operations and integrate with the Django backend.
 * Each tool handles authentication via user context and returns structured responses.
 */

import { createTool } from '@mastra/core';
import { z } from 'zod';
import { post<PERSON><PERSON>, get<PERSON><PERSON>, put<PERSON><PERSON>, delete<PERSON><PERSON> } from '../lib/backend-client';

/**
 * Create a new project
 */
export const createProjectTool = createTool({
	id: 'createProject',
	description: 'Create a new project in a workspace',
	inputSchema: z.object({
		name: z.string().min(3).max(100),
		description: z.string().optional(),
		workspace_id: z.number(),
		project_type_id: z.number(),
		project_platform_id: z.number(),
		project_purpose_id: z.number().optional(),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z
			.object({
				id: z.number(),
				name: z.string(),
				description: z.string().optional(),
				workspace_id: z.number(),
				project_type: z.object({
					id: z.number(),
					name: z.string()
				}),
				project_platform: z.object({
					id: z.number(),
					name: z.string()
				}),
				created_at: z.string(),
				updated_at: z.string()
			})
			.optional(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, ...projectData } = input;

			const [status, data] = await postJson(user_context, '/api/v1/projects/', projectData);

			if (status && status < 400) {
				return {
					success: true,
					message: 'Project created successfully',
					data: data
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to create project';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error creating project: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});

/**
 * List projects in a workspace
 */
export const listProjectsTool = createTool({
	id: 'listProjects',
	description: 'List all projects in a workspace',
	inputSchema: z.object({
		workspace_id: z.number(),
		search: z.string().optional(),
		project_type_id: z.number().optional(),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z
			.array(
				z.object({
					id: z.number(),
					name: z.string(),
					description: z.string().optional(),
					workspace_id: z.number(),
					project_type: z.object({
						id: z.number(),
						name: z.string()
					}),
					project_platform: z.object({
						id: z.number(),
						name: z.string()
					}),
					created_at: z.string(),
					updated_at: z.string()
				})
			)
			.optional(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, workspace_id, search, project_type_id } = input;

			// Build query parameters
			const params = new URLSearchParams();
			if (search) params.append('search', search);
			if (project_type_id) params.append('project_type_id', project_type_id.toString());

			const url = `/api/v1/workspaces/${workspace_id}/projects/${params.toString() ? '?' + params.toString() : ''}`;

			const [status, data] = await getJson(user_context, url);

			if (status && status < 400) {
				return {
					success: true,
					message: 'Projects retrieved successfully',
					data: data?.results || data || []
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to retrieve projects';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error retrieving projects: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});

/**
 * Get a specific project
 */
export const getProjectTool = createTool({
	id: 'getProject',
	description: 'Get details of a specific project',
	inputSchema: z.object({
		project_id: z.number(),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z
			.object({
				id: z.number(),
				name: z.string(),
				description: z.string().optional(),
				workspace_id: z.number(),
				project_type: z.object({
					id: z.number(),
					name: z.string()
				}),
				project_platform: z.object({
					id: z.number(),
					name: z.string()
				}),
				created_at: z.string(),
				updated_at: z.string()
			})
			.optional(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, project_id } = input;

			const [status, data] = await getJson(user_context, `/api/v1/projects/${project_id}/`);

			if (status && status < 400) {
				return {
					success: true,
					message: 'Project retrieved successfully',
					data: data
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to retrieve project';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error retrieving project: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});

/**
 * Update a project
 */
export const updateProjectTool = createTool({
	id: 'updateProject',
	description: 'Update an existing project',
	inputSchema: z.object({
		project_id: z.number(),
		updates: z.record(z.string(), z.any()),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z
			.object({
				id: z.number(),
				name: z.string(),
				description: z.string().optional(),
				workspace_id: z.number(),
				project_type: z.object({
					id: z.number(),
					name: z.string()
				}),
				project_platform: z.object({
					id: z.number(),
					name: z.string()
				}),
				created_at: z.string(),
				updated_at: z.string()
			})
			.optional(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, project_id, updates } = input;

			const [status, data] = await putJson(
				user_context,
				`/api/v1/projects/${project_id}/`,
				updates
			);

			if (status && status < 400) {
				return {
					success: true,
					message: 'Project updated successfully',
					data: data
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to update project';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error updating project: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});

/**
 * Delete a project
 */
export const deleteProjectTool = createTool({
	id: 'deleteProject',
	description: 'Delete a project',
	inputSchema: z.object({
		project_id: z.number(),
		user_context: z.any().optional()
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		error: z.string().optional()
	}),
	execute: async ({ input }) => {
		try {
			const { user_context, project_id } = input;

			const [status, data] = await deleteJson(user_context, `/api/v1/projects/${project_id}/`);

			if (status && status < 400) {
				return {
					success: true,
					message: 'Project deleted successfully'
				};
			} else {
				const errorMsg = data?.detail || data?.message || 'Failed to delete project';
				return {
					success: false,
					message: errorMsg,
					error: errorMsg
				};
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : 'Unknown error';
			return {
				success: false,
				message: `Error deleting project: ${errorMsg}`,
				error: errorMsg
			};
		}
	}
});
