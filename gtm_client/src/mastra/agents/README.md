# Mastra Agents

This directory contains Mastra agent definitions for the GTM Mixer application.

## Workspace Agent

**File:** `workspace-agent.ts`

The workspace agent handles all workspace-related operations.

### Tools

1. **createWorkspace**
   - Create a new workspace
   - Parameters: `title`, `description` (optional), `organization_id` (optional)
   - Returns: Created workspace data

2. **listWorkspaces**
   - List workspaces with optional filtering
   - Parameters: `organization_id` (optional), `title` (optional)
   - Returns: Array of workspaces

3. **updateWorkspace**
   - Update an existing workspace
   - Parameters: `workspace_id`, `updates` (object with fields to update)
   - Returns: Updated workspace data

4. **deleteWorkspace**
   - Delete a workspace by ID
   - Parameters: `workspace_id`
   - Returns: Deletion confirmation

5. **getWorkspace**
   - Get a specific workspace by ID
   - Parameters: `workspace_id`
   - Returns: Workspace data

### Usage

#### Via API Endpoint

```typescript
// Natural language query
const response = await fetch('/api/mastra/workspace', {
	method: 'POST',
	headers: { 'Content-Type': 'application/json' },
	body: JSON.stringify({
		message: 'List all workspaces in my organization'
	})
});

// Direct action
const response = await fetch('/api/mastra/workspace', {
	method: 'POST',
	headers: { 'Content-Type': 'application/json' },
	body: JSON.stringify({
		action: 'list',
		parameters: {}
	})
});
```

#### Programmatically

```typescript
import { mastra } from '$mastra';

const agent = mastra.getAgent('workspaceAgent');
const result = await agent.generate('Create a workspace called "Marketing"');
```

### Authentication

The workspace agent automatically uses the authenticated user's context from SvelteKit's `locals.user`. This includes:

- `user_id` - User ID
- `organization_id` - Current organization ID
- `user_email` - User email

These are passed to the Django backend for authentication and authorization.

### Testing

Visit `/test-mastra-workspace` to test the workspace agent with a simple UI.

## Future Agents

- **Project Agent** - Project management operations
- **Page Agent** - Page and URL rule management
- **Permission Agent** - Permission checking and management
- **Master Agent** - Orchestrates and delegates to specialized agents
