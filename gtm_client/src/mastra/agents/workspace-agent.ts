/**
 * Workspace Agent for Mastra
 *
 * Handles workspace management and organization operations.
 * Migrated from FastMCP workspace_agent.
 */

import { Agent } from '@mastra/core';
import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { openai } from '@ai-sdk/openai';
import { postJson, getJson } from '$mastra/lib/backend-client';
import { normalizeWorkspacePayload } from '$mastra/lib/tool-helpers';
import type { UserContext } from '$mastra/lib/types';

// ============================================================================
// Tool Definitions
// ============================================================================

/**
 * Create a new workspace
 */
const createWorkspaceTool = {
	id: 'create-workspace',
	description: 'Create a new workspace in the organization',
	parameters: z.object({
		title: z.string().min(3).max(50).describe('The workspace title (3-50 characters)'),
		description: z.string().optional().describe('Optional workspace description'),
		organization_id: z
			.number()
			.optional()
			.describe("Optional organization ID (will use user's org if not provided)"),
		user_context: z.any().optional().describe('User context for authentication')
	}),
	execute: async ({
		title,
		description = '',
		organization_id,
		user_context
	}: {
		title: string;
		description?: string;
		organization_id?: number;
		user_context?: UserContext;
	}) => {
		try {
			// Build payload
			const payload: any = { title };
			if (description) {
				payload.description = description;
			}
			if (organization_id) {
				payload.organization_id = organization_id;
			} else if (user_context?.organization_id) {
				payload.organization_id = user_context.organization_id;
			}

			// Call Django API
			const result = await postJson<any>('/api/v1/workspaces/new/', payload, user_context);

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);
				return {
					success: true,
					status: 'success',
					workspace: normalized.workspace || normalized,
					raw: result.data
				};
			} else {
				return {
					success: false,
					status: 'error',
					error: result.error,
					raw: result.data
				};
			}
		} catch (error) {
			return {
				success: false,
				status: 'error',
				error: error instanceof Error ? error.message : 'Unknown error'
			};
		}
	}
};

/**
 * List workspaces with optional filtering
 */
const listWorkspacesTool = {
	id: 'list-workspaces',
	description: 'List workspaces with optional filtering by organization or title',
	parameters: z.object({
		organization_id: z.number().optional().describe('Optional organization ID filter'),
		title: z.string().optional().describe('Optional title filter'),
		user_context: z.any().optional().describe('User context for authentication')
	}),
	execute: async ({
		organization_id,
		title,
		user_context
	}: {
		organization_id?: number;
		title?: string;
		user_context?: UserContext;
	}) => {
		try {
			// Build query parameters
			let path = '/api/v1/workspaces/';
			const params: string[] = [];

			const orgId = organization_id || user_context?.organization_id;
			if (orgId) {
				params.push(`organization_id=${orgId}`);
			}
			if (title) {
				params.push(`title=${encodeURIComponent(title)}`);
			}

			if (params.length > 0) {
				path += '?' + params.join('&');
			}

			// Call Django API
			const result = await getJson<any>(path, user_context);

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);
				return {
					success: true,
					message: 'Workspaces retrieved successfully',
					data: normalized
				};
			} else {
				return {
					success: false,
					message: `Error listing workspaces: ${result.error}`,
					data: result.data
				};
			}
		} catch (error) {
			return {
				success: false,
				message: `Error listing workspaces: ${error instanceof Error ? error.message : 'Unknown error'}`
			};
		}
	}
};

/**
 * Update an existing workspace
 */
const updateWorkspaceTool = {
	id: 'update-workspace',
	description: 'Update an existing workspace by ID',
	parameters: z.object({
		workspace_id: z.number().describe('The ID of the workspace to update'),
		updates: z.record(z.any()).describe('Dictionary of fields to update'),
		user_context: z.any().optional().describe('User context for authentication')
	}),
	execute: async ({
		workspace_id,
		updates,
		user_context
	}: {
		workspace_id: number;
		updates: Record<string, any>;
		user_context?: UserContext;
	}) => {
		try {
			if (!updates || Object.keys(updates).length === 0) {
				return {
					success: false,
					message: 'Error: No updates provided'
				};
			}

			// Call Django API
			const result = await postJson<any>(
				`/api/v1/workspaces/${workspace_id}/`,
				updates,
				user_context
			);

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);
				return {
					success: true,
					message: 'Workspace updated successfully',
					data: normalized
				};
			} else {
				return {
					success: false,
					message: `Error updating workspace: ${result.error}`,
					data: result.data
				};
			}
		} catch (error) {
			return {
				success: false,
				message: `Error updating workspace: ${error instanceof Error ? error.message : 'Unknown error'}`
			};
		}
	}
};

/**
 * Delete a workspace by ID
 */
const deleteWorkspaceTool = {
	id: 'delete-workspace',
	description: 'Delete a workspace by ID',
	parameters: z.object({
		workspace_id: z.number().describe('The ID of the workspace to delete'),
		user_context: z.any().optional().describe('User context for authentication')
	}),
	execute: async ({
		workspace_id,
		user_context
	}: {
		workspace_id: number;
		user_context?: UserContext;
	}) => {
		try {
			// Call Django API (using POST to /delete/ endpoint)
			const result = await postJson<any>(
				`/api/v1/workspaces/${workspace_id}/delete/`,
				{},
				user_context
			);

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);
				return {
					success: true,
					message: 'Workspace deleted successfully',
					data: normalized
				};
			} else {
				return {
					success: false,
					message: `Error deleting workspace: ${result.error}`,
					data: result.data
				};
			}
		} catch (error) {
			return {
				success: false,
				message: `Error deleting workspace: ${error instanceof Error ? error.message : 'Unknown error'}`
			};
		}
	}
};

/**
 * Get a specific workspace by ID
 */
const getWorkspaceTool = {
	id: 'get-workspace',
	description: 'Get a specific workspace by ID',
	parameters: z.object({
		workspace_id: z.number().describe('The ID of the workspace to retrieve'),
		user_context: z.any().optional().describe('User context for authentication')
	}),
	execute: async ({
		workspace_id,
		user_context
	}: {
		workspace_id: number;
		user_context?: UserContext;
	}) => {
		try {
			// Call Django API (using POST to /get/ endpoint)
			const result = await postJson<any>(
				`/api/v1/workspaces/${workspace_id}/get/`,
				{},
				user_context
			);

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);
				return {
					success: true,
					message: 'Workspace retrieved successfully',
					data: normalized
				};
			} else {
				return {
					success: false,
					message: `Error retrieving workspace: ${result.error}`,
					data: result.data
				};
			}
		} catch (error) {
			return {
				success: false,
				message: `Error retrieving workspace: ${error instanceof Error ? error.message : 'Unknown error'}`
			};
		}
	}
};

// ============================================================================
// Agent Definition
// ============================================================================

export const workspaceAgent = new Agent({
	name: 'Weather Agent',
	instructions: `You are a workspace management assistant. You help users create, list, update, delete, and retrieve workspaces.

Key capabilities:
- Create new workspaces with title and optional description
- List all workspaces or filter by organization/title
- Update workspace properties (title, description)
- Delete workspaces by ID
- Retrieve specific workspace details by ID

Always use the user's organization context when available. Be helpful and provide clear feedback about operations.`,
	model: openai('gpt-4o-mini'),
	tools: {
		createWorkspace: createWorkspaceTool,
		listWorkspaces: listWorkspacesTool,
		updateWorkspace: updateWorkspaceTool,
		deleteWorkspace: deleteWorkspaceTool,
		getWorkspace: getWorkspaceTool
	}
});
