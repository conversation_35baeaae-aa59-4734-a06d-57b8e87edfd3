# Mastra Backend Integration Layer

This directory contains utilities for integrating Mastra tools with the Django backend API.

## Files

### `backend-client.ts`

HTTP client for communicating with Django backend. Provides:

- `postJson()` - POST requests
- `getJson()` - GET requests
- `put<PERSON><PERSON>()` - PUT requests
- `deleteJson()` - DELE<PERSON> requests

All functions handle authentication headers (`X-User-Id`, `X-Org-Id`) automatically.

### `tool-helpers.ts`

Common utilities for building Mastra tools:

- `normalizeWorkspacePayload()` - Normalize workspace data from API
- `formatToolResult()` - Format tool responses consistently
- `extractUserContext()` - Extract user context from tool arguments
- `createErrorResponse()` - Create standardized error responses
- `createSuccessResponse()` - Create standardized success responses
- `validateRequiredParams()` - Validate required parameters

### `types.ts`

TypeScript type definitions for:

- User context
- Workspace, Project, Page entities
- Permission types
- Tool response types

## Usage Example

```typescript
import { postJson, formatToolResult, type UserContext } from '../lib/backend-client';

async function createWorkspace(args: {
	title: string;
	description?: string;
	user_context?: UserContext;
}) {
	const result = await postJson(
		'/api/v1/workspaces/new/',
		{
			title: args.title,
			description: args.description
		},
		args.user_context
	);

	return formatToolResult(result, 'Workspace created successfully');
}
```

## Authentication

The backend client automatically includes authentication headers when `UserContext` is provided:

- `X-User-Id` - User ID
- `X-Org-Id` - Organization ID

These headers are used by Django to authenticate and authorize requests.
