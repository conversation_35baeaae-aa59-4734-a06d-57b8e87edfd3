/**
 * Tool Helpers for Mastra
 *
 * Common utilities and patterns for building Mastra tools that interact with Django backend.
 */

import type { UserContext, BackendResponse } from './types';

/**
 * Normalize workspace data from API responses
 * Handles the difference between 'title' and 'name' fields
 */
export function normalizeWorkspacePayload(data: any): any {
	if (typeof data !== 'object' || data === null) {
		return data;
	}

	if (Array.isArray(data)) {
		return data.map(normalizeWorkspacePayload);
	}

	const normalized = { ...data };

	// Handle nested workspace object
	if ('workspace' in normalized) {
		normalized.workspace = normalizeWorkspacePayload(normalized.workspace);
		return normalized;
	}

	// Normalize title/name
	const title = normalized.title;
	const name = normalized.name;
	if (!title && name) {
		normalized.title = name;
	}

	// Ensure description exists
	if (!('description' in normalized) || normalized.description === null) {
		normalized.description = normalized.description || '';
	}

	return normalized;
}

/**
 * Format a tool result for consistent response structure
 */
export function formatToolResult<T = any>(
	result: BackendResponse<T>,
	successMessage?: string
): {
	success: boolean;
	data?: T;
	message?: string;
	error?: string;
} {
	if (!result.success) {
		return {
			success: false,
			error: result.error || 'Request failed',
			data: result.data
		};
	}

	return {
		success: true,
		data: result.data,
		message: successMessage
	};
}

/**
 * Extract user context from Mastra tool arguments
 * Mastra tools receive context as part of their arguments
 */
export function extractUserContext(args: any): UserContext | undefined {
	if (!args) return undefined;

	// Check if user_context is passed directly
	if (args.user_context) {
		return args.user_context;
	}

	// Check if user info is in the args directly
	if (args.user_id || args.organization_id) {
		return {
			user_id: args.user_id,
			organization_id: args.organization_id,
			user_email: args.user_email
		};
	}

	return undefined;
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(error: string | Error, data?: any) {
	return {
		success: false,
		error: error instanceof Error ? error.message : error,
		data
	};
}

/**
 * Create a standardized success response
 */
export function createSuccessResponse<T = any>(data: T, message?: string) {
	return {
		success: true,
		data,
		message
	};
}

/**
 * Validate required parameters for a tool
 */
export function validateRequiredParams(
	args: any,
	requiredParams: string[]
): { valid: boolean; missing?: string[] } {
	const missing: string[] = [];

	for (const param of requiredParams) {
		if (!(param in args) || args[param] === undefined || args[param] === null) {
			missing.push(param);
		}
	}

	if (missing.length > 0) {
		return { valid: false, missing };
	}

	return { valid: true };
}
