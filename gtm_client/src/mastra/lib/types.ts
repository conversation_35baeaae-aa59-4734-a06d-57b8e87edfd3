/**
 * Type Definitions for Mastra Tools
 *
 * Common types used across Mastra agents and tools.
 */

// ============================================================================
// User Context
// ============================================================================

export interface UserContext {
	user_id?: string | number;
	organization_id?: string | number;
	user_email?: string;
	[key: string]: any;
}

// ============================================================================
// Workspace Types
// ============================================================================

export interface Workspace {
	id: number;
	title: string;
	description?: string;
	organization_id?: number;
	created_at?: string;
	updated_at?: string;
}

export interface WorkspaceCreateInput {
	title: string;
	description?: string;
	organization_id?: number;
}

export interface WorkspaceUpdateInput {
	workspace_id: number;
	updates: {
		title?: string;
		description?: string;
	};
}

export interface WorkspaceListFilters {
	organization_id?: number;
	title?: string;
}

// ============================================================================
// Project Types
// ============================================================================

export interface Project {
	id: number;
	name: string;
	description?: string;
	workspace_id: number;
	type?: string;
	platform?: string;
	created_at?: string;
	updated_at?: string;
}

export interface ProjectCreateInput {
	name: string;
	workspace_id: number;
	description?: string;
	type?: string;
	platform?: string;
}

export interface ProjectUpdateInput {
	project_id: number;
	updates: {
		name?: string;
		description?: string;
		type?: string;
		platform?: string;
	};
}

export interface ProjectListFilters {
	workspace_id?: number;
}

// ============================================================================
// Page Types
// ============================================================================

export interface Page {
	id: number;
	name: string;
	project_id: number;
	url_rules?: any[];
	created_at?: string;
	updated_at?: string;
}

export interface PageCreateInput {
	name: string;
	project_id: number;
	url_rules?: any[];
}

export interface PageUpdateInput {
	page_id: number;
	updates: {
		name?: string;
		url_rules?: any[];
	};
}

export interface PageListFilters {
	project_id?: number;
}

// ============================================================================
// Permission Types
// ============================================================================

export interface Permission {
	resource_type: string;
	resource_id: number;
	action: string;
	allowed: boolean;
}

export interface PermissionCheckInput {
	resource_type: 'workspace' | 'project' | 'page';
	resource_id: number;
	action: 'view' | 'edit' | 'delete' | 'manage' | 'publish' | 'approve';
}

export interface UserPermissions {
	user_id: number;
	permissions: Permission[];
}

// ============================================================================
// Tool Response Types
// ============================================================================

export interface ToolResponse<T = any> {
	success: boolean;
	data?: T;
	message?: string;
	error?: string;
}

export interface BackendResponse<T = any> {
	status: number;
	data: T;
	success: boolean;
	error?: string;
}
