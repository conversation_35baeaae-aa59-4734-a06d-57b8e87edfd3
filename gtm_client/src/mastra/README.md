# Mastra Integration for GTM Mixer

This directory contains the Mastra AI agent framework integration for the GTM Mixer application.

## Overview

We've migrated from FastMCP (Python) to Mastra (TypeScript) to better integrate with the SvelteKit frontend. The architecture follows a similar pattern to the original MCP server but uses Mastra's native TypeScript APIs.

## Architecture

### Master Chat Agent Pattern

Instead of exposing individual workspace/project/page agents, we use a **master chat agent** that orchestrates all operations:

```
User → Chat Agent → Workspace Tools
                  → Project Tools (future)
                  → Page Tools (future)
                  → Permission Tools (future)
```

This matches the original `master_agent` pattern from the FastMCP implementation.

### LLM Provider: Google Gemini

We use **Google Gemini** (`gemini-1.5-pro`) as the LLM provider, matching the original MCP server configuration.

## Directory Structure

```
src/mastra/
├── index.ts                    # Main Mastra configuration
├── agents/
│   ├── chat-agent.ts          # Master chat agent (orchestrator)
│   └── workspace-agent.ts     # (deprecated - kept for reference)
├── tools/
│   └── workspace-tools.ts     # Workspace management tools
└── lib/
    ├── backend-client.ts      # Django API client
    ├── tool-helpers.ts        # Common utilities
    └── types.ts               # TypeScript types
```

## Components

### 1. Chat Agent (`agents/chat-agent.ts`)

The main conversational agent that users interact with. It:

- Uses Google Gemini (`gemini-1.5-pro`)
- Has access to all workspace tools
- Provides a natural language interface
- Handles user context and authentication

**Example interactions:**

- "List my workspaces"
- "Create a workspace called Marketing"
- "Update workspace 5 to have description 'Q1 2024 campaigns'"

### 2. Workspace Tools (`tools/workspace-tools.ts`)

Individual tools for workspace operations:

- `createWorkspaceTool` - Create new workspaces
- `listWorkspacesTool` - List/filter workspaces
- `updateWorkspaceTool` - Update workspace properties
- `deleteWorkspaceTool` - Delete workspaces
- `getWorkspaceTool` - Get workspace details

Each tool:

- Uses Zod schemas for input/output validation
- Calls Django backend via `backend-client`
- Handles authentication via user context
- Returns structured responses

### 3. Backend Integration Layer (`lib/`)

**backend-client.ts**

- HTTP client for Django API
- Handles authentication headers (`X-User-Id`, `X-Org-Id`)
- Provides `postJson()`, `getJson()`, `putJson()`, `deleteJson()`

**tool-helpers.ts**

- Common utilities for tools
- `normalizeWorkspacePayload()` - Normalize API responses
- Response formatting helpers

**types.ts**

- TypeScript type definitions
- Workspace, Project, Page, Permission types
- User context and response types

## Configuration

### Environment Variables

Create a `.env` file based on `.env.mastra.example`:

```bash
# Google Gemini Configuration
VITE_GOOGLE_API_KEY='your-google-api-key-here'
VITE_DEFAULT_MODEL='gemini-1.5-pro'
```

### API Endpoint

The chat agent is exposed via SvelteKit server endpoint:

**Endpoint:** `POST /api/mastra/workspace`

**Request:**

```json
{
	"message": "List my workspaces"
}
```

**Response:**

```json
{
  "success": true,
  "result": "Here are your workspaces: ...",
  "toolCalls": [...]
}
```

## Usage

### From Frontend

```typescript
const response = await fetch('/api/mastra/workspace', {
	method: 'POST',
	headers: { 'Content-Type': 'application/json' },
	body: JSON.stringify({
		message: 'List all workspaces in my organization'
	})
});

const data = await response.json();
console.log(data.result); // Agent's response
```

### Programmatically

```typescript
import { mastra } from '$mastra';

const agent = mastra.getAgent('chatAgent');
const result = await agent.generate('Create a workspace called "Marketing"');
console.log(result.text);
```

## Authentication Flow

1. **SvelteKit Request** → User authenticated via hooks
2. **User Context** → Extracted from `locals.user`
3. **API Endpoint** → Passes context to agent
4. **Agent** → Includes context in tool calls
5. **Tools** → Add `X-User-Id` and `X-Org-Id` headers
6. **Django Backend** → Authenticates and authorizes request

## Migration from FastMCP

### What Changed

| FastMCP (Python)               | Mastra (TypeScript)        |
| ------------------------------ | -------------------------- |
| `@main_mcp.tool` decorator     | `createTool()` function    |
| `master_agent` with delegation | `chatAgent` with tools     |
| Python async functions         | TypeScript async functions |
| `httpx` for HTTP               | `fetch` API                |
| FastAPI server                 | SvelteKit server routes    |

### What Stayed the Same

- **Architecture**: Master agent orchestrating specialized tools
- **LLM Provider**: Google Gemini (`gemini-1.5-pro`)
- **Authentication**: User context with `X-User-Id` and `X-Org-Id` headers
- **Backend API**: Same Django REST endpoints
- **Tool Structure**: Create, List, Update, Delete, Get operations

## Future Enhancements

### Phase 4: Additional Tools

- **Project Tools** - Project management operations
- **Page Tools** - Page and URL rule management
- **Permission Tools** - Permission checking

### Phase 5: Advanced Features

- **Streaming Responses** - Real-time agent responses
- **Memory/Context** - Conversation history and context
- **Multi-turn Conversations** - Thread-based conversations
- **Tool Chaining** - Complex multi-step operations

### Phase 6: Integration

- **Replace AI Agent Sidebar** - Use Mastra chat agent instead of MCP server
- **Unified Chat Interface** - Single conversational interface for all operations
- **Real-time Updates** - WebSocket support for live updates

## Testing

Visit `/test-mastra-workspace` to test the chat agent with a simple UI.

## Development

### Running Mastra Dev Server

```bash
cd gtm_client
pnpm run dev:mastra
```

### Building Mastra

```bash
cd gtm_client
pnpm run build:mastra
```

## Troubleshooting

### "Chat agent not found"

Make sure the agent is registered in `src/mastra/index.ts`:

```typescript
export const mastra = new Mastra({
	agents: {
		chatAgent // ← Must be registered
	}
});
```

### "Authentication credentials were not provided"

Ensure user context is being passed from SvelteKit hooks to the API endpoint.

### Tool execution errors

Check that:

1. Django backend is running
2. API_URL environment variable is set correctly
3. User has proper permissions in Django

## Documentation

- [Mastra Documentation](https://mastra.ai/docs)
- [Google AI SDK](https://ai.google.dev/gemini-api/docs)
- [SvelteKit Documentation](https://kit.svelte.dev/docs)
