/**
 * Filter Engine Integration Hook
 *
 * Provides reactive integration between Svelte components and the filter engine
 * for real-time validation and feedback.
 */

import { writable, derived, type Readable } from 'svelte/store';
import { debounce } from 'lodash-es';
import {
	filterEngineAPI,
	type ValidationResult,
	type ValidationIssue,
	type RuleEffect,
	formatValidationIssue,
	formatRuleEffect,
	isConfigurationValid
} from '$lib/api/filterEngine';
import type { GTMPage, GTMEvent, GTMPageRule, GTMEventRule } from '$lib/filter-engine';

export interface FilterEngineState {
	isValidating: boolean;
	lastValidation?: Date;
	validationResult?: ValidationResult;
	firingLogic?: string;
	errors: ValidationIssue[];
	warnings: ValidationIssue[];
	ruleEffects: RuleEffect[];
	isValid: boolean;
}

export interface FilterEngineActions {
	validateConfiguration: (pages: GTMPage[], events?: GTMEvent[]) => Promise<void>;
	validatePages: (pages: GTMPage[]) => Promise<void>;
	validateEvents: (pages: GTMPage[], events: GTMEvent[]) => Promise<void>;
	validateSingleRule: (
		rule: GTMPageRule | GTMEventRule,
		level: 'page' | 'event',
		context?: any
	) => Promise<boolean>;
	generateFiringLogic: (pages: GTMPage[], events?: GTMEvent[]) => Promise<string>;
	getInputHints: (
		targetType: 'url' | 'datalayer',
		operation: string,
		contextPages?: GTMPage[]
	) => Promise<any>;
	clearValidation: () => void;
}

export function createFilterEngineStore() {
	// Core state
	const state = writable<FilterEngineState>({
		isValidating: false,
		errors: [],
		warnings: [],
		ruleEffects: [],
		isValid: true
	});

	// Derived stores for easy access
	const isValidating = derived(state, ($state) => $state.isValidating);
	const isValid = derived(state, ($state) => $state.isValid);
	const errors = derived(state, ($state) => $state.errors);
	const warnings = derived(state, ($state) => $state.warnings);
	const ruleEffects = derived(state, ($state) => $state.ruleEffects);
	const firingLogic = derived(state, ($state) => $state.firingLogic);
	const hasIssues = derived(
		state,
		($state) => $state.errors.length > 0 || $state.warnings.length > 0
	);

	// Debounced validation to avoid excessive API calls
	const debouncedValidation = debounce(
		async (
			pages: GTMPage[],
			events: GTMEvent[] = [],
			validationType: 'full' | 'pages' | 'events' = 'full'
		) => {
			try {
				state.update((s) => ({ ...s, isValidating: true }));

				let result: ValidationResult;

				switch (validationType) {
					case 'pages':
						result = await filterEngineAPI.validatePages(pages);
						break;
					case 'events':
						result = await filterEngineAPI.validateEvents(pages, events);
						break;
					default:
						const response = await filterEngineAPI.validateConfiguration(pages, events);
						result = response.result;
				}

				// Generate firing logic
				let firingLogicText = result.firing_logic;
				if (!firingLogicText && (pages.length > 0 || events.length > 0)) {
					try {
						const logicResponse = await filterEngineAPI.generateFiringLogic(pages, events);
						firingLogicText = logicResponse.firing_logic;
					} catch (error) {
						console.warn('Failed to generate firing logic:', error);
					}
				}

				// Update state
				state.update((s) => ({
					...s,
					isValidating: false,
					lastValidation: new Date(),
					validationResult: result,
					firingLogic: firingLogicText,
					errors: result.issues.filter((issue) => issue.severity === 'error'),
					warnings: result.issues.filter((issue) => issue.severity === 'warning'),
					ruleEffects: result.rule_effects,
					isValid: isConfigurationValid(result)
				}));
			} catch (error) {
				console.error('Validation failed:', error);
				state.update((s) => ({
					...s,
					isValidating: false,
					errors: [
						{
							severity: 'error' as const,
							message: error instanceof Error ? error.message : 'Validation failed'
						}
					],
					isValid: false
				}));
			}
		},
		300
	); // 300ms debounce

	// Actions
	const actions: FilterEngineActions = {
		async validateConfiguration(pages: GTMPage[], events: GTMEvent[] = []) {
			await debouncedValidation(pages, events, 'full');
		},

		async validatePages(pages: GTMPage[]) {
			await debouncedValidation(pages, [], 'pages');
		},

		async validateEvents(pages: GTMPage[], events: GTMEvent[]) {
			await debouncedValidation(pages, events, 'events');
		},

		async validateSingleRule(
			rule: GTMPageRule | GTMEventRule,
			level: 'page' | 'event',
			context?: any
		): Promise<boolean> {
			try {
				const result = await filterEngineAPI.validateSingleRule(
					rule,
					level,
					context?.pages || [],
					context?.events || []
				);
				return result.is_valid;
			} catch (error) {
				console.error('Single rule validation failed:', error);
				return false;
			}
		},

		async generateFiringLogic(pages: GTMPage[], events: GTMEvent[] = []): Promise<string> {
			try {
				const response = await filterEngineAPI.generateFiringLogic(pages, events);

				state.update((s) => ({
					...s,
					firingLogic: response.firing_logic
				}));

				return response.firing_logic;
			} catch (error) {
				console.error('Failed to generate firing logic:', error);
				return 'Failed to generate firing logic';
			}
		},

		async getInputHints(
			targetType: 'url' | 'datalayer',
			operation: string,
			contextPages: GTMPage[] = []
		) {
			try {
				return await filterEngineAPI.getInputHints(targetType, operation, contextPages);
			} catch (error) {
				console.error('Failed to get input hints:', error);
				return { suggestions: [], warnings: [], prefixes: [], suffixes: [] };
			}
		},

		clearValidation() {
			state.set({
				isValidating: false,
				errors: [],
				warnings: [],
				ruleEffects: [],
				isValid: true
			});
		}
	};

	return {
		// State
		subscribe: state.subscribe,

		// Derived stores
		isValidating,
		isValid,
		errors,
		warnings,
		ruleEffects,
		firingLogic,
		hasIssues,

		// Actions
		...actions
	};
}

// Utility functions for component integration
export function useFilterEngine() {
	return createFilterEngineStore();
}

export function formatIssuesForUI(issues: ValidationIssue[]) {
	return issues.map(formatValidationIssue);
}

export function formatEffectsForUI(effects: RuleEffect[]) {
	return effects.map(formatRuleEffect);
}

export function getIssuesForRule(issues: ValidationIssue[], ruleId: string) {
	return issues.filter((issue) => issue.rule_id === ruleId);
}

export function getEffectsForRule(effects: RuleEffect[], ruleId: string) {
	return effects.filter((effect) => effect.rule_id === ruleId);
}

export function hasErrorsForRule(issues: ValidationIssue[], ruleId: string): boolean {
	return issues.some((issue) => issue.rule_id === ruleId && issue.severity === 'error');
}

export function hasWarningsForRule(issues: ValidationIssue[], ruleId: string): boolean {
	return issues.some((issue) => issue.rule_id === ruleId && issue.severity === 'warning');
}

// Real-time validation hook for form inputs
export function useRuleValidation(
	rule: GTMPageRule | GTMEventRule,
	level: 'page' | 'event',
	context?: any
) {
	const validationState = writable<{
		isValidating: boolean;
		isValid: boolean;
		issues: ValidationIssue[];
	}>({
		isValidating: false,
		isValid: true,
		issues: []
	});

	const debouncedValidateRule = debounce(async (currentRule: GTMPageRule | GTMEventRule) => {
		if (!currentRule.value.trim()) {
			validationState.set({ isValidating: false, isValid: true, issues: [] });
			return;
		}

		try {
			validationState.update((s) => ({ ...s, isValidating: true }));

			const result = await filterEngineAPI.validateSingleRule(
				currentRule,
				level,
				context?.pages,
				context?.events
			);

			validationState.set({
				isValidating: false,
				isValid: result.is_valid,
				issues: result.issues
			});
		} catch (error) {
			validationState.set({
				isValidating: false,
				isValid: false,
				issues: [
					{
						severity: 'error',
						message: 'Validation failed'
					}
				]
			});
		}
	}, 500);

	return {
		subscribe: validationState.subscribe,
		validate: (currentRule: GTMPageRule | GTMEventRule) => debouncedValidateRule(currentRule)
	};
}
