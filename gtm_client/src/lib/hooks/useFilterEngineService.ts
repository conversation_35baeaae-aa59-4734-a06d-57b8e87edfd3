/**
 * Svelte Hook for Filter Engine Microservice
 *
 * Provides reactive integration with the Filter Engine microservice
 */

import { writable, derived } from 'svelte/store';
import {
	filterEngineService,
	type ValidationResult,
	type ValidationIssue,
	type GTMPage,
	type GTMEvent
} from '$lib/api/filterEngineService';

interface FilterEngineState {
	isValidating: boolean;
	result: ValidationResult | null;
	lastValidated: Date | null;
	error: string | null;
}

const initialState: FilterEngineState = {
	isValidating: false,
	result: null,
	lastValidated: null,
	error: null
};

export function useFilterEngineService() {
	const state = writable<FilterEngineState>(initialState);

	// Derived stores for easy access
	const isValidating = derived(state, ($state) => $state.isValidating);

	const hasIssues = derived(state, ($state) =>
		$state.result ? $state.result.issues.length > 0 : false
	);

	const errors = derived(state, ($state) =>
		$state.result ? $state.result.issues.filter((issue) => issue.severity === 'error') : []
	);

	const warnings = derived(state, ($state) =>
		$state.result ? $state.result.issues.filter((issue) => issue.severity === 'warning') : []
	);

	const firingLogic = derived(
		state,
		($state) => $state.result?.firing_logic || 'No firing conditions defined'
	);

	const isValid = derived(state, ($state) =>
		$state.result
			? $state.result.is_valid &&
				$state.result.issues.filter((i) => i.severity === 'error').length === 0
			: false
	);

	// Validation functions
	async function validatePages(pages: GTMPage[]) {
		state.update((s) => ({ ...s, isValidating: true, error: null }));

		try {
			const response = await filterEngineService.validatePages(pages);

			if (response.success && response.result) {
				state.update((s) => ({
					...s,
					isValidating: false,
					result: response.result!,
					lastValidated: new Date()
				}));
			} else {
				state.update((s) => ({
					...s,
					isValidating: false,
					error: response.message || 'Validation failed'
				}));
			}
		} catch (error) {
			console.error('Page validation failed:', error);
			state.update((s) => ({
				...s,
				isValidating: false,
				error: error instanceof Error ? error.message : 'Unknown error'
			}));
		}
	}

	async function validateEvents(pages: GTMPage[], events: GTMEvent[]) {
		state.update((s) => ({ ...s, isValidating: true, error: null }));

		try {
			const response = await filterEngineService.validateEvents(pages, events);

			if (response.success && response.result) {
				state.update((s) => ({
					...s,
					isValidating: false,
					result: response.result!,
					lastValidated: new Date()
				}));
			} else {
				state.update((s) => ({
					...s,
					isValidating: false,
					error: response.message || 'Validation failed'
				}));
			}
		} catch (error) {
			console.error('Event validation failed:', error);
			state.update((s) => ({
				...s,
				isValidating: false,
				error: error instanceof Error ? error.message : 'Unknown error'
			}));
		}
	}

	async function validateConfiguration(pages: GTMPage[], events: GTMEvent[] = []) {
		state.update((s) => ({ ...s, isValidating: true, error: null }));

		try {
			const response = await filterEngineService.validateConfiguration({ pages, events });

			if (response.success && response.result) {
				state.update((s) => ({
					...s,
					isValidating: false,
					result: response.result!,
					lastValidated: new Date()
				}));
			} else {
				state.update((s) => ({
					...s,
					isValidating: false,
					error: response.message || 'Validation failed'
				}));
			}
		} catch (error) {
			console.error('Configuration validation failed:', error);
			state.update((s) => ({
				...s,
				isValidating: false,
				error: error instanceof Error ? error.message : 'Unknown error'
			}));
		}
	}

	async function generateFiringLogic(pages: GTMPage[], events: GTMEvent[] = []) {
		try {
			const response = await filterEngineService.generateFiringLogic(pages, events);

			if (response.success && response.result) {
				state.update((s) => ({
					...s,
					result: s.result
						? {
								...s.result,
								firing_logic: response.result!.firing_logic
							}
						: null
				}));
				return response.result.firing_logic;
			}
		} catch (error) {
			console.error('Firing logic generation failed:', error);
		}

		return 'Unable to generate firing logic';
	}

	function clearValidation() {
		state.set(initialState);
	}

	async function checkServiceHealth() {
		try {
			const response = await filterEngineService.healthCheck();
			return response.success;
		} catch (error) {
			console.error('Service health check failed:', error);
			return false;
		}
	}

	return {
		// Stores
		isValidating,
		hasIssues,
		errors,
		warnings,
		firingLogic,
		isValid,

		// Functions
		validatePages,
		validateEvents,
		validateConfiguration,
		generateFiringLogic,
		clearValidation,
		checkServiceHealth,

		// Raw state access if needed
		state
	};
}

// Export the hook as default
export default useFilterEngineService;
