import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { get } from 'svelte/store';
import { organizationStore } from '../stores/organization';
import * as organizationsApi from '../api/organizations';

// Mock the API
vi.mock('../api/organizations', () => ({
	getUserOrganizations: vi.fn(),
	createOrganization: vi.fn(),
	deleteOrganization: vi.fn(),
	organizationAPIService: {
		listOrganizations: vi.fn(),
		getOrganization: vi.fn(),
		createOrganization: vi.fn(),
		updateOrganization: vi.fn(),
		deleteOrganization: vi.fn(),
		listMembers: vi.fn(),
		updateMember: vi.fn(),
		removeMember: vi.fn(),
		inviteUser: vi.fn(),
		listInvitations: vi.fn(),
		acceptInvitation: vi.fn(),
		declineInvitation: vi.fn(),
		cancelInvitation: vi.fn(),
		resendInvitation: vi.fn(),
		getMyOrganizations: vi.fn()
	}
}));

describe('Organization Store', () => {
	const mockOrganizations = [
		{
			id: '123e4567-e89b-12d3-a456-426614174000',
			name: 'Personal',
			type: 'personal',
			owner: {
				id: 'user-1',
				email: '<EMAIL>',
				first_name: 'Test',
				last_name: 'User'
			}
		},
		{
			id: '123e4567-e89b-12d3-a456-426614174001',
			name: 'Team Organization',
			type: 'team',
			owner: {
				id: 'user-1',
				email: '<EMAIL>',
				first_name: 'Test',
				last_name: 'User'
			}
		}
	];

	beforeEach(() => {
		vi.clearAllMocks();

		// Set up default mocks
		vi.mocked(organizationsApi.organizationAPIService.listMembers).mockResolvedValue([]);

		// Reset store state
		organizationStore.reset();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	it('initializes with empty state', () => {
		const state = get(organizationStore);

		expect(state.organizations).toEqual([]);
		expect(state.currentOrganization).toBeNull();
		expect(state.isLoading).toBe(false);
		expect(state.error).toBeNull();
	});

	it('loads organizations successfully', async () => {
		vi.mocked(organizationsApi.organizationAPIService.getMyOrganizations).mockResolvedValue(
			mockOrganizations
		);

		await organizationStore.loadOrganizations();

		const state = get(organizationStore);
		expect(state.organizations).toEqual(mockOrganizations);
		expect(state.isLoading).toBe(false);
		expect(state.error).toBeNull();
	});

	it('handles load organizations error', async () => {
		const errorMessage = 'Failed to load organizations';
		vi.mocked(organizationsApi.organizationAPIService.getMyOrganizations).mockRejectedValue(
			new Error(errorMessage)
		);

		await organizationStore.loadOrganizations();

		const state = get(organizationStore);
		expect(state.organizations).toEqual([]);
		expect(state.isLoading).toBe(false);
		expect(state.error).toBe(errorMessage);
	});

	it('sets loading state during load organizations', async () => {
		let resolvePromise: (value: any) => void;
		const promise = new Promise((resolve) => {
			resolvePromise = resolve;
		});

		vi.mocked(organizationsApi.organizationAPIService.getMyOrganizations).mockReturnValue(promise);

		const loadPromise = organizationStore.loadOrganizations();

		// Check loading state
		const loadingState = get(organizationStore);
		expect(loadingState.isLoading).toBe(true);

		// Resolve the promise
		resolvePromise!(mockOrganizations);

		await loadPromise;

		// Check final state
		const finalState = get(organizationStore);
		expect(finalState.isLoading).toBe(false);
	});

	it('switches to organization', async () => {
		organizationStore._testSetState({ organizations: mockOrganizations });

		await organizationStore.switchOrganization(mockOrganizations[1]);

		const state = get(organizationStore);
		expect(state.currentOrganization).toEqual(mockOrganizations[1]);
	});

	it('handles switch to non-existent organization', async () => {
		organizationStore._testSetState({
			organizations: mockOrganizations,
			currentOrganization: mockOrganizations[0]
		});

		// Try to find non-existent organization
		const nonExistentOrg = mockOrganizations.find((org) => org.id === 'non-existent-id');

		// Since the organization doesn't exist, we shouldn't call switchOrganization
		// The current organization should remain unchanged
		const state = get(organizationStore);
		expect(state.currentOrganization).toEqual(mockOrganizations[0]);
	});

	it('creates organization successfully', async () => {
		const newOrganization = {
			id: '123e4567-e89b-12d3-a456-426614174002',
			name: 'New Team',
			type: 'team',
			owner: {
				id: 'user-1',
				email: '<EMAIL>',
				first_name: 'Test',
				last_name: 'User'
			}
		};

		vi.mocked(organizationsApi.organizationAPIService.createOrganization).mockResolvedValue(
			newOrganization
		);

		organizationStore._testSetState({ organizations: mockOrganizations });

		const result = await organizationStore.createOrganization('New Team');

		expect(result).toEqual(newOrganization);

		const state = get(organizationStore);
		expect(state.organizations).toContain(newOrganization);
		expect(state.currentOrganization).toEqual(newOrganization);
	});

	it('handles create organization error', async () => {
		const errorMessage = 'Failed to create organization';
		vi.mocked(organizationsApi.organizationAPIService.createOrganization).mockRejectedValue(
			new Error(errorMessage)
		);

		organizationStore._testSetState({ organizations: mockOrganizations });

		await expect(organizationStore.createOrganization('New Team')).rejects.toThrow(errorMessage);

		const state = get(organizationStore);
		expect(state.organizations).toEqual(mockOrganizations); // Unchanged
	});

	it('deletes organization successfully', async () => {
		vi.mocked(organizationsApi.organizationAPIService.deleteOrganization).mockResolvedValue({
			transferred_workspaces: 2,
			message: 'Organization deleted'
		});

		organizationStore._testSetState({
			organizations: mockOrganizations,
			currentOrganization: mockOrganizations[1]
		});

		const result = await organizationStore.deleteOrganization(mockOrganizations[1].id);

		expect(result).toEqual({ transferred_workspaces: 2, message: 'Organization deleted' });

		const state = get(organizationStore);
		expect(state.organizations).not.toContain(mockOrganizations[1]);
		expect(state.organizations).toContain(mockOrganizations[0]); // Personal org remains
		expect(state.currentOrganization).toEqual(mockOrganizations[0]); // Switched to personal
	});

	it('handles delete organization error', async () => {
		const errorMessage = 'Failed to delete organization';
		vi.mocked(organizationsApi.organizationAPIService.deleteOrganization).mockRejectedValue(
			new Error(errorMessage)
		);

		organizationStore._testSetState({
			organizations: mockOrganizations,
			currentOrganization: mockOrganizations[1]
		});

		await expect(organizationStore.deleteOrganization(mockOrganizations[1].id)).rejects.toThrow(
			errorMessage
		);

		const state = get(organizationStore);
		expect(state.organizations).toEqual(mockOrganizations); // Unchanged
		expect(state.currentOrganization).toEqual(mockOrganizations[1]); // Unchanged
	});

	it('switches to personal organization after deleting current team organization', async () => {
		vi.mocked(organizationsApi.organizationAPIService.deleteOrganization).mockResolvedValue({
			transferred_workspaces: 0,
			message: 'Organization deleted'
		});

		organizationStore._testSetState({
			organizations: mockOrganizations,
			currentOrganization: mockOrganizations[1]
		});

		await organizationStore.deleteOrganization(mockOrganizations[1].id);

		const state = get(organizationStore);
		expect(state.currentOrganization).toEqual(mockOrganizations[0]); // Personal org
	});

	it('does not switch organization after deleting non-current organization', async () => {
		const anotherTeamOrg = {
			id: '123e4567-e89b-12d3-a456-426614174002',
			name: 'Another Team',
			type: 'team',
			owner: {
				id: 'user-1',
				email: '<EMAIL>',
				first_name: 'Test',
				last_name: 'User'
			}
		};

		vi.mocked(organizationsApi.organizationAPIService.deleteOrganization).mockResolvedValue({
			transferred_workspaces: 0,
			message: 'Organization deleted'
		});

		organizationStore._testSetState({
			organizations: [...mockOrganizations, anotherTeamOrg],
			currentOrganization: mockOrganizations[1]
		});

		await organizationStore.deleteOrganization(anotherTeamOrg.id);

		const state = get(organizationStore);
		expect(state.currentOrganization).toEqual(mockOrganizations[1]); // Unchanged
		expect(state.organizations).not.toContain(anotherTeamOrg);
	});

	it('gets personal organization', () => {
		organizationStore._testSetState({ organizations: mockOrganizations });

		const personalOrg = organizationStore.getPersonalOrganization();

		expect(personalOrg).toEqual(mockOrganizations[0]);
	});

	it('returns null when no personal organization exists', () => {
		organizationStore._testSetState({ organizations: [mockOrganizations[1]] });

		const personalOrg = organizationStore.getPersonalOrganization();

		expect(personalOrg).toBeNull();
	});

	it('gets team organizations', () => {
		organizationStore._testSetState({ organizations: mockOrganizations });

		const teamOrgs = organizationStore.getTeamOrganizations();

		expect(teamOrgs).toEqual([mockOrganizations[1]]);
	});

	it('returns empty array when no team organizations exist', () => {
		organizationStore._testSetState({ organizations: [mockOrganizations[0]] });

		const teamOrgs = organizationStore.getTeamOrganizations();

		expect(teamOrgs).toEqual([]);
	});

	it('clears error when loading organizations', async () => {
		// Set initial error
		organizationStore._testSetState({ error: 'Previous error' });

		vi.mocked(organizationsApi.organizationAPIService.getMyOrganizations).mockResolvedValue(
			mockOrganizations
		);

		await organizationStore.loadOrganizations();

		const state = get(organizationStore);
		expect(state.error).toBeNull();
	});

	it('preserves current organization when reloading if it still exists', async () => {
		organizationStore._testSetState({
			organizations: mockOrganizations,
			currentOrganization: mockOrganizations[1]
		});

		vi.mocked(organizationsApi.organizationAPIService.getMyOrganizations).mockResolvedValue(
			mockOrganizations
		);

		await organizationStore.loadOrganizations();

		const state = get(organizationStore);
		expect(state.currentOrganization).toEqual(mockOrganizations[1]);
	});

	it('clears current organization when reloading if it no longer exists', async () => {
		organizationStore._testSetState({
			organizations: mockOrganizations,
			currentOrganization: mockOrganizations[1]
		});

		// Return only personal organization
		vi.mocked(organizationsApi.organizationAPIService.getMyOrganizations).mockResolvedValue([
			mockOrganizations[0]
		]);

		await organizationStore.loadOrganizations();

		const state = get(organizationStore);
		expect(state.currentOrganization).toEqual(mockOrganizations[0]); // Auto-selected personal org
	});
});
