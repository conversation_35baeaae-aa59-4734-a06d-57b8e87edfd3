import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/svelte';
import { get } from 'svelte/store';
import TeamsPage from '../../routes/(dashboard)/teams/+page.svelte';
import { organizationStore } from '../stores/organization';
import * as organizationsApi from '../api/organizations';

// Mock the API
vi.mock('../api/organizations', () => ({
	getUserOrganizations: vi.fn(),
	getOrganizationMembers: vi.fn(),
	inviteUser: vi.fn(),
	listInvitations: vi.fn(),
	cancelInvitation: vi.fn(),
	resendInvitation: vi.fn(),
	deleteOrganization: vi.fn(),
	createOrganization: vi.fn()
}));

// Mock the organization store
vi.mock('../stores/organization', () => ({
	organizationStore: {
		subscribe: vi.fn(),
		switchOrganization: vi.fn(),
		deleteOrganization: vi.fn(),
		addOrganization: vi.fn()
	}
}));

describe('Teams Page', () => {
	const mockOrganizations = [
		{
			id: '123e4567-e89b-12d3-a456-426614174000',
			name: 'Personal',
			type: 'personal',
			owner: {
				id: 'user-1',
				email: '<EMAIL>',
				first_name: 'Test',
				last_name: 'User'
			}
		},
		{
			id: '123e4567-e89b-12d3-a456-426614174001',
			name: 'Team Organization',
			type: 'team',
			owner: {
				id: 'user-1',
				email: '<EMAIL>',
				first_name: 'Test',
				last_name: 'User'
			}
		}
	];

	const mockMembers = [
		{
			id: 'member-1',
			user: {
				id: 'user-2',
				email: '<EMAIL>',
				first_name: 'Team',
				last_name: 'Member'
			},
			role: 'member',
			can_view: true,
			can_create: false,
			can_edit: false,
			can_delete: false,
			joined_at: '2024-01-01T00:00:00Z'
		}
	];

	const mockInvitations = [
		{
			id: 'invitation-1',
			email: '<EMAIL>',
			role: 'member',
			status: 'pending',
			invited_by: {
				id: 'user-1',
				email: '<EMAIL>',
				first_name: 'Test',
				last_name: 'User'
			},
			created_at: '2024-01-01T00:00:00Z',
			expires_at: '2024-01-08T00:00:00Z'
		}
	];

	beforeEach(() => {
		vi.clearAllMocks();

		// Setup default API responses
		vi.mocked(organizationsApi.getUserOrganizations).mockResolvedValue({
			status_code: 200,
			message: 'Success',
			data: mockOrganizations
		});

		vi.mocked(organizationsApi.getOrganizationMembers).mockResolvedValue({
			status_code: 200,
			message: 'Success',
			data: mockMembers
		});

		vi.mocked(organizationsApi.listInvitations).mockResolvedValue({
			status_code: 200,
			message: 'Success',
			data: mockInvitations
		});
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	it('renders teams page with organization list', async () => {
		render(TeamsPage);

		await waitFor(() => {
			expect(screen.getByText('Personal')).toBeInTheDocument();
			expect(screen.getByText('Team Organization')).toBeInTheDocument();
		});
	});

	it('shows members tab content when selected', async () => {
		render(TeamsPage);

		await waitFor(() => {
			expect(screen.getByText('Team Organization')).toBeInTheDocument();
		});

		// Click on members tab
		const membersTab = screen.getByText('Members');
		await fireEvent.click(membersTab);

		await waitFor(() => {
			expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
			expect(screen.getByText('Team Member')).toBeInTheDocument();
		});
	});

	it('shows invitations tab content when selected', async () => {
		render(TeamsPage);

		await waitFor(() => {
			expect(screen.getByText('Team Organization')).toBeInTheDocument();
		});

		// Click on invitations tab
		const invitationsTab = screen.getByText('Invitations');
		await fireEvent.click(invitationsTab);

		await waitFor(() => {
			expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
			expect(screen.getByText('Pending')).toBeInTheDocument();
		});
	});

	it('opens invite dialog when invite button is clicked', async () => {
		render(TeamsPage);

		await waitFor(() => {
			expect(screen.getByText('Team Organization')).toBeInTheDocument();
		});

		// Click invite member button
		const inviteButton = screen.getByText('Invite Member');
		await fireEvent.click(inviteButton);

		await waitFor(() => {
			expect(screen.getByText('Invite Team Member')).toBeInTheDocument();
			expect(screen.getByPlaceholderText('Enter email address')).toBeInTheDocument();
		});
	});

	it('sends invitation when form is submitted', async () => {
		vi.mocked(organizationsApi.inviteUser).mockResolvedValue({
			status_code: 201,
			message: 'Invitation sent',
			data: {
				id: 'new-invitation',
				email: '<EMAIL>',
				role: 'member',
				status: 'pending'
			}
		});

		render(TeamsPage);

		await waitFor(() => {
			expect(screen.getByText('Team Organization')).toBeInTheDocument();
		});

		// Open invite dialog
		const inviteButton = screen.getByText('Invite Member');
		await fireEvent.click(inviteButton);

		await waitFor(() => {
			expect(screen.getByText('Invite Team Member')).toBeInTheDocument();
		});

		// Fill form
		const emailInput = screen.getByPlaceholderText('Enter email address');
		await fireEvent.input(emailInput, { target: { value: '<EMAIL>' } });

		const messageInput = screen.getByPlaceholderText('Optional message...');
		await fireEvent.input(messageInput, { target: { value: 'Welcome to the team!' } });

		// Submit form
		const sendButton = screen.getByText('Send Invitation');
		await fireEvent.click(sendButton);

		await waitFor(() => {
			expect(organizationsApi.inviteUser).toHaveBeenCalledWith(
				'123e4567-e89b-12d3-a456-426614174001',
				{
					email: '<EMAIL>',
					role: 'member',
					message: 'Welcome to the team!'
				}
			);
		});
	});

	it('validates email format in invite form', async () => {
		render(TeamsPage);

		await waitFor(() => {
			expect(screen.getByText('Team Organization')).toBeInTheDocument();
		});

		// Open invite dialog
		const inviteButton = screen.getByText('Invite Member');
		await fireEvent.click(inviteButton);

		await waitFor(() => {
			expect(screen.getByText('Invite Team Member')).toBeInTheDocument();
		});

		// Enter invalid email
		const emailInput = screen.getByPlaceholderText('Enter email address');
		await fireEvent.input(emailInput, { target: { value: 'invalid-email' } });

		// Try to submit
		const sendButton = screen.getByText('Send Invitation');
		await fireEvent.click(sendButton);

		// Should show validation error
		await waitFor(() => {
			expect(screen.getByText('Please enter a valid email address')).toBeInTheDocument();
		});

		// API should not be called
		expect(organizationsApi.inviteUser).not.toHaveBeenCalled();
	});

	it('cancels invitation when cancel button is clicked', async () => {
		vi.mocked(organizationsApi.cancelInvitation).mockResolvedValue({
			status_code: 200,
			message: 'Invitation cancelled',
			data: {}
		});

		render(TeamsPage);

		await waitFor(() => {
			expect(screen.getByText('Team Organization')).toBeInTheDocument();
		});

		// Go to invitations tab
		const invitationsTab = screen.getByText('Invitations');
		await fireEvent.click(invitationsTab);

		await waitFor(() => {
			expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
		});

		// Click cancel button
		const cancelButton = screen.getByText('Cancel');
		await fireEvent.click(cancelButton);

		await waitFor(() => {
			expect(organizationsApi.cancelInvitation).toHaveBeenCalledWith(
				'123e4567-e89b-12d3-a456-426614174001',
				'invitation-1'
			);
		});
	});

	it('resends invitation when resend button is clicked', async () => {
		vi.mocked(organizationsApi.resendInvitation).mockResolvedValue({
			status_code: 200,
			message: 'Invitation resent',
			data: {}
		});

		render(TeamsPage);

		await waitFor(() => {
			expect(screen.getByText('Team Organization')).toBeInTheDocument();
		});

		// Go to invitations tab
		const invitationsTab = screen.getByText('Invitations');
		await fireEvent.click(invitationsTab);

		await waitFor(() => {
			expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
		});

		// Click resend button
		const resendButton = screen.getByText('Resend');
		await fireEvent.click(resendButton);

		await waitFor(() => {
			expect(organizationsApi.resendInvitation).toHaveBeenCalledWith(
				'123e4567-e89b-12d3-a456-426614174001',
				'invitation-1'
			);
		});
	});

	it('shows settings tab for team organizations', async () => {
		render(TeamsPage);

		await waitFor(() => {
			expect(screen.getByText('Team Organization')).toBeInTheDocument();
		});

		// Settings tab should be visible for team organizations
		expect(screen.getByText('Settings')).toBeInTheDocument();
	});

	it('deletes organization when delete button is clicked', async () => {
		vi.mocked(organizationsApi.deleteOrganization).mockResolvedValue({
			status_code: 200,
			message: 'Organization deleted',
			data: {
				transferred_workspaces: 2
			}
		});

		render(TeamsPage);

		await waitFor(() => {
			expect(screen.getByText('Team Organization')).toBeInTheDocument();
		});

		// Go to settings tab
		const settingsTab = screen.getByText('Settings');
		await fireEvent.click(settingsTab);

		await waitFor(() => {
			expect(screen.getByText('Delete Organization')).toBeInTheDocument();
		});

		// Click delete button
		const deleteButton = screen.getByText('Delete Organization');
		await fireEvent.click(deleteButton);

		// Confirm deletion in dialog
		await waitFor(() => {
			expect(
				screen.getByText('Are you sure you want to delete this organization?')
			).toBeInTheDocument();
		});

		const confirmButton = screen.getByText('Delete');
		await fireEvent.click(confirmButton);

		await waitFor(() => {
			expect(organizationsApi.deleteOrganization).toHaveBeenCalledWith(
				'123e4567-e89b-12d3-a456-426614174001'
			);
		});
	});

	it('does not show settings tab for personal organizations', async () => {
		// Mock to return only personal organization
		vi.mocked(organizationsApi.getUserOrganizations).mockResolvedValue({
			status_code: 200,
			message: 'Success',
			data: [mockOrganizations[0]] // Only personal org
		});

		render(TeamsPage);

		await waitFor(() => {
			expect(screen.getByText('Personal')).toBeInTheDocument();
		});

		// Settings tab should not be visible for personal organizations
		expect(screen.queryByText('Settings')).not.toBeInTheDocument();
	});

	it('handles API errors gracefully', async () => {
		vi.mocked(organizationsApi.getUserOrganizations).mockRejectedValue(new Error('API Error'));

		render(TeamsPage);

		await waitFor(() => {
			expect(screen.getByText('Failed to load organizations')).toBeInTheDocument();
		});
	});

	it('shows loading state while fetching data', async () => {
		// Mock delayed response
		vi.mocked(organizationsApi.getUserOrganizations).mockImplementation(
			() =>
				new Promise((resolve) =>
					setTimeout(
						() =>
							resolve({
								status_code: 200,
								message: 'Success',
								data: mockOrganizations
							}),
						100
					)
				)
		);

		render(TeamsPage);

		// Should show loading state initially
		expect(screen.getByText('Loading organizations...')).toBeInTheDocument();

		// Wait for data to load
		await waitFor(() => {
			expect(screen.getByText('Personal')).toBeInTheDocument();
		});
	});
});
