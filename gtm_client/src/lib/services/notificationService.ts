import {
	notificationStore,
	type InvitationNotification,
	type BillingNotification,
	type UrgentNotification,
	type BaseNotification
} from '$lib/stores/notifications';
import { organizationAPIService, type OrganizationInvitation } from '$lib/api/organizations';
import { toast } from 'svelte-sonner';
// import { page } from '$app/state';

class NotificationService {
	// Initialize the service - load existing notifications
	async initialize() {
		await this.loadInvitationNotifications();
		// Add other notification types here
		// await this.loadBillingNotifications();
		// await this.loadUrgentNotifications();
	}

	// Load invitation notifications
	async loadInvitationNotifications() {
		try {
			const invitations = [];
			// await organizationAPIService.getUserPendingInvitations();

			// Clear existing invitation notifications
			notificationStore.clearByType('invitation');

			// Add new invitation notifications
			for (const invitation of invitations) {
				this.addInvitationNotification(invitation);
			}
		} catch (error) {
			console.error('Failed to load invitation notifications:', error);
		}
	}

	// Add invitation notification
	addInvitationNotification(invitation: OrganizationInvitation) {
		const notification: Omit<InvitationNotification, 'id' | 'timestamp' | 'read'> = {
			type: 'invitation',
			priority: 'medium',
			title: 'Organization Invitation',
			message: `${invitation.organization.name} invited you to join as ${invitation.role}`,
			persistent: true,
			actions: [
				{
					id: 'accept',
					label: 'Accept',
					variant: 'default',
					handler: async () => {
						await organizationAPIService.acceptInvitation(invitation.token);
						toast.success(`Successfully joined ${invitation.organization.name}!`);
						// Refresh page to update organization context
						setTimeout(() => window.location.reload(), 1000);
					}
				},
				{
					id: 'decline',
					label: 'Decline',
					variant: 'outline',
					handler: async () => {
						await organizationAPIService.declineInvitation(invitation.token);
						toast.success('Invitation declined');
					}
				}
			],
			metadata: {
				invitationId: invitation.id,
				organizationName: invitation.organization.name,
				role: invitation.role,
				inviterName: `${invitation.invited_by.first_name} ${invitation.invited_by.last_name}`,
				expiresAt: invitation.expires_at,
				token: invitation.token
			}
		};

		return notificationStore.add(notification);
	}

	// Add billing notification
	addBillingNotification(data: {
		amount: number;
		currency: string;
		dueDate: string;
		status: 'overdue' | 'due_soon' | 'failed' | 'requires_action';
		invoiceId?: string;
	}) {
		const notification: Omit<BillingNotification, 'id' | 'timestamp' | 'read'> = {
			type: 'billing',
			priority: data.status === 'overdue' ? 'high' : 'medium',
			title: this.getBillingTitle(data.status),
			message: `${data.currency} ${data.amount} due ${new Date(data.dueDate).toLocaleDateString()}`,
			persistent: true,
			actions: [
				{
					id: 'pay',
					label: 'Pay Now',
					variant: 'default',
					handler: async () => {
						// Navigate to billing page
						window.location.href = '/billing';
					}
				},
				{
					id: 'view',
					label: 'View Details',
					variant: 'outline',
					handler: async () => {
						// Navigate to invoice details
						window.location.href = `/billing/invoices/${data.invoiceId}`;
					}
				}
			],
			metadata: data
		};

		return notificationStore.add(notification);
	}

	// Add urgent notification
	addUrgentNotification(data: {
		title: string;
		message: string;
		actionRequired: boolean;
		deadline?: string;
		severity: 'low' | 'medium' | 'high' | 'critical';
		actions?: Array<{
			label: string;
			variant: 'primary' | 'secondary' | 'destructive' | 'outline';
			handler: () => Promise<void> | void;
		}>;
	}) {
		const notification: Omit<UrgentNotification, 'id' | 'timestamp' | 'read'> = {
			type: 'urgent',
			priority:
				data.severity === 'critical' ? 'critical' : data.severity === 'high' ? 'high' : 'medium',
			title: data.title,
			message: data.message,
			persistent: data.actionRequired,
			actions: data.actions?.map((action, index) => ({
				id: `action-${index}`,
				...action
			})),
			metadata: {
				actionRequired: data.actionRequired,
				deadline: data.deadline,
				severity: data.severity
			}
		};

		return notificationStore.add(notification);
	}

	// Add info notification
	addInfoNotification(data: {
		title: string;
		message: string;
		persistent?: boolean;
		autoHideMs?: number;
		actions?: Array<{
			label: string;
			variant: 'primary' | 'secondary' | 'destructive' | 'outline';
			handler: () => Promise<void> | void;
		}>;
	}) {
		const notification: Omit<BaseNotification, 'id' | 'timestamp' | 'read'> = {
			type: 'info',
			priority: 'low',
			title: data.title,
			message: data.message,
			persistent: data.persistent ?? false,
			autoHideMs: data.autoHideMs,
			actions: data.actions?.map((action, index) => ({
				id: `action-${index}`,
				...action
			}))
		};

		return notificationStore.add(notification);
	}

	// Add success notification
	addSuccessNotification(title: string, message: string, autoHideMs: number = 5000) {
		const notification: Omit<BaseNotification, 'id' | 'timestamp' | 'read'> = {
			type: 'success',
			priority: 'low',
			title,
			message,
			persistent: false,
			autoHideMs
		};

		return notificationStore.add(notification);
	}

	// Add warning notification
	addWarningNotification(title: string, message: string, persistent: boolean = false) {
		const notification: Omit<BaseNotification, 'id' | 'timestamp' | 'read'> = {
			type: 'warning',
			priority: 'medium',
			title,
			message,
			persistent
		};

		return notificationStore.add(notification);
	}

	private getBillingTitle(status: string): string {
		switch (status) {
			case 'overdue':
				return 'Payment Overdue';
			case 'due_soon':
				return 'Payment Due Soon';
			case 'failed':
				return 'Payment Failed';
			case 'requires_action':
				return 'Payment Action Required';
			default:
				return 'Billing Notice';
		}
	}

	// Periodic refresh
	startPeriodicRefresh(intervalMs: number = 5 * 60 * 1000) {
		const interval = setInterval(() => {
			this.loadInvitationNotifications();
		}, intervalMs);

		return () => clearInterval(interval);
	}
}

export const notificationService = new NotificationService();
