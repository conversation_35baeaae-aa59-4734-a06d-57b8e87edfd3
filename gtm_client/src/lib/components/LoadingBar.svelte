<script lang="ts">
	import { beforeNavigate, afterNavigate } from '$app/navigation';
	import { browser } from '$app/environment';
	import { loadingState, startLoading, stopLoading } from '$lib/stores/loading.svelte';

	let progress = $state(0);
	let visible = $state(false);
	let progressInterval: ReturnType<typeof setInterval>;

	// Watch the loading state
	$effect(() => {
		if (loadingState.isLoading) {
			startLoadingAnimation();
		} else {
			stopLoadingAnimation();
		}
	});

	beforeNavigate(() => {
		startLoading();
	});

	afterNavigate(() => {
		stopLoading();
	});

	function startLoadingAnimation() {
		if (!visible) {
			visible = true;
			progress = 0;

			// Simulate progress
			progressInterval = setInterval(() => {
				if (progress < 90) {
					progress += Math.random() * 10;
				}
			}, 100);
		}
	}

	function stopLoadingAnimation() {
		if (progressInterval) {
			clearInterval(progressInterval);
		}

		if (visible) {
			// Complete the progress bar
			progress = 100;

			// Hide after animation completes
			setTimeout(() => {
				visible = false;
				progress = 0;
			}, 200);
		}
	}

	// Global loading functions for manual control
	if (browser) {
		// Extend window interface for loading bar controls
		interface LoadingBarWindow extends Window {
			showLoadingBar?: () => void;
			hideLoadingBar?: () => void;
		}

		const loadingWindow = window as LoadingBarWindow;
		loadingWindow.showLoadingBar = startLoading;
		loadingWindow.hideLoadingBar = stopLoading;
	}
</script>

{#if visible}
	<div
		class="pointer-events-none fixed top-0 right-0 left-0 z-50 h-1 bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-200 ease-out"
		style="width: {progress}%"
	></div>
{/if}

<style>
	/* Optional: Add a subtle glow effect */
	div {
		box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
	}
</style>
