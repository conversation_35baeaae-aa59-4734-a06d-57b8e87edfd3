<script lang="ts">
	import { onMount } from 'svelte';

	// Icons
	import Bo<PERSON> from '@lucide/svelte/icons/bot';
	import User from '@lucide/svelte/icons/user';
	import Send from '@lucide/svelte/icons/send';
	import Loader2 from '@lucide/svelte/icons/loader-2';
	import AlertCircle from '@lucide/svelte/icons/alert-circle';

	// Props
	let {
		initialMessage = '',
		showHeader = true
	}: {
		initialMessage?: string;
		showHeader?: boolean;
	} = $props();

	// State
	let messages = $state<
		Array<{ role: 'user' | 'assistant'; content: string; toolCalls?: any[]; uiActions?: any[] }>
	>([]);
	let input = $state('');
	let loading = $state(false);
	let error = $state<string | null>(null);
	let streamingMessage = $state('');
	let isStreaming = $state(false);
	let isConnected = $state(true);
	let chatContainer: HTMLElement;

	// Get context from page data
	let pageContext = $derived({
		user_id: $page.data.user?.id,
		user_email: $page.data.user?.email,
		organization_id: $page.data.currentOrganizationID,
		current_organization: $page.data.currentOrganization
			? {
					id: $page.data.currentOrganization.id?.toString(),
					name: $page.data.currentOrganization.name,
					role: $page.data.currentOrganization.role
				}
			: undefined,
		current_workspace: $page.data.currentWorkspace
			? {
					id: $page.data.currentWorkspace.id?.toString(),
					name: $page.data.currentWorkspace.name,
					organization_id: $page.data.currentWorkspace.organization_id?.toString()
				}
			: undefined,
		current_project: $page.data.currentProject
			? {
					id: $page.data.currentProject.id?.toString(),
					name: $page.data.currentProject.name,
					workspace_id: $page.data.currentProject.workspace_id?.toString(),
					type: $page.data.currentProject.type,
					platform: $page.data.currentProject.platform
				}
			: undefined
	});

	// Use provided context or derive from page data
	let finalContext = $derived(context || pageContext);

	/**
	 * Send message to Mastra chat agent with streaming support
	 */
	async function sendMessage(messageText: string, useStreaming = true) {
		if (!messageText.trim()) return;

		// Add user message to chat
		messages.push({ role: 'user', content: messageText });
		input = '';
		loading = true;
		error = null;
		streamingMessage = '';
		isStreaming = useStreaming;

		try {
			if (useStreaming) {
				// Streaming response
				const response = await fetch('/api/mastra/chat', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						message: messageText,
						messages: messages, // Send conversation history
						stream: true,
						context: finalContext
					})
				});

				if (!response.ok) {
					throw new Error(`HTTP ${response.status}: ${response.statusText}`);
				}

				const reader = response.body?.getReader();
				const decoder = new TextDecoder();

				if (!reader) {
					throw new Error('No response body');
				}

				let buffer = '';

				while (true) {
					const { done, value } = await reader.read();
					if (done) break;

					buffer += decoder.decode(value, { stream: true });
					const lines = buffer.split('\n');
					buffer = lines.pop() || '';

					for (const line of lines) {
						if (line.startsWith('data: ')) {
							try {
								const data = JSON.parse(line.slice(6));

								if (data.type === 'step') {
									streamingMessage += data.step.text || '';
								} else if (data.type === 'done') {
									messages.push({
										role: 'assistant',
										content: data.result.text,
										toolCalls: data.result.toolCalls,
										uiActions: data.result.uiActions
									});
									streamingMessage = '';
									isStreaming = false;
								} else if (data.type === 'error') {
									throw new Error(data.error);
								}
							} catch (parseError) {
								console.error('Failed to parse streaming data:', parseError);
							}
						}
					}
				}
			} else {
				// Non-streaming response
				const response = await fetch('/api/mastra/chat', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						message: messageText,
						messages: messages, // Send conversation history
						stream: false,
						context: finalContext
					})
				});

				const data = await response.json();

				if (!data.success) {
					throw new Error(data.error || 'Request failed');
				}

				messages.push({
					role: 'assistant',
					content: data.result,
					toolCalls: data.toolCalls,
					uiActions: data.uiActions
				});
			}
		} catch (err) {
			error = err instanceof Error ? err.message : 'Unknown error';
			console.error('Chat error:', err);
		} finally {
			loading = false;
		}
	}

	/**
	 * Handle form submission
	 */
	function handleSubmit(e: Event) {
		e.preventDefault();
		if (input.trim() && !loading) {
			sendMessage(input);
		}
	}

	/**
	 * Handle UI action clicks
	 */
	function handleUIAction(action: any) {
		if (action.type === 'navigate' && action.data?.url) {
			// Navigate to the URL
			window.location.href = action.data.url;
		}
		// Add more action types as needed
	}

	/**
	 * Auto-scroll to bottom
	 */
	function scrollToBottom() {
		if (chatContainer) {
			chatContainer.scrollTop = chatContainer.scrollHeight;
		}
	}

	/**
	 * Send initial message if provided
	 */
	// Auto-scroll when messages change
	$effect(() => {
		if (messages.length > 0) {
			setTimeout(scrollToBottom, 100);
		}
	});

	onMount(() => {
		if (initialMessage) {
			sendMessage(initialMessage);
		}
	});
</script>

<!-- Chat Container -->
<div class="bg-background flex h-full flex-col {showHeader ? 'border-l' : ''}">
	<!-- Header (conditional) -->
	{#if showHeader}
		<div class="bg-muted/50 flex items-center justify-between border-b p-4">
			<div class="flex items-center gap-2">
				<Bot class="text-primary h-5 w-5" />
				<h3 class="font-semibold">GTM Mixer Assistant</h3>
				{#if isConnected}
					<div class="h-2 w-2 rounded-full bg-green-500"></div>
				{:else}
					<div class="h-2 w-2 rounded-full bg-red-500"></div>
				{/if}
			</div>
		</div>
	{/if}

	<!-- Context Display -->
	{#if finalContext && Object.keys(finalContext).length > 0}
		<div class="bg-muted/30 text-muted-foreground border-b px-4 py-2 text-sm">
			<span class="font-medium">Context:</span>
			User: {finalContext.user_email || 'Unknown'} | Org: {finalContext.organization_id || 'None'}
		</div>
	{/if}

	<!-- Messages -->
	<div bind:this={chatContainer} class="min-h-0 flex-1 space-y-4 overflow-y-auto p-4">
		{#each messages as message, index (index)}
			<div class="flex gap-3 {message.role === 'user' ? 'justify-end' : 'justify-start'}">
				{#if message.role === 'assistant'}
					<div class="flex-shrink-0">
						<Bot class="text-primary h-6 w-6" />
					</div>
				{/if}

				<div class="max-w-[80%] {message.role === 'user' ? 'order-2' : ''}">
					<div
						class="rounded-lg p-3 {message.role === 'user'
							? 'bg-primary text-primary-foreground ml-auto'
							: 'bg-muted'}"
					>
						<p class="whitespace-pre-wrap text-sm">{message.content}</p>
						{#if message.toolCalls && message.toolCalls.length > 0}
							<div class="mt-2 space-y-1">
								{#each message.toolCalls as toolCall, toolIndex (toolIndex)}
									<div class="bg-background/50 rounded px-2 py-1 text-xs">
										<span class="font-medium">{toolCall.toolName}</span>
										{#if toolCall.result}
											<span class="text-green-600">✓</span>
										{:else if toolCall.error}
											<span class="text-red-600">✗</span>
										{/if}
									</div>
								{/each}
							</div>
						{/if}

						{#if message.uiActions && message.uiActions.length > 0}
							<div class="mt-3 flex flex-wrap gap-2">
								{#each message.uiActions as action, actionIndex (actionIndex)}
									<button
										class="bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-3 py-1 text-xs transition-colors"
										onclick={() => handleUIAction(action)}
									>
										{action.label}
									</button>
								{/each}
							</div>
						{/if}
					</div>
				</div>

				{#if message.role === 'user'}
					<div class="flex-shrink-0">
						<User class="text-muted-foreground h-6 w-6" />
					</div>
				{/if}
			</div>
		{/each}

		{#if isStreaming && streamingMessage}
			<div class="flex gap-3">
				<Bot class="text-primary h-6 w-6 flex-shrink-0" />
				<div class="bg-muted rounded-lg p-3">
					<p class="whitespace-pre-wrap text-sm">
						{streamingMessage}<span class="animate-pulse">▊</span>
					</p>
				</div>
			</div>
		{/if}

		{#if loading && !isStreaming}
			<div class="flex gap-3">
				<Bot class="text-primary h-6 w-6 flex-shrink-0" />
				<div class="bg-muted rounded-lg p-3">
					<div class="flex items-center gap-2">
						<Loader2 class="h-4 w-4 animate-spin" />
						<span class="text-muted-foreground text-sm">Thinking...</span>
					</div>
				</div>
			</div>
		{/if}

		{#if error}
			<div class="flex gap-3">
				<Bot class="text-primary h-6 w-6 flex-shrink-0" />
				<div class="bg-destructive/10 border-destructive/20 rounded-lg border p-3">
					<div class="text-destructive flex items-center gap-2">
						<AlertCircle class="h-4 w-4" />
						<span class="text-sm">{error}</span>
					</div>
				</div>
			</div>
		{/if}
	</div>

	<!-- Input Area -->
	<div class="border-t p-4">
		<form onsubmit={handleSubmit} class="flex items-end gap-2">
			<textarea
				bind:value={input}
				placeholder="Ask about workspaces, projects, pages..."
				class="textarea textarea-bordered max-h-32 min-h-[2.5rem] flex-1 resize-none overflow-y-auto"
				disabled={loading}
				rows="1"
				oninput={(e) => {
					const target = e.target as HTMLTextAreaElement;
					target.style.height = 'auto';
					target.style.height = Math.min(target.scrollHeight, 128) + 'px';
				}}
				onkeydown={(e) => {
					if (e.key === 'Enter' && !e.shiftKey) {
						e.preventDefault();
						if (input.trim() && !loading) {
							sendMessage(input);
						}
					}
				}}
			></textarea>
			<button
				type="submit"
				disabled={!input.trim() || loading || !isConnected}
				class="bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-3 py-2 transition-colors disabled:cursor-not-allowed disabled:opacity-50"
			>
				{#if loading}
					<Loader2 class="h-4 w-4 animate-spin" />
				{:else}
					<Send class="h-4 w-4" />
				{/if}
			</button>
		</form>
		<div class="mt-2 text-xs opacity-60">
			Try: "List my workspaces" or "Create a workspace called Marketing" • Press Shift+Enter for new
			line
		</div>

		{#if !isConnected}
			<p class="text-destructive mt-1 text-xs">AI Agent is not available</p>
		{/if}
	</div>
</div>
