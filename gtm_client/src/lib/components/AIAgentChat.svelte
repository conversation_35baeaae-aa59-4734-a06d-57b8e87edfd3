<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import {
		aiAgentService
		// type AgentContext,
		// type ChatResponse,
		// type AgentUpdate,
		// type Conversation
	} from '$lib/services/aiAgentService';
	import { toast } from 'svelte-sonner';

	// Icons
	import Send from '@lucide/svelte/icons/send';
	import Bot from '@lucide/svelte/icons/bot';
	import User from '@lucide/svelte/icons/user';
	import Loader2 from '@lucide/svelte/icons/loader-2';
	import AlertCircle from '@lucide/svelte/icons/alert-circle';
	import MessageSquarePlus from '@lucide/svelte/icons/message-square-plus';
	import History from '@lucide/svelte/icons/history';
	import Trash2 from '@lucide/svelte/icons/trash-2';

	// Props
	let {
		isOpen = $bindable(false),
		context = $bindable({}),
		showHeader = true,
		currentConversationId = $bindable<string | undefined>(undefined)
	} = $props();

	// State
	let message = $state('');
	let messages = $state<
		Array<{
			id: string;
			type: 'user' | 'agent';
			content: string;
			timestamp: Date;
			actions?: any[];
			error?: string;
		}>
	>([]);
	let isLoading = $state(false);
	let isConnected = $state(true);
	let chatContainer: HTMLElement;
	let messageInput: HTMLInputElement;
	let conversations = $state<any[]>([]);
	let showConversationList = $state(false);

	// Persist current conversation ID in localStorage
	$effect(() => {
		if (typeof window !== 'undefined') {
			if (currentConversationId) {
				localStorage.setItem('ai-agent-current-conversation', currentConversationId);
			} else {
				localStorage.removeItem('ai-agent-current-conversation');
			}
		}
	});

	// Load persisted conversation ID on mount
	function loadPersistedConversationId() {
		if (typeof window !== 'undefined') {
			const persistedId = localStorage.getItem('ai-agent-current-conversation');
			if (persistedId) {
				currentConversationId = persistedId;
			}
		}
	}

	// Save conversation messages to localStorage as backup
	function saveMessagesToLocalStorage() {
		if (typeof window !== 'undefined' && currentConversationId) {
			const conversationKey = `ai-agent-messages-${currentConversationId}`;
			localStorage.setItem(conversationKey, JSON.stringify(messages));
		}
	}

	// Load conversation messages from localStorage as fallback
	function loadMessagesFromLocalStorage(conversationId: string) {
		if (typeof window !== 'undefined') {
			const conversationKey = `ai-agent-messages-${conversationId}`;
			const savedMessages = localStorage.getItem(conversationKey);
			if (savedMessages) {
				try {
					return JSON.parse(savedMessages);
				} catch (error) {
					console.error('Failed to parse saved messages:', error);
				}
			}
		}
		return [];
	}

	// Real-time updates
	let unsubscribe: (() => void) | undefined;

	// Auto-scroll to bottom
	function scrollToBottom() {
		if (chatContainer) {
			chatContainer.scrollTop = chatContainer.scrollHeight;
		}
	}

	// Handle real-time updates
	function handleUpdate(update) {
		console.log('AI Agent update:', update);

		if (update.type === 'action_completed' && update.action) {
			toast.success(`Action completed: ${update.action.action}`);
		} else if (update.type === 'action_failed' && update.action) {
			toast.error(`Action failed: ${update.action.action}`);
		}
	}

	// Send message to AI Agent
	async function sendMessage(event: Event) {
		event.preventDefault();
		if (!message.trim() || isLoading) return;

		const userMessage = message.trim();
		message = '';

		// Add user message to chat
		const messageId = Date.now().toString();
		messages.push({
			id: messageId,
			type: 'user',
			content: userMessage,
			timestamp: new Date()
		});

		isLoading = true;
		scrollToBottom();

		// Add a placeholder agent message for streaming
		const agentMessageId = (Date.now() + 1).toString();
		messages.push({
			id: agentMessageId,
			type: 'agent',
			content: 'Thinking...',
			timestamp: new Date(),
			actions: [],
			error: null
		});
		scrollToBottom();

		try {
			const response = await aiAgentService.sendMessage(
				userMessage,
				context,
				(chunk: string) => {
					// Update the agent message with streaming content
					const messageIndex = messages.findIndex((m) => m.id === agentMessageId);
					if (messageIndex !== -1) {
						messages[messageIndex].content = chunk;
						scrollToBottom();
					}
				},
				currentConversationId
			);

			// Handle conversation management
			if (!currentConversationId) {
				// This is a new conversation - create it locally
				const conversationTitle =
					userMessage.length > 50 ? userMessage.substring(0, 47) + '...' : userMessage;

				try {
					const createdId = await aiAgentService.createConversation(conversationTitle);
					currentConversationId = createdId;

					// Refresh conversations list
					await loadConversations();
				} catch (error) {
					console.warn('Failed to create conversation entry:', error);
				}
			}

			// Also check if MCP server returned a conversation_id
			if (response.conversation_id && response.conversation_id !== currentConversationId) {
				// Update our local conversation ID to match the server
				currentConversationId = response.conversation_id;
			}

			// Update the final agent response
			const messageIndex = messages.findIndex((m) => m.id === agentMessageId);
			if (messageIndex !== -1) {
				messages[messageIndex] = {
					id: agentMessageId,
					type: 'agent',
					content: response.response || 'No response',
					timestamp: new Date(),
					actions: response.actions || [],
					error: response.error
				};
			}

			// Save messages to localStorage as backup
			saveMessagesToLocalStorage();

			// Update context if provided
			if (response.context_updates) {
				context = { ...context, ...response.context_updates };
			}

			// Show success/error toast
			if (response.success) {
				if (response.actions && response.actions.length > 0) {
					toast.success(`Executed ${response.actions.length} action(s)`);
				}
			} else if (response.error) {
				toast.error(response.error);
			}
		} catch (error) {
			console.error('Chat error:', error);

			// Add error message to chat
			messages.push({
				id: (Date.now() + 1).toString(),
				type: 'agent',
				content: 'Sorry, I encountered an error processing your request. Please try again.',
				timestamp: new Date(),
				error: error instanceof Error ? error.message : 'Unknown error'
			});

			toast.error('Failed to send message');
		} finally {
			isLoading = false;
			scrollToBottom();
		}
	}

	// Handle suggestion click
	function handleSuggestion(suggestion: string) {
		message = suggestion;
		// Create a fake event for sendMessage
		const fakeEvent = new Event('submit');
		sendMessage(fakeEvent);
	}

	// Load conversations
	async function loadConversations() {
		try {
			conversations = await aiAgentService.getConversations();
		} catch (error) {
			console.error('Failed to load conversations:', error);
		}
	}

	// Start new conversation
	async function startNewConversation() {
		try {
			currentConversationId = undefined;
			messages = [];
			showConversationList = false;

			// Clear persisted conversation
			if (typeof window !== 'undefined') {
				localStorage.removeItem('ai-agent-current-conversation');
			}

			// Add welcome message
			messages.push({
				id: 'welcome',
				type: 'agent',
				content: `Hello! I'm your AI assistant for GTM management. I can help you create workspaces, projects, pages, and more. What would you like to do?`,
				timestamp: new Date()
			});

			await loadConversations();
		} catch (error) {
			console.error('Failed to start new conversation:', error);
			toast.error('Failed to start new conversation');
		}
	}

	// Load specific conversation with message history
	async function loadConversation(conversationId: string) {
		try {
			currentConversationId = conversationId;
			messages = [];
			showConversationList = false;

			// Persist the selected conversation
			if (typeof window !== 'undefined') {
				localStorage.setItem('ai-agent-current-conversation', conversationId);
			}

			// Load conversation messages from backend
			try {
				const response = await fetch(`/api/v1/conversations/${conversationId}/messages/`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json'
					}
				});

				if (response.ok) {
					const conversationData = await response.json();

					// Convert backend messages to chat format
					if (conversationData.results && Array.isArray(conversationData.results)) {
						messages = conversationData.results.map((msg: any) => ({
							id: msg.id || Date.now().toString(),
							type: msg.role === 'user' ? 'user' : 'agent',
							content: msg.content,
							timestamp: new Date(msg.timestamp),
							actions: msg.actions_performed || [],
							error: null
						}));
					}
				} else {
					// Fallback to localStorage if backend fails
					const localMessages = loadMessagesFromLocalStorage(conversationId);
					if (localMessages.length > 0) {
						messages = localMessages;
					}
				}
			} catch (messageError) {
				console.error('Failed to load conversation messages:', messageError);
				// Fallback to localStorage
				const localMessages = loadMessagesFromLocalStorage(conversationId);
				if (localMessages.length > 0) {
					messages = localMessages;
				}
			}

			// Add a message indicating the conversation was loaded if no messages
			if (messages.length === 0) {
				messages.push({
					id: 'loaded',
					type: 'agent',
					content: `Conversation loaded. Continue our chat!`,
					timestamp: new Date()
				});
			}

			scrollToBottom();
		} catch (error) {
			console.error('Failed to load conversation:', error);
			toast.error('Failed to load conversation');
		}
	}

	// Delete conversation
	async function deleteConversation(conversationId: string) {
		try {
			await aiAgentService.deleteConversation(conversationId);
			await loadConversations();

			// If we deleted the current conversation, start a new one
			if (currentConversationId === conversationId) {
				// Clear persisted conversation
				if (typeof window !== 'undefined') {
					localStorage.removeItem('ai-agent-current-conversation');
				}
				await startNewConversation();
			}

			toast.success('Conversation deleted');
		} catch (error) {
			console.error('Failed to delete conversation:', error);
			toast.error('Failed to delete conversation');
		}
	}

	// Initialize chat
	onMount(async () => {
		try {
			// Load persisted conversation ID first
			loadPersistedConversationId();

			// Check AI Agent health
			try {
				await aiAgentService.checkHealth();
				isConnected = true;
			} catch (healthError) {
				console.warn('AI Agent health check failed:', healthError);
				isConnected = true; // Continue anyway for now
			}

			// Load conversations
			await loadConversations();

			// If we have a persisted conversation, try to load its messages
			if (currentConversationId) {
				try {
					await loadConversation(currentConversationId);
				} catch (error) {
					console.error('Failed to restore conversation:', error);
					// If restoration fails, start fresh
					currentConversationId = undefined;
					if (typeof window !== 'undefined') {
						localStorage.removeItem('ai-agent-current-conversation');
					}
				}
			}

			// Add welcome message if no current conversation or no messages
			if (!currentConversationId || messages.length === 0) {
				messages.push({
					id: 'welcome',
					type: 'agent',
					content: `Hello! I'm your AI assistant for GTM management. I can help you create workspaces, projects, pages, and more. What would you like to do?`,
					timestamp: new Date()
				});
			}

			// Focus input when opened
			if (isOpen && messageInput) {
				messageInput.focus();
			}
		} catch (error) {
			console.error('Failed to initialize AI Agent:', error);
			isConnected = false;
			toast.error('AI Agent is not available');
		}
	});

	onDestroy(() => {
		if (unsubscribe) {
			unsubscribe();
		}
	});

	// Focus input when chat opens
	$effect(() => {
		if (isOpen && messageInput) {
			setTimeout(() => messageInput.focus(), 100);
		}
	});

	// Auto-scroll when messages change
	$effect(() => {
		if (messages.length > 0) {
			setTimeout(scrollToBottom, 100);
		}
	});

	// Auto-save messages to localStorage when they change
	$effect(() => {
		if (messages.length > 0 && currentConversationId) {
			saveMessagesToLocalStorage();
		}
	});

	// Get suggestions based on context
	// let suggestions = $derived(aiAgentService.getSuggestions(context));
	// let contextDisplay = $derived(aiAgentService.formatContext(context));
</script>

<!-- Chat Container -->
<div class="bg-background flex h-full flex-col {showHeader ? 'border-l' : ''}">
	<!-- Header (conditional) -->
	{#if showHeader}
		<div class="bg-muted/50 flex items-center justify-between border-b p-4">
			<div class="flex items-center gap-2">
				<Bot class="text-primary h-5 w-5" />
				<h3 class="font-semibold">AI Assistant</h3>
				{#if isConnected}
					<div class="h-2 w-2 rounded-full bg-green-500"></div>
				{:else}
					<div class="h-2 w-2 rounded-full bg-red-500"></div>
				{/if}
			</div>
			<button onclick={() => (isOpen = false)} class="text-muted-foreground hover:text-foreground">
				×
			</button>
		</div>
	{/if}

	<!-- Context Display -->
	<!-- {#if contextDisplay !== 'No context'}
		<div class="bg-muted/30 text-muted-foreground border-b px-4 py-2 text-sm">
			<span class="font-medium">Context:</span>
			{contextDisplay}
		</div>
	{/if} -->

	<!-- Conversation Management -->
	<div class="bg-muted/20 border-b px-4 py-2">
		<div class="flex items-center justify-between">
			<div class="flex items-center gap-2">
				<button
					onclick={() => (showConversationList = !showConversationList)}
					class="text-muted-foreground hover:text-foreground flex items-center gap-1 text-sm"
					title="Conversation History"
				>
					<History class="h-4 w-4" />
					History ({conversations.length})
				</button>
			</div>
			<button
				onclick={startNewConversation}
				class="text-muted-foreground hover:text-foreground flex items-center gap-1 text-sm"
				title="New Conversation"
			>
				<MessageSquarePlus class="h-4 w-4" />
				New
			</button>
		</div>

		<!-- Conversation List -->
		{#if showConversationList}
			<div class="mt-2 max-h-32 space-y-1 overflow-y-auto">
				{#each conversations as conversation (conversation.id)}
					<div class="flex items-center justify-between rounded bg-white/50 p-2">
						<button
							onclick={() => loadConversation(conversation.id)}
							class="hover:text-primary flex-1 truncate text-left text-xs {currentConversationId ===
							conversation.id
								? 'text-primary font-medium'
								: 'text-muted-foreground'}"
						>
							{conversation.title || `Conversation ${conversation.id.slice(0, 8)}...`}
						</button>
						<button
							onclick={() => deleteConversation(conversation.id)}
							class="text-muted-foreground hover:text-destructive ml-2"
							title="Delete conversation"
						>
							<Trash2 class="h-3 w-3" />
						</button>
					</div>
				{:else}
					<p class="text-muted-foreground text-xs">No conversations yet</p>
				{/each}
			</div>
		{/if}
	</div>

	<!-- Messages -->
	<div bind:this={chatContainer} class="min-h-0 flex-1 space-y-4 overflow-y-auto p-4">
		{#each messages as msg (msg.id)}
			<div class="flex gap-3 {msg.type === 'user' ? 'justify-end' : 'justify-start'}">
				{#if msg.type === 'agent'}
					<div class="flex-shrink-0">
						<Bot class="text-primary h-6 w-6" />
					</div>
				{/if}

				<div class="max-w-[80%] {msg.type === 'user' ? 'order-2' : ''}">
					<div
						class="rounded-lg p-3 {msg.type === 'user'
							? 'bg-primary text-primary-foreground ml-auto'
							: 'bg-muted'}"
					>
						<p class="text-sm whitespace-pre-wrap">{msg.content}</p>

						{#if msg.error}
							<div class="text-destructive mt-2 flex items-center gap-1">
								<AlertCircle class="h-3 w-3" />
								<span class="text-xs">{msg.error}</span>
							</div>
						{/if}

						{#if msg.actions && msg.actions.length > 0}
							<div class="mt-2 space-y-1">
								{#each msg.actions as action, index (index)}
									<div class="bg-background/50 rounded px-2 py-1 text-xs">
										<span class="font-medium">{action.action}</span>
										{#if action.result}
											<span class="text-green-600">✓</span>
										{:else if action.error}
											<span class="text-red-600">✗</span>
										{/if}
									</div>
								{/each}
							</div>
						{/if}
					</div>

					<div
						class="text-muted-foreground mt-1 text-xs {msg.type === 'user'
							? 'text-right'
							: 'text-left'}"
					>
						{msg.timestamp.toLocaleTimeString()}
					</div>
				</div>

				{#if msg.type === 'user'}
					<div class="flex-shrink-0">
						<User class="text-muted-foreground h-6 w-6" />
					</div>
				{/if}
			</div>
		{/each}

		{#if isLoading}
			<div class="flex gap-3">
				<Bot class="text-primary h-6 w-6 flex-shrink-0" />
				<div class="bg-muted rounded-lg p-3">
					<div class="flex items-center gap-2">
						<Loader2 class="h-4 w-4 animate-spin" />
						<span class="text-muted-foreground text-sm">Thinking...</span>
					</div>
				</div>
			</div>
		{/if}
	</div>

	<!-- Suggestions -->
	<!-- {#if suggestions.length > 0 && messages.length <= 1}
		<div class="bg-muted/30 border-t px-4 py-2">
			<p class="text-muted-foreground mb-2 text-xs">Try asking:</p>
			<div class="flex flex-wrap gap-1">
				{#each suggestions.slice(0, 3) as suggestion}
					<button
						onclick={() => handleSuggestion(suggestion)}
						class="bg-background hover:bg-muted rounded-full border px-2 py-1 text-xs transition-colors"
						disabled={isLoading}
					>
						{suggestion}
					</button>
				{/each}
			</div>
		</div>
	{/if} -->

	<!-- Input -->
	<div class="border-t p-4">
		<form onsubmit={sendMessage} class="flex gap-2">
			<input
				bind:this={messageInput}
				bind:value={message}
				placeholder="Ask me to create a workspace, deploy a project..."
				class="focus:ring-primary flex-1 rounded-md border px-3 py-2 text-sm focus:ring-2 focus:outline-none"
				disabled={isLoading || !isConnected}
			/>
			<button
				type="submit"
				disabled={!message.trim() || isLoading || !isConnected}
				class="bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-3 py-2 transition-colors disabled:cursor-not-allowed disabled:opacity-50"
			>
				{#if isLoading}
					<Loader2 class="h-4 w-4 animate-spin" />
				{:else}
					<Send class="h-4 w-4" />
				{/if}
			</button>
		</form>

		{#if !isConnected}
			<p class="text-destructive mt-1 text-xs">AI Agent is not available</p>
		{/if}
	</div>
</div>
