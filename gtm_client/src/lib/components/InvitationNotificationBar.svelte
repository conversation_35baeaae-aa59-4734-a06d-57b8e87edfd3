<script lang="ts">
	import { onMount } from 'svelte';
	import { organizationAPIService, type OrganizationInvitation } from '$lib/api/organizations';
	import { Button } from '$lib/shad-components/ui/button';
	import { Badge } from '$lib/shad-components/ui/badge';
	import * as Drawer from '$lib/shad-components/ui/drawer';
	import { withLoadingBar } from '$lib/utils/navigation';
	import { toast } from 'svelte-sonner';
	import MailIcon from '@lucide/svelte/icons/mail';
	import CheckIcon from '@lucide/svelte/icons/check';
	import XIcon from '@lucide/svelte/icons/x';
	import ClockIcon from '@lucide/svelte/icons/clock';
	import BuildingIcon from '@lucide/svelte/icons/building';
	import UserIcon from '@lucide/svelte/icons/user';
	import { page } from '$app/state';

	let pendingInvitations = $derived<OrganizationInvitation[]>(page.data.invitations);
	let isVisible = $derived(pendingInvitations.length > 0);
	let isProcessing = $state(false);
	let drawerOpen = $state(false);

	onMount(() => {
		// Listen for custom event to open drawer
		const handleOpenDrawer = () => {
			drawerOpen = true;
		};

		window.addEventListener('openInvitationDrawer', handleOpenDrawer);

		return () => {
			window.removeEventListener('openInvitationDrawer', handleOpenDrawer);
		};
	});

	async function acceptInvitation(invitation: OrganizationInvitation) {
		if (isProcessing) return;

		isProcessing = true;
		try {
			await withLoadingBar(async () => {
				await organizationAPIService.acceptInvitation(invitation.token);
			});

			toast.success(`Successfully joined ${invitation.organization.name}!`);

			// Remove from pending list
			pendingInvitations = pendingInvitations.filter((inv) => inv.id !== invitation.id);

			// Refresh the page to update organization context
			setTimeout(() => {
				window.location.reload();
			}, 1000);
		} catch (error) {
			toast.error(error instanceof Error ? error.message : 'Failed to accept invitation');
		} finally {
			isProcessing = false;
		}
	}

	async function declineInvitation(invitation: OrganizationInvitation) {
		if (isProcessing) return;

		isProcessing = true;
		try {
			await withLoadingBar(async () => {
				await organizationAPIService.declineInvitation(invitation.token);
			});

			toast.success('Invitation declined');

			// Remove from pending list
			pendingInvitations = pendingInvitations.filter((inv) => inv.id !== invitation.id);
		} catch (error) {
			toast.error(error instanceof Error ? error.message : 'Failed to decline invitation');
		} finally {
			isProcessing = false;
		}
	}

	function formatTimeRemaining(expiresAt: string): string {
		const now = new Date();
		const expiry = new Date(expiresAt);
		const diffMs = expiry.getTime() - now.getTime();

		if (diffMs <= 0) return 'Expired';

		const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
		const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

		if (days > 0) return `${days}d ${hours}h remaining`;
		if (hours > 0) return `${hours}h remaining`;

		const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
		return `${minutes}m remaining`;
	}
</script>

<!-- Floating Invitation Button -->
{#if isVisible}
	<div class="fixed right-6 bottom-6 z-50">
		<Button
			onclick={() => (drawerOpen = true)}
			class="relative h-14 w-14 rounded-full bg-blue-600 text-white shadow-lg transition-all duration-200 hover:bg-blue-700 hover:shadow-xl"
			size="icon"
		>
			<MailIcon class="h-6 w-6" />

			<!-- Count Badge -->
			{#if pendingInvitations.length > 0}
				<div
					class="absolute -top-2 -right-2 flex h-6 w-6 items-center justify-center rounded-full bg-red-500 text-xs font-bold text-white"
				>
					{pendingInvitations.length > 9 ? '9+' : pendingInvitations.length}
				</div>
			{/if}
		</Button>
	</div>
{/if}

<!-- Invitation Drawer -->
<Drawer.Root bind:open={drawerOpen}>
	<Drawer.Content class="max-h-[80vh]">
		<Drawer.Header class="text-left">
			<Drawer.Title class="flex items-center gap-2">
				<MailIcon class="h-5 w-5" />
				Pending Invitations
				<Badge variant="secondary" class="ml-2">
					{pendingInvitations.length}
				</Badge>
			</Drawer.Title>
			<Drawer.Description>Review and respond to organization invitations</Drawer.Description>
		</Drawer.Header>

		<div class="max-h-[60vh] overflow-y-auto px-4 pb-4">
			{#if pendingInvitations.length === 0}
				<div class="py-8 text-center">
					<MailIcon class="text-muted-foreground mx-auto mb-4 h-12 w-12" />
					<p class="text-muted-foreground">No pending invitations</p>
				</div>
			{:else}
				<div class="space-y-4">
					{#each pendingInvitations as invitation (invitation.id)}
						<div class="space-y-3 rounded-lg border p-4">
							<div class="flex items-start justify-between">
								<div class="flex-1">
									<div class="mb-2 flex items-center gap-2">
										<BuildingIcon class="text-muted-foreground h-4 w-4" />
										<h3 class="font-semibold">{invitation.organization.name}</h3>
										<Badge variant="outline" class="text-xs">
											{invitation.role}
										</Badge>
									</div>

									<div class="text-muted-foreground space-y-1 text-sm">
										<div class="flex items-center gap-2">
											<UserIcon class="h-3 w-3" />
											<span>
												Invited by {invitation.invited_by.first_name}
												{invitation.invited_by.last_name}
											</span>
										</div>

										<div class="flex items-center gap-2">
											<ClockIcon class="h-3 w-3" />
											<span>{formatTimeRemaining(invitation.expires_at)}</span>
										</div>
									</div>
								</div>
							</div>

							<div class="flex gap-2">
								<Button
									size="sm"
									onclick={() => acceptInvitation(invitation)}
									disabled={isProcessing}
									class="flex-1"
								>
									<CheckIcon class="mr-2 h-4 w-4" />
									Accept
								</Button>

								<Button
									size="sm"
									variant="outline"
									onclick={() => declineInvitation(invitation)}
									disabled={isProcessing}
									class="flex-1"
								>
									<XIcon class="mr-2 h-4 w-4" />
									Decline
								</Button>
							</div>
						</div>
					{/each}
				</div>
			{/if}
		</div>

		<Drawer.Footer>
			<Button onclick={() => (drawerOpen = false)} variant="outline" class="w-full">Close</Button>
		</Drawer.Footer>
	</Drawer.Content>
</Drawer.Root>
