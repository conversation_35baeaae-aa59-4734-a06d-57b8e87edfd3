<script lang="ts">
	import { onMount } from 'svelte';
	import { notificationStore, type Notification } from '$lib/stores/notifications';
	import { Button } from '$lib/shad-components/ui/button';
	import { Badge } from '$lib/shad-components/ui/badge';
	import { Separator } from '$lib/shad-components/ui/separator';
	import { fly, fade } from 'svelte/transition';
	import { clickOutside } from '$lib/utils/clickOutside';

	// Icons
	import BellIcon from '@lucide/svelte/icons/bell';
	import MailIcon from '@lucide/svelte/icons/mail';
	import CreditCardIcon from '@lucide/svelte/icons/credit-card';
	import AlertTriangleIcon from '@lucide/svelte/icons/alert-triangle';
	import InfoIcon from '@lucide/svelte/icons/info';
	import CheckCircleIcon from '@lucide/svelte/icons/check-circle';
	import XIcon from '@lucide/svelte/icons/x';
	import ClockIcon from '@lucide/svelte/icons/clock';
	import BuildingIcon from '@lucide/svelte/icons/building';

	let notifications = $state<Notification[]>([]);
	let isOpen = $state(false);
	let unreadCount = $derived(notificationStore.getUnreadCount(notifications));

	// Subscribe to notifications
	onMount(() => {
		const unsubscribe = notificationStore.subscribe((value) => {
			notifications = value;
		});

		return unsubscribe;
	});

	function getNotificationIcon(type: string) {
		switch (type) {
			case 'invitation':
				return MailIcon;
			case 'billing':
				return CreditCardIcon;
			case 'urgent':
				return AlertTriangleIcon;
			case 'warning':
				return AlertTriangleIcon;
			case 'success':
				return CheckCircleIcon;
			case 'info':
			default:
				return InfoIcon;
		}
	}

	function getNotificationColor(type: string, priority: string) {
		if (priority === 'critical') return 'text-red-600 bg-red-50 border-red-200';
		if (priority === 'high') return 'text-orange-600 bg-orange-50 border-orange-200';

		switch (type) {
			case 'invitation':
				return 'text-blue-600 bg-blue-50 border-blue-200';
			case 'billing':
				return 'text-purple-600 bg-purple-50 border-purple-200';
			case 'urgent':
				return 'text-red-600 bg-red-50 border-red-200';
			case 'warning':
				return 'text-yellow-600 bg-yellow-50 border-yellow-200';
			case 'success':
				return 'text-green-600 bg-green-50 border-green-200';
			case 'info':
			default:
				return 'text-gray-600 bg-gray-50 border-gray-200';
		}
	}

	function formatTimeAgo(date: Date): string {
		const now = new Date();
		const diffMs = now.getTime() - date.getTime();
		const diffMins = Math.floor(diffMs / (1000 * 60));
		const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
		const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

		if (diffMins < 1) return 'Just now';
		if (diffMins < 60) return `${diffMins}m ago`;
		if (diffHours < 24) return `${diffHours}h ago`;
		return `${diffDays}d ago`;
	}

	function togglePanel() {
		isOpen = !isOpen;
		if (isOpen) {
			// Mark all as read when opening
			notificationStore.markAllAsRead();
		}
	}

	function closePanel() {
		isOpen = false;
	}

	async function handleAction(notification: Notification, actionId: string) {
		const action = notification.actions?.find((a) => a.id === actionId);
		if (!action) return;

		try {
			notificationStore.updateActionLoading(notification.id, actionId, true);
			await action.handler();
			// Remove notification after successful action
			notificationStore.remove(notification.id);
		} catch (error) {
			console.error('Action failed:', error);
		} finally {
			notificationStore.updateActionLoading(notification.id, actionId, false);
		}
	}
</script>

<!-- Floating Notification Button -->
<div class="fixed bottom-6 right-6 z-50 hidden">
	<Button
		onclick={togglePanel}
		class="relative h-14 w-14 rounded-full bg-gray-900 text-white shadow-lg transition-all duration-200 hover:bg-gray-800 hover:shadow-xl"
		size="icon"
	>
		<BellIcon class="h-6 w-6" />

		<!-- Unread Count Badge -->
		{#if unreadCount > 0}
			<div
				class="absolute -right-2 -top-2 flex h-6 w-6 items-center justify-center rounded-full bg-red-500 text-xs font-bold text-white"
			>
				{unreadCount > 9 ? '9+' : unreadCount}
			</div>
		{/if}
	</Button>
</div>

<!-- Notification Panel -->
{#if isOpen}
	<div
		class="fixed bottom-24 right-6 z-50 w-96 max-w-[calc(100vw-3rem)]"
		transition:fly={{ y: 20, duration: 200 }}
		use:clickOutside={closePanel}
	>
		<div
			class="rounded-lg border border-gray-200 bg-white shadow-xl dark:border-gray-700 dark:bg-gray-900"
		>
			<!-- Header -->
			<div
				class="flex items-center justify-between border-b border-gray-200 p-4 dark:border-gray-700"
			>
				<h3 class="font-semibold text-gray-900 dark:text-gray-100">Notifications</h3>
				<div class="flex items-center gap-2">
					{#if notifications.length > 0}
						<Badge variant="secondary" class="text-xs">
							{notifications.length}
						</Badge>
					{/if}
					<Button size="icon" variant="ghost" onclick={closePanel} class="h-6 w-6">
						<XIcon class="h-4 w-4" />
					</Button>
				</div>
			</div>

			<!-- Notifications List -->
			<div class="max-h-96 overflow-y-auto">
				{#if notifications.length === 0}
					<div class="p-8 text-center">
						<BellIcon class="mx-auto mb-3 h-12 w-12 text-gray-400" />
						<p class="text-gray-500 dark:text-gray-400">No notifications</p>
					</div>
				{:else}
					<div class="divide-y divide-gray-100 dark:divide-gray-800">
						{#each notifications as notification (notification.id)}
							{@const IconComponent = getNotificationIcon(notification.type)}
							{@const colorClasses = getNotificationColor(notification.type, notification.priority)}

							<div class="p-4 transition-colors hover:bg-gray-50 dark:hover:bg-gray-800/50">
								<div class="flex gap-3">
									<!-- Icon -->
									<div class="mt-1 flex-shrink-0">
										<div
											class="flex h-8 w-8 items-center justify-center rounded-full border {colorClasses}"
										>
											<!-- svelte-ignore svelte_component_deprecated -->
											<IconComponent />
											<!-- <svelte:component this={IconComponent} class="h-4 w-4" /> -->
										</div>
									</div>

									<!-- Content -->
									<div class="min-w-0 flex-1">
										<div class="flex items-start justify-between gap-2">
											<h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
												{notification.title}
											</h4>
											<span class="flex-shrink-0 text-xs text-gray-500 dark:text-gray-400">
												{formatTimeAgo(notification.timestamp)}
											</span>
										</div>

										<p class="mt-1 text-sm text-gray-600 dark:text-gray-300">
											{notification.message}
										</p>

										<!-- Metadata for specific types -->
										{#if notification.type === 'invitation' && notification.metadata}
											<div class="mt-2 space-y-1 text-xs text-gray-500 dark:text-gray-400">
												<div class="flex items-center gap-1">
													<BuildingIcon class="h-3 w-3" />
													<span>{notification.metadata.organizationName}</span>
													<Badge variant="outline" class="ml-1 text-xs">
														{notification.metadata.role}
													</Badge>
												</div>
												<div class="flex items-center gap-1">
													<ClockIcon class="h-3 w-3" />
													<span
														>Expires {new Date(
															notification.metadata.expiresAt
														).toLocaleDateString()}</span
													>
												</div>
											</div>
										{/if}

										<!-- Actions -->
										{#if notification.actions && notification.actions.length > 0}
											<div class="mt-3 flex gap-2">
												{#each notification.actions as action}
													<Button
														size="sm"
														variant={action.variant}
														onclick={() => handleAction(notification, action.id)}
														disabled={action.loading}
														class="h-7 text-xs"
													>
														{action.loading ? 'Loading...' : action.label}
													</Button>
												{/each}
											</div>
										{/if}
									</div>
								</div>
							</div>
						{/each}
					</div>
				{/if}
			</div>

			<!-- Footer -->
			{#if notifications.length > 0}
				<div class="border-t border-gray-200 p-3 dark:border-gray-700">
					<Button
						variant="ghost"
						size="sm"
						onclick={() => notificationStore.clear()}
						class="w-full text-xs"
					>
						Clear All
					</Button>
				</div>
			{/if}
		</div>
	</div>
{/if}
