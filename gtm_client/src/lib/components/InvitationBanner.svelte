<script lang="ts">
	import { organizationAPIService, type OrganizationInvitation } from '$lib/api/organizations';
	import Button from '$lib/shad-components/ui/button/button.svelte';
	import * as Dialog from '$lib/shad-components/ui/dialog';
	import * as Card from '$lib/shad-components/ui/card';
	import Badge from '$lib/shad-components/ui/badge/badge.svelte';
	import XIcon from '@lucide/svelte/icons/x';
	import MailIcon from '@lucide/svelte/icons/mail';
	import UsersIcon from '@lucide/svelte/icons/users';
	import CheckIcon from '@lucide/svelte/icons/check';
	import { toast } from 'svelte-sonner';
	import { invalidateWithLoading } from '$lib/utils/navigation';
	import { page } from '$app/state';

	let pendingInvitations = $derived<OrganizationInvitation[]>(page.data?.personalInvitations ?? []);
	let isLoading = $state(false);
	let showInvitationsDialog = $state(false);
	let isAcceptingInvitation = $state(false);
	let isDecliningInvitation = $state(false);
	let processingInvitationId = $state<string | null>(null);

	const acceptInvitation = async (invitation: OrganizationInvitation) => {
		try {
			isAcceptingInvitation = true;
			processingInvitationId = invitation.id;

			await organizationAPIService.acceptInvitation(invitation.token);

			toast.success('Invitation Accepted', {
				description: `You've joined ${invitation.organization.name}!`
			});

			// Reload invitations and invalidate related data

			await invalidateWithLoading('app:invitations');
			await invalidateWithLoading('app:organizations');
			await invalidateWithLoading('app:currentOrganization');
		} catch (error) {
			toast.error('Failed to Accept Invitation', {
				description: error instanceof Error ? error.message : 'Please try again.'
			});
		} finally {
			isAcceptingInvitation = false;
			processingInvitationId = null;
		}
	};

	const declineInvitation = async (invitation: OrganizationInvitation) => {
		try {
			isDecliningInvitation = true;
			processingInvitationId = invitation.id;

			await organizationAPIService.declineInvitation(invitation.token);

			toast.success('Invitation Declined', {
				description: `You've declined the invitation to ${invitation.organization.name}.`
			});

			// Reload invitations
			await invalidateWithLoading('app:invitations');
		} catch (error) {
			toast.error('Failed to Decline Invitation', {
				description: error instanceof Error ? error.message : 'Please try again.'
			});
		} finally {
			isDecliningInvitation = false;
			processingInvitationId = null;
		}
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	};

	const getRoleBadgeVariant = (role: string) => {
		switch (role) {
			case 'owner':
				return 'default';
			case 'admin':
				return 'secondary';
			case 'member':
				return 'outline';
			default:
				return 'outline';
		}
	};

	// Add/remove body padding when banner is shown/hidden
	$effect(() => {
		if (typeof document !== 'undefined') {
			if (pendingInvitations.length > 0) {
				document.body.style.paddingTop = '64px'; // Height of banner (slightly taller now)
			} else {
				document.body.style.paddingTop = '0px';
			}
		}
	});
</script>

<!-- Invitation Banner - Only show if there are pending invitations -->
{#if pendingInvitations.length > 0}
	<div
		class="fixed top-0 right-0 left-0 z-50 border-b border-amber-200 bg-white shadow-sm backdrop-blur-sm"
	>
		<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
			<div class="flex items-center justify-between py-3">
				<div class="flex items-center gap-3">
					<div class="flex h-8 w-8 items-center justify-center rounded-full bg-amber-100">
						<MailIcon class="h-4 w-4 text-amber-600" />
					</div>
					<div class="flex flex-col sm:flex-row sm:items-center sm:gap-2">
						<div class="flex items-center gap-2">
							<span class="text-sm font-semibold text-gray-900">
								{pendingInvitations.length} pending invitation{pendingInvitations.length > 1
									? 's'
									: ''}
							</span>
							<Badge variant="secondary" class="bg-amber-100 text-amber-800">
								{pendingInvitations.length}
							</Badge>
						</div>
						<span class="text-sm text-gray-600">
							{#if pendingInvitations.length === 1}
								You've been invited to join <span class="font-medium text-gray-900"
									>{pendingInvitations[0].organization.name}</span
								>
							{:else}
								You've been invited to join {pendingInvitations.length} organizations
							{/if}
						</span>
					</div>
				</div>
				<div class="flex items-center gap-2">
					{#if pendingInvitations.length === 1}
						<Button
							size="sm"
							onclick={() => acceptInvitation(pendingInvitations[0])}
							disabled={processingInvitationId === pendingInvitations[0].id}
							class="bg-green-600 text-white hover:bg-green-700"
						>
							{#if isAcceptingInvitation && processingInvitationId === pendingInvitations[0].id}
								<div class="mr-1 h-3 w-3 animate-spin rounded-full border-b-2 border-current"></div>
								Accepting...
							{:else}
								<CheckIcon class="mr-1 h-3 w-3" />
								Accept
							{/if}
						</Button>
					{/if}
					<Button
						variant="outline"
						size="sm"
						onclick={() => (showInvitationsDialog = true)}
						class="border-amber-300 bg-white text-amber-700 hover:bg-amber-50 hover:text-amber-800"
						disabled={isLoading}
					>
						{#if isLoading}
							<div class="mr-2 h-3 w-3 animate-spin rounded-full border-b-2 border-current"></div>
							Loading...
						{:else}
							<UsersIcon class="mr-1 h-3 w-3" />
							{pendingInvitations.length === 1 ? 'View Details' : 'View All'}
						{/if}
					</Button>
				</div>
			</div>
		</div>
	</div>
{/if}

<!-- Invitations Dialog -->
<Dialog.Root bind:open={showInvitationsDialog}>
	<Dialog.Content class="sm:max-w-2xl">
		<Dialog.Header>
			<Dialog.Title class="flex items-center gap-2">
				<UsersIcon class="h-5 w-5" />
				Pending Invitations
			</Dialog.Title>
			<Dialog.Description>
				You have {pendingInvitations.length} pending invitation{pendingInvitations.length > 1
					? 's'
					: ''} to join organizations.
			</Dialog.Description>
		</Dialog.Header>

		<div class="max-h-96 space-y-4 overflow-y-auto">
			{#each pendingInvitations as invitation (invitation.id)}
				<Card.Root class="border-l-4 border-l-amber-400">
					<Card.Content class="p-4">
						<div class="flex items-start justify-between">
							<div class="flex-1">
								<div class="mb-2 flex items-center gap-2">
									<h3 class="text-lg font-semibold">{invitation.organization.name}</h3>
									<Badge variant={getRoleBadgeVariant(invitation.role)}>
										{invitation.role}
									</Badge>
								</div>

								<div class="text-muted-foreground space-y-1 text-sm">
									<p>
										<span class="font-medium">Invited by:</span>
										{invitation.invited_by.first_name}
										{invitation.invited_by.last_name}
										({invitation.invited_by.email})
									</p>
									<p>
										<span class="font-medium">Expires:</span>
										{formatDate(invitation.expires_at)}
									</p>
									<div class="mt-3">
										<span class="text-xs font-medium text-gray-700">Permissions:</span>
										<div class="mt-1 flex flex-wrap gap-1">
											{#if invitation.can_view}
												<Badge
													variant="outline"
													class="border-blue-200 bg-blue-50 text-xs text-blue-700">View</Badge
												>
											{/if}
											{#if invitation.can_create}
												<Badge
													variant="outline"
													class="border-green-200 bg-green-50 text-xs text-green-700">Create</Badge
												>
											{/if}
											{#if invitation.can_edit}
												<Badge
													variant="outline"
													class="border-yellow-200 bg-yellow-50 text-xs text-yellow-700">Edit</Badge
												>
											{/if}
											{#if invitation.can_delete}
												<Badge
													variant="outline"
													class="border-red-200 bg-red-50 text-xs text-red-700">Delete</Badge
												>
											{/if}
										</div>
									</div>
								</div>
							</div>

							<div class="ml-4 flex gap-2">
								<Button
									size="sm"
									onclick={() => acceptInvitation(invitation)}
									disabled={processingInvitationId === invitation.id}
									class="bg-green-600 hover:bg-green-700"
								>
									{#if isAcceptingInvitation && processingInvitationId === invitation.id}
										<div
											class="mr-2 h-3 w-3 animate-spin rounded-full border-b-2 border-current"
										></div>
										Accepting...
									{:else}
										<CheckIcon class="mr-1 h-3 w-3" />
										Accept
									{/if}
								</Button>
								<Button
									variant="outline"
									size="sm"
									onclick={() => declineInvitation(invitation)}
									disabled={processingInvitationId === invitation.id}
									class="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
								>
									{#if isDecliningInvitation && processingInvitationId === invitation.id}
										<div
											class="mr-2 h-3 w-3 animate-spin rounded-full border-b-2 border-current"
										></div>
										Declining...
									{:else}
										<XIcon class="mr-1 h-3 w-3" />
										Decline
									{/if}
								</Button>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>

		<Dialog.Footer>
			<Button variant="outline" onclick={() => (showInvitationsDialog = false)}>Close</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
