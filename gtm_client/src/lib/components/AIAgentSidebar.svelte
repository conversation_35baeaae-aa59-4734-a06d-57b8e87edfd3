<script lang="ts">
	import { onMount } from 'svelte';
	import MastraChatAgent from './MastraChatAgent.svelte';

	// Icons
	import Bo<PERSON> from '@lucide/svelte/icons/bot';
	import Sparkles from '@lucide/svelte/icons/sparkles';
	import ChevronLeft from '@lucide/svelte/icons/chevron-left';
	import ChevronRight from '@lucide/svelte/icons/chevron-right';

	// Props
	let { isOpen = $bindable(false), className = '' } = $props();

	// State
	let isMinimized = $state(false);

	// Toggle sidebar
	function toggleSidebar() {
		isOpen = !isOpen;
		if (isOpen) {
			isMinimized = false;
		}
	}

	// Toggle minimized state
	function toggleMinimized() {
		isMinimized = !isMinimized;
	}

	// Keyboard shortcut to open AI Agent
	function handleKeydown(event: KeyboardEvent) {
		// Ctrl/Cmd + K to open AI Agent
		if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
			event.preventDefault();
			toggleSidebar();
		}

		// Escape to close
		if (event.key === 'Escape' && isOpen) {
			isOpen = false;
		}
	}

	onMount(() => {
		document.addEventListener('keydown', handleKeydown);
		return () => {
			document.removeEventListener('keydown', handleKeydown);
		};
	});
</script>

<!-- AI Agent Toggle Button (when closed) -->
{#if !isOpen}
	<button
		onclick={toggleSidebar}
		class="bg-primary text-primary-foreground hover:bg-primary/90 group fixed right-6 bottom-6 z-50 rounded-full p-3 shadow-lg transition-all duration-200 hover:scale-105"
		title="Open AI Assistant (Ctrl+K)"
	>
		<Bot class="h-6 w-6" />
		<div
			class="absolute -top-2 -right-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 p-1"
		>
			<Sparkles class="h-3 w-3 text-white" />
		</div>

		<!-- Tooltip -->
		<div
			class="absolute right-0 bottom-full mb-2 rounded bg-black px-2 py-1 text-xs whitespace-nowrap text-white opacity-0 transition-opacity group-hover:opacity-100"
		>
			AI Assistant (Ctrl+K)
		</div>
	</button>
{/if}

<!-- AI Agent Sidebar -->
{#if isOpen}
	<div class="fixed inset-0 z-40 lg:relative lg:inset-auto lg:h-full lg:max-h-full">
		<!-- Backdrop (mobile only) -->
		<button
			class="absolute inset-0 cursor-default border-0 bg-black/50 p-0 lg:hidden"
			onclick={() => (isOpen = false)}
			aria-label="Close AI Assistant"
		></button>

		<!-- Sidebar -->
		<div
			class="bg-background absolute top-0 right-0 h-full border-l shadow-xl transition-all duration-300 lg:relative lg:h-full lg:max-h-full lg:shadow-none {isMinimized
				? 'w-12'
				: 'w-96'} {className}"
		>
			{#if isMinimized}
				<!-- Minimized State -->
				<div class="flex h-full flex-col">
					<!-- Header -->
					<div class="border-b p-3">
						<button
							onclick={toggleMinimized}
							class="text-muted-foreground hover:text-foreground flex w-full items-center justify-center"
							title="Expand AI Assistant"
						>
							<ChevronLeft class="h-5 w-5" />
						</button>
					</div>

					<!-- Minimized Content -->
					<div class="flex flex-1 flex-col items-center justify-center space-y-4">
						<Bot class="text-primary h-8 w-8" />
						<div class="writing-mode-vertical text-muted-foreground text-xs">AI Assistant</div>
					</div>

					<!-- Close Button -->
					<div class="border-t p-3">
						<button
							onclick={() => (isOpen = false)}
							class="text-muted-foreground hover:text-foreground flex w-full items-center justify-center"
							title="Close AI Assistant"
						>
							×
						</button>
					</div>
				</div>
			{:else}
				<!-- Full State -->
				<div class="flex h-full flex-col">
					<!-- Header with minimize/close -->
					<div class="bg-muted/50 flex items-center justify-between border-b p-3">
						<div class="flex items-center gap-2">
							<Bot class="text-primary h-5 w-5" />
							<h3 class="font-semibold">AI Assistant</h3>
							<div class="rounded-full bg-gradient-to-r from-purple-500 to-pink-500 p-1">
								<Sparkles class="h-3 w-3 text-white" />
							</div>
						</div>

						<div class="flex items-center gap-1">
							<button
								onclick={toggleMinimized}
								class="text-muted-foreground hover:text-foreground rounded p-1"
								title="Minimize"
							>
								<ChevronRight class="h-4 w-4" />
							</button>
							<button
								onclick={() => (isOpen = false)}
								class="text-muted-foreground hover:text-foreground rounded p-1"
								title="Close (Esc)"
							>
								×
							</button>
						</div>
					</div>

					<!-- Chat Component -->
					<div class="flex-1 overflow-hidden">
						<MastraChatAgent />
					</div>
				</div>
			{/if}
		</div>
	</div>
{/if}

<style>
	.writing-mode-vertical {
		writing-mode: vertical-rl;
		text-orientation: mixed;
	}
</style>
