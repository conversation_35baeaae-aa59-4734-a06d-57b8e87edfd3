// Global loading state using Svelte 5 reactive state
let loadingCount = $state(0);

// Create reactive loading state object
export const loadingState = {
	get isLoading() {
		return loadingCount > 0;
	},

	start() {
		loadingCount++;
	},

	stop() {
		loadingCount = Math.max(0, loadingCount - 1);
	},

	reset() {
		loadingCount = 0;
	}
};

// Export convenience functions for backward compatibility
export function isLoading() {
	return loadingState.isLoading;
}

export function startLoading() {
	loadingState.start();
}

export function stopLoading() {
	loadingState.stop();
}

export function resetLoading() {
	loadingState.reset();
}
