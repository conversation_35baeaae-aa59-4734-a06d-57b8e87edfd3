import { writable } from 'svelte/store';

export type NotificationType = 'invitation' | 'billing' | 'urgent' | 'info' | 'warning' | 'success';

export type NotificationPriority = 'low' | 'medium' | 'high' | 'critical';

export interface BaseNotification {
	id: string;
	type: NotificationType;
	priority: NotificationPriority;
	title: string;
	message: string;
	timestamp: Date;
	read: boolean;
	persistent: boolean; // Whether it stays until manually dismissed
	autoHideMs?: number; // Auto-hide after X milliseconds
	actions?: NotificationAction[];
	metadata?: Record<string, any>;
}

export interface NotificationAction {
	id: string;
	label: string;
	variant: 'default' | 'secondary' | 'destructive' | 'outline' | 'ghost' | 'link';
	handler: () => Promise<void> | void;
	loading?: boolean;
}

export interface InvitationNotification extends BaseNotification {
	type: 'invitation';
	metadata: {
		invitationId: string;
		organizationName: string;
		role: string;
		inviterName: string;
		expiresAt: string;
		token: string;
	};
}

export interface BillingNotification extends BaseNotification {
	type: 'billing';
	metadata: {
		amount: number;
		currency: string;
		dueDate: string;
		invoiceId?: string;
		status: 'overdue' | 'due_soon' | 'failed' | 'requires_action';
	};
}

export interface UrgentNotification extends BaseNotification {
	type: 'urgent';
	metadata: {
		actionRequired: boolean;
		deadline?: string;
		severity: 'low' | 'medium' | 'high' | 'critical';
	};
}

export type Notification =
	| InvitationNotification
	| BillingNotification
	| UrgentNotification
	| BaseNotification;

// Notification store
function createNotificationStore() {
	const { subscribe, set, update } = writable<Notification[]>([]);

	return {
		subscribe,

		// Add a new notification
		add: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
			const newNotification: Notification = {
				...notification,
				id: crypto.randomUUID(),
				timestamp: new Date(),
				read: false
			};

			update((notifications) => {
				// Add to beginning (newest first)
				const updated = [newNotification, ...notifications];

				// Limit to 50 notifications max
				return updated.slice(0, 50);
			});

			// Auto-hide if specified
			if (newNotification.autoHideMs) {
				setTimeout(() => {
					notificationStore.remove(newNotification.id);
				}, newNotification.autoHideMs);
			}

			return newNotification.id;
		},

		// Remove a notification
		remove: (id: string) => {
			update((notifications) => notifications.filter((n) => n.id !== id));
		},

		// Mark as read
		markAsRead: (id: string) => {
			update((notifications) => notifications.map((n) => (n.id === id ? { ...n, read: true } : n)));
		},

		// Mark all as read
		markAllAsRead: () => {
			update((notifications) => notifications.map((n) => ({ ...n, read: true })));
		},

		// Clear all notifications
		clear: () => {
			set([]);
		},

		// Clear by type
		clearByType: (type: NotificationType) => {
			update((notifications) => notifications.filter((n) => n.type !== type));
		},

		// Get unread count
		getUnreadCount: (notifications: Notification[]) => {
			return notifications.filter((n) => !n.read).length;
		},

		// Get count by type
		getCountByType: (notifications: Notification[], type: NotificationType) => {
			return notifications.filter((n) => n.type === type && !n.read).length;
		},

		// Update action loading state
		updateActionLoading: (notificationId: string, actionId: string, loading: boolean) => {
			update((notifications) =>
				notifications.map((n) => {
					if (n.id === notificationId && n.actions) {
						return {
							...n,
							actions: n.actions.map((action) =>
								action.id === actionId ? { ...action, loading } : action
							)
						};
					}
					return n;
				})
			);
		}
	};
}

export const notificationStore = createNotificationStore();
