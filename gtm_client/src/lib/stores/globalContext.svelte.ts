import type { Organization } from '$lib/api/organizations';
import type { PublicUser } from '$lib/interfaces/user';
import type { Workspace } from '$lib/interfaces/workspace';

export type UIVisualStates = {
	currentOrganizationId?: string | number;
	currentOrganizationInformation?: Organization;
	currentWorkspaceId?: string | number;
	currentWorkspaceInformation?: Workspace;
	currentProjectId?: string | number;
	currentProjectInformation?: any;
};

export type GlobalContext = {
	userId: string | number;
	userEmail: string;
	userInformation?: PublicUser;
} & UIVisualStates & {
		extra?: Record<string, any>;
	};

let globalUIContext = $state<GlobalContext>({
	userId: '',
	userEmail: ''
});

export function getGlobalUIContext() {
	return globalUIContext;
}

export function setGlobalUIContext(context: Partial<GlobalContext>) {
	globalUIContext = { ...globalUIContext, ...context };
}

type GlobalContextChanges = {
	userId?: string | number;
	userEmail?: string;
	userInformation?: PublicUser;
	uiVisualStates?: Partial<UIVisualStates>;
};

type GlobalContextSubscriber = (context: GlobalContext, changes: GlobalContextChanges) => void;

let subscribers: Set<GlobalContextSubscriber> = new Set();
let previousContext: GlobalContext | null = null;

/**
 * Subscribe to globalContext changes
 * @param callback Function that receives the current context and the changes that occurred
 * @returns Unsubscribe function
 */
export function subscribeToGlobalContext(callback: GlobalContextSubscriber): () => void {
	subscribers.add(callback);

	// Call immediately with current state (no changes on first call)
	callback(globalUIContext, {});

	return () => {
		subscribers.delete(callback);
	};
}

/**
 * Initialize the global context change tracking
 * This should be called once in your app's root component
 */
export function initializeGlobalContextTracking() {
	$effect(() => {
		// Deep clone current context to detect changes
		const currentContext = JSON.parse(JSON.stringify(globalUIContext)) as GlobalContext;

		if (previousContext) {
			// Calculate what changed
			const changes: GlobalContextChanges = {};

			// Check top-level changes
			if (currentContext.userId !== previousContext.userId) {
				changes.userId = currentContext.userId;
			}
			if (currentContext.userEmail !== previousContext.userEmail) {
				changes.userEmail = currentContext.userEmail;
			}
			if (
				JSON.stringify(currentContext.userInformation) !==
				JSON.stringify(previousContext.userInformation)
			) {
				changes.userInformation = currentContext.userInformation;
			}

			// Check uiVisualStates changes
			const uiChanges: Partial<UIVisualStates> = {};
			if (
				currentContext.uiVisualStates.currentOrganizationId !==
				previousContext.uiVisualStates.currentOrganizationId
			) {
				uiChanges.currentOrganizationId = currentContext.uiVisualStates.currentOrganizationId;
			}
			if (
				JSON.stringify(currentContext.uiVisualStates.currentOrganizationInformation) !==
				JSON.stringify(previousContext.uiVisualStates.currentOrganizationInformation)
			) {
				uiChanges.currentOrganizationInformation =
					currentContext.uiVisualStates.currentOrganizationInformation;
			}
			if (
				currentContext.uiVisualStates.currentWorkspaceId !==
				previousContext.uiVisualStates.currentWorkspaceId
			) {
				uiChanges.currentWorkspaceId = currentContext.uiVisualStates.currentWorkspaceId;
			}
			if (
				JSON.stringify(currentContext.uiVisualStates.currentWorkspaceInformation) !==
				JSON.stringify(previousContext.uiVisualStates.currentWorkspaceInformation)
			) {
				uiChanges.currentWorkspaceInformation =
					currentContext.uiVisualStates.currentWorkspaceInformation;
			}
			if (
				currentContext.uiVisualStates.currentProjectId !==
				previousContext.uiVisualStates.currentProjectId
			) {
				uiChanges.currentProjectId = currentContext.uiVisualStates.currentProjectId;
			}
			if (
				JSON.stringify(currentContext.uiVisualStates.currentProjectInformation) !==
				JSON.stringify(previousContext.uiVisualStates.currentProjectInformation)
			) {
				uiChanges.currentProjectInformation =
					currentContext.uiVisualStates.currentProjectInformation;
			}

			if (Object.keys(uiChanges).length > 0) {
				changes.uiVisualStates = uiChanges;
			}

			// Notify subscribers if there are changes
			if (Object.keys(changes).length > 0) {
				subscribers.forEach((callback) => {
					callback(currentContext, changes);
				});
			}
		}

		previousContext = currentContext;
	});
}
