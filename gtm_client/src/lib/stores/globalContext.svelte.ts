import type { Organization } from '$lib/api/organizations';
import type { PublicUser } from '$lib/interfaces/user';
import type { Workspace } from '$lib/interfaces/workspace';

export type UIVisualStates = {
	currentOrganizationId: string | number;
	currentOrganizationInformation?: Organization;
	currentWorkspaceId?: string | number;
	currentWorkspaceInformation?: Workspace;
	currentProjectId?: string | number;
	currentProjectInformation?: any;
};

export type GlobalContext = {
	userId: string | number;
	userEmail: string;
	userInformation?: PublicUser;
	uiVisualStates: UIVisualStates;
};

export const globalContext = $state<GlobalContext>({
	userId: '',
	userEmail: '',
	uiVisualStates: {
		currentOrganizationId: '',
		currentWorkspaceId: '',
		currentProjectId: ''
	}
});
