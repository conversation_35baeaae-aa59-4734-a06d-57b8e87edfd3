/**
 * Filter Engine Integration for GTM Client
 *
 * This module provides a bridge between the standalone filter-engine
 * and the GTM client's Pages/Events components.
 */

// Re-export core types and functions from the filter engine
export type {
	Rule,
	TargetId,
	OperationId,
	EngineInput,
	EngineOutput,
	Issue,
	UIHint,
	RuleEffect,
	LevelCanonical,
	NormalizationOptions
} from '../../../filter-engine/types';

export { evaluateFilters } from '../../../filter-engine/engine';
export { getEarlyL2InputHint } from '../../../filter-engine/early-hints';

// GTM-specific types that map to filter engine types
export interface GTMPageRule {
	id: string;
	type: 'url' | 'datalayer';
	condition: 'Contains' | 'Equals' | 'StartsWith' | 'EndsWith';
	value: string;
	isActive: boolean;
}

export interface GTMEventRule {
	id: string;
	type: 'url' | 'datalayer';
	condition: 'Contains' | 'Equals' | 'StartsWith' | 'EndsWith';
	value: string;
	isActive: boolean;
}

export interface GTMPage {
	id: string;
	name: string;
	rules: GTMPageRule[];
	isActive: boolean;
}

export interface GTMEvent {
	id: string;
	name: string;
	rules: GTMEventRule[];
	isActive: boolean;
	pageId?: string; // Associated page
}

// Mapping functions between GTM types and Filter Engine types
export function mapGTMRuleToFilterRule(gtmRule: GTMPageRule | GTMEventRule): Rule {
	return {
		id: gtmRule.id,
		target: gtmRule.type === 'url' ? 'page_url' : 'page_path', // Map GTM types to filter engine targets
		op: mapGTMConditionToOperation(gtmRule.condition),
		value: gtmRule.value,
		isActive: gtmRule.isActive
	};
}

export function mapGTMConditionToOperation(condition: string): OperationId {
	switch (condition) {
		case 'Contains':
			return 'contains';
		case 'Equals':
			return 'equal';
		case 'StartsWith':
			return 'starts_with';
		case 'EndsWith':
			return 'ends_with';
		default:
			return 'contains';
	}
}

export function mapOperationToGTMCondition(operation: OperationId): string {
	switch (operation) {
		case 'contains':
			return 'Contains';
		case 'equal':
			return 'Equals';
		case 'starts_with':
			return 'StartsWith';
		case 'ends_with':
			return 'EndsWith';
		default:
			return 'Contains';
	}
}

// GTM-specific validation and processing
export class GTMFilterEngine {
	/**
	 * Validate page rules and get UI hints
	 */
	static validatePageRules(pages: GTMPage[], testUrls?: string[]) {
		const l1Rules = pages
			.filter((page) => page.isActive)
			.flatMap((page) => page.rules.filter((rule) => rule.isActive))
			.map(mapGTMRuleToFilterRule);

		return evaluateFilters({
			l1: { rules: l1Rules },
			options: { testUrls }
		});
	}

	/**
	 * Validate event rules in context of page rules
	 */
	static validateEventRules(
		pages: GTMPage[],
		events: GTMEvent[],
		context?: { lastEditedEventId?: string },
		testUrls?: string[]
	) {
		const l1Rules = pages
			.filter((page) => page.isActive)
			.flatMap((page) => page.rules.filter((rule) => rule.isActive))
			.map(mapGTMRuleToFilterRule);

		const l2Rules = events
			.filter((event) => event.isActive)
			.flatMap((event) => event.rules.filter((rule) => rule.isActive))
			.map(mapGTMRuleToFilterRule);

		return evaluateFilters({
			l1: { rules: l1Rules },
			l2: { rules: l2Rules },
			context: context?.lastEditedEventId
				? {
						lastEdited: { level: 'L2', ruleId: context.lastEditedEventId }
					}
				: undefined,
			options: { testUrls }
		});
	}

	/**
	 * Get input hints for event rule creation
	 */
	static getEventInputHints(pages: GTMPage[], targetType: 'url' | 'datalayer', operation: string) {
		const l1Rules = pages
			.filter((page) => page.isActive)
			.flatMap((page) => page.rules.filter((rule) => rule.isActive))
			.map(mapGTMRuleToFilterRule);

		const result = evaluateFilters({
			l1: { rules: l1Rules }
		});

		if (!result.canonical.l1) return null;

		return getEarlyL2InputHint({
			l1: result.canonical.l1,
			l2Target: targetType === 'url' ? 'page_url' : 'page_path',
			l2Op: mapGTMConditionToOperation(operation),
			targets: {} // Will need to import from filter engine
		});
	}

	/**
	 * Generate firing logic summary
	 */
	static generateFiringLogic(pages: GTMPage[], events: GTMEvent[]): string {
		const result = this.validateEventRules(pages, events);
		return result.summary?.combined || 'No firing conditions defined';
	}

	/**
	 * Check if event configuration is valid
	 */
	static isValidConfiguration(
		pages: GTMPage[],
		events: GTMEvent[]
	): {
		isValid: boolean;
		errors: string[];
		warnings: string[];
	} {
		const result = this.validateEventRules(pages, events);

		const errors = result.issues
			.filter((issue) => issue.severity === 'error')
			.map((issue) => issue.message);

		const warnings = result.issues
			.filter((issue) => issue.severity === 'warning')
			.map((issue) => issue.message);

		return {
			isValid: errors.length === 0,
			errors,
			warnings
		};
	}
}

// Utility functions for UI integration
export function formatIssueForUI(issue: Issue): {
	type: 'error' | 'warning' | 'info';
	message: string;
	ruleId?: string;
} {
	return {
		type: issue.severity,
		message: issue.message,
		ruleId: issue.ruleId
	};
}

export function formatRuleEffectForUI(effect: RuleEffect): {
	type: 'narrows' | 'redundant' | 'ineffective' | 'impossible';
	message: string;
	ruleId: string;
} {
	return {
		type: effect.effect,
		message: effect.message || `Rule ${effect.effect}`,
		ruleId: effect.ruleId
	};
}
