export type ProjectStatus = 'deployed' | 'archived' | 'draft';

export type Project = {
	id: number | string;
	name: string;
	status: ProjectStatus;
	server_supported: boolean;
	subdomain?: {
		id: number | string;
		name: string;
		full_uri: string;
		domain_name: string;
		domain_id: number | string;
	};
	workspace_id: number | string;
	project_purposes: {
		id: number | string;
		name: string;
	}[];
	project_type_id: number | string;
	project_platform_id: number | string;
};
