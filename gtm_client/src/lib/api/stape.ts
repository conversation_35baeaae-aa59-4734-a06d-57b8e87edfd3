// Stape API integration for gtm_client
// Based on old-client/src/lib/api/stape-wrapper.svelte.ts

export enum StapeRegion {
	EU = 'eu',
	GLOBAL = 'global'
}

export enum StapeTokenType {
	NORMAL = 'normal',
	PARTNER = 'partner'
}

export type StapeConfig = {
	eu: { baseUrl: string };
	global: { baseUrl: string };
};

export type StapeRequestPayload<T = any> = {
	resource: string;
	action: string;
	payload: T[];
	region?: StapeRegion;
	token_type?: StapeTokenType;
};

export type StapeErrorResponse = {
	error: string;
	message: string;
	details?: string;
};

export type StapeAPIWrapperResponse<T = unknown> = {
	status_code: 200 | 400 | 429 | number;
	status: 'success' | 'error' | string;
	message: 'Request successful' | string;
	data: T;
};

export interface ContainerLocation {
	label: string;
	type: string;
	ip: string;
	ipV6: string;
	defaultDomain: string;
	loadDomain: string;
	cdnAvailable: boolean;
}

export interface StapeContainer {
	identifier: string;
	name: string;
	status: { label: string };
	location: string;
	subscription?: {
		plan: {
			type: string;
		};
	};
}

export interface StapeDomain {
	identifier: string;
	name: string;
	status: { label: string };
	connectionType: string;
	cdnType: string;
}

export interface UserForm {
	name_first?: string;
	name_last?: string;
	password?: string;
	sendEmail?: boolean;
	email: string;
}

export class StapeAPIService {
	private config: StapeConfig;

	constructor(config: StapeConfig) {
		this.config = config;
	}

	private getBaseUrl(region: StapeRegion = StapeRegion.GLOBAL): string {
		return this.config[region].baseUrl;
	}

	private async makeStapeRequest<TSuccess>(
		resource: string,
		action: string,
		payload?: any,
		region?: StapeRegion,
		tokenType?: StapeTokenType,
		options?: RequestInit
	): Promise<TSuccess> {
		const requestPayload: StapeRequestPayload = {
			resource,
			action,
			payload: payload || [],
			region,
			token_type: tokenType
		};

		const response = await fetch(`${this.getBaseUrl(region)}/`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				...options?.headers
			},
			body: JSON.stringify(requestPayload),
			...options
		});

		if (!response.ok) {
			throw new Error(`Stape API request failed: ${response.statusText}`);
		}

		return response.json();
	}

	// Container management methods
	public async listContainers<TSuccess>(
		userIdentifier?: string,
		region?: StapeRegion,
		tokenType?: StapeTokenType,
		options?: RequestInit
	): Promise<TSuccess> {
		return this.makeStapeRequest<TSuccess>(
			'containers',
			'get_containers',
			{
				userIdentifier
			},
			region,
			tokenType,
			options
		);
	}

	public async getContainer<TSuccess>(
		identifier: string,
		region?: StapeRegion,
		tokenType?: StapeTokenType,
		options?: RequestInit
	): Promise<TSuccess> {
		return this.makeStapeRequest<TSuccess>(
			'containers',
			'get_container',
			{ identifier },
			region,
			tokenType,
			options
		);
	}

	public async createContainer<TSuccess>(
		containerData: any,
		region?: StapeRegion,
		tokenType?: StapeTokenType
	): Promise<TSuccess> {
		return this.makeStapeRequest<TSuccess>(
			'containers',
			'create_container',
			{ data: containerData },
			region,
			tokenType
		);
	}

	// User management methods
	public async getUser<TSuccess>(
		identifier: string,
		region?: StapeRegion,
		tokenType?: StapeTokenType,
		options?: RequestInit
	): Promise<TSuccess> {
		return this.makeStapeRequest<TSuccess>(
			'users',
			'get_user',
			{ identifier },
			region,
			tokenType,
			options
		);
	}

	public async createUser<TSuccess>(
		userData: UserForm,
		region?: StapeRegion,
		tokenType?: StapeTokenType
	): Promise<TSuccess> {
		return this.makeStapeRequest<TSuccess>('users', 'create_user', userData, region, tokenType);
	}

	public async attachUser<TSuccess>(
		email: string,
		productName?: string,
		hasNoProducts: boolean = true,
		region?: StapeRegion,
		tokenType?: StapeTokenType
	): Promise<TSuccess> {
		const data = {
			email,
			has_no_products: hasNoProducts,
			...(productName && { productName })
		};

		return this.makeStapeRequest<TSuccess>('users', 'attach_user', data, region, tokenType);
	}

	// Workspace management methods
	public async getWorkspaces<TSuccess>(
		page: number = 1,
		limit: number = 25,
		region?: StapeRegion,
		tokenType?: StapeTokenType
	): Promise<TSuccess> {
		return this.makeStapeRequest<TSuccess>(
			'workspaces',
			'get_workspaces',
			{ page, limit },
			region,
			tokenType
		);
	}

	public async getWorkspace<TSuccess>(
		identifier: string,
		region?: StapeRegion,
		tokenType?: StapeTokenType
	): Promise<TSuccess> {
		return this.makeStapeRequest<TSuccess>(
			'workspaces',
			'get_workspace',
			{ identifier },
			region,
			tokenType
		);
	}

	// Container zones and locations
	public async getContainerZones<TSuccess>(
		region?: StapeRegion,
		tokenType?: StapeTokenType
	): Promise<TSuccess> {
		return this.makeStapeRequest<TSuccess>(
			'resources',
			'get_container_zones',
			{},
			region,
			tokenType
		);
	}

	// Domain management methods
	public async getContainerDomains<TSuccess>(
		containerIdentifier: string,
		region?: StapeRegion,
		tokenType?: StapeTokenType,
		options?: RequestInit
	): Promise<TSuccess> {
		return this.makeStapeRequest<TSuccess>(
			'domains',
			'get_container_domains',
			{ container_id: containerIdentifier },
			region,
			tokenType,
			options
		);
	}

	public async createContainerDomain<TSuccess>(
		containerIdentifier: string,
		domainData: any,
		region?: StapeRegion,
		tokenType?: StapeTokenType,
		options?: RequestInit
	): Promise<TSuccess> {
		return this.makeStapeRequest<TSuccess>(
			'domains',
			'create_container_domain',
			{ container_id: containerIdentifier, data: domainData },
			region,
			tokenType,
			options
		);
	}

	/**
	 * Find workspace by user email
	 */
	public async findWorkspaceByEmail<TSuccess>(
		email: string,
		page: number = 1,
		limit: number = 25,
		region?: StapeRegion,
		tokenType?: StapeTokenType
	): Promise<TSuccess | null> {
		const response: any = await this.getWorkspaces<any>(page, limit, region, tokenType);

		if (response?.data?.body?.items && Array.isArray(response.data.body.items)) {
			const items = response.data.body.items;
			const workspace = items.find((item: any) => item.user && item.user.username === email);

			if (workspace) {
				return workspace as TSuccess;
			}

			// Check next page if available
			if (items.length === limit && page * limit < response.body.total) {
				return this.findWorkspaceByEmail<TSuccess>(email, page + 1, limit, region, tokenType);
			}
		}

		return null;
	}
}

// Default configuration
const stapeConfig: StapeConfig = {
	eu: {
		baseUrl: '/spark/stape'
	},
	global: {
		baseUrl: '/spark/stape'
	}
};

export const stapeAPIService = new StapeAPIService(stapeConfig);
