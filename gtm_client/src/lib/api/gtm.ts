// GTM API Service for gtm_client
// Based on the old-client wrapper structure with proper task polling

interface GTMRequestPayload {
	resource: string;
	action: string;
	payload?: Record<string, any>;
}

interface TaskQueued {
	task_id: string;
	status: 'queued';
}

interface TaskSuccess<T = any> {
	status: 'success';
	message: string;
	data: T;
}

interface TaskError {
	status: 'error';
	message: string;
	error?: any;
}

interface GTMAccount {
	accountId: string;
	name: string;
}

interface ListAccountsResponse {
	account: GTMAccount[];
}

interface GTMContainer {
	accountId: string;
	containerId: string;
	name: string;
	usageContext: string[];
	fingerprint?: string;
	domainName?: string[];
	notes?: string;
	publicId?: string;
}

interface ListContainersResponse {
	container: GTMContainer[];
}

interface UserPermission {
	path: string;
	accountId: string;
	emailAddress: string;
	accountAccess: {
		permission: 'read' | 'admin' | 'publish';
	};
	containerAccess: Array<{
		containerId: string;
		permission: 'read' | 'admin' | 'publish';
	}>;
}

interface AccountPermissionsResponse {
	userPermission: UserPermission[];
}

interface AccountPermissions {
	accountAccess?: {
		permission: 'read' | 'admin' | 'publish';
	};
	containerAccess?: Array<{
		containerId: string;
		permission: 'read' | 'admin' | 'publish';
	}>;
}

type GTMResponse<T> = TaskSuccess<T> | TaskError | TaskQueued;

class GTMAPIService {
	private baseUrl = '/spark/gtm/api/v1/process-gtm-request/';

	private async makeRequest<T>(
		resource: string,
		action: string,
		payload?: Record<string, any>
	): Promise<GTMResponse<T>> {
		const requestPayload: GTMRequestPayload = {
			resource,
			action,
			payload
		};

		const response = await fetch(this.baseUrl, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(requestPayload)
		});

		if (!response.ok) {
			throw new Error(`HTTP ${response.status}: ${response.statusText}`);
		}

		return await response.json();
	}

	private async pollTaskResult<T>(taskId: string, maxAttempts = 30, interval = 2000): Promise<T> {
		for (let attempt = 0; attempt < maxAttempts; attempt++) {
			const result = await this.makeRequest<T>('task_results', 'retrieve_task_result', {
				task_id: taskId
			});

			if (result.status === 'success') {
				return result.data;
			} else if (result.status === 'error') {
				throw new Error(result.message || 'Task failed');
			}

			// If still queued, wait and try again
			await new Promise((resolve) => setTimeout(resolve, interval));
		}

		throw new Error('Task polling timeout');
	}

	private async makeRequestWithPolling<T>(
		resource: string,
		action: string,
		payload?: Record<string, any>
	): Promise<T> {
		const response = await this.makeRequest<T>(resource, action, payload);

		// Check if it's a structured response with status
		if (typeof response === 'object' && response !== null && 'status' in response) {
			if (response.status === 'success') {
				return response.data;
			} else if (response.status === 'error') {
				throw new Error(response.message || 'Request failed');
			} else if ('task_id' in response) {
				// Poll for the result
				return await this.pollTaskResult<T>(response.task_id);
			}
		}

		// Handle direct data responses (from cache)
		// If response doesn't have status/task_id structure, treat it as direct data
		return response as T;
	}

	// Account Operations
	async listAccounts(): Promise<ListAccountsResponse> {
		return await this.makeRequestWithPolling<ListAccountsResponse>('accounts', 'list_accounts');
	}

	async getAccount(accountId: string): Promise<GTMAccount> {
		return await this.makeRequestWithPolling<GTMAccount>('accounts', 'get_account', {
			accountId
		});
	}

	async getAccountPermissions(accountId: string, userEmail?: string): Promise<AccountPermissions> {
		try {
			const response = await this.makeRequestWithPolling<AccountPermissionsResponse>(
				'user_permissions',
				'list_user_permissions',
				[{ accountId }]
			);

			// If no user email provided, try to get the first user's permissions
			// In a real app, you'd want to match against the current user's email
			const userPermissions = response.userPermission;

			if (!userPermissions || userPermissions.length === 0) {
				return {};
			}

			// Find the current user's permissions
			// For now, we'll use the first user if no email is provided
			// In production, you should match against the current user's email
			let currentUserPermission: UserPermission;

			if (userEmail) {
				const foundUser = userPermissions.find((user) => user.emailAddress === userEmail);
				currentUserPermission = foundUser || userPermissions[0];
			} else {
				// Use the first user as fallback
				currentUserPermission = userPermissions[0];
			}

			return {
				accountAccess: currentUserPermission.accountAccess,
				containerAccess: currentUserPermission.containerAccess
			};
		} catch (error) {
			// If permissions endpoint fails, return empty permissions
			console.warn('Failed to fetch account permissions:', error);
			return {};
		}
	}

	// Cache Operations
	async clearAccountsCache(): Promise<void> {
		await this.makeRequestWithPolling<void>('cache', 'clear_cache', [
			{
				gtm_resource: 'accounts'
			}
		]);
	}

	// Container Operations
	async listContainers(accountId: string): Promise<ListContainersResponse> {
		return await this.makeRequestWithPolling<ListContainersResponse>(
			'containers',
			'list_containers',
			[{ accountId }]
		);
	}

	async createContainer(
		accountId: string,
		name: string,
		usageContext: string[] = ['web']
	): Promise<GTMContainer> {
		return await this.makeRequestWithPolling<GTMContainer>('containers', 'create_container', [
			{
				accountId,
				name,
				usageContext
			}
		]);
	}

	// Workspace Operations (for future use)
	async listWorkspaces(accountId: string, containerId: string): Promise<any> {
		return await this.makeRequestWithPolling('workspaces', 'list_workspaces', [
			{
				accountId,
				containerId
			}
		]);
	}

	async createWorkspace(accountId: string, containerId: string, name: string): Promise<any> {
		return await this.makeRequestWithPolling('workspaces', 'create_workspace', [
			{
				accountId,
				containerId,
				name
			}
		]);
	}
}

// Export singleton instance
export const gtmAPIService = new GTMAPIService();

// Export types for use in components
export type {
	GTMAccount,
	ListAccountsResponse,
	GTMContainer,
	ListContainersResponse,
	AccountPermissions,
	AccountPermissionsResponse,
	UserPermission,
	GTMResponse,
	TaskQueued,
	TaskSuccess,
	TaskError
};
