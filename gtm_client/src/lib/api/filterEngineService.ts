/**
 * Filter Engine Microservice API Client
 *
 * Communicates with the Filter Engine microservice through Django proxy
 */

// Types
export interface ValidationIssue {
	severity: 'error' | 'warning' | 'info';
	message: string;
	rule_id?: string;
	target?: string;
	page_id?: string;
	event_id?: string;
}

export interface RuleEffect {
	type: 'narrows' | 'expands' | 'conflicts' | 'redundant';
	description: string;
	affected_rules: string[];
}

export interface ValidationResult {
	is_valid: boolean;
	issues: ValidationIssue[];
	rule_effects: RuleEffect[];
	firing_logic: string;
}

export interface ServiceResponse<T = any> {
	success: boolean;
	result?: T;
	error?: string;
	message?: string;
	details?: any;
	timestamp: string;
}

export interface GTMPage {
	id: string;
	name: string;
	rules: GTMPageRule[];
	isActive: boolean;
}

export interface GTMEvent {
	id: string;
	name: string;
	triggerType: string;
	rules: GTMEventRule[];
	isActive: boolean;
	pageInstanceId?: string;
}

export interface GTMPageRule {
	id: string;
	type: 'url' | 'datalayer';
	condition: 'Contains' | 'Equals' | 'StartsWith' | 'EndsWith';
	value: string;
	isActive: boolean;
}

export interface GTMEventRule {
	id: string;
	type: 'url' | 'datalayer';
	condition: 'Contains' | 'Equals' | 'StartsWith' | 'EndsWith';
	value: string;
	isActive: boolean;
}

class FilterEngineServiceAPI {
	private baseUrl: string;

	constructor() {
		// Use Django proxy endpoints
		this.baseUrl = '/spark/api/v1/filter-engine';
	}

	private async makeRequest<T>(endpoint: string, data: any): Promise<ServiceResponse<T>> {
		try {
			const response = await fetch(`${this.baseUrl}/${endpoint}/`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'X-Requested-With': 'XMLHttpRequest'
				},
				body: JSON.stringify(data)
			});

			const result = await response.json();

			if (!response.ok) {
				throw new Error(result.message || `HTTP ${response.status}`);
			}

			return result;
		} catch (error) {
			console.error(`Filter Engine API Error (${endpoint}):`, error);
			throw error;
		}
	}

	/**
	 * Validate complete configuration (pages + events)
	 */
	async validateConfiguration(data: {
		pages: GTMPage[];
		events?: GTMEvent[];
	}): Promise<ServiceResponse<ValidationResult>> {
		return this.makeRequest<ValidationResult>('validate-configuration', data);
	}

	/**
	 * Validate pages only
	 */
	async validatePages(pages: GTMPage[]): Promise<ServiceResponse<ValidationResult>> {
		return this.makeRequest<ValidationResult>('validate-pages', { pages });
	}

	/**
	 * Validate events in context of pages
	 */
	async validateEvents(
		pages: GTMPage[],
		events: GTMEvent[]
	): Promise<ServiceResponse<ValidationResult>> {
		return this.makeRequest<ValidationResult>('validate-events', { pages, events });
	}

	/**
	 * Validate a single rule
	 */
	async validateSingleRule(
		rule: GTMPageRule | GTMEventRule,
		level: 'page' | 'event',
		context?: {
			pages?: GTMPage[];
			events?: GTMEvent[];
		}
	): Promise<ServiceResponse<{ is_valid: boolean; issues: ValidationIssue[] }>> {
		return this.makeRequest('validate-single-rule', { rule, level, context });
	}

	/**
	 * Generate human-readable firing logic
	 */
	async generateFiringLogic(
		pages: GTMPage[],
		events?: GTMEvent[]
	): Promise<ServiceResponse<{ firing_logic: string }>> {
		return this.makeRequest('generate-firing-logic', { pages, events: events || [] });
	}

	/**
	 * Get input hints for UI
	 */
	async getInputHints(
		targetType: 'url' | 'datalayer',
		operation: string,
		contextPages?: GTMPage[]
	): Promise<ServiceResponse<string[]>> {
		return this.makeRequest('get-input-hints', {
			targetType,
			operation,
			contextPages
		});
	}

	/**
	 * Check service health
	 */
	async healthCheck(): Promise<ServiceResponse<any>> {
		try {
			const response = await fetch(`${this.baseUrl}/health/`);
			return await response.json();
		} catch (error) {
			console.error('Filter Engine health check failed:', error);
			throw error;
		}
	}
}

// Export singleton instance
export const filterEngineService = new FilterEngineServiceAPI();

// Utility functions for formatting
export function formatValidationIssue(issue: ValidationIssue): string {
	const prefix = issue.severity === 'error' ? '❌' : issue.severity === 'warning' ? '⚠️' : 'ℹ️';
	return `${prefix} ${issue.message}`;
}

export function formatRuleEffect(effect: RuleEffect): string {
	const prefix = effect.type === 'conflicts' ? '⚠️' : 'ℹ️';
	return `${prefix} ${effect.description}`;
}

export function isConfigurationValid(result: ValidationResult): boolean {
	return (
		result.is_valid && result.issues.filter((issue) => issue.severity === 'error').length === 0
	);
}
