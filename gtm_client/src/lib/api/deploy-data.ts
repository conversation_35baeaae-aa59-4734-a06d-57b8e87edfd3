// Deploy Data Persistence Service
// Handles saving and loading deployment configuration data

import type { GTMAccount, GTMContainer } from './gtm';

export interface DeploymentData {
	gtmAccount: GTMAccount | null;
	containers: {
		web: GTMContainer | null;
		server: GTMContainer | null;
	};
	stapeConfig: {
		isUseExistingAccount: boolean | undefined;
		isEuAccount: boolean | undefined;
		userIdentifier: string;
		workspaceIdentifier: string;
		email: string;
		containerName: string;
	};
	containerConfig?: {
		useExistingContainer: boolean | undefined;
		selectedContainer: any;
		isComplete: boolean;
	};
	domainConfig?: {
		useExistingDomain: boolean | undefined;
		selectedDomain: any;
		isComplete: boolean;
	};
	paymentConfig?: {
		isVerified: boolean;
		plan: string;
		billingCycle: string;
	};
}

export interface ProjectRawData {
	deployment?: DeploymentData;
	// Other raw data fields can be added here
}

class DeployDataService {
	private baseUrl = '/spark/api/v1/projects';

	async saveDeploymentData(projectId: string, deploymentData: DeploymentData): Promise<void> {
		// First, fetch the current project data to get existing raw_data
		const currentResponse = await fetch(`${this.baseUrl}/${projectId}/`);
		if (!currentResponse.ok) {
			throw new Error(`Failed to fetch current project data: ${currentResponse.statusText}`);
		}

		const currentProject = await currentResponse.json();
		const existingRawData = currentProject.raw_data || {};

		// Merge deployment data with existing raw_data
		const updatedRawData = {
			...existingRawData,
			deployment: deploymentData
		};

		const response = await fetch(`${this.baseUrl}/${projectId}/`, {
			method: 'PATCH',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				data: {
					raw_data: updatedRawData
				}
			})
		});

		if (!response.ok) {
			throw new Error(`Failed to save deployment data: ${response.statusText}`);
		}
	}

	async loadDeploymentData(projectId: string): Promise<DeploymentData | null> {
		const response = await fetch(`${this.baseUrl}/${projectId}/`);

		if (!response.ok) {
			throw new Error(`Failed to load project data: ${response.statusText}`);
		}

		const project = await response.json();
		return project.raw_data?.deployment || null;
	}

	// Validation functions
	validateGTMAccountStep(deploymentData: DeploymentData): { isValid: boolean; errors: string[] } {
		const errors: string[] = [];

		if (!deploymentData.gtmAccount) {
			errors.push('Please select a GTM account');
		}

		return {
			isValid: errors.length === 0,
			errors
		};
	}

	validateContainerStep(
		deploymentData: DeploymentData,
		serverSupported: boolean
	): { isValid: boolean; errors: string[] } {
		const errors: string[] = [];

		if (!deploymentData.containers.web) {
			errors.push('Please configure a web container');
		}

		if (serverSupported && !deploymentData.containers.server) {
			errors.push('Please configure a server container');
		}

		return {
			isValid: errors.length === 0,
			errors
		};
	}

	validateStapeStep(deploymentData: DeploymentData): { isValid: boolean; errors: string[] } {
		const errors: string[] = [];
		const config = deploymentData.stapeConfig;

		// Check account type selection
		if (config.isUseExistingAccount === undefined) {
			errors.push('Please choose whether to use existing Stape account or create new one');
		}

		// Check region selection
		if (config.isEuAccount === undefined) {
			errors.push('Please select a server region');
		}

		// For existing accounts, validate additional fields
		if (config.isUseExistingAccount === true) {
			if (!config.userIdentifier.trim()) {
				errors.push('Please provide your Stape user identifier');
			}
			if (!config.workspaceIdentifier.trim()) {
				errors.push('Please provide your Stape workspace identifier');
			}
		}

		// For new accounts, validate email and container name
		if (config.isUseExistingAccount === false) {
			if (!config.email.trim()) {
				errors.push('Please provide an email address for the new Stape account');
			}
			if (!config.containerName.trim()) {
				errors.push('Please provide a container name');
			}
		}

		return {
			isValid: errors.length === 0,
			errors
		};
	}

	// Get the next available step based on current data
	getNextStep(
		deploymentData: DeploymentData,
		serverSupported: boolean
	): 'gtm-account' | 'containers' | 'stape' | 'deploy' {
		const gtmValid = this.validateGTMAccountStep(deploymentData).isValid;
		const containerValid = this.validateContainerStep(deploymentData, serverSupported).isValid;
		const stapeValid = this.validateStapeStep(deploymentData).isValid;

		if (!gtmValid) return 'gtm-account';
		if (!containerValid) return 'containers';
		if (!stapeValid) return 'stape';
		return 'deploy';
	}

	// Check if user can navigate to a specific step
	canNavigateToStep(
		targetStep: string,
		deploymentData: DeploymentData,
		serverSupported: boolean
	): { canNavigate: boolean; reason?: string } {
		const gtmValid = this.validateGTMAccountStep(deploymentData).isValid;
		const containerValid = this.validateContainerStep(deploymentData, serverSupported).isValid;

		switch (targetStep) {
			case 'gtm-account':
				return { canNavigate: true };

			case 'containers':
				if (!gtmValid) {
					return {
						canNavigate: false,
						reason: 'Please select a GTM account first'
					};
				}
				return { canNavigate: true };

			case 'stape':
				if (!gtmValid) {
					return {
						canNavigate: false,
						reason: 'Please select a GTM account first'
					};
				}
				if (!containerValid) {
					return {
						canNavigate: false,
						reason: 'Please configure containers first'
					};
				}
				return { canNavigate: true };

			case 'deploy':
				if (!gtmValid || !containerValid) {
					return {
						canNavigate: false,
						reason: 'Please complete all previous steps first'
					};
				}
				return { canNavigate: true };

			default:
				return { canNavigate: true };
		}
	}
}

// Export singleton instance
export const deployDataService = new DeployDataService();

// Export types
export type { GTMAccount };
