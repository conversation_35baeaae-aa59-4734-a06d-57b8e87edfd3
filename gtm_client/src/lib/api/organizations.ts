import type { RequestEvent } from '@sveltejs/kit';

export interface Organization {
	id: string;
	name: string;
	description: string;
	owner: {
		id: number;
		username: string;
		email: string;
		first_name: string;
		last_name: string;
	};
	created_at: string;
	updated_at: string;
	is_personal: boolean;
}

export interface OrganizationMember {
	id: string;
	user: {
		id: number;
		username: string;
		email: string;
		first_name: string;
		last_name: string;
	};
	organization: string;
	role: 'viewer' | 'member' | 'admin' | 'owner';
	can_view: boolean;
	can_create: boolean;
	can_edit: boolean;
	can_delete: boolean;
	is_active: boolean;
	joined_at: string;
	updated_at: string;
}

export interface OrganizationInvitation {
	id: string;
	organization: Organization;
	email: string;
	role: 'viewer' | 'member' | 'admin';
	can_view: boolean;
	can_create: boolean;
	can_edit: boolean;
	can_delete: boolean;
	status: 'pending' | 'accepted' | 'declined' | 'cancelled' | 'expired';
	invited_by: {
		id: number;
		email: string;
		first_name: string;
		last_name: string;
		avatar?: string;
	};
	expires_at: string;
	created_at: string;
	updated_at: string;
	token: string;
}

export interface CreateOrganizationRequest {
	name: string;
	description?: string;
}

export interface UpdateOrganizationRequest {
	name?: string;
	description?: string;
}

export interface InviteUserRequest {
	email: string;
	role: 'viewer' | 'member' | 'admin';
	can_view?: boolean;
	can_create?: boolean;
	can_edit?: boolean;
	can_delete?: boolean;
}

export interface UpdateMemberRequest {
	role: 'viewer' | 'member' | 'admin';
}

export class OrganizationAPIService {
	private baseUrl = '/spark/api/v1';
	private fetch: typeof fetch;

	constructor(event?: RequestEvent) {
		this.fetch = event?.fetch || fetch;
	}

	async getOrganizations(): Promise<Organization[]> {
		const response = await this.fetch(`${this.baseUrl}/organizations/`);
		if (!response.ok) {
			throw new Error('Failed to fetch organizations');
		}
		const data = await response.json();
		return data.data || [];
	}

	async getOrganization(id: string): Promise<Organization> {
		// Note: Backend doesn't have individual organization endpoint
		// Get from organizations list and filter by ID
		const organizations = await this.getOrganizations();
		const organization = organizations.find((org) => org.id === id);
		if (!organization) {
			throw new Error('Organization not found');
		}
		return organization;
	}

	async createOrganization(request: CreateOrganizationRequest): Promise<Organization> {
		const response = await this.fetch(`${this.baseUrl}/organizations/create/`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(request)
		});
		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.message || 'Failed to create organization');
		}
		const data = await response.json();
		return data.data;
	}

	async updateOrganization(
		_id: string,
		_request: UpdateOrganizationRequest
	): Promise<Organization> {
		// Note: Backend doesn't have organization update endpoint
		// This would need to be implemented in the backend if needed
		throw new Error('Organization update not implemented in backend');
	}

	async deleteOrganization(id: string): Promise<void> {
		const response = await this.fetch(`${this.baseUrl}/organizations/${id}/delete/`, {
			method: 'DELETE'
		});
		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.message || 'Failed to delete organization');
		}
	}

	async getMembers(organizationId: string): Promise<OrganizationMember[]> {
		const response = await this.fetch(`${this.baseUrl}/organizations/${organizationId}/members/`);
		if (!response.ok) {
			throw new Error('Failed to this.fetch members');
		}
		const data = await response.json();
		return data.data || [];
	}

	async updateMember(
		organizationId: string,
		memberId: string,
		request: UpdateMemberRequest
	): Promise<OrganizationMember> {
		const response = await this.fetch(
			`${this.baseUrl}/organizations/${organizationId}/members/${memberId}/`,
			{
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(request)
			}
		);
		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.message || 'Failed to update member');
		}
		const data = await response.json();
		return data.data;
	}

	async removeMember(organizationId: string, memberId: string): Promise<void> {
		const response = await this.fetch(
			`${this.baseUrl}/organizations/${organizationId}/members/${memberId}/`,
			{
				method: 'DELETE'
			}
		);
		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.message || 'Failed to remove member');
		}
	}

	async getInvitations(organizationId: string): Promise<OrganizationInvitation[]> {
		const response = await this.fetch(
			`${this.baseUrl}/organizations/${organizationId}/invitations/`
		);
		if (!response.ok) {
			throw new Error('Failed to this.fetch invitations');
		}
		const data = await response.json();
		return data.data || [];
	}

	async inviteUser(
		organizationId: string,
		request: InviteUserRequest
	): Promise<OrganizationInvitation> {
		const response = await this.fetch(`${this.baseUrl}/organizations/${organizationId}/invite/`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(request)
		});
		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.message || 'Failed to send invitation');
		}
		const data = await response.json();
		return data.data;
	}

	async cancelInvitation(organizationId: string, invitationId: string): Promise<void> {
		const response = await this.fetch(
			`${this.baseUrl}/organizations/${organizationId}/invitations/${invitationId}/cancel/`,
			{
				method: 'POST'
			}
		);
		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.message || 'Failed to cancel invitation');
		}
	}

	async resendInvitation(
		organizationId: string,
		invitationId: string
	): Promise<OrganizationInvitation> {
		const response = await this.fetch(
			`${this.baseUrl}/organizations/${organizationId}/invitations/${invitationId}/resend/`,
			{
				method: 'POST'
			}
		);
		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.message || 'Failed to resend invitation');
		}
		const data = await response.json();
		return data.data;
	}

	async getUserPendingInvitations(): Promise<OrganizationInvitation[]> {
		const response = await this.fetch(`${this.baseUrl}/user-pending-invitations/`);
		if (!response.ok) {
			const error = await response.json();
			return [];
			// throw new Error(error.message || 'Failed to get pending invitations');
		}
		const data = await response.json();
		return data.data;
	}

	async acceptInvitation(token: string): Promise<{ organization: Organization }> {
		const response = await this.fetch(`${this.baseUrl}/invitations/accept/`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ token })
		});
		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.message || 'Failed to accept invitation');
		}
		const data = await response.json();
		return data.data;
	}

	async declineInvitation(token: string): Promise<void> {
		const response = await this.fetch(`${this.baseUrl}/invitations/decline/`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ token })
		});
		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.message || 'Failed to decline invitation');
		}
	}
}

export const organizationAPIService = new OrganizationAPIService();
