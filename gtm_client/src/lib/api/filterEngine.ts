/**
 * Filter Engine API Service
 *
 * Provides frontend API calls to the backend filter engine
 * for validation and processing of GTM configurations.
 */

import type { GTMPage, GTMEvent, GTMPageRule, GTMEventRule } from '$lib/filter-engine';

export interface ValidationIssue {
	severity: 'error' | 'warning' | 'info';
	message: string;
	rule_id?: string;
	target?: string;
	level?: 'L1' | 'L2';
}

export interface RuleEffect {
	rule_id: string;
	effect: 'narrows' | 'redundant' | 'ineffective' | 'impossible';
	message?: string;
	affected_rule_id?: string;
}

export interface ValidationResult {
	is_valid: boolean;
	issues: ValidationIssue[];
	rule_effects: RuleEffect[];
	firing_logic?: string;
	canonical_rules: any[];
}

export interface ConfigurationValidationResponse {
	result: ValidationResult;
	test_results?: Array<{
		url: string;
		matches_pages: string[];
		matches_events: string[];
		firing_result: string;
	}>;
}

export interface FiringLogicResponse {
	firing_logic: string;
	summary: {
		pages: string;
		events: string;
		combined: string;
	};
	is_valid: boolean;
}

export interface InputHints {
	suggestions: string[];
	warnings: string[];
	prefixes: string[];
	suffixes: string[];
}

class FilterEngineAPIService {
	private baseUrl = '/api/v1/filter-engine';

	/**
	 * Validate complete GTM configuration
	 */
	async validateConfiguration(
		pages: GTMPage[],
		events: GTMEvent[] = [],
		testUrls: string[] = [],
		context?: any
	): Promise<ConfigurationValidationResponse> {
		const response = await fetch(`${this.baseUrl}/validate-configuration/`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				pages: pages.map(this.serializePage),
				events: events.map(this.serializeEvent),
				test_urls: testUrls,
				context
			})
		});

		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.message || 'Configuration validation failed');
		}

		return response.json();
	}

	/**
	 * Generate firing logic description
	 */
	async generateFiringLogic(
		pages: GTMPage[],
		events: GTMEvent[] = []
	): Promise<FiringLogicResponse> {
		const response = await fetch(`${this.baseUrl}/generate-firing-logic/`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				pages: pages.map(this.serializePage),
				events: events.map(this.serializeEvent)
			})
		});

		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.message || 'Failed to generate firing logic');
		}

		return response.json();
	}

	/**
	 * Validate a single rule in context
	 */
	async validateSingleRule(
		rule: GTMPageRule | GTMEventRule,
		level: 'page' | 'event',
		contextPages: GTMPage[] = [],
		contextEvents: GTMEvent[] = []
	): Promise<{
		is_valid: boolean;
		issues: ValidationIssue[];
		firing_logic?: string;
	}> {
		const response = await fetch(`${this.baseUrl}/validate-single-rule/`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				rule: this.serializeRule(rule),
				level,
				context_pages: contextPages.map(this.serializePage),
				context_events: contextEvents.map(this.serializeEvent)
			})
		});

		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.message || 'Rule validation failed');
		}

		const result = await response.json();
		return result.data;
	}

	/**
	 * Get input hints for rule creation
	 */
	async getInputHints(
		targetType: 'url' | 'datalayer',
		operation: string,
		contextPages: GTMPage[] = []
	): Promise<InputHints> {
		const response = await fetch(`${this.baseUrl}/get-input-hints/`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				target_type: targetType,
				operation,
				context_pages: contextPages.map(this.serializePage)
			})
		});

		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.message || 'Failed to get input hints');
		}

		const result = await response.json();
		return result.data;
	}

	/**
	 * Validate pages only (L1 validation)
	 */
	async validatePages(pages: GTMPage[], testUrls: string[] = []): Promise<ValidationResult> {
		const response = await this.validateConfiguration(pages, [], testUrls);
		return response.result;
	}

	/**
	 * Validate events in context of pages (L2 validation)
	 */
	async validateEvents(
		pages: GTMPage[],
		events: GTMEvent[],
		context?: { lastEditedEventId?: string }
	): Promise<ValidationResult> {
		const response = await this.validateConfiguration(pages, events, [], context);
		return response.result;
	}

	/**
	 * Test URLs against configuration
	 */
	async testUrls(
		pages: GTMPage[],
		events: GTMEvent[],
		testUrls: string[]
	): Promise<
		Array<{
			url: string;
			matches_pages: string[];
			matches_events: string[];
			firing_result: string;
		}>
	> {
		const response = await this.validateConfiguration(pages, events, testUrls);
		return response.test_results || [];
	}

	// Serialization helpers
	private serializePage(page: GTMPage): any {
		return {
			id: page.id,
			name: page.name,
			rules: page.rules.map(this.serializeRule),
			is_active: page.isActive
		};
	}

	private serializeEvent(event: GTMEvent): any {
		return {
			id: event.id,
			name: event.name,
			rules: event.rules.map(this.serializeRule),
			is_active: event.isActive,
			page_id: event.pageId
		};
	}

	private serializeRule(rule: GTMPageRule | GTMEventRule): any {
		return {
			id: rule.id,
			type: rule.type,
			condition: rule.condition,
			value: rule.value,
			is_active: rule.isActive
		};
	}
}

export const filterEngineAPI = new FilterEngineAPIService();

// Utility functions for UI integration
export function formatValidationIssue(issue: ValidationIssue): {
	type: 'error' | 'warning' | 'info';
	title: string;
	message: string;
	ruleId?: string;
} {
	const typeMap = {
		error: 'Error',
		warning: 'Warning',
		info: 'Info'
	};

	return {
		type: issue.severity,
		title: typeMap[issue.severity],
		message: issue.message,
		ruleId: issue.rule_id
	};
}

export function formatRuleEffect(effect: RuleEffect): {
	type: 'success' | 'warning' | 'error' | 'info';
	message: string;
	ruleId: string;
} {
	const typeMap = {
		narrows: 'success',
		redundant: 'warning',
		ineffective: 'warning',
		impossible: 'error'
	};

	const messageMap = {
		narrows: 'Narrows selection',
		redundant: 'Redundant rule',
		ineffective: 'Ineffective rule',
		impossible: 'Impossible condition'
	};

	return {
		type: typeMap[effect.effect] || 'info',
		message: effect.message || messageMap[effect.effect] || effect.effect,
		ruleId: effect.rule_id
	};
}

export function isConfigurationValid(result: ValidationResult): boolean {
	return result.is_valid && !result.issues.some((issue) => issue.severity === 'error');
}

export function getErrorCount(result: ValidationResult): number {
	return result.issues.filter((issue) => issue.severity === 'error').length;
}

export function getWarningCount(result: ValidationResult): number {
	return result.issues.filter((issue) => issue.severity === 'warning').length;
}
