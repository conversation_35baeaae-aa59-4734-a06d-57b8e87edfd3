import { env } from '$env/dynamic/private';

/**
 * Server-side configuration that accesses environment variables
 */
export const serverConfig = {
	/**
	 * Get the MCP server URL from environment variables
	 */
	getMcpServerUrl(): string {
		return env.MCP_SERVER_URL || 'http://localhost:8006';
	},

	/**
	 * Get the API URL from environment variables
	 */
	getApiUrl(): string {
		return env.API_URL || 'http://localhost:8000';
	},

	/**
	 * Get the AI Agent URL from environment variables
	 */
	getAiAgentUrl(): string {
		return env.AI_AGENT_URL || 'http://localhost:3002';
	}
};
