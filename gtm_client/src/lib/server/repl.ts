import { type Actions, fail, redirect, type RequestEvent } from '@sveltejs/kit';
import { gh } from '$lib/server/request';
import { flash } from '$lib/server/flash';
import { SERVER_ERROR_500 } from '$lib/defaults/status';
import { SERVER_ERROR_MSG } from '$lib/defaults/messages';
import type { ZodObject } from 'zod/v4';
// import { superValidate } from 'sveltekit-superforms';
import { zod4 } from 'sveltekit-superforms/adapters';
import { setError, superValidate } from 'sveltekit-superforms';

export type BASE_METHOD = 'GET' | 'POST' | 'PUT' | 'DELETE';
export type NamedActionInfo = {
	name: string;
	method: BASE_METHOD;
	allowCookies?: boolean;
	schema?: ZodObject<any>;
	namespace?: string;
};

type Options = { djangoBaseApi?: string; allowCookies?: boolean; addHeaders?: boolean };

const default_options = {
	djangoBaseApi: '/spark',
	allowCookies: true,
	addHeaders: true
} as Options;

const formDataToString = (formData: FormData) => {
	const entries = formData.entries();
	let result = '';
	for (const [key, value] of entries) {
		result += `${key}=${value}&`;
	}
	return result;
};

const triggerFlashMessage = (event: RequestEvent, data?: Partial<Message>) => {
	if (typeof data?.redirect === 'string' && typeof data?.message === 'string') {
		const flashMessage: FlashMessage = {
			...(data as Message),
			alias: data.alias || 'default_alias', // Ensure alias is provided
			path: data.redirect
		};
		flash(event.cookies, flashMessage);
		redirect(303, flashMessage.path);
	}
};
/*
This function is used to create action triggers for Django's API endpoints via their names.
Example: path('do-something/', do_something, name='do_something_view_name'),
export the actions as via_route_name("do_something_view_name");
 */
export const callViaRouteName = <T extends Partial<Message> = Partial<Message>>(
	proxyPaths: string | string[] | NamedActionInfo[],
	initialOptions: Options = {}
) => {
	initialOptions = { ...default_options, ...initialOptions };
	let actions: Actions = {};
	if (typeof proxyPaths === 'string') {
		proxyPaths = [proxyPaths];
	}
	proxyPaths.map((proxyAction) => {
		if (typeof proxyAction === 'string') {
			proxyAction = { name: proxyAction, method: 'POST', allowCookies: false };
		}
		const action: Actions = {
			[proxyAction.name]: async (event: RequestEvent) => {
				let formData;
				let form;
				if (proxyAction.schema) {
					form = await superValidate(event, zod4(proxyAction.schema));
					if (!form.valid) {
						return fail(400, { form });
					}
					formData = form.data;
				} else {
					formData = await event.request.formData();
				}

				let url = `${initialOptions.djangoBaseApi}/action/?url_name=${proxyAction.name}`;

				if (proxyAction?.namespace) {
					url += `&namespace=${proxyAction.namespace}`;
				}

				let options: RequestInit = {
					method: proxyAction.method,
					credentials: 'include' // Include cookies for authentication
				};

				if (initialOptions.addHeaders) options = { ...options, headers: gh(event, false) };

				if (proxyAction.method === 'POST' || proxyAction.method === 'PUT') {
					const stringify = JSON.stringify(formData);

					options = { ...options, body: stringify };
				} else {
					url = `${url}&${formDataToString(formData)}`;
				}

				let response: Response;
				let data: T = {} as T;
				try {
					response = await event.fetch(url, options);

					if (response.status === 204) {
						return;
					}
					data = await response.json();
				} catch (e) {
					console.error(`Proxy call error \`${proxyAction.name}\`:`, e);
					return fail(SERVER_ERROR_500, SERVER_ERROR_MSG);
				}

				// // Check if response has 'redirect' and 'message' properties and handle accordingly
				triggerFlashMessage(event, data);

				if (response.ok) {
					if (proxyAction.schema && form) {
						return {
							form,
							...data
						};
					}
					return data;
				}

				if (proxyAction.schema && form) {
					// console.log('form', data);
					const errorMessage = data?.message ?? 'Error occurred';
					return setError(form, '', errorMessage, { status: response.status });
				}

				return fail(response.status, { ...data });
			}
		};
		actions = { ...actions, ...action };
	});
	return actions;
};
