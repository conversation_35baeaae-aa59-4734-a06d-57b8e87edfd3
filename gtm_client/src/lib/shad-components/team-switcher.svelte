<script lang="ts">
	import * as DropdownMenu from '$lib/shad-components/ui/dropdown-menu/index.js';
	import * as Sidebar from '$lib/shad-components/ui/sidebar/index.js';
	import { useSidebar } from '$lib/shad-components/ui/sidebar/index.js';
	import ChevronsUpDownIcon from '@lucide/svelte/icons/chevrons-up-down';
	import PlusIcon from '@lucide/svelte/icons/plus';
	import UserIcon from '@lucide/svelte/icons/user';
	import UsersIcon from '@lucide/svelte/icons/users';

	import { goto } from '$app/navigation';

	import type { Organization } from '$lib/api/organizations';

	import { invalidateWithLoading } from '$lib/utils/navigation';
	import {
		organizationsPersistedStore,
		currentOrganizationPersistedStore
	} from '$lib/stores/organization.svelte';
	import { watch } from 'runed';
	import { page } from '$app/state';
	import { onMount } from 'svelte';

	const sidebar = useSidebar();

	const handleOrganizationSwitch = async (org: Organization) => {
		if (!currentOrganization || currentOrganization.id == org.id) {
			return;
		}
		// currentOrganization = org;
		try {
			const response = await fetch('/local/organization/set', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(org)
			});

			if (response.ok) {
				await invalidateWithLoading('app:currentOrganization');

				// await goto('/dashboard', { invalidateAll: true, replaceState: true });
			} else {
				console.error('Failed to set organization on server');
			}
		} catch (error) {
			console.error('Network error while setting organization:', error);
		}
	};

	const getOrganizationIcon = (type: string) => {
		return type === 'personal' ? UserIcon : UsersIcon;
	};

	const getOrganizationPlan = (type: string) => {
		return type === 'personal' ? 'Personal' : 'Team';
	};

	const handleCreateOrganization = () => {
		goto('/teams?create=true');
	};

	watch(
		() => page.data.currentOrganizationID,
		() => {
			const _org = organizationsPersistedStore.current?.find(
				(org) => org.id === page.data.currentOrganizationID
			);
			if (_org) {
				currentOrganization = _org;
			}
		}
	);

	let currentOrganization = $derived.by(() => {
		const _org = organizationsPersistedStore.current?.find(
			(org) => org.id === page.data.currentOrganizationID
		);
		return _org;
	});

	watch(
		() => currentOrganization,
		() => {
			currentOrganizationPersistedStore.current = currentOrganization;
		}
	);

	const getUserOrganizations = async () => {
		const relativePath = '/spark/api/v1/organizations/';
		const absoluteUrl = new URL(relativePath, page.url.origin);
		return fetch(absoluteUrl)
			.then(async (res) => {
				if (!res.ok) {
					console.error('Failed to fetch organizations:', await res.text());
					return [];
				} else {
					return res.json();
				}
			})
			.then((data) => data?.data ?? [])
			.then((data) => {
				organizationsPersistedStore.current = data;
			});
	};

	onMount(() => {
		getUserOrganizations();
	});

	let organizations = $derived(organizationsPersistedStore.current);
</script>

<Sidebar.Menu>
	<Sidebar.MenuItem>
		<DropdownMenu.Root
			onOpenChange={() => {
				getUserOrganizations();
			}}
		>
			<DropdownMenu.Trigger>
				{#snippet child({ props })}
					<Sidebar.MenuButton
						{...props}
						size="lg"
						class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
					>
						{#if currentOrganization}
							{@const IconComponent = getOrganizationIcon(currentOrganization.type)}
							<div
								class="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg"
							>
								<IconComponent class="size-4" />
							</div>
							<div class="grid flex-1 text-left text-sm leading-tight">
								<span class="truncate font-medium">
									{currentOrganization.name}
								</span>
								<span class="truncate text-xs">{getOrganizationPlan(currentOrganization.type)}</span
								>
							</div>
						{:else}
							<div
								class="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg"
							>
								<UserIcon class="size-4" />
							</div>
							<div class="grid flex-1 text-left text-sm leading-tight">
								<span class="truncate font-medium">Loading...</span>
								<span class="truncate text-xs">Please wait</span>
							</div>
						{/if}
						<ChevronsUpDownIcon class="ml-auto" />
					</Sidebar.MenuButton>
				{/snippet}
			</DropdownMenu.Trigger>
			<DropdownMenu.Content
				class="w-(--bits-dropdown-menu-anchor-width) min-w-56 rounded-lg"
				align="start"
				side={sidebar.isMobile ? 'bottom' : 'right'}
				sideOffset={4}
			>
				<DropdownMenu.Label class="text-muted-foreground text-xs">Organizations</DropdownMenu.Label>
				{#each organizations as org, index (org.id)}
					{@const IconComponent = getOrganizationIcon(org.type)}
					<DropdownMenu.Item onSelect={() => handleOrganizationSwitch(org)} class="gap-2 p-2">
						<div class="flex size-6 items-center justify-center rounded-md border">
							<IconComponent class="size-3.5 shrink-0" />
						</div>
						{org.name}
						<DropdownMenu.Shortcut>⌘{index + 1}</DropdownMenu.Shortcut>
					</DropdownMenu.Item>
				{/each}
				<DropdownMenu.Separator />
				<DropdownMenu.Item onSelect={handleCreateOrganization} class="gap-2 p-2">
					<div class="flex size-6 items-center justify-center rounded-md border bg-transparent">
						<PlusIcon class="size-4" />
					</div>
					<div class="text-muted-foreground font-medium">Create Organization</div>
				</DropdownMenu.Item>
			</DropdownMenu.Content>
		</DropdownMenu.Root>
	</Sidebar.MenuItem>
</Sidebar.Menu>
