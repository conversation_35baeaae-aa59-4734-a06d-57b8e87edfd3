<script lang="ts">
	import SearchIcon from '@lucide/svelte/icons/search';
	import { Label } from '$lib/shad-components/ui/label/index.js';
	import * as Sidebar from '$lib/shad-components/ui/sidebar/index.js';
	import type { HTMLFormAttributes } from 'svelte/elements';
	import type { WithElementRef } from './utils';

	let { ref = $bindable(null), ...restProps }: WithElementRef<HTMLFormAttributes> = $props();
</script>

<form {...restProps} bind:this={ref}>
	<div class="relative">
		<Label for="search" class="sr-only">Search</Label>
		<Sidebar.Input id="search" name="search" placeholder="Type to search..." class="h-8 pl-7" />
		<SearchIcon
			class="pointer-events-none absolute top-1/2 left-2 size-4 -translate-y-1/2 opacity-50 select-none"
		/>
	</div>
</form>
