<script lang="ts">
	import SidebarIcon from '@lucide/svelte/icons/sidebar';

	import * as Breadcrumb from '$lib/shad-components/ui/breadcrumb/index.js';
	import { Button } from '$lib/shad-components/ui/button/index.js';
	import { Separator } from '$lib/shad-components/ui/separator/index.js';
	import * as Sidebar from '$lib/shad-components/ui/sidebar/index.js';

	import IconLogo from '$lib/icons/gettrackiton/IconLogo.svelte';
	import { toggleMode } from 'mode-watcher';
	import SunIcon from '@lucide/svelte/icons/sun';
	import MoonIcon from '@lucide/svelte/icons/moon';

	import { globalBreadcrumbs } from '$lib/stores/side-nav.svelte';

	let { children }: { children?: any } = $props();

	let breadcrumbs = $derived(globalBreadcrumbs.crumbs ?? []);

	const sidebar = Sidebar.useSidebar();
</script>

{#snippet ActionArea()}
	{#if children}
		{@render children()}
	{/if}
{/snippet}

<header class="sticky top-0 z-50 flex w-full items-center border-b backdrop-blur">
	<div class="flex h-(--header-height) w-full items-center gap-2 px-4">
		<Button class="size-8" variant="ghost" size="icon" onclick={sidebar.toggle}>
			<SidebarIcon />
		</Button>
		<Separator orientation="vertical" class="mr-2 h-4" />

		<Breadcrumb.Root class="hidden sm:block">
			<Breadcrumb.List>
				{#each breadcrumbs as breadcrumb, index (breadcrumb)}
					<Breadcrumb.Item>
						<Breadcrumb.Link class="capitalize" href={breadcrumb.href}>
							{#if typeof breadcrumb.title === 'string'}
								{breadcrumb.title}
							{:else}
								{@render breadcrumb.title()}
							{/if}
						</Breadcrumb.Link>
					</Breadcrumb.Item>
					{#if index < breadcrumbs.length - 1}
						<Breadcrumb.Separator />
					{/if}
				{/each}
			</Breadcrumb.List>
		</Breadcrumb.Root>

		<a href="/dashboard" class=" flex items-center justify-center sm:mx-auto">
			<IconLogo />
			<h4 class="my-0 font-semibold">GTM Mixer</h4>
		</a>
		<div class="hidden lg:block">
			{@render ActionArea()}
		</div>

		<Button onclick={toggleMode} variant="outline" size="icon" class="ml-auto">
			<SunIcon
				class="h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90"
			/>
			<MoonIcon
				class="absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0"
			/>
			<span class="sr-only">Toggle theme</span>
		</Button>
	</div>
</header>
