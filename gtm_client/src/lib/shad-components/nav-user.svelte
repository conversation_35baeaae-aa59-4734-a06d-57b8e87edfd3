<script lang="ts">
	import * as Avatar from '$lib/shad-components/ui/avatar/index.js';
	import * as DropdownMenu from '$lib/shad-components/ui/dropdown-menu/index.js';
	import * as Sidebar from '$lib/shad-components/ui/sidebar/index.js';
	import { useSidebar } from '$lib/shad-components/ui/sidebar/index.js';
	import BadgeCheckIcon from '@lucide/svelte/icons/badge-check';
	import BellIcon from '@lucide/svelte/icons/bell';
	import ChevronsUpDownIcon from '@lucide/svelte/icons/chevrons-up-down';
	import CreditCardIcon from '@lucide/svelte/icons/credit-card';
	import LogOutIcon from '@lucide/svelte/icons/log-out';
	import SparklesIcon from '@lucide/svelte/icons/sparkles';
	import { logout } from '$lib/utils/auth';

	let { user }: { user: { first_name: string; last_name: string; email: string; avatar: string } } =
		$props();
	const sidebar = useSidebar();

	let avatarFallBack = $derived.by(() => {
		const fn = user?.first_name?.trim();
		const ln = user?.last_name?.trim();
		const email = user?.email?.trim();

		let initials = '';

		if (fn && fn.length > 0) {
			initials += fn[0];
		}
		if (ln && ln.length > 0) {
			initials += ln[0];
		}

		if (initials.length > 0) {
			return initials.toUpperCase();
		} else if (email && email.length > 0) {
			return email.slice(0, 2).toUpperCase();
		}

		return '??'; // Default fallback if no name or email parts
	});

	let nameFallback = $derived.by(() => {
		const fn = user?.first_name?.trim();
		const ln = user?.last_name?.trim();
		const email = user?.email?.trim();

		if (fn && fn.length > 0 && ln && ln.length > 0) {
			return `${fn} ${ln}`;
		}
		if (fn && fn.length > 0) {
			return fn;
		}
		if (ln && ln.length > 0) {
			return ln;
		}
		if (email && email.length > 0) {
			return email;
		}
		return 'User'; // Default fallback display name
	});

	async function handleLogout() {
		await logout();
	}
</script>

<Sidebar.Menu>
	<Sidebar.MenuItem>
		<DropdownMenu.Root>
			<DropdownMenu.Trigger>
				{#snippet child({ props })}
					<Sidebar.MenuButton
						size="lg"
						class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
						{...props}
					>
						<Avatar.Root class="size-8 rounded-lg">
							<Avatar.Image src={user?.avatar} alt={nameFallback} />
							<Avatar.Fallback class="rounded-lg">{avatarFallBack}</Avatar.Fallback>
						</Avatar.Root>
						<div class="grid flex-1 text-left text-sm leading-tight">
							<span class="truncate font-medium">{user?.first_name}</span>
							<span class="truncate text-xs">{user?.email}</span>
						</div>
						<ChevronsUpDownIcon class="ml-auto size-4" />
					</Sidebar.MenuButton>
				{/snippet}
			</DropdownMenu.Trigger>
			<DropdownMenu.Content
				class="w-(--bits-dropdown-menu-anchor-width) min-w-56 rounded-lg"
				side={sidebar.isMobile ? 'bottom' : 'right'}
				align="end"
				sideOffset={4}
			>
				<DropdownMenu.Label class="p-0 font-normal">
					<div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
						<Avatar.Root class="size-8 rounded-lg">
							<Avatar.Image src={user.avatar} alt={user?.first_name} />
							<Avatar.Fallback class="rounded-lg">{avatarFallBack}</Avatar.Fallback>
						</Avatar.Root>
						<div class="grid flex-1 text-left text-sm leading-tight">
							<span class="truncate font-medium">{user?.first_name}</span>
							<span class="truncate text-xs">{user.email}</span>
						</div>
					</div>
				</DropdownMenu.Label>
				<DropdownMenu.Separator />
				<DropdownMenu.Group>
					<DropdownMenu.Item>
						<SparklesIcon />
						Upgrade to Pro
					</DropdownMenu.Item>
				</DropdownMenu.Group>
				<DropdownMenu.Separator />
				<DropdownMenu.Group>
					<DropdownMenu.Item>
						<BadgeCheckIcon />
						Account
					</DropdownMenu.Item>
					<DropdownMenu.Item>
						<CreditCardIcon />
						Billing
					</DropdownMenu.Item>
					<DropdownMenu.Item>
						<BellIcon />
						Notifications
					</DropdownMenu.Item>
				</DropdownMenu.Group>
				<DropdownMenu.Separator />
				<DropdownMenu.Item onclick={handleLogout}>
					<LogOutIcon />
					Log out
				</DropdownMenu.Item>
			</DropdownMenu.Content>
		</DropdownMenu.Root>
	</Sidebar.MenuItem>
</Sidebar.Menu>
