<script lang="ts">
	import NavMain from './nav-main.svelte';

	import NavUser from './nav-user.svelte';
	import TeamSwitcher from './team-switcher.svelte';
	import * as Sidebar from '$lib/shad-components/ui/sidebar/index.js';
	import type { ComponentProps } from 'svelte';
	import Separator from './ui/separator/separator.svelte';

	import { globalNavLinks } from '$lib/stores/side-nav.svelte';
	import Users from '@lucide/svelte/icons/users';
	import { page } from '$app/state';

	let navItems = $derived.by(() => {
		let links = [
			...globalNavLinks.links,
			{
				title: 'Teams',
				url: '/teams',
				icon: Users
			}
		];
		return links;
	});

	let {
		ref = $bindable(null),
		collapsible = 'icon',
		...restProps
	}: ComponentProps<typeof Sidebar.Root> = $props();
</script>

<Sidebar.Root
	{collapsible}
	class="top-(--header-height) h-[calc(100svh-var(--header-height))]!"
	{...restProps}
>
	<Sidebar.Content>
		<NavMain items={navItems} />
	</Sidebar.Content>
	<Sidebar.Footer>
		<Sidebar.GroupContent>
			<Sidebar.GroupLabel>Select Account</Sidebar.GroupLabel>
			<TeamSwitcher />
		</Sidebar.GroupContent>
		<Separator class="my-12" />
		<Sidebar.GroupContent class="mb-4">
			<Sidebar.GroupLabel>Manage Profile</Sidebar.GroupLabel>
			<NavUser user={page.data.user} />
		</Sidebar.GroupContent>
	</Sidebar.Footer>
	<Sidebar.Rail />
</Sidebar.Root>
