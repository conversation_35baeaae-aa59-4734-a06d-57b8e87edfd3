<script lang="ts">
	import ChevronLeftIcon from '@lucide/svelte/icons/chevron-left';
	import ChevronRightIcon from '@lucide/svelte/icons/chevron-right';
	import { MediaQuery } from 'svelte/reactivity';
	import * as Pagination from '$lib/shad-components/ui/pagination/index.js';

	const isDesktop = new MediaQuery('(min-width: 768px)');
	let { total_items, limit, page = $bindable(), fetching, onPageChange, ...props } = $props();

	const count = $derived(total_items);
	const perPage = $derived(limit);
	const siblingCount = $derived(isDesktop.current ? 1 : 0);

	// Handle page changes - use callback if provided, otherwise use bindable
	function handlePageChange(newPage: number) {
		if (onPageChange) {
			onPageChange(newPage);
		} else {
			page = newPage;
		}
	}
</script>

<Pagination.Root {count} {page} onPageChange={handlePageChange} {perPage} {siblingCount} {...props}>
	{#snippet children({ pages, currentPage })}
		<Pagination.Content>
			<Pagination.Item>
				<Pagination.PrevButton disabled={fetching}>
					<ChevronLeftIcon class="size-4" />
					<span class="hidden sm:block">Previous</span>
				</Pagination.PrevButton>
			</Pagination.Item>
			{#each pages as page (page.key)}
				{#if page.type === 'ellipsis'}
					<Pagination.Item>
						<Pagination.Ellipsis />
					</Pagination.Item>
				{:else}
					<Pagination.Item>
						<Pagination.Link disabled={fetching} {page} isActive={currentPage === page.value}>
							{page.value}
						</Pagination.Link>
					</Pagination.Item>
				{/if}
			{/each}
			<Pagination.Item>
				<Pagination.NextButton disabled={fetching}>
					<span class="hidden sm:block">Next</span>
					<ChevronRightIcon class="size-4" />
				</Pagination.NextButton>
			</Pagination.Item>
		</Pagination.Content>
	{/snippet}
</Pagination.Root>
