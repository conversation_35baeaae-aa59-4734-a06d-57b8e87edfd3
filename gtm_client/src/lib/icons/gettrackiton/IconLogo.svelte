<script lang="ts">
	import { type IconProps } from '$lib/icons/types';
	type Custom = Omit<IconProps, 'stroke'>;
	let { width = 39, height = 38, fill = '#2E90FA', ...props }: Custom = $props();
</script>

<svg {width} {height} viewBox="0 0 39 38" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
	<g clip-path="url(#clip0_71_13611)">
		<circle cx="19.5537" cy="19.0004" r="15.2275" {fill} />
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M26.6923 3.45966C27.4357 4.203 27.4357 5.40818 26.6923 6.15152L12.3343 20.5096C11.5909 21.2529 10.3857 21.2529 9.6424 20.5096L4.37523 15.2424C3.63189 14.4991 3.63189 13.2939 4.37523 12.5506L18.7333 -1.80751C19.4766 -2.55085 20.6818 -2.55085 21.4252 -1.80751L26.6923 3.45966Z"
			fill="white"
			fill-opacity="0.5"
		/>
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M33.7634 23.6915C34.5067 24.4348 34.5067 25.64 33.7634 26.3834L21.263 38.8837C20.5197 39.627 19.3145 39.627 18.5712 38.8837L12.1389 32.4514C11.3956 31.7081 11.3956 30.5029 12.1389 29.7595L24.6392 17.2592C25.3826 16.5159 26.5877 16.5159 27.3311 17.2592L33.7634 23.6915Z"
			fill="black"
			fill-opacity="0.3"
		/>
	</g>
	<defs>
		<clipPath id="clip0_71_13611">
			<rect x="4.78125" y="4" width="30" height="30" rx="15" fill="white" />
		</clipPath>
	</defs>
</svg>
