/**
 * Authentication utilities for the GTM client
 */

import { goto } from '$app/navigation';

/**
 * Logout the current user by using the SvelteKit logout route
 * which handles Django session clearing server-side via the /spark API proxy
 */
export async function logout(): Promise<void> {
	console.log('🔍 [AUTH] Starting logout process');

	// Use the SvelteKit logout route which handles Django logout server-side
	// The server route calls /spark/auth/logout/ which gets proxied to Django
	// This avoids CSRF issues and ensures proper session handling
	goto('/auth/logout', { replaceState: true });
}

/**
 * Check if the user is authenticated based on the presence of session data
 * This is a client-side check and should not be relied upon for security
 */
export function isAuthenticated(): boolean {
	// This is a simple client-side check
	// Real authentication state is managed server-side
	if (typeof document === 'undefined') {
		return false; // SSR context
	}

	// Check if sessionid cookie exists
	return document.cookie.includes('sessionid=');
}

/**
 * Redirect to login page if not authenticated
 */
export function requireAuth(): void {
	if (!isAuthenticated()) {
		goto('/auth/login');
	}
}
