import { invalidate } from '$app/navigation';
import { startLoading, stopLoading } from '$lib/stores/loading.svelte';

/**
 * Enhanced invalidate with loading bar
 */
export async function invalidateWithLoading(dependency?: string | URL | ((url: URL) => boolean)) {
	startLoading();

	try {
		if (dependency) {
			await invalidate(dependency);
		} else {
			await invalidate(() => true); // Invalidate all
		}
	} finally {
		stopLoading();
	}
}

/**
 * Show loading bar manually
 */
export function showLoadingBar() {
	startLoading();
}

/**
 * Hide loading bar manually
 */
export function hideLoadingBar() {
	stopLoading();
}

/**
 * Execute any async operation with loading bar
 */
export async function withLoadingBar<T>(operation: () => Promise<T>): Promise<T> {
	startLoading();
	try {
		return await operation();
	} finally {
		stopLoading();
	}
}

/**
 * Create a loading state for components
 */
export function createLoadingState() {
	let loading = $state(false);

	return {
		get loading() {
			return loading;
		},
		async execute<T>(fn: () => Promise<T>): Promise<T> {
			loading = true;
			startLoading();
			try {
				return await fn();
			} finally {
				loading = false;
				stopLoading();
			}
		}
	};
}
