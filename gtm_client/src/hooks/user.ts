import type { Handle, RequestEvent } from '@sveltejs/kit';
import { dev } from '$app/environment';

export const getUserData = async (event: RequestEvent) => {
	const response = await event.fetch(`/spark/api/v1/user/`);
	return await response.json();
};

export const handleUser: Handle = async ({ event, resolve }) => {
	if (dev) {
		event.locals.user = {
			id: 1,
			email: '<EMAIL>',
			first_name: 'Admin',
			last_name: 'GTM',
			avatar: ''
		};
		return resolve(event);
	}
	const res = await getUserData(event);
	if (res.status_code == '200') {
		event.locals.user = res.data;
	}

	return await resolve(event);
};
