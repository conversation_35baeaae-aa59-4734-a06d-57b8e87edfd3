import type { Handle } from '@sveltejs/kit';

export const handleMessagesCookies: Handle = async ({ event, resolve }) => {
	if (event.cookies.get('message')) {
		event.locals.message = event.cookies.get('message');
		event.cookies.delete('message', {
			path: '/'
		});
	}
	if (event.cookies.get('messageType')) {
		event.locals.messageType = event.cookies.get('messageType');
		event.cookies.delete('messageType', {
			path: '/'
		});
	}
	return resolve(event);
};
