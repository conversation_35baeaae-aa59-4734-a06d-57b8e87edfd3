import type { Handle } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';
import { dev } from '$app/environment';
// import { generateCacheControl } from '$lib/server/cache';

export const handleApi: Handle = async ({ event, resolve }) => {
	if (event.url.pathname.startsWith('/.well-known/appspecific/com.chrome.devtools')) {
		return new Response(null, { status: 204 }); // Return empty response with 204 No Content
	}

	if (!env.API_URL) {
		console.error('❌ API_URL environment variable is not set');
		return new Response(
			JSON.stringify({
				error: 'API configuration missing',
				message: 'API_URL environment variable is not configured'
			}),
			{
				status: 500,
				headers: { 'content-type': 'application/json' }
			}
		);
	}

	if (event.url.pathname.startsWith('/ai-agent')) {
		const agentPath = event.url.pathname;
		console.log('Agent path:', agentPath);
		const targetUrl = `${env.API_URL}${agentPath}${event.url.search}`;
		console.log('Agent target URL:', targetUrl);

		const headers = new Headers(event.request.headers);

		if (dev) {
			headers.set('X-GTM-API-KEY', '1234');
		}

		if (event.locals.currentOrganizationID) {
			headers.set('X-Org-Id', event.locals.currentOrganizationID);
		}

		if (event.locals.user) {
			headers.set('X-User-Id', String(event.locals.user.id));
		}

		headers.set('Accept', 'application/json, text/event-stream');

		try {
			const response = await event.fetch(targetUrl, {
				method: event.request.method,
				headers: headers,
				body: ['GET', 'HEAD'].includes(event.request.method)
					? undefined
					: await event.request.text(),
				signal: event.request.signal,
				keepalive: event.request.keepalive,
				credentials: 'include'
			});

			// Handle Server-Sent Events
			if (response.headers.get('content-type')?.includes('text/event-stream')) {
				return new Response(response.body, {
					status: response.status,
					statusText: response.statusText,
					headers: {
						'content-type': 'text/event-stream',
						'cache-control': 'no-cache',
						connection: 'keep-alive'
					}
				});
			}

			// Handle regular responses
			const contentType = response.headers.get('content-type') || 'application/json';
			return new Response(response.body, {
				status: response.status,
				statusText: response.statusText,
				headers: {
					'content-type': contentType
				}
			});
		} catch (error) {
			console.error(`❌ AI Agent Request failed: ${error}`);
			console.error(`URL: ${targetUrl}`);
			console.error(`Method: ${event.request.method}`);

			return new Response(
				JSON.stringify({
					error: 'AI Agent request failed',
					message: error instanceof Error ? error.message : 'Unknown error',
					url: targetUrl
				}),
				{
					status: 502,
					headers: { 'content-type': 'application/json' }
				}
			);
		}
	}

	if (event.url.pathname.startsWith('/spark')) {
		event.url.pathname = event.url.pathname.replace('/spark', '');
		const prepared_url = event.url.href.replace(event.url.origin, env.API_URL);

		console.log(`🔗 API Request: ${event.request.method} ${event.url.pathname} → ${prepared_url}`);

		const _backedUpHeaders = new Headers(event.request.headers);
		if (dev) {
			_backedUpHeaders.append('X-GTM-API-KEY', '1234');
		}

		try {
			const response = await event.fetch(prepared_url, {
				headers: _backedUpHeaders,
				method: event.request.method,
				signal: event.request.signal,
				keepalive: event.request.keepalive,
				credentials: 'include',
				body: ['GET', 'HEAD'].includes(event.request.method)
					? undefined
					: await event.request.text()
			});

			const req_content_type = event.request.headers.get('content-type') || 'application/json';
			const content_type = response.headers.get('content-type') || req_content_type;
			const set_cookie = response.headers.get('set-cookie') || '';

			// const cache_control = generateCacheControl({
			// 	headers: response.headers,
			// 	method: event.request.method
			// });

			return new Response(response.body, {
				status: response.status,
				statusText: response.statusText,
				headers: {
					'content-type': content_type,
					'set-cookie': set_cookie
					// 'cache-control': cache_control
				}
			});
		} catch (error) {
			console.error(`❌ API Request failed: ${error}`);
			console.error(`URL: ${prepared_url}`);
			console.error(`Method: ${event.request.method}`);

			return new Response(
				JSON.stringify({
					error: 'API request failed',
					message: error instanceof Error ? error.message : 'Unknown error',
					url: prepared_url
				}),
				{
					status: 502,
					headers: { 'content-type': 'application/json' }
				}
			);
		}
	}
	return resolve(event);
};
