import type { Organization } from '$lib/api/organizations';
import type { ResponseSchema } from '$lib/interfaces/req-resp';
import type { Handle, RequestEvent } from '@sveltejs/kit';

async function getPersonalOrganization(event: RequestEvent): Promise<ResponseSchema<Organization>> {
	const response = await event.fetch(`/spark/api/v1/organizations/personal-organizations/`);
	return await response.json();
}

const getAndSetPersonalOrg = async (event: RequestEvent) => {
	/**
	 * This method retrieves the User's personal organization and sets the currentOrganizationID cookie
	 */
	const res = await getPersonalOrganization(event);
	if (res.status_code != '200') {
		return;
	}
	const orgId = res?.data?.id;

	console.log('Personal org:', orgId);
	if (!orgId) {
		return;
	}
	event.cookies.set('currentOrganizationID', orgId, {
		path: '/',
		httpOnly: true,
		secure: process.env.NODE_ENV === 'production',
		sameSite: 'lax',
		maxAge: 60 * 60 * 24 * 30 // 30 days
	});
	return orgId;
};

export const handleOrganization: Handle = async ({ event, resolve }) => {
	const orgCookieID = event.cookies.get('currentOrganizationID');
	console.log('Org cookie:', orgCookieID);
	if (!orgCookieID) {
		await getAndSetPersonalOrg(event);
	}
	console.log('orgCookieID', orgCookieID);
	event.locals.currentOrganizationID = orgCookieID;
	return await resolve(event);
};
