import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request, cookies }) => {
	const organization = await request.json();

	if (organization) {
		cookies.set('currentOrganizationID', organization.id, {
			path: '/',
			httpOnly: true,
			secure: process.env.NODE_ENV === 'production',
			sameSite: 'lax',
			maxAge: 60 * 60 * 24 * 30 // 30 days
		});
	} else {
		cookies.delete('currentOrganizationID', { path: '/' });
	}

	return json({ success: true });
};
