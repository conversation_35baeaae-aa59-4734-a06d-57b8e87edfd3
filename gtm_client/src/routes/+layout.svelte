<script module lang="ts">
	export { HouseIcon, NetworkIcon, ProjectIcon, EditProjectIcon };
</script>

<script>
	import { ModeWatcher } from 'mode-watcher';
	import { page } from '$app/state';

	import '../app.css';
	import '../fonts/styles/inter.css';
	import { locales, localizeHref } from '$lib/paraglide/runtime';

	import { Toaster } from '$lib/shad-components/ui/sonner/index.js';
	import LoadingBar from '$lib/components/LoadingBar.svelte';
	import NotificationPanel from '$lib/components/NotificationPanel.svelte';
	import { notificationService } from '$lib/services/notificationService';

	import { toast } from 'svelte-sonner';

	import House from '@lucide/svelte/icons/house';
	import Network from '@lucide/svelte/icons/network';
	import Pencil from '@lucide/svelte/icons/pencil';

	import FolderOpenDot from '@lucide/svelte/icons/folder-open-dot';
	let { children } = $props();

	let message = $derived(page.data?.message);
	let messageType = $state(page.data?.messageType);

	$effect(() => {
		if (message) {
			try {
				const type = messageType;
				if (type === 'error') {
					toast.error(page.data.message);
					return;
				}
				if (type === 'success') {
					toast.success(page.data.message);
					return;
				}
				if (type === 'warning') {
					toast.warning(page.data.message);
					return;
				}
				if (type === 'info') {
					toast.info(page.data.message);
					return;
				}
			} finally {
				page.data.message = '';
				page.data.messageType = '';
				message = '';
				messageType = '';
			}
		}
	});
</script>

<!--BreadCrumb Icons -->
{#snippet HouseIcon()}
	<div class="flex items-center gap-1">
		<House />
		<i>Home</i>
	</div>
{/snippet}

{#snippet NetworkIcon()}
	<div class="flex items-center gap-1">
		<Network />
		<i>Workspace</i>
	</div>
{/snippet}

{#snippet ProjectIcon()}
	<div class="flex items-center gap-1">
		<FolderOpenDot />
		<i>Project</i>
	</div>
{/snippet}

{#snippet EditProjectIcon()}
	<div class="flex items-center gap-1">
		<Pencil />
		<i>Edit</i>
	</div>
{/snippet}

<!--
<PutFlash />
<Flash /> -->
<LoadingBar />
<NotificationPanel />
<Toaster position="top-right" />
<ModeWatcher />

{#if typeof window !== 'undefined'}
	{#await notificationService.initialize() then}
		<!-- Notification service initialized -->
	{/await}
{/if}

{@render children()}
<div style="display:none">
	{#each locales as locale}
		<a href={localizeHref(page.url.pathname, { locale })}>{locale}</a>
	{/each}
</div>
