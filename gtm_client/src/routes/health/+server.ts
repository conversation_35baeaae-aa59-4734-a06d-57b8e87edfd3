import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async () => {
	try {
		// Basic health check - you can add more sophisticated checks here
		// like database connectivity, external service availability, etc.

		const healthStatus = {
			status: 'healthy',
			timestamp: new Date().toISOString(),
			uptime: process.uptime(),
			environment: process.env.NODE_ENV || 'development',
			version: process.env.npm_package_version || '1.0.0'
		};

		return json(healthStatus, { status: 200 });
	} catch (error) {
		const errorStatus = {
			status: 'unhealthy',
			timestamp: new Date().toISOString(),
			error: error instanceof Error ? error.message : 'Unknown error'
		};

		return json(errorStatus, { status: 503 });
	}
};
