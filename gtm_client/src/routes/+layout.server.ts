import type { LayoutServerLoad } from './$types';

export const load: LayoutServerLoad = ({ locals, depends }) => {
	depends('app:user');
	depends('app:currentOrganization');

	const message = locals?.message;
	const messageType = locals?.messageType;

	locals.message = null;
	locals.messageType = null;
	return {
		user: locals?.user ?? null,
		currentOrganizationID: locals?.currentOrganizationID ?? null,
		message,
		messageType
	};
};
