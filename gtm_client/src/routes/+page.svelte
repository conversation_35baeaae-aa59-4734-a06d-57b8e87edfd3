<script lang="ts">
	import { onMount } from 'svelte';
	import IconLogo from '$lib/icons/gettrackiton/IconLogo.svelte';
	import type { ResponseSchema } from '$lib/interfaces/req-resp';
	import { Skeleton } from '$lib/shad-components/ui/skeleton/index.js';
	import * as Alert from '$lib/shad-components/ui/alert/index.js';
	import CircleAlertIcon from '@lucide/svelte/icons/circle-alert';

	import { Button } from '$lib/shad-components/ui/button';

	let loading = $state(true);
	let authUrl = $state<string | null>(null);

	const fetchGoogleAuthUrl = async (): Promise<
		ResponseSchema<{
			authorization_url: string;
		}>
	> => {
		return fetch('/spark/auth/login/authorize-url/').then((res) => res.json());
	};

	onMount(() => {
		fetchGoogleAuthUrl()
			.then((res) => {
				if (res.status_code == '200' && res?.data) {
					authUrl = res.data.authorization_url;
				}
			})
			.finally(() => {
				loading = false;
			});
	});
</script>

{#snippet GoogleIcon()}
	<svg
		aria-label="Google logo"
		width="20"
		height="20"
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 512 512"
		><g
			><path d="m0 0H512V512H0" fill="#fff"></path><path
				fill="#34a853"
				d="M153 292c30 82 118 95 171 60h62v48A192 192 0 0190 341"
			></path><path fill="#4285f4" d="m386 400a140 175 0 0053-179H260v74h102q-7 37-38 57"
			></path><path fill="#fbbc02" d="m90 341a208 200 0 010-171l63 49q-12 37 0 73"></path><path
				fill="#ea4335"
				d="m153 219c22-69 116-109 179-50l55-54c-78-75-230-72-297 55"
			></path></g
		></svg
	>
{/snippet}

<div class="flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10">
	<div class="flex w-full max-w-sm flex-col gap-6">
		<a href="/" class="prose prose-lg flex items-center gap-2 self-center font-medium">
			<div class=" flex items-center justify-center rounded-md">
				<IconLogo />
			</div>
			<h1>GetTracktion</h1>
		</a>
		{#if loading}
			<Skeleton class="mx-auto mt-20 h-12 w-full max-w-[250px]" />
		{:else if authUrl}
			<Button href={authUrl} class="mx-auto w-full max-w-[250px]">
				{@render GoogleIcon()}
				Login with Google
			</Button>
		{:else if !loading}
			<Alert.Root variant="destructive">
				<CircleAlertIcon class="size-4" />
				<Alert.Title>Error</Alert.Title>
				<Alert.Description>Something went wrong. Are you offline?</Alert.Description>
			</Alert.Root>
		{/if}
	</div>
</div>
