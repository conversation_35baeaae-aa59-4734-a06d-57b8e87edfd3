import { json } from '@sveltejs/kit';
import { serverConfig } from '$lib/server/config';
import type { RequestHandler } from './$types';

/**
 * GET /api/config
 * Returns client configuration including service URLs
 */
export const GET: RequestHandler = async () => {
	return json({
		mcpServerUrl: serverConfig.getMcpServerUrl(),
		apiUrl: serverConfig.getApiUrl(),
		aiAgentUrl: serverConfig.getAiAgentUrl()
	});
};
