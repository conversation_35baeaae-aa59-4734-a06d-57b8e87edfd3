/**
 * Chat Agent API Endpoint
 *
 * Exposes the Mastra chat agent via SvelteKit server endpoint.
 * The chat agent has access to workspace tools and can handle conversational requests.
 * Supports both streaming and non-streaming responses.
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { mastra } from '$mastra';

export const POST: RequestHandler = async ({ request, locals }) => {
	try {
		const { message, stream = false, context: clientContext } = await request.json();

		// Get user context from locals and client context
		// Merge server-side auth with client-side context (organization, workspace, etc.)
		const userContext = {
			user_id: locals.user?.id,
			organization_id: clientContext?.organization_id,
			user_email: locals.user?.email,
			...clientContext // Include all client context (workspace, project, page, etc.)
		};

		// Get the chat agent
		const agent = mastra.getAgent('chatAgent');

		if (!agent) {
			return json(
				{
					success: false,
					error: 'Chat agent not found'
				},
				{ status: 500 }
			);
		}

		// Validate message
		if (!message) {
			return json(
				{
					success: false,
					error: 'Message is required'
				},
				{ status: 400 }
			);
		}

		// Add user context to the message for the agent
		const contextualMessage = `User context: ${JSON.stringify(userContext)}\n\nUser message: ${message}`;

		// Handle streaming response
		if (stream) {
			const encoder = new TextEncoder();
			const readable = new ReadableStream({
				async start(controller) {
					try {
						const result = await agent.generateVNext(contextualMessage, {
							onStepFinish: (step) => {
								console.log('Step finished:', step);
								// Send step updates to client
								const data = JSON.stringify({
									type: 'step',
									step: {
										text: step.text,
										toolCalls: step.toolCalls,
										finishReason: step.finishReason
									}
								});
								controller.enqueue(encoder.encode(`data: ${data}\n\n`));
							}
						});

						// Send final result
						const finalData = JSON.stringify({
							type: 'done',
							result: {
								text: result.text,
								toolCalls: result.toolCalls
							}
						});
						controller.enqueue(encoder.encode(`data: ${finalData}\n\n`));
						controller.close();
					} catch (error) {
						const errorData = JSON.stringify({
							type: 'error',
							error: error instanceof Error ? error.message : 'Unknown error'
						});
						controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
						controller.close();
					}
				}
			});

			return new Response(readable, {
				headers: {
					'Content-Type': 'text/event-stream',
					'Cache-Control': 'no-cache',
					Connection: 'keep-alive'
				}
			});
		}

		// Handle non-streaming response
		const result = await agent.generateVNext(contextualMessage, {
			onStepFinish: (step) => {
				console.log('Step finished:', step);
			}
		});

		return json({
			success: true,
			result: result.text,
			toolCalls: result.toolCalls
		});
	} catch (error) {
		console.error('Chat agent error:', error);
		return json(
			{
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
