/**
 * Mastra Chat Agent API Endpoint
 *
 * This is the main orchestrator endpoint for the <PERSON><PERSON> chat agent.
 * The chat agent has access to all tools (workspace, project, page, permission)
 * and can handle conversational requests across all domains.
 *
 * Supports both streaming and non-streaming responses.
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { mastra } from '$mastra';

/**
 * Extract UI actions from tool call results
 */
function extractUIActions(toolCalls: any[], toolResults: any[]): any[] {
	const uiActions: any[] = [];

	// First check tool results (this is where <PERSON><PERSON> puts the actual responses)
	if (toolResults) {
		for (const toolResult of toolResults) {
			// Check for UI actions in the tool result payload
			if (toolResult.payload?.result?.ui_actions) {
				uiActions.push(...toolResult.payload.result.ui_actions);
			}
		}
	}

	// Fallback: check tool calls (legacy support)
	if (toolCalls && uiActions.length === 0) {
		for (const toolCall of toolCalls) {
			// Check multiple possible locations for UI actions
			if (toolCall.output?.ui_actions) {
				uiActions.push(...toolCall.output.ui_actions);
			} else if (toolCall.result?.ui_actions) {
				uiActions.push(...toolCall.result.ui_actions);
			}
		}
	}

	return uiActions;
}

export const POST: RequestHandler = async ({ request, locals }) => {
	try {
		const { message, messages = [], stream = false, context: clientContext } = await request.json();

		// Get user context from locals and client context
		// Merge server-side auth with client-side context (organization, workspace, etc.)
		const userContext = {
			user_id: locals.user?.id,
			organization_id: locals.currentOrganizationID || clientContext?.organization_id,
			user_email: locals.user?.email,
			...clientContext // Include all client context (workspace, project, page, etc.)
		};

		// Get the chat agent (orchestrator)
		const agent = mastra.getAgent('chatAgent');

		if (!agent) {
			return json(
				{
					success: false,
					error: 'Chat agent not found'
				},
				{ status: 500 }
			);
		}

		// Validate message
		if (!message) {
			return json(
				{
					success: false,
					error: 'Message is required'
				},
				{ status: 400 }
			);
		}

		// Format conversation history and current message for the agent
		let contextualMessage = `User context: ${JSON.stringify(userContext)}\n\n`;

		// Add conversation history if available
		if (messages.length > 1) {
			contextualMessage += `Conversation history:\n`;
			// Include all messages except the last one (which is the current message)
			const historyMessages = messages.slice(0, -1);
			for (const msg of historyMessages) {
				const role = msg.role === 'user' ? 'User' : 'Assistant';
				contextualMessage += `${role}: ${msg.content}\n`;
			}
			contextualMessage += `\nCurrent user message: ${message}`;
		} else {
			contextualMessage += `User message: ${message}`;
		}

		// Handle streaming response
		if (stream) {
			const encoder = new TextEncoder();
			const readable = new ReadableStream({
				async start(controller) {
					try {
						const result = await agent.generateVNext(contextualMessage, {
							onStepFinish: (step) => {
								console.log('Step finished:', step);
								// Send step updates to client
								const data = JSON.stringify({
									type: 'step',
									step: {
										text: step.text,
										toolCalls: step.toolCalls,
										finishReason: step.finishReason
									}
								});
								controller.enqueue(encoder.encode(`data: ${data}\n\n`));
							}
						});

						// Send final result
						const uiActions = extractUIActions(result.toolCalls || [], result.toolResults || []);
						const finalData = JSON.stringify({
							type: 'done',
							result: {
								text: result.text,
								toolCalls: result.toolCalls,
								uiActions: uiActions
							}
						});
						controller.enqueue(encoder.encode(`data: ${finalData}\n\n`));
						controller.close();
					} catch (error) {
						console.error('Error in agent stream', { error, runId: (error as any).runId });
						const errorData = JSON.stringify({
							type: 'error',
							error: error instanceof Error ? error.message : 'Unknown error'
						});
						controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
						controller.close();
					}
				}
			});

			return new Response(readable, {
				headers: {
					'Content-Type': 'text/event-stream',
					'Cache-Control': 'no-cache',
					Connection: 'keep-alive'
				}
			});
		}

		// Handle non-streaming response
		const result = await agent.generateVNext(contextualMessage, {
			onStepFinish: (step) => {
				console.log('Step finished:', step);
			}
		});

		const uiActions = extractUIActions(result.toolCalls || [], result.toolResults || []);
		return json({
			success: true,
			result: result.text,
			toolCalls: result.toolCalls,
			uiActions: uiActions
		});
	} catch (error) {
		console.error('Chat agent error:', error);
		return json(
			{
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
