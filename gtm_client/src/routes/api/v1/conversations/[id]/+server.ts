import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { dev } from '$app/environment';

const DJANGO_BASE_URL = 'http://localhost:8000';

// Helper function to get user session info from cookies or locals
function getUserInfo(request: Request, locals: any) {
	// In development mode, use mock user from locals
	if (dev && locals.user) {
		return {
			userId: locals.user.id,
			cookieHeader: request.headers.get('cookie') || '',
			isDev: true
		};
	}

	// In production, extract session from cookies
	const cookieHeader = request.headers.get('cookie');
	if (!cookieHeader) {
		return null;
	}

	// Extract session ID from cookies (Django session)
	const sessionMatch = cookieHeader.match(/sessionid=([^;]+)/);
	if (!sessionMatch) {
		return null;
	}

	return {
		sessionId: sessionMatch[1],
		cookieHeader,
		isDev: false
	};
}

// GET /api/v1/conversations/[id]/ - Get conversation details
export const GET: RequestHandler = async ({ request, params, locals }) => {
	try {
		const userInfo = getUserInfo(request, locals);
		if (!userInfo) {
			throw error(401, 'Authentication required');
		}

		const { id } = params;
		if (!id) {
			throw error(400, 'Conversation ID required');
		}

		// In development mode, return mock conversation
		if (userInfo.isDev) {
			const mockConversation = {
				id: id,
				title: `Conversation ${id.slice(0, 8)}`,
				created_at: new Date().toISOString(),
				updated_at: new Date().toISOString(),
				user: userInfo.userId,
				message_count: 0
			};
			return json(mockConversation);
		}

		// Forward request to Django backend
		const response = await fetch(`${DJANGO_BASE_URL}/api/v1/conversations/${id}/`, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Cookie: userInfo.cookieHeader,
				'X-Requested-With': 'XMLHttpRequest'
			}
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Django API error:', response.status, errorText);
			throw error(response.status, `Backend error: ${response.statusText}`);
		}

		const data = await response.json();
		return json(data);
	} catch (err) {
		console.error('Conversation fetch error:', err);
		if (err instanceof Error && 'status' in err) {
			throw err;
		}
		throw error(500, 'Failed to fetch conversation');
	}
};

// DELETE /api/v1/conversations/[id]/ - Delete conversation
export const DELETE: RequestHandler = async ({ request, params, locals }) => {
	try {
		const userInfo = getUserInfo(request, locals);
		if (!userInfo) {
			throw error(401, 'Authentication required');
		}

		const { id } = params;
		if (!id) {
			throw error(400, 'Conversation ID required');
		}

		// In development mode, just return success
		if (userInfo.isDev) {
			return new Response(null, { status: 204 });
		}

		// Forward request to Django backend
		const response = await fetch(`${DJANGO_BASE_URL}/api/v1/conversations/${id}/`, {
			method: 'DELETE',
			headers: {
				'Content-Type': 'application/json',
				Cookie: userInfo.cookieHeader,
				'X-Requested-With': 'XMLHttpRequest'
			}
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Django API error:', response.status, errorText);
			throw error(response.status, `Backend error: ${response.statusText}`);
		}

		// Return empty response for successful deletion
		return new Response(null, { status: 204 });
	} catch (err) {
		console.error('Conversation deletion error:', err);
		if (err instanceof Error && 'status' in err) {
			throw err;
		}
		throw error(500, 'Failed to delete conversation');
	}
};
