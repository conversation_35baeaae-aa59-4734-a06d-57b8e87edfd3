import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { dev } from '$app/environment';

const DJANGO_BASE_URL = 'http://localhost:8000';

// Helper function to get user session info from cookies or locals
function getUserInfo(request: Request, locals: any) {
	// In development mode, use mock user from locals
	if (dev && locals.user) {
		return {
			userId: locals.user.id,
			cookieHeader: request.headers.get('cookie') || '',
			isDev: true
		};
	}

	// In production, extract session from cookies
	const cookieHeader = request.headers.get('cookie');
	if (!cookieHeader) {
		return null;
	}

	// Extract session ID from cookies (Django session)
	const sessionMatch = cookieHeader.match(/sessionid=([^;]+)/);
	if (!sessionMatch) {
		return null;
	}

	return {
		sessionId: sessionMatch[1],
		cookieHeader,
		isDev: false
	};
}

// GET /api/v1/conversations/[id]/messages/ - Get conversation messages
export const GET: RequestHandler = async ({ request, params, url, locals }) => {
	try {
		const userInfo = getUserInfo(request, locals);
		if (!userInfo) {
			throw error(401, 'Authentication required');
		}

		const { id } = params;
		if (!id) {
			throw error(400, 'Conversation ID required');
		}

		// In development mode, return empty messages
		if (userInfo.isDev) {
			return json({
				count: 0,
				next: null,
				previous: null,
				results: []
			});
		}

		// Forward request to Django backend
		const djangoUrl = new URL(`/api/v1/conversations/${id}/messages/`, DJANGO_BASE_URL);

		// Copy query parameters (for pagination, filtering, etc.)
		url.searchParams.forEach((value, key) => {
			djangoUrl.searchParams.set(key, value);
		});

		const response = await fetch(djangoUrl.toString(), {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Cookie: userInfo.cookieHeader,
				'X-Requested-With': 'XMLHttpRequest'
			}
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Django API error:', response.status, errorText);
			throw error(response.status, `Backend error: ${response.statusText}`);
		}

		const data = await response.json();
		return json(data);
	} catch (err) {
		console.error('Conversation messages fetch error:', err);
		if (err instanceof Error && 'status' in err) {
			throw err;
		}
		throw error(500, 'Failed to fetch conversation messages');
	}
};

// POST /api/v1/conversations/[id]/messages/ - Create message
export const POST: RequestHandler = async ({ request, params, locals }) => {
	try {
		const userInfo = getUserInfo(request, locals);
		if (!userInfo) {
			throw error(401, 'Authentication required');
		}

		const { id } = params;
		if (!id) {
			throw error(400, 'Conversation ID required');
		}

		const body = await request.json();

		// In development mode, return mock message
		if (userInfo.isDev) {
			const mockMessage = {
				id: `dev-msg-${Date.now()}`,
				conversation: id,
				role: body.role || 'user',
				content: body.content || '',
				timestamp: new Date().toISOString(),
				actions_performed: body.actions_performed || [],
				metadata: body.metadata || {}
			};
			return json(mockMessage);
		}

		// Forward request to Django backend
		const response = await fetch(`${DJANGO_BASE_URL}/api/v1/conversations/${id}/messages/`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Cookie: userInfo.cookieHeader,
				'X-Requested-With': 'XMLHttpRequest'
			},
			body: JSON.stringify(body)
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Django API error:', response.status, errorText);
			throw error(response.status, `Backend error: ${response.statusText}`);
		}

		const data = await response.json();
		return json(data);
	} catch (err) {
		console.error('Message creation error:', err);
		if (err instanceof Error && 'status' in err) {
			throw err;
		}
		throw error(500, 'Failed to create message');
	}
};
