import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { dev } from '$app/environment';

const DJANGO_BASE_URL = 'http://localhost:8000';

// Helper function to get user session info from cookies or locals
function getUserInfo(request: Request, locals: any) {
	// In development mode, use mock user from locals
	if (dev && locals.user) {
		return {
			userId: locals.user.id,
			cookieHeader: request.headers.get('cookie') || '',
			isDev: true
		};
	}

	// In production, extract session from cookies
	const cookieHeader = request.headers.get('cookie');
	if (!cookieHeader) {
		return null;
	}

	// Extract session ID from cookies (Django session)
	const sessionMatch = cookieHeader.match(/sessionid=([^;]+)/);
	if (!sessionMatch) {
		return null;
	}

	return {
		sessionId: sessionMatch[1],
		cookieHeader,
		isDev: false
	};
}

// GET /api/v1/conversations/ - List conversations
export const GET: RequestHandler = async ({ request, url, locals }) => {
	try {
		const userInfo = getUserInfo(request, locals);
		if (!userInfo) {
			throw error(401, 'Authentication required');
		}

		// In development mode, return empty conversations (managed client-side)
		if (userInfo.isDev) {
			return json({
				count: 0,
				next: null,
				previous: null,
				results: []
			});
		}

		// Forward request to Django backend
		const djangoUrl = new URL('/api/v1/conversations/', DJANGO_BASE_URL);

		// Copy query parameters
		url.searchParams.forEach((value, key) => {
			djangoUrl.searchParams.set(key, value);
		});

		const response = await fetch(djangoUrl.toString(), {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Cookie: userInfo.cookieHeader,
				'X-Requested-With': 'XMLHttpRequest'
			}
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Django API error:', response.status, errorText);
			throw error(response.status, `Backend error: ${response.statusText}`);
		}

		const data = await response.json();
		return json(data);
	} catch (err) {
		console.error('Conversation list error:', err);
		if (err instanceof Error && 'status' in err) {
			throw err;
		}
		throw error(500, 'Failed to fetch conversations');
	}
};

// POST /api/v1/conversations/ - Create conversation
export const POST: RequestHandler = async ({ request, locals }) => {
	try {
		const userInfo = getUserInfo(request, locals);
		if (!userInfo) {
			throw error(401, 'Authentication required');
		}

		const body = await request.json();

		// In development mode, return mock conversation (managed client-side)
		if (userInfo.isDev) {
			const mockConversation = {
				id: `dev-conv-${Date.now()}`,
				title: body.title || 'New Conversation',
				created_at: new Date().toISOString(),
				updated_at: new Date().toISOString(),
				user: userInfo.userId,
				message_count: 0
			};

			return json(mockConversation);
		}

		// Forward request to Django backend
		const response = await fetch(`${DJANGO_BASE_URL}/api/v1/conversations/`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Cookie: userInfo.cookieHeader,
				'X-Requested-With': 'XMLHttpRequest'
			},
			body: JSON.stringify(body)
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Django API error:', response.status, errorText);
			throw error(response.status, `Backend error: ${response.statusText}`);
		}

		const data = await response.json();
		return json(data);
	} catch (err) {
		console.error('Conversation creation error:', err);
		if (err instanceof Error && 'status' in err) {
			throw err;
		}
		throw error(500, 'Failed to create conversation');
	}
};
