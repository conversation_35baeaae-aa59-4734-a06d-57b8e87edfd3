import { redirect, type RequestHandler } from '@sveltejs/kit';

export const GET: RequestHandler = async ({ fetch, cookies, locals }) => {
	console.log('🔍 [LOGOUT ROUTE] Starting logout process');

	try {
		// Get CSRF token from cookies
		const csrfToken = cookies.get('csrftoken');
		const headers: Record<string, string> = {
			'Content-Type': 'application/json'
		};

		if (csrfToken) {
			headers['X-CSRFToken'] = csrfToken;
		}

		// Call the Django logout endpoint with correct URL (via /spark proxy)
		const response = await fetch('/spark/auth/logout/', {
			method: 'POST',
			headers
		});

		console.log('🔍 [LOGOUT ROUTE] Django logout response:', response.status);

		if (response.ok) {
			const result = await response.json();
			console.log('🔍 [LOGOUT ROUTE] Logout successful:', result);
		} else {
			console.log('🔍 [LOGOUT ROUTE] Logout failed:', await response.text());
		}
	} catch (error) {
		console.error('🔍 [LOGOUT ROUTE] Logout error:', error);
	}

	// Clear all authentication-related cookies regardless of Django response
	console.log('🔍 [LOGOUT ROUTE] Clearing cookies');

	// Clear session cookies with proper attributes
	cookies.delete('sessionid', {
		path: '/',
		httpOnly: true,
		secure: process.env.NODE_ENV === 'production',
		sameSite: 'lax'
	});

	cookies.delete('csrftoken', {
		path: '/',
		httpOnly: false, // CSRF tokens are typically not httpOnly
		secure: process.env.NODE_ENV === 'production',
		sameSite: 'lax'
	});

	// Clear user cookie if it exists
	cookies.delete('user', {
		path: '/',
		httpOnly: true,
		secure: process.env.NODE_ENV === 'production',
		sameSite: 'lax'
	});

	// Clear organization cookie
	cookies.delete('currentOrganizationID', {
		path: '/',
		httpOnly: true,
		secure: process.env.NODE_ENV === 'production',
		sameSite: 'lax'
	});

	// Clear locals user data
	locals.user = undefined;
	locals.currentOrganizationID = undefined;

	console.log('🔍 [LOGOUT ROUTE] Logout complete, redirecting to home');

	// Redirect to home page
	redirect(302, '/');
};
