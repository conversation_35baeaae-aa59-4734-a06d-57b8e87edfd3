import type { PageServerLoad } from './$types';
import { redirect } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ url, fetch, cookies, locals }) => {
	if (!locals.user) {
		redirect(302, '/');
	}

	const logout = async () => {
		const relativePath = `/spark/auth/logout/`;

		// Get CSRF token from cookies
		const csrfToken = cookies.get('csrftoken');
		const headers: Record<string, string> = {
			'Content-Type': 'application/json'
		};

		if (csrfToken) {
			headers['X-CSRFToken'] = csrfToken;
		}

		return fetch(relativePath, {
			method: 'POST',
			headers
		}).then(async (res) => {
			if (!res.ok) {
				cookies.set('message', 'Logout failed', { path: '/' });
				cookies.set('messageType', 'error', { path: '/' });
			} else {
				// Clear all authentication-related cookies
				cookies.delete('sessionid', {
					path: '/',
					httpOnly: true,
					secure: process.env.NODE_ENV === 'production',
					sameSite: 'lax'
				});

				cookies.delete('csrftoken', {
					path: '/',
					httpOnly: false,
					secure: process.env.NODE_ENV === 'production',
					sameSite: 'lax'
				});

				cookies.delete('user', {
					path: '/',
					httpOnly: true,
					secure: process.env.NODE_ENV === 'production',
					sameSite: 'lax'
				});

				cookies.delete('currentOrganizationID', {
					path: '/',
					httpOnly: true,
					secure: process.env.NODE_ENV === 'production',
					sameSite: 'lax'
				});

				cookies.set('message', 'Logged out successfully', { path: '/' });
				cookies.set('messageType', 'success', { path: '/' });
				locals.user = undefined;
				locals.currentOrganizationID = undefined;
			}
		});
	};

	await logout();
	redirect(302, '/');
};
