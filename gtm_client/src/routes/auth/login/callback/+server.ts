import { redirect } from '@sveltejs/kit';
import { parse } from 'set-cookie-parser';

export const GET = async (event) => {
	const query = event.url.searchParams;
	const authUrl = '/spark/auth/login/callback/';
	const newUrlPlusQueryParams = `${authUrl}?${query.toString()}`;

	const response = await event.fetch(newUrlPlusQueryParams);
	if (response.ok) {
		// Get the raw 'set-cookie' headers from the API response
		const setCookieHeaders = response.headers.getSetCookie();

		// Parse the headers using the library
		const cookies = parse(setCookieHeaders);

		// Set each cookie on the browser response
		for (const cookie of cookies) {
			event.cookies.set(cookie.name, cookie.value, {
				path: cookie.path,
				expires: cookie.expires,
				maxAge: cookie.maxAge,
				httpOnly: cookie.httpOnly,
				sameSite: cookie.sameSite,
				secure: cookie.secure
			});
		}

		const publicUser = await response.json();
		event.locals.user = publicUser.data;

		event.cookies.set('user', publicUser.data.id, {
			path: '/',
			httpOnly: true,
			secure: process.env.NODE_ENV === 'production',
			sameSite: 'lax',
			maxAge: 60 * 60 * 24 * 6 // 6 days
		});
	} else {
		console.error('Failed to fetch user:', await response.text());
	}

	throw redirect(302, '/');
};
