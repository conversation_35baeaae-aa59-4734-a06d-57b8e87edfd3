import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, fetch, depends }) => {
	depends('app:user');
	depends('app:currentOrganization');

	// Get current organization data if user is logged in and has an organization
	let currentOrganization = null;
	if (locals.user && locals.currentOrganizationID) {
		try {
			const response = await fetch(`/spark/api/v1/organizations/${locals.currentOrganizationID}/`);
			if (response.ok) {
				const data = await response.json();
				currentOrganization = data.data;
			}
		} catch (error) {
			console.error('Failed to fetch current organization:', error);
		}
	}

	return {
		user: locals.user,
		currentOrganization,
		currentOrganizationID: locals.currentOrganizationID
	};
};
