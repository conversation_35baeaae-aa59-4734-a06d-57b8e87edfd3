<script lang="ts">
	import AIAgentSidebar from '$lib/components/AIAgentSidebar.svelte';
	import { aiAgentService } from '$lib/services/aiAgentService';
	import { onMount } from 'svelte';

	let isOpen = $state(false);
	let healthStatus = $state<any>(null);
	let healthError = $state<string | null>(null);

	onMount(async () => {
		try {
			healthStatus = await aiAgentService.checkHealth();
		} catch (error) {
			healthError = error instanceof Error ? error.message : 'Unknown error';
		}
	});
</script>

<div class="min-h-screen bg-gray-50 p-8">
	<div class="mx-auto max-w-4xl">
		<h1 class="mb-4 text-3xl font-bold">AI Agent Integration Test</h1>
		<p class="mb-8 text-gray-600">
			Test the AI Agent integration and verify all functionality works correctly. Press <kbd
				class="rounded bg-gray-200 px-2 py-1 text-sm">Ctrl+K</kbd
			> to open the AI Agent sidebar.
		</p>

		<!-- Health Status -->
		<div class="mb-8 rounded-lg bg-white p-6 shadow">
			<h2 class="mb-4 text-xl font-semibold">AI Agent Health Status</h2>

			{#if healthStatus}
				<div class="text-green-600">
					<p>✅ AI Agent is healthy</p>
					<pre class="mt-2 rounded bg-gray-100 p-2 text-sm">{JSON.stringify(
							healthStatus,
							null,
							2
						)}</pre>
				</div>
			{:else if healthError}
				<div class="text-red-600">
					<p>❌ AI Agent is not available</p>
					<p class="mt-1 text-sm">{healthError}</p>
				</div>
			{:else}
				<div class="text-gray-600">
					<p>🔄 Checking AI Agent status...</p>
				</div>
			{/if}
		</div>

		<!-- Test Controls -->
		<div class="mb-8 rounded-lg bg-white p-6 shadow">
			<h2 class="mb-4 text-xl font-semibold">Test Controls</h2>

			<div class="space-y-4">
				<button
					onclick={() => (isOpen = true)}
					class="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
				>
					Open AI Agent Sidebar
				</button>

				<button
					onclick={() => (isOpen = false)}
					class="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
				>
					Close AI Agent Sidebar
				</button>
			</div>
		</div>

		<!-- Instructions -->
		<div class="rounded-lg bg-white p-6 shadow">
			<h2 class="mb-4 text-xl font-semibold">Test Instructions</h2>

			<div class="space-y-2 text-gray-700">
				<p>1. Check that the AI Agent health status shows as healthy</p>
				<p>2. Click "Open AI Agent Sidebar" to open the chat interface</p>
				<p>3. Try typing a message like "help" or "create workspace test"</p>
				<p>4. Use keyboard shortcut Ctrl+K (or Cmd+K) to toggle the sidebar</p>
				<p>5. Test the floating action button (bottom-right corner)</p>
			</div>
		</div>
	</div>
</div>

<!-- AI Agent Sidebar -->
<AIAgentSidebar bind:isOpen />

<style>
	:global(body) {
		margin: 0;
		padding: 0;
	}
</style>
