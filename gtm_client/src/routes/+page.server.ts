import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async (event) => {
	event.depends('app:user');
	const urlHasNextRedirect = event.url.searchParams.get('next');
	const localsUser = event.locals?.user;
	const nextRedirect = urlHasNextRedirect ? urlHasNextRedirect : '/dashboard';
	if (!localsUser) {
		return;
	}
	redirect(302, nextRedirect);
};
