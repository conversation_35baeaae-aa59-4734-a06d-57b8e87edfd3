<script lang="ts">
	import { But<PERSON> } from '$lib/shad-components/ui/button';
	import * as Card from '$lib/shad-components/ui/card';
	import { Badge } from '$lib/shad-components/ui/badge';
	import { notificationService } from '$lib/services/notificationService';

	// Test different notification types
	function testInvitationNotification() {
		const mockInvitation = {
			id: 'test-invitation-123',
			organization: {
				id: 'org-123',
				name: 'Acme Corporation',
				description: 'A test organization',
				owner: {
					id: 1,
					username: 'owner',
					email: '<EMAIL>',
					first_name: '<PERSON>',
					last_name: 'Owner'
				},
				created_at: new Date().toISOString(),
				updated_at: new Date().toISOString(),
				is_personal: false
			},
			email: '<EMAIL>',
			role: 'admin' as const,
			can_view: true,
			can_create: true,
			can_edit: true,
			can_delete: false,
			status: 'pending' as const,
			invited_by: {
				id: 2,
				email: '<EMAIL>',
				first_name: '<PERSON>',
				last_name: 'Inviter',
				avatar: undefined
			},
			expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
			created_at: new Date().toISOString(),
			updated_at: new Date().toISOString(),
			token: 'test-token-123'
		};

		notificationService.addInvitationNotification(mockInvitation);
	}

	function testBillingNotification() {
		notificationService.addBillingNotification({
			amount: 99.99,
			currency: 'USD',
			dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
			status: 'due_soon',
			invoiceId: 'inv-123'
		});
	}

	function testUrgentNotification() {
		notificationService.addUrgentNotification({
			title: 'Security Alert',
			message: 'Unusual login activity detected from a new device',
			actionRequired: true,
			deadline: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
			severity: 'high',
			actions: [
				{
					label: 'Review Activity',
					variant: 'primary',
					handler: () => {
						alert('Would navigate to security page');
					}
				},
				{
					label: 'Secure Account',
					variant: 'destructive',
					handler: () => {
						alert('Would trigger account security flow');
					}
				}
			]
		});
	}

	function testInfoNotification() {
		notificationService.addInfoNotification({
			title: 'Feature Update',
			message: 'New dashboard features are now available. Check them out!',
			persistent: false,
			autoHideMs: 8000,
			actions: [
				{
					label: 'Learn More',
					variant: 'primary',
					handler: () => {
						alert('Would navigate to feature tour');
					}
				}
			]
		});
	}

	function testWarningNotification() {
		notificationService.addWarningNotification(
			'Storage Limit',
			'You are approaching your storage limit. Consider upgrading your plan.',
			true
		);
	}

	function testSuccessNotification() {
		notificationService.addSuccessNotification(
			'Backup Complete',
			'Your data has been successfully backed up to the cloud.'
		);
	}
</script>

<svelte:head>
	<title>Test Notifications</title>
</svelte:head>

<div class="container mx-auto px-4 py-8">
	<div class="mb-8">
		<h1 class="text-3xl font-bold tracking-tight">Test Notification System</h1>
		<p class="text-muted-foreground mt-2">
			Test the new multi-purpose notification system with different types
		</p>
	</div>

	<div class="grid max-w-2xl gap-6">
		<Card.Root>
			<Card.Header>
				<Card.Title>Multi-Purpose Notification System</Card.Title>
				<Card.Description>
					A flexible notification system that handles invitations, billing, urgent alerts, and more
					with a clean floating panel UI
				</Card.Description>
			</Card.Header>
			<Card.Content class="space-y-4">
				<div class="space-y-2">
					<h3 class="font-semibold">Features:</h3>
					<ul class="text-muted-foreground ml-4 space-y-1 text-sm">
						<li>• Floating button in bottom-right corner</li>
						<li>• Red count badge showing number of pending invitations</li>
						<li>• Drawer slides up from bottom when clicked</li>
						<li>• Shows all invitation details in organized cards</li>
						<li>• Accept/decline actions for each invitation</li>
						<li>• Mobile-friendly design</li>
					</ul>
				</div>

				<div class="grid grid-cols-2 gap-2">
					<Button onclick={testInvitationNotification}>Test Invitation</Button>
					<Button variant="outline" onclick={testBillingNotification}>Test Billing</Button>
					<Button variant="destructive" onclick={testUrgentNotification}>Test Urgent</Button>
					<Button variant="secondary" onclick={testInfoNotification}>Test Info</Button>
					<Button variant="outline" onclick={testWarningNotification}>Test Warning</Button>
					<Button variant="ghost" onclick={testSuccessNotification}>Test Success</Button>
				</div>

				<div class="mt-4 rounded-lg border border-amber-200 bg-amber-50 p-4">
					<h4 class="mb-2 font-medium text-amber-900">✨ Improved: Top Banner Invitations</h4>
					<p class="mb-3 text-sm text-amber-700">
						The invitation banner now features a clean, professional design with:
					</p>
					<ul class="mb-3 ml-4 space-y-1 text-sm text-amber-700">
						<li>• Clean white background with subtle amber accents</li>
						<li>• Quick accept button for single invitations</li>
						<li>• Color-coded permission badges</li>
						<li>• Better responsive layout for mobile devices</li>
						<li>• Improved visual hierarchy and readability</li>
					</ul>
					<p class="text-xs text-amber-600">
						Note: The banner will automatically appear when you have real pending invitations from
						organizations.
					</p>
				</div>
			</Card.Content>
		</Card.Root>

		<Card.Root>
			<Card.Header>
				<Card.Title>How It Works</Card.Title>
			</Card.Header>
			<Card.Content class="space-y-4">
				<div class="space-y-3">
					<div class="flex items-start gap-3">
						<Badge variant="outline" class="mt-1">1</Badge>
						<div>
							<h4 class="font-medium">Persistent Floating Button</h4>
							<p class="text-muted-foreground text-sm">
								When you have pending invitations, a blue floating button appears in the
								bottom-right corner
							</p>
						</div>
					</div>

					<div class="flex items-start gap-3">
						<Badge variant="outline" class="mt-1">2</Badge>
						<div>
							<h4 class="font-medium">Count Badge</h4>
							<p class="text-muted-foreground text-sm">
								A red badge shows the number of pending invitations (up to 9+)
							</p>
						</div>
					</div>

					<div class="flex items-start gap-3">
						<Badge variant="outline" class="mt-1">3</Badge>
						<div>
							<h4 class="font-medium">Drawer Interface</h4>
							<p class="text-muted-foreground text-sm">
								Click the button to open a drawer from the bottom showing all invitation details
							</p>
						</div>
					</div>

					<div class="flex items-start gap-3">
						<Badge variant="outline" class="mt-1">4</Badge>
						<div>
							<h4 class="font-medium">Quick Actions</h4>
							<p class="text-muted-foreground text-sm">
								Accept or decline invitations directly from the drawer with loading states
							</p>
						</div>
					</div>
				</div>
			</Card.Content>
		</Card.Root>

		<Card.Root>
			<Card.Header>
				<Card.Title>Benefits Over Previous Design</Card.Title>
			</Card.Header>
			<Card.Content>
				<div class="grid gap-3">
					<div class="flex items-center gap-2">
						<div class="h-2 w-2 rounded-full bg-green-500"></div>
						<span class="text-sm">No longer blocks the top of the page</span>
					</div>
					<div class="flex items-center gap-2">
						<div class="h-2 w-2 rounded-full bg-green-500"></div>
						<span class="text-sm">Handles multiple invitations elegantly</span>
					</div>
					<div class="flex items-center gap-2">
						<div class="h-2 w-2 rounded-full bg-green-500"></div>
						<span class="text-sm">Better mobile experience</span>
					</div>
					<div class="flex items-center gap-2">
						<div class="h-2 w-2 rounded-full bg-green-500"></div>
						<span class="text-sm">Clear visual count indicator</span>
					</div>
					<div class="flex items-center gap-2">
						<div class="h-2 w-2 rounded-full bg-green-500"></div>
						<span class="text-sm">Persistent but non-intrusive</span>
					</div>
				</div>
			</Card.Content>
		</Card.Root>
	</div>
</div>
