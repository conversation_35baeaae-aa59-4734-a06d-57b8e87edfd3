<script lang="ts">
	import { onMount } from 'svelte';

	import { zodClient } from 'sveltekit-superforms/adapters';
	import { superForm } from 'sveltekit-superforms';

	import { globalBreadcrumbs, globalNavLinks } from '$lib/stores/side-nav.svelte';

	import { createProjectSchema } from '../../create/formSchemas';

	import type { PageData } from './$types';

	import { Button } from '$lib/shad-components/ui/button';
	import * as Tabs from '$lib/shad-components/ui/tabs/index.js';
	import * as Alert from '$lib/shad-components/ui/alert/index.js';
	import * as AlertDialog from '$lib/shad-components/ui/alert-dialog/index.js';

	import EditProjectView from './EditProjectView.svelte';
	import AdNetworkForm from './AdNetworkForm.svelte';

	import { goto, invalidate } from '$app/navigation';
	import { page } from '$app/state';
	import PagesMain from './DraggablePages/PagesMain.svelte';
	import EventsMain from './DraggablePages/EventsMain.svelte';
	import Next from '@lucide/svelte/icons/chevron-right';
	import Back from '@lucide/svelte/icons/chevron-left';

	import HardDriveUpload from '@lucide/svelte/icons/hard-drive-upload';

	import House from '@lucide/svelte/icons/house';
	import Network from '@lucide/svelte/icons/network';
	import Pencil from '@lucide/svelte/icons/pencil';

	import FolderOpenDot from '@lucide/svelte/icons/folder-open-dot';

	import { toast } from 'svelte-sonner';
	import {
		EditProjectIcon,
		HouseIcon,
		NetworkIcon,
		ProjectIcon
	} from '$lib/shared-ui/layoutExports';

	let {
		data
	}: {
		data: PageData;
	} = $props();

	// --- Interfaces for Shared State ---
	interface PageRule {
		id: number;
		condition: 'Contains' | 'Equals' | 'StartsWith' | 'EndsWith';
		value: string;
	}
	interface SelectedPage {
		instanceId: string;
		templateId: string;
		name: string;
		rules: PageRule[];
	}
	interface SelectedEvent {
		instanceId: string;
		templateId: string;
		name: string;
		pageInstanceId: string;
		triggerType: 'page-load' | 'click' | 'scroll';
		rules: PageRule[];
	}

	let selectedPages = $state<SelectedPage[]>([]);
	let selectedEvents = $state<SelectedEvent[]>([]);

	let hasUnsavedChanges = $state(false);
	let initialPages = $state('');
	let initialEvents = $state('');
	let formHasUnsavedChanges = $state(false);
	let showUnsavedChangesDialog = $state(false);
	let nextView = $state<string | null>(null);
	let navigateAfterSave = $state(false);

	onMount(() => {
		globalBreadcrumbs.crumbs = [
			{
				title: HouseIcon,
				href: '/dashboard'
			},
			{
				title: NetworkIcon,
				href: `/workspaces/${data?.workspace?.id}`
			},
			{
				title: ProjectIcon,
				href: `/projects/${data?.project?.id}`
			},
			{
				title: EditProjectIcon,
				href: `/projects/${data?.project?.id}/edit`
			}
		];

		globalNavLinks.links = [
			{
				title: 'Dashboard',
				url: '/dashboard',

				icon: House
			},
			{
				title: 'Workspace',
				url: `/workspaces/${data?.workspace?.id}`,
				icon: Network
			},
			{
				title: 'Project',
				url: `/projects/${data?.project?.id}`,
				icon: FolderOpenDot
			},
			{
				title: 'Edit project',
				url: `/projects/${data?.project?.id}/edit`,
				icon: Pencil
			}
		];
		if (data.project?.raw_data) {
			selectedPages = data.project.raw_data.pages || [];
			selectedEvents = data.project.raw_data.events || [];
		}
		initialPages = JSON.stringify(selectedPages);
		initialEvents = JSON.stringify(selectedEvents);
	});

	$effect(() => {
		const pagesChanged = JSON.stringify(selectedPages) !== initialPages;
		const eventsChanged = JSON.stringify(selectedEvents) !== initialEvents;
		hasUnsavedChanges = pagesChanged || eventsChanged || formHasUnsavedChanges;
	});

	let raw_data_derived = $derived({
		pages: selectedPages,
		events: selectedEvents
	});

	async function updateProject({
		pages,
		events,
		ad_networks
	}: {
		pages: SelectedPage[];
		events: SelectedEvent[];
		ad_networks?: any[];
	}) {
		try {
			// First, fetch current project data to get existing raw_data
			const currentResponse = await fetch(`/spark/api/v1/projects/${data.project.id}/`);
			if (!currentResponse.ok) {
				throw new Error(`Failed to fetch current project data: ${currentResponse.statusText}`);
			}

			const currentProject = await currentResponse.json();
			const existingRawData = currentProject.raw_data || {};
			console.log('existingRawData', existingRawData);

			// Merge pages and events with existing raw_data
			const updatedRawData = {
				...existingRawData,
				pages,
				events
			};

			return fetch(`/spark/api/v1/projects/${data.project.id}/`, {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					data: {
						raw_data: updatedRawData,
						ad_networks
					}
				})
			})
				.then((response) => {
					if (!response.ok) {
						throw new Error('Network response was not ok');
					}
					return response.json();
				})
				.catch((error) => {
					// throw new Error(error);
					toast.error('Error updating project');
				});
		} catch (error) {
			toast.error('Error updating project');
		}
	}
	async function handleSave() {
		if (formHasUnsavedChanges) {
			formHasUnsavedChanges = false;
			submit();
		} else {
			updateProject({
				pages: selectedPages,
				events: selectedEvents
			});
		}
		hasUnsavedChanges = false;
	}

	async function proceedWithNavigation() {
		hasUnsavedChanges = false;
		if (nextView) {
			window.location.href = `/projects/${data.project.id}/edit?view=${nextView}`;
		}
		showUnsavedChangesDialog = false;
	}

	function handleSaveAndContinue() {
		if (formHasUnsavedChanges) {
			navigateAfterSave = true;
			submit();
		} else {
			handleSave();
			proceedWithNavigation();
		}
	}

	let activeTab = $derived(page.url.searchParams.get('view') ?? 'metadata');

	const navigateToView = (view: string | null, previousView: string | null) => {
		// if (hasUnsavedChanges) {
		// 	console.error('Invalid Logic, impossible to navigate with unsaved changes');
		// 	return;
		// }
		if (!view) return;
		const url = new URL(page.url);
		url.searchParams.set('view', view);
		goto(url, { keepFocus: true, noScroll: true, replaceState: true });
	};

	let previousTab = $derived.by(() => {
		if (activeTab === 'pages') return 'metadata';
		if (activeTab === 'events') return 'pages';
		if (activeTab === 'ad-networks') return 'events';
		return activeTab;
	});

	let nextTab = $derived.by(() => {
		if (activeTab === 'metadata') return 'pages';
		if (activeTab === 'pages') return 'events';
		if (activeTab === 'events') return 'ad-networks';
		return activeTab;
	});

	const form = superForm(data.form, {
		dataType: 'json',
		validators: zodClient(createProjectSchema),
		invalidateAll: 'force',
		onUpdate() {
			invalidate(`project:${data.project.id}`);
			formHasUnsavedChanges = false;
			toast.success('Project updated successfully!');
			if (navigateAfterSave) {
				proceedWithNavigation();
				navigateAfterSave = false;
			}
		},

		onResult(event) {
			if (event.result.type !== 'success') {
				return;
			}
			const projectId = event?.result?.data?.data?.id;

			if (!projectId) {
				toast.error('Error', {
					description: 'Error occurred while editing the project.'
				});
			}
		},
		onError(event) {
			toast.error('Submission Error', {
				description: event.result.error.message || 'Please check the form for errors.'
			});
		},
		onChange() {
			formHasUnsavedChanges = true;
		}
	});

	const { form: formData, enhance, submitting, submit, errors } = form;

	function getTabValue() {
		return activeTab;
	}

	function setTabValue(newValue: string) {
		const previousTab = activeTab;
		const navigationBetweenPageAndEvent = previousTab === 'pages' && newValue === 'events';
		const navigationBetweenEventAndPage = previousTab === 'events' && newValue === 'pages';

		if (hasUnsavedChanges) {
			if (!(navigationBetweenPageAndEvent || navigationBetweenEventAndPage)) {
				showUnsavedChangesDialog = true;
				nextView = newValue;
				return;
			}
		}
		navigateToView(newValue, previousTab);
	}
</script>

{#snippet Footer()}
	<div class="my-8 flex w-full max-w-full gap-4 border-t pt-4 lg:flex-row">
		<div class="mx-auto flex">
			<Button class="capitalize" href="/projects/{page.data.project.id}/edit?view={previousTab}">
				<Back class="mr-2 h-4 w-4" />
				{previousTab}
			</Button>
			{#if activeTab !== 'ad-networks'}
				<Button class="ml-4 capitalize" href="/projects/{page.data.project.id}/edit?view={nextTab}">
					{nextTab}
					<Next class="ml-2 h-4 w-4" />
				</Button>
			{/if}
		</div>
	</div>
{/snippet}

<AlertDialog.Root bind:open={showUnsavedChangesDialog}>
	<AlertDialog.Content>
		<AlertDialog.Header>
			<AlertDialog.Title>You have unsaved changes</AlertDialog.Title>
			<AlertDialog.Description>
				Are you sure you want to leave? Your changes will be lost if you don't save them.
			</AlertDialog.Description>
		</AlertDialog.Header>
		<AlertDialog.Footer>
			<div class="flex w-full flex-col-reverse justify-between gap-4 md:flex-row">
				<AlertDialog.Cancel>Cancel</AlertDialog.Cancel>

				<div class="flex gap-2">
					<Button variant="destructive" onclick={proceedWithNavigation}
						>Continue without saving</Button
					>
					<AlertDialog.Action onclick={handleSaveAndContinue}>Save and Continue</AlertDialog.Action>
				</div>
			</div>
		</AlertDialog.Footer>
	</AlertDialog.Content>
</AlertDialog.Root>

<div class="flex h-full w-full flex-col">
	<Tabs.Root
		bind:value={getTabValue, setTabValue}
		orientation="horizontal"
		class="flex h-full w-full flex-col"
	>
		<div
			class="bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky top-14 z-10 border-b backdrop-blur"
		>
			<Tabs.List class="max-w-8xl mx-auto flex h-14 items-center justify-start gap-4 px-4">
				<Tabs.Trigger value="metadata">Meta Data</Tabs.Trigger>
				<Tabs.Trigger value="pages">Pages</Tabs.Trigger>
				<Tabs.Trigger value="events">Events</Tabs.Trigger>
				<Tabs.Trigger value="ad-networks">Ad-Networks</Tabs.Trigger>
			</Tabs.List>
		</div>

		<main class="flex-1 overflow-y-auto">
			<div class="max-w-8xl mx-auto p-4 lg:p-6">
				{#if hasUnsavedChanges}
					<Alert.Root class="animate-slide-in-left mb-4">
						<Alert.Title>Unsaved Changes</Alert.Title>

						<Alert.Description
							class="flex flex-col gap-4 md:flex-row md:items-center md:justify-between"
						>
							You have changes that have not been saved.
							<Button onclick={handleSave} size="sm">
								<HardDriveUpload class="mr-2 h-4 w-4" />
								Save Changes
							</Button>
						</Alert.Description>
					</Alert.Root>
				{/if}

				<Tabs.Content value="metadata" class="h-full">
					<EditProjectView
						bind:formHasUnsavedChanges
						{form}
						{formData}
						{enhance}
						{submitting}
						{errors}
					/>
				</Tabs.Content>

				<Tabs.Content value="pages" class="h-full">
					<PagesMain bind:selectedPages pageData={page} />
				</Tabs.Content>

				<Tabs.Content value="events" class="h-full">
					<EventsMain bind:selectedPages bind:selectedEvents {page} />
				</Tabs.Content>

				<Tabs.Content value="ad-networks" class="h-full">
					<AdNetworkForm project={data.project} />
				</Tabs.Content>
				{@render Footer()}
			</div>
		</main>
	</Tabs.Root>
</div>
