<script lang="ts">
	import * as Accordion from '$lib/shad-components/ui/accordion/index.js';
	import { Button } from '$lib/shad-components/ui/button/index.js';
	import { Input } from '$lib/shad-components/ui/input/index.js';
	import { Label } from '$lib/shad-components/ui/label/index.js';
	import * as Select from '$lib/shad-components/ui/select/index.js';
	import { Checkbox } from '$lib/shad-components/ui/checkbox/index.js';
	import * as Card from '$lib/shad-components/ui/card/index.js';
	import { onMount } from 'svelte';

	type AdNetworkFieldType =
		| 'text'
		| 'number'
		| 'url'
		| 'email'
		| 'boolean'
		| 'select'
		| 'multi_select';

	type AdNetworkField = {
		id?: number;
		name: string;
		description: string;
		field_type: AdNetworkFieldType;
		is_required: boolean;
		validation_regex?: string;
		options?: {
			choices?: string[];
		};
		server_only: boolean;
	};

	type AdNetwork = {
		id?: number;
		name: string;
		description: string;
		network_key: string;
		icon?: {
			url: string;
			full_url: string;
		};
		is_active: boolean;
		available_fields: AdNetworkField[];
	};

	type ProjectAdNetwork = {
		id?: number;
		ad_network: AdNetwork;
		field_values: Record<string, any>;
		is_active: boolean;
	};

	type Project = {
		id?: number;
		name: string;
		// workspace: Workspace;
		// project_type: ProjectType;
		// project_platform: ProjectPlatform;
		// project_purposes: ProjectPurpose[];
		ad_network: ProjectAdNetwork[];
		server_supported: boolean;
		deploy_data: Record<string, unknown>;

		subdomain?: {
			created_by: number;
			id: number;
			name: string;
			updated_at: string;
			domain: {
				id: number;
				name: string;
			};
		};
		description: string;
		created_at?: string;
		updated_at?: string;
		created_by?: number;
		status?: 'deployed' | 'draft' | 'archived';
		latest_versions?: [];
	};
	import { toast } from 'svelte-sonner';

	let { project }: { project: Project } = $props();

	let fieldValues = $state<Record<number, Record<string, any>>>({});
	let originalFieldValues = $state<Record<number, Record<string, any>>>({});
	let hasChanges = $state<Record<number, boolean>>({});

	onMount(() => {
		// Initialize fieldValues with existing values from project
		project.ad_network.forEach((network: ProjectAdNetwork) => {
			if (network.ad_network.id) {
				const values = network.field_values || {};
				fieldValues[network.ad_network.id] = { ...values };
				originalFieldValues[network.ad_network.id] = { ...values };
				hasChanges[network.ad_network.id] = false;
			}
		});
	});

	const getVisibleFields = (network: AdNetwork) => {
		return network.available_fields?.filter(
			(field) => !field.server_only || (field.server_only && project.server_supported)
		);
	};

	const handleFieldChange = (networkId: number, fieldName: string, value: any) => {
		if (!fieldValues[networkId]) {
			fieldValues[networkId] = {};
		}
		fieldValues[networkId][fieldName] = value;

		// Update hasChanges state
		hasChanges[networkId] = checkForChanges(networkId);
	};

	const checkForChanges = (networkId: number): boolean => {
		const network = project.ad_network.find((n) => n.ad_network.id === networkId);
		if (!network) return false;

		const visibleFields = getVisibleFields(network.ad_network);
		const currentValues = fieldValues[networkId] || {};
		const originalValues = originalFieldValues[networkId] || {};

		// If there's only one field and it's empty, show save button
		if (visibleFields.length === 1) {
			const field = visibleFields[0];
			const currentValue = currentValues[field.name];
			return currentValue !== undefined && currentValue !== '' && currentValue !== false;
		}

		// For multiple fields, check if any value has changed from original
		for (const field of visibleFields) {
			const currentValue = currentValues[field.name];
			const originalValue = originalValues[field.name];

			if (currentValue !== originalValue) {
				return true;
			}
		}

		return false;
	};

	const shouldShowSaveButton = (networkId: number): boolean => {
		return hasChanges[networkId] || false;
	};

	const handleSave = async (networkId: number) => {
		const values = fieldValues[networkId];
		if (!networkId || !values) return;

		try {
			const response = await fetch(
				`/spark/api/v1/projects/${project.id}/ad-networks/${networkId}/`,
				{
					method: 'PATCH',
					headers: {
						'Content-Type': 'application/json'
						// 'X-CSRFToken': csrfService().csrf
					},
					body: JSON.stringify({
						field_values: values
					})
				}
			);
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || 'Failed to update ad network fields');
			}

			// Update original values and reset change tracking after successful save
			originalFieldValues[networkId] = { ...fieldValues[networkId] };
			hasChanges[networkId] = false;

			toast.success(
				`Ad network settings for ${project.ad_network.find((n) => n.ad_network.id === networkId)?.ad_network.name} saved successfully!`
			);
		} catch (error) {
			console.error('Failed to update ad network fields:', error);
			toast.error('Failed to update ad network fields.');
		}
	};
</script>

<Card.Root>
	<Card.Header>
		<Card.Title>Ad-Networks Integration</Card.Title>
		<Card.Description>Manage integrations with various ad networks.</Card.Description>
	</Card.Header>
	<Card.Content class="grid gap-6">
		<Accordion.Root type="multiple" class="w-full">
			{#each project.ad_network as projectAdNetwork (projectAdNetwork.ad_network.id)}
				{@const network = projectAdNetwork.ad_network}
				<Accordion.Item value={`item-${network.id}`}>
					<Accordion.Trigger>
						<div class="flex items-center gap-4">
							{#if network.icon}
								<img src={network.icon.full_url} alt={network.name} class="h-6 w-6" />
							{/if}
							<span>{network.name}</span>
						</div>
					</Accordion.Trigger>
					<Accordion.Content class="flex flex-col gap-4 text-balance">
						<div class="field-container flex flex-col gap-4">
							{#each getVisibleFields(network) as field (field.name)}
								<div class="field-group grid gap-2">
									<Label for={`${network.id}-${field.name}`}>
										{field.name}
										{#if field.is_required}<span class="text-destructive">*</span>{/if}
									</Label>

									{#if field.field_type === 'select'}
										<Select.Root
											value={fieldValues[network.id]?.[field.name] || ''}
											onValueChange={(value) => {
												if (value) handleFieldChange(network.id, field.name, value);
											}}
										>
											<Select.Trigger>
												<Select.Item value={`Select a ${field.name}`} />
											</Select.Trigger>
											<Select.Content>
												{#each field.options?.choices || [] as choice (choice)}
													<Select.Item value={choice}>{choice}</Select.Item>
												{/each}
											</Select.Content>
										</Select.Root>
									{:else if field.field_type === 'boolean'}
										<div class="flex items-center gap-2">
											<Checkbox
												id={`${network.id}-${field.name}`}
												checked={fieldValues[network.id]?.[field.name] || false}
												onCheckedChange={(checked) => {
													handleFieldChange(network.id, field.name, checked);
												}}
											/>
											<Label for={`${network.id}-${field.name}`} class="text-sm font-medium">
												{field.description || 'Enable'}
											</Label>
										</div>
									{:else}
										<Input
											type={field.field_type === 'number' ? 'number' : 'text'}
											id={`${network.id}-${field.name}`}
											placeholder={`Enter ${field.name}`}
											required={field.is_required}
											value={fieldValues[network.id]?.[field.name] || ''}
											oninput={(e) =>
												handleFieldChange(network.id, field.name, e.currentTarget.value)}
										/>
									{/if}

									{#if field.description && field.field_type !== 'boolean'}
										<p class="text-muted-foreground text-sm">{field.description}</p>
									{/if}
								</div>
							{/each}
						</div>
						{#if shouldShowSaveButton(network.id)}
							<div class="flex justify-end">
								<Button onclick={() => handleSave(network.id)}>Save Settings</Button>
							</div>
						{/if}
					</Accordion.Content>
				</Accordion.Item>
			{/each}
		</Accordion.Root>
	</Card.Content>
</Card.Root>
