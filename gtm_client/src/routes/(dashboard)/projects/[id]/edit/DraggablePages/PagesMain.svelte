<script lang="ts">
	import { onMount } from 'svelte';
	import { flip } from 'svelte/animate';
	import { fade, fly } from 'svelte/transition';
	import { toast } from 'svelte-sonner';
	import { cn } from '$lib/shad-components/utils.js';
	import { createId as cuid } from '@paralleldrive/cuid2';

	// Drag and Drop
	import {
		draggable,
		dropTargetForElements,
		monitorForElements
	} from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
	import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine';

	// ShadCN/Lucide Components
	import * as Card from '$lib/shad-components/ui/card/index.js';
	import { Button } from '$lib/shad-components/ui/button';
	import { Input } from '$lib/shad-components/ui/input';
	import * as Select from '$lib/shad-components/ui/select';
	import { Label } from '$lib/shad-components/ui/label';
	import { Separator } from '$lib/shad-components/ui/separator';
	import * as Dialog from '$lib/shad-components/ui/dialog';
	import * as Popover from '$lib/shad-components/ui/popover';

	import { Badge } from '$lib/shad-components/ui/badge';
	import * as DropdownMenu from '$lib/shad-components/ui/dropdown-menu';
	// import Search from '@lucide/svelte/icons/search';
	import Plus from '@lucide/svelte/icons/plus';
	import Trash2 from '@lucide/svelte/icons/trash-2';
	import X from '@lucide/svelte/icons/x';
	import Info from '@lucide/svelte/icons/info';
	import FileCode2 from '@lucide/svelte/icons/file-code-2';
	import CodeXml from '@lucide/svelte/icons/code-xml';
	import HelpCircle from '@lucide/svelte/icons/help-circle';
	import GripVertical from '@lucide/svelte/icons/grip-vertical';

	// Filter Engine Integration
	import { useFilterEngineService } from '$lib/hooks/useFilterEngineService';

	// --- Component Props ---
	let { selectedPages = $bindable([]), pageData } = $props<{
		selectedPages: SelectedPage[];
		readonly pageData: any;
	}>();

	// --- State Management ---
	interface PageRule {
		id: number;
		type: 'url' | 'datalayer'; // Add rule type
		condition: 'Contains' | 'Equals' | 'StartsWith' | 'EndsWith';
		value: string;
	}

	// Validation state
	let urlValidationErrors = $state<Record<number, string>>({});
	let showEqualsWarning = $state(false);
	interface PageTemplate {
		id: string;
		name: string;
		isCustom?: boolean;
		baseType?: string; // For custom pages, stores the base page type they're derived from
	}
	interface SelectedPage {
		instanceId: string;
		templateId: string;
		name: string;
		customName?: string; // Optional custom name for pages
		rules: PageRule[];
		isDefault?: boolean; // Mark the default "all-pages" page
		baseType?: string; // For custom pages, stores the base page type they're derived from
	}

	let availablePages = $state<PageTemplate[]>([
		{ id: 'pdp', name: 'Product Detail Page' },
		{ id: 'cat', name: 'Category Page' },
		{ id: 'cart', name: 'Cart Page' },
		{ id: 'checkout', name: 'Checkout Page' },
		{ id: 'conf', name: 'Confirmation Page' }
	]);
	let activePageInstanceId = $state<string | null>(null);
	let isMobile = $state(false);
	let isDragging = $state(false);
	let isDropTarget = $state(false);
	let isCreateModalOpen = $state(false);
	let newPageName = $state('');
	let newPageType = $state<string | undefined>(undefined);

	// Filter Engine Integration
	const filterEngineService = useFilterEngineService();
	const { hasIssues, errors, warnings, firingLogic } = filterEngineService;

	// Convert selectedPages to GTM format for validation
	const gtmPages = $derived.by(() => {
		return selectedPages.map((page) => ({
			id: page.instanceId,
			name: page.name,
			rules: page.rules.map((rule) => ({
				id: rule.id.toString(),
				type: rule.type as 'url' | 'datalayer',
				condition: rule.condition as 'Contains' | 'Equals' | 'StartsWith' | 'EndsWith',
				value: rule.value,
				isActive: true
			})),
			isActive: true
		}));
	});

	// Reactive validation
	$effect(() => {
		if (gtmPages.length > 0) {
			filterEngineService.validatePages(gtmPages);
		} else {
			filterEngineService.clearValidation();
		}
	});

	let availableItemsMap = new Map<string, HTMLElement>();
	let dropTargetEl: HTMLElement;

	const urlConditions = [
		{ value: 'Contains', label: 'Contains' },
		{ value: 'Equals', label: 'Equals' },
		{ value: 'StartsWith', label: 'Starts With' },
		{ value: 'EndsWith', label: 'Ends With' }
	];

	function mapElement(node: HTMLElement, id: string) {
		availableItemsMap.set(id, node);
		return {
			destroy() {
				availableItemsMap.delete(id);
			}
		};
	}

	const activePage = $derived.by(() => {
		if (!activePageInstanceId) return null;
		return selectedPages.find((p: SelectedPage) => p.instanceId === activePageInstanceId);
	});

	// --- Core Logic ---
	let allowDuplicates = $state(false);

	// Ensure default "all-pages" page exists
	function ensureDefaultPage() {
		const hasDefaultPage = selectedPages.some((p: SelectedPage) => p.templateId === 'all-pages');
		if (!hasDefaultPage) {
			const defaultPage: SelectedPage = {
				instanceId: 'all-pages-default',
				templateId: 'all-pages',
				name: 'All Pages',
				rules: [{ id: Date.now(), type: 'url', condition: 'Contains', value: '' }],
				isDefault: true
			};
			selectedPages = [defaultPage, ...selectedPages];
		}
	}

	// Call ensureDefaultPage when component mounts or selectedPages changes
	$effect(() => {
		ensureDefaultPage();
	});

	function addPage(pageTemplate: PageTemplate) {
		const isAlreadySelected = selectedPages.some(
			(p: SelectedPage) => p.templateId === pageTemplate.id
		);
		if (!isAlreadySelected || allowDuplicates) {
			const newPageInstance: SelectedPage = {
				instanceId: cuid(),
				templateId: pageTemplate.id,
				name: pageTemplate.name,
				rules: [{ id: Date.now(), type: 'url', condition: 'Contains', value: '' }],
				baseType: pageTemplate.baseType
			};
			selectedPages = [...selectedPages, newPageInstance];
			if (!activePageInstanceId) {
				activePageInstanceId = newPageInstance.instanceId;
			}
		} else {
			toast.info(`"${pageTemplate.name}" is already in the list.`);
		}
	}

	function deleteSelectedPage(e: MouseEvent, pageToDelete: SelectedPage) {
		e.stopPropagation();

		// Prevent deletion of default page
		if (pageToDelete.isDefault) {
			toast.error('The "All Pages" page cannot be deleted.');
			return;
		}

		if (activePageInstanceId === pageToDelete.instanceId) {
			activePageInstanceId = null;
		}
		selectedPages = selectedPages.filter(
			(p: SelectedPage) => p.instanceId !== pageToDelete.instanceId
		);
	}

	function handleCreateNewPage() {
		if (!newPageName.trim()) {
			toast.error('Page name cannot be empty.');
			return;
		}
		if (!newPageType) {
			toast.error('Please select a base page type.');
			return;
		}
		const newCustomPage: PageTemplate = {
			id: cuid(),
			name: newPageName,
			isCustom: true,
			baseType: newPageType
		};
		availablePages = [...availablePages, newCustomPage];
		isCreateModalOpen = false;
		newPageName = '';
		newPageType = undefined;
	}

	function addUrlRule() {
		if (!activePage) return;
		const pageToUpdate = selectedPages.find(
			(p: SelectedPage) => p.instanceId === activePage.instanceId
		);
		if (!pageToUpdate) return;

		// Check if there's already a URL rule with "Equals" condition
		const hasEqualsUrlRule = pageToUpdate.rules.some(
			(rule) => rule.type === 'url' && rule.condition === 'Equals'
		);
		if (hasEqualsUrlRule) {
			// Show warning instead of toast
			showEqualsWarning = true;
			return;
		}

		// Only allow one rule per page (either URL or DataLayer)
		if (pageToUpdate.rules.length > 0) {
			toast.info('Only one rule is allowed per page.');
			return;
		}

		pageToUpdate.rules = [
			...pageToUpdate.rules,
			{ id: Date.now(), type: 'url', condition: 'Contains', value: '' }
		];
		selectedPages = [...selectedPages];
	}

	function addDataLayerRule() {
		if (!activePage) return;
		const pageToUpdate = selectedPages.find(
			(p: SelectedPage) => p.instanceId === activePage.instanceId
		);
		if (!pageToUpdate) return;

		// Only allow one rule per page (either URL or DataLayer)
		if (pageToUpdate.rules.length > 0) {
			toast.info('Only one rule is allowed per page.');
			return;
		}

		pageToUpdate.rules = [
			...pageToUpdate.rules,
			{ id: Date.now(), type: 'datalayer', condition: 'Equals', value: '' }
		];
		selectedPages = [...selectedPages];
	}

	function deleteRule(page: SelectedPage, ruleId: number) {
		if (!page) return;
		page.rules = page.rules.filter((r) => r.id !== ruleId);
	}

	// Validation function for URL inputs
	function validateUrlInput(value: string): string {
		// Remove leading slash if present
		if (value.startsWith('/')) {
			return value.substring(1);
		}
		return value;
	}

	// --- Lifecycle & DnD Setup ---
	onMount(() => {
		const mediaQuery = window.matchMedia('(max-width: 1023px)');
		isMobile = mediaQuery.matches;
		const listener = (e: MediaQueryListEvent) => (isMobile = e.matches);
		mediaQuery.addEventListener('change', listener);
		const cleanup = combine(
			monitorForElements({
				onDragStart: () => (isDragging = true),
				onDrop: () => (isDragging = false)
			}),
			dropTargetForElements({
				element: dropTargetEl,
				onDragEnter: () => (isDropTarget = true),
				onDragLeave: () => (isDropTarget = false),
				onDrop: ({ source }) => {
					isDropTarget = false;
					const pageData = source.data.page as PageTemplate;
					if (pageData) {
						addPage(pageData);
					}
				}
			})
		);
		return () => {
			mediaQuery.removeEventListener('change', listener);
			cleanup();
		};
	});

	$effect(() => {
		if (isMobile) return;
		const unregisterDraggables = availablePages.map((page) => {
			const el = availableItemsMap.get(page.id);
			if (!el) return () => {};
			return draggable({ element: el, getInitialData: () => ({ page }) });
		});
		return () => {
			unregisterDraggables.forEach((unregister) => unregister());
		};
	});
</script>

{#snippet AddPage()}{/snippet}

<div class="w-full">
	<Card.Root>
		<Card.Header>
			<div class="flex items-center justify-between">
				<div>
					<Card.Title>Page Configuration</Card.Title>
					<Card.Description
						>Select pages from the list and configure their URL rules.</Card.Description
					>
				</div>
				<div class="flex items-center gap-2">
					<Button variant="ghost" class="hidden sm:inline-flex">
						<HelpCircle class="mr-2 h-4 w-4" /> Help
					</Button>
				</div>
			</div>
		</Card.Header>
		<Card.Content>
			<div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
				<div class="flex flex-col space-y-4 lg:col-span-1">
					<div class="flex items-center justify-between">
						<h3 class="text-lg font-semibold">Available Pages</h3>
						<Button variant="outline" size="sm" onclick={() => (isCreateModalOpen = true)}>
							<Plus class="mr-2 h-4 w-4" />
							Create Custom
						</Button>
					</div>
					{@render AddPage()}
					<div class={cn('space-y-1', isMobile && 'flex gap-2 overflow-x-auto py-2')}>
						{#each availablePages as page (page.id)}
							<div
								use:mapElement={page.id}
								class={cn(
									'my-2 flex items-center justify-between rounded-md border p-2.5 transition-colors',
									isMobile ? 'bg-secondary/50 w-48 flex-shrink-0' : 'hover:bg-accent',
									isDragging ? 'cursor-grabbing' : 'cursor-grab'
								)}
							>
								<div class="flex items-center gap-2 overflow-hidden">
									<GripVertical class="text-muted-foreground h-5 w-5 flex-shrink-0" />
									<span class="text-foreground truncate text-sm font-medium">{page.name}</span>
									{#if page.isCustom}
										<Badge variant="outline">Custom</Badge>
									{/if}
								</div>
								{#if isMobile}
									<Button
										size="icon"
										variant="ghost"
										class="h-7 w-7 flex-shrink-0"
										onclick={() => addPage(page)}
									>
										<Plus class="h-4 w-4" />
									</Button>
								{/if}
							</div>
						{/each}
					</div>
				</div>

				<div class="grid h-full grid-cols-1 gap-6 lg:col-span-2 xl:grid-cols-2">
					<div class="flex flex-col space-y-4">
						<h3 class="text-foreground text-lg font-semibold">Selected Pages</h3>
						<div
							bind:this={dropTargetEl}
							class={cn(
								'flex h-full min-h-48 flex-col rounded-lg border-2 border-dashed transition-colors',
								isDropTarget ? 'border-primary bg-accent' : 'border-transparent'
							)}
						>
							{#if selectedPages.length === 0}
								<div class="flex flex-grow items-center justify-center p-4">
									<p class="text-muted-foreground text-center font-medium">
										{#if isMobile}
											Add pages from the list above
										{:else}
											Drag and drop pages here
										{/if}
									</p>
								</div>
							{:else}
								<div class="space-y-2 p-2">
									{#each selectedPages as page (page.instanceId)}
										<div
											in:fly={{ y: -10, duration: 200 }}
											out:fly={{ duration: 200, y: -10 }}
											animate:flip={{ duration: 300 }}
											role="button"
											tabindex="0"
											onclick={() => (activePageInstanceId = page.instanceId)}
											onkeydown={(e) =>
												e.key === 'Enter' && (activePageInstanceId = page.instanceId)}
											class={cn(
												'bg-background cursor-pointer rounded-lg border p-3 transition-all',
												activePageInstanceId === page.instanceId
													? 'border-primary ring-primary/20 ring-2'
													: 'hover:bg-accent/50'
											)}
										>
											<div class="flex items-center justify-between">
												<div class="flex items-center gap-2">
													<span class="font-medium">{page.customName || page.name}</span>
													{#if page.isDefault}
														<Badge variant="secondary" class="text-xs">Default</Badge>
													{:else}
														{@const pageTemplate = availablePages.find(
															(p) => p.id === page.templateId
														)}
														{#if pageTemplate?.isCustom}
															{@const baseTemplate = availablePages.find(
																(p) => p.id === pageTemplate.baseType
															)}
															{#if baseTemplate}
																<Badge variant="default" class="text-xs">{baseTemplate.name}</Badge>
															{/if}
															<Badge variant="outline" class="text-xs">Custom</Badge>
														{:else if pageTemplate}
															<Badge variant="default" class="text-xs">{pageTemplate.name}</Badge>
														{/if}
													{/if}
												</div>
												{#if !page.isDefault}
													<Button
														variant="ghost"
														size="icon"
														class="text-muted-foreground hover:bg-destructive/10 hover:text-destructive h-7 w-7"
														onclick={(e) => deleteSelectedPage(e, page)}
													>
														<Trash2 class="h-4 w-4" />
													</Button>
												{/if}
											</div>

											<!-- Additional Page Information -->
											<div class="mt-2 space-y-1">
												<!-- Page Type Information -->
												<!-- {#if !page.isDefault}
													{@const pageTemplate = availablePages.find(
														(p) => p.id === page.templateId
													)}
													<div class="text-muted-foreground text-xs">
														<span class="font-medium">Type:</span>
														{#if pageTemplate?.isCustom && pageTemplate?.baseType}
															{@const baseTemplate = availablePages.find(
																(p) => p.id === pageTemplate.baseType
															)}
															Custom based on {baseTemplate?.name || 'Unknown'}
														{:else if pageTemplate}
															{pageTemplate.name}
														{:else}
															Unknown
														{/if}
													</div>
												{/if} -->

												<!-- Enhanced Trigger Summary -->
												{#if page.isDefault}
													<div class="text-muted-foreground text-xs">
														<span class="font-medium">Triggers on:</span> ALL pages (page category)
													</div>
												{:else if page.rules && page.rules.length > 0}
													<div class="text-muted-foreground text-xs">
														<span class="font-medium">Triggers on:</span> page category where
														{#each page.rules as rule, i (rule.id)}
															{#if i > 0}<span> AND </span>{/if}
															{#if rule.type === 'url'}
																{@const hasEmptyPath = !rule.value?.trim()}
																URL {rule.condition.toLowerCase()}
																{#if hasEmptyPath}
																	<span class="text-amber-600">(empty path)</span>
																{:else}
																	"{rule.value}"
																{/if}
															{:else if rule.type === 'datalayer'}
																{@const hasEmptyValue = !rule.value?.trim()}
																DataLayer Event
																{#if hasEmptyValue}
																	<span class="text-red-600">(empty)</span>
																{:else}
																	"{rule.value}"
																{/if}
															{/if}
														{/each}
													</div>
												{:else}
													<div class="text-muted-foreground text-xs">
														<span class="font-medium">Triggers on:</span> ALL pages (no rules defined)
													</div>
												{/if}

												<!-- Full URL Preview -->
												{#if page.rules && page.rules.length > 0}
													{@const urlRule = page.rules.find((r: PageRule) => r.type === 'url')}
													{#if urlRule && urlRule.value}
														<div class="text-muted-foreground text-xs">
															<span class="font-medium">URL:</span>
															<span class="font-mono"
																>{pageData?.data?.project?.subdomain?.full_uri ||
																	'https://example.com'}{urlRule.value}</span
															>
														</div>
													{/if}
												{:else if page.isDefault}
													<div class="text-muted-foreground text-xs">
														<span class="font-medium">Scope:</span> All pages on your website
													</div>
												{/if}
											</div>
										</div>
									{/each}
								</div>
							{/if}
						</div>
					</div>

					<div class="relative flex flex-col space-y-4">
						<h3 class="text-foreground text-lg font-semibold">Rules</h3>
						<div class="flex-grow">
							{#if activePage}
								<div class="space-y-4" transition:fly={{ x: 10, duration: 300 }}>
									<h4 class="font-semibold">{activePage.customName || activePage.name}</h4>

									<!-- Custom Name Input -->
									{#if !activePage.isDefault}
										<div class="space-y-2">
											<Label for="page-custom-name" class="text-xs font-semibold"
												>Custom Page Name (Optional)</Label
											>
											<Input
												id="page-custom-name"
												placeholder={`e.g., Custom ${activePage.name}`}
												bind:value={activePage.customName}
											/>
											<p class="text-muted-foreground text-xs">
												Give this page a custom name to distinguish it from other instances.
											</p>
										</div>
										<Separator />
									{/if}

									<!-- Page Type Display -->
									<div class="bg-muted/30 rounded-lg border p-3">
										<div class="mb-2 flex items-center justify-between">
											<div class="flex items-center gap-2">
												<Info class="text-muted-foreground h-4 w-4" />
												<span class="text-sm font-medium">Page Configuration</span>
											</div>
											<Popover.Root>
												<Popover.Trigger>
													<Info class="h-4 w-4 cursor-pointer text-blue-500 hover:text-blue-600" />
												</Popover.Trigger>
												<Popover.Content side="right" class="w-96">
													<div class="space-y-3">
														<h4 class="text-sm font-semibold">Page Details</h4>
														<div class="space-y-2 text-xs">
															<div class="rounded border-l-4 border-blue-400 bg-blue-50 p-3">
																<div class="mb-1 font-medium text-blue-800">Page Information</div>
																<div class="space-y-1 text-blue-700">
																	<div>
																		<span class="font-medium">Name:</span>
																		<span class="rounded bg-white px-1 font-mono"
																			>{activePage.customName || activePage.name}</span
																		>
																	</div>
																	<div>
																		<span class="font-medium">Type:</span>
																		{#if activePage.isDefault}
																			<span
																				class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800"
																				>Default (All Pages)</span
																			>
																		{:else}
																			{@const pageTemplate = availablePages.find(
																				(p) => p.id === activePage.templateId
																			)}
																			{#if pageTemplate?.isCustom && pageTemplate?.baseType}
																				{@const baseTemplate = availablePages.find(
																					(p) => p.id === pageTemplate.baseType
																				)}
																				<span
																					class="rounded bg-purple-100 px-2 py-1 text-xs text-purple-800"
																					>Custom</span
																				>
																				<span class="ml-1"
																					>based on {baseTemplate?.name || 'Unknown'}</span
																				>
																			{:else if pageTemplate}
																				<span
																					class="rounded bg-green-100 px-2 py-1 text-xs text-green-800"
																					>{pageTemplate.name}</span
																				>
																			{:else}
																				<span
																					class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-800"
																					>Unknown</span
																				>
																			{/if}
																		{/if}
																	</div>
																</div>
															</div>

															<div class="rounded border-l-4 border-green-400 bg-green-50 p-3">
																<div class="mb-1 font-medium text-green-800">Trigger Logic</div>
																<div class="text-green-700">
																	{#if activePage.isDefault}
																		<div>
																			Triggers on <strong>ALL</strong> page visits across your website
																		</div>
																	{:else if activePage.rules && activePage.rules.length > 0}
																		<div class="mb-2">
																			Triggers when <strong>ALL</strong> of these conditions are met:
																		</div>
																		{#each activePage.rules as rule}
																			<div class="mt-1 ml-2 flex items-center gap-2">
																				{#if rule.type === 'url'}
																					<span
																						class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800"
																						>URL</span
																					>
																					<span>{rule.condition}</span>
																					<span class="rounded bg-white px-1 font-mono"
																						>{rule.value || 'empty'}</span
																					>
																				{:else if rule.type === 'datalayer'}
																					<span
																						class="rounded bg-purple-100 px-2 py-1 text-xs text-purple-800"
																						>DataLayer</span
																					>
																					<span class="rounded bg-white px-1 font-mono"
																						>{rule.value || 'empty'}</span
																					>
																				{/if}
																			</div>
																		{/each}
																	{:else}
																		<div>
																			No rules defined - triggers on <strong>ALL</strong> page visits
																		</div>
																	{/if}
																</div>
															</div>

															<div class="rounded border-l-4 border-amber-400 bg-amber-50 p-3">
																<div class="mb-1 font-medium text-amber-800">Base URL</div>
																<div
																	class="rounded border bg-white p-2 font-mono text-xs text-amber-700"
																>
																	{pageData?.data?.project?.subdomain?.full_uri ||
																		'https://example.com'}
																	{#if activePage.rules && activePage.rules.length > 0}
																		{@const urlRule = activePage.rules.find(
																			(r) => r.type === 'url'
																		)}
																		{#if urlRule && urlRule.value}
																			{urlRule.value}
																		{/if}
																	{/if}
																</div>
															</div>
														</div>
													</div>
												</Popover.Content>
											</Popover.Root>
										</div>
										<div class="space-y-2">
											<div class="flex items-center gap-2 text-sm">
												<span class="text-muted-foreground">Page:</span>
												<Badge variant="outline" class="text-xs">
													{activePage.customName || activePage.name}
												</Badge>
												{#if activePage.isDefault}
													<Badge variant="secondary" class="text-xs">Default</Badge>
												{:else}
													{@const pageTemplate = availablePages.find(
														(p) => p.id === activePage.templateId
													)}
													{#if pageTemplate?.isCustom}
														<Badge variant="outline" class="text-xs">Custom</Badge>
													{/if}
												{/if}
											</div>

											<!-- Enhanced Page Rules Summary with Context -->
											{#if activePage.isDefault}
												<div class="rounded border-l-2 border-blue-400 bg-blue-50 p-2 text-xs">
													<span class="font-medium text-blue-800">
														This page configuration applies to ALL pages across your website
													</span>
												</div>
											{:else if activePage.rules && activePage.rules.length > 0}
												<div class="space-y-1">
													<div class="flex items-center gap-2 text-xs">
														<span class="text-muted-foreground">Page rules (ALL must match):</span>
													</div>
													{#each activePage.rules as rule, i}
														<div class="flex items-center gap-2 text-xs">
															{#if i > 0}<span class="text-muted-foreground ml-4">AND</span>{/if}
															{#if rule.type === 'url'}
																{@const hasEmptyPath = !rule.value?.trim()}
																<Badge
																	variant="outline"
																	class="text-xs {hasEmptyPath
																		? 'border-amber-400 bg-amber-50'
																		: ''}"
																>
																	URL {rule.condition}
																	{hasEmptyPath ? '(empty path)' : `${rule.value}`}
																</Badge>
																{#if hasEmptyPath}
																	<div class="rounded bg-amber-50 px-2 py-1 text-xs text-amber-700">
																		{#if rule.condition === 'Equals' || rule.condition === 'EndsWith'}
																			⚠️ Will likely only match homepage
																		{:else if rule.condition === 'Contains' || rule.condition === 'StartsWith'}
																			⚠️ Will match all pages - consider adding a path
																		{/if}
																	</div>
																{/if}
															{:else if rule.type === 'datalayer'}
																{@const hasEmptyValue = !rule.value?.trim()}
																<Badge
																	variant="outline"
																	class="text-xs {hasEmptyValue ? 'border-red-400 bg-red-50' : ''}"
																>
																	DataLayer: {hasEmptyValue ? '(empty event name)' : rule.value}
																</Badge>
																{#if hasEmptyValue}
																	<div class="rounded bg-red-50 px-2 py-1 text-xs text-red-700">
																		❌ DataLayer event name is required
																	</div>
																{/if}
															{/if}
														</div>
													{/each}
												</div>
											{:else}
												<div class="rounded border-l-2 border-amber-400 bg-amber-50 p-2 text-xs">
													<span class="font-medium text-amber-800">
														No page rules defined - will match ALL page visits
													</span>
													<div class="mt-1 text-amber-700">
														Consider adding URL or DataLayer rules to target specific pages
													</div>
												</div>
											{/if}
										</div>
									</div>

									<div class="bg-muted/30 rounded-lg border p-3 text-center">
										<p class="text-muted-foreground text-sm">
											Rules are relative to your project URL:
											<strong class="text-foreground"
												>{pageData?.data?.project?.subdomain?.full_uri ||
													'https://example.com'}/</strong
											>
										</p>
									</div>

									{#if activePage.isDefault}
										<!-- Default "All Pages" - Rules are locked -->
										<div class="space-y-3">
											<div class="bg-muted/30 rounded-lg border p-3">
												<div class="mb-2 flex items-center gap-2">
													<Info class="text-muted-foreground h-4 w-4" />
													<span class="text-sm font-medium">Default Page Configuration</span>
												</div>
												<p class="text-muted-foreground text-xs">
													The "All Pages" page applies to all pages on your website. URL rules
													cannot be customized.
												</p>
											</div>
											<div class="space-y-3">
												{#each activePage.rules as rule (rule.id)}
													<div class="space-y-2">
														<Label class="text-xs font-semibold">URL</Label>
														<div class="flex items-center gap-2">
															<div
																class="bg-muted w-[150px] flex-shrink-0 rounded-md border px-3 py-2 text-sm"
															>
																{rule.condition}
															</div>
															<Input placeholder="all pages" value="*" disabled class="bg-muted" />
															<!-- No delete button for default page rules -->
														</div>
													</div>
												{/each}
											</div>
										</div>
									{:else}
										<!-- Regular Pages - Full functionality -->
										<div class="space-y-3">
											{#if activePage.rules.length === 0}
												<p class="text-muted-foreground py-4 text-center text-sm">
													No rules defined. Click below to add one.
												</p>
											{/if}
											{#each activePage.rules as rule (rule.id)}
												<div
													in:fade={{ duration: 150 }}
													out:fade={{ duration: 150 }}
													animate:flip={{ duration: 300 }}
													class="space-y-2"
												>
													<!-- Rule Type Selector -->
													<div class="space-y-2">
														<Label class="text-xs font-semibold">Rule Type</Label>
														<Select.Root type="single" bind:value={rule.type}>
															<Select.Trigger class="w-full">
																{rule.type === 'url' ? 'URL Path' : 'DataLayer Event'}
															</Select.Trigger>
															<Select.Content>
																<Select.Group>
																	<Select.Item value="url" label="URL Path">URL Path</Select.Item>
																	<Select.Item value="datalayer" label="DataLayer Event"
																		>DataLayer Event</Select.Item
																	>
																</Select.Group>
															</Select.Content>
														</Select.Root>
													</div>

													{#if rule.type === 'url'}
														<Label for={`rule-value-${rule.id}`} class="text-xs font-semibold"
															>URL Path</Label
														>
														<div class="flex items-center gap-2">
															<Select.Root type="single" bind:value={rule.condition}>
																<Select.Trigger class="w-[150px] flex-shrink-0">
																	{rule.condition}
																</Select.Trigger>
																<Select.Content>
																	<Select.Group>
																		{#each urlConditions as condition (condition.value)}
																			<Select.Item value={condition.value} label={condition.label}
																				>{condition.label}</Select.Item
																			>
																		{/each}
																	</Select.Group>
																</Select.Content>
															</Select.Root>
															<div class="relative flex-grow">
																{#if rule.condition === 'Equals'}
																	<span
																		class="text-muted-foreground absolute top-1/2 left-3 -translate-y-1/2 text-sm"
																	>
																		/
																	</span>
																{/if}
																<Input
																	id={`rule-value-${rule.id}`}
																	placeholder={rule.condition === 'Contains' ||
																	rule.condition === 'EndsWith'
																		? 'search-term'
																		: 'example-path'}
																	bind:value={rule.value}
																	oninput={(e) => {
																		const target = e.target as HTMLInputElement;
																		if (target) {
																			rule.value = validateUrlInput(target.value);
																		}
																	}}
																	onblur={() => {
																		if (!rule.value.trim()) {
																			urlValidationErrors[rule.id] = 'URL path is required';
																		} else {
																			delete urlValidationErrors[rule.id];
																		}
																		urlValidationErrors = { ...urlValidationErrors };
																	}}
																	class={rule.condition === 'Equals' ? 'pl-8' : ''}
																/>
															</div>
															<Button
																variant="ghost"
																size="icon"
																class="text-muted-foreground hover:bg-destructive/10 hover:text-destructive h-8 w-8 flex-shrink-0"
																onclick={() => deleteRule(activePage, rule.id)}
															>
																<X class="h-4 w-4" />
															</Button>
														</div>
														{#if urlValidationErrors[rule.id]}
															<p class="text-destructive mt-1 text-xs">
																{urlValidationErrors[rule.id]}
															</p>
														{/if}
													{:else if rule.type === 'datalayer'}
														<Label class="text-xs font-semibold">DataLayer Event Name</Label>
														<div class="flex items-center gap-2">
															<Input
																placeholder="e.g., page.category, user.type"
																bind:value={rule.value}
																class="flex-grow"
															/>
															<Button
																variant="ghost"
																size="icon"
																class="text-muted-foreground hover:bg-destructive/10 hover:text-destructive h-8 w-8 flex-shrink-0"
																onclick={() => deleteRule(activePage, rule.id)}
															>
																<X class="h-4 w-4" />
															</Button>
														</div>
													{/if}
												</div>
											{/each}
										</div>
										<Separator class="my-4" />

										<!-- Warning for Equals URL rule -->
										{#if showEqualsWarning && activePage.rules.some((rule) => rule.type === 'url' && rule.condition === 'Equals')}
											<div
												class="rounded-lg border border-amber-200 bg-amber-50 p-3 text-amber-800"
											>
												<div class="flex items-center gap-2">
													<Info class="h-4 w-4" />
													<span class="text-sm font-medium">Cannot add additional URL rules</span>
												</div>
												<p class="mt-1 text-xs">
													This page already has an "Equals" URL condition. You can add DataLayer
													rules instead.
												</p>
												<Button
													variant="ghost"
													size="sm"
													class="mt-2 h-6 px-2 text-xs"
													onclick={() => (showEqualsWarning = false)}
												>
													Dismiss
												</Button>
											</div>
										{/if}

										<!-- Filter Engine Validation Results -->
										{#if $hasIssues}
											<div class="space-y-2">
												{#each $errors as error}
													<div class="rounded-lg border border-red-200 bg-red-50 p-3 text-red-800">
														<div class="flex items-start gap-2">
															<X class="mt-0.5 h-4 w-4 flex-shrink-0" />
															<div>
																<p class="text-sm font-medium">Error</p>
																<p class="text-xs">{error.message}</p>
															</div>
														</div>
													</div>
												{/each}

												{#each $warnings as warning}
													<div
														class="rounded-lg border border-amber-200 bg-amber-50 p-3 text-amber-800"
													>
														<div class="flex items-start gap-2">
															<HelpCircle class="mt-0.5 h-4 w-4 flex-shrink-0" />
															<div>
																<p class="text-sm font-medium">Warning</p>
																<p class="text-xs">{warning.message}</p>
															</div>
														</div>
													</div>
												{/each}
											</div>
										{/if}

										<!-- Firing Logic Display -->
										{#if $firingLogic && $firingLogic !== 'No firing conditions defined'}
											<div class="rounded-lg border border-blue-200 bg-blue-50 p-3 text-blue-800">
												<div class="flex items-start gap-2">
													<CodeXml class="mt-0.5 h-4 w-4 flex-shrink-0" />
													<div>
														<p class="text-sm font-medium">Firing Logic</p>
														<p class="text-xs">{$firingLogic}</p>
													</div>
												</div>
											</div>
										{/if}

										{#if activePage.rules.length === 0}
											<DropdownMenu.Root>
												<DropdownMenu.Trigger>
													<Button variant="outline" class="w-full">
														<Plus class="mr-2 h-4 w-4" /> Add Rule
													</Button>
												</DropdownMenu.Trigger>
												<DropdownMenu.Content class="w-56">
													<DropdownMenu.Item onclick={addUrlRule}>
														<FileCode2 class="mr-2 h-4 w-4" />
														URL Path Rule
													</DropdownMenu.Item>
													<DropdownMenu.Item onclick={addDataLayerRule}>
														<CodeXml class="mr-2 h-4 w-4" />
														DataLayer Rule
													</DropdownMenu.Item>
												</DropdownMenu.Content>
											</DropdownMenu.Root>
										{/if}
									{/if}
								</div>
							{:else}
								<div
									class="bg-muted/50 flex h-full min-h-48 items-center justify-center rounded-lg"
								>
									<p class="text-muted-foreground text-center font-medium">
										Select a page to configure its rules.
									</p>
								</div>
							{/if}
						</div>
					</div>
				</div>
			</div>
		</Card.Content>
		<!-- <Card.Footer>
			<div class="flex w-full justify-end gap-2">
				<Button variant="outline" href={`/projects/${page.data.project?.id}/edit?view=metadata`}>
					Metadata
				</Button>
				<Button href={`/projects/${page.data.project?.id}/edit?view=events`}>Events</Button>
			</div>
		</Card.Footer> -->
	</Card.Root>
</div>

<!-- Create Custom Page Dialog -->
<Dialog.Root bind:open={isCreateModalOpen}>
	<Dialog.Content class="sm:max-w-md">
		<Dialog.Header>
			<Dialog.Title>Create Custom Page</Dialog.Title>
			<Dialog.Description>
				Create a custom page template that can be reused and configured with specific URL rules.
			</Dialog.Description>
		</Dialog.Header>
		<div class="space-y-4 py-4">
			<div class="space-y-2">
				<Label for="page-name">Page Name</Label>
				<Input
					id="page-name"
					placeholder="e.g., Landing Page, Thank You Page"
					bind:value={newPageName}
				/>
			</div>
			<div class="space-y-2">
				<Label for="page-type">Base Page Type</Label>
				<Select.Root type="single" bind:value={newPageType}>
					<Select.Trigger>
						{newPageType
							? availablePages.find((p) => p.id === newPageType)?.name ||
								'Select a base page type...'
							: 'Select a base page type...'}
					</Select.Trigger>
					<Select.Content>
						<Select.Group>
							{#each availablePages as pageTemplate (pageTemplate.id)}
								{#if !pageTemplate.isCustom}
									<Select.Item value={pageTemplate.id} label={pageTemplate.name}>
										{pageTemplate.name}
									</Select.Item>
								{/if}
							{/each}
						</Select.Group>
					</Select.Content>
				</Select.Root>
				<p class="text-muted-foreground text-xs">
					Choose a base page type that this custom page will be based on.
				</p>
			</div>
		</div>
		<Dialog.Footer>
			<Button variant="outline" onclick={() => (isCreateModalOpen = false)}>Cancel</Button>
			<Button onclick={handleCreateNewPage}>Create Page</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
