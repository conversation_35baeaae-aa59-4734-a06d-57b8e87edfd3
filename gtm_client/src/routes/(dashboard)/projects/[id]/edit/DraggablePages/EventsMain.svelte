<script lang="ts">
	import { onMount } from 'svelte';
	import { flip } from 'svelte/animate';
	import { fly } from 'svelte/transition';
	import { cn } from '$lib/shad-components/utils.js';
	import { createId as cuid } from '@paralleldrive/cuid2';
	import { toast } from 'svelte-sonner';

	// Drag and Drop
	import {
		draggable,
		dropTargetForElements,
		monitorForElements
	} from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
	import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine';

	// ShadCN/Lucide Components
	import * as Card from '$lib/shad-components/ui/card/index.js';
	import { Button } from '$lib/shad-components/ui/button';
	import { Input } from '$lib/shad-components/ui/input';
	import * as Select from '$lib/shad-components/ui/select';
	import * as DropdownMenu from '$lib/shad-components/ui/dropdown-menu';
	import { Label } from '$lib/shad-components/ui/label';
	import { Badge } from '$lib/shad-components/ui/badge';
	import Search from '@lucide/svelte/icons/search';
	import Plus from '@lucide/svelte/icons/plus';
	import Trash2 from '@lucide/svelte/icons/trash-2';
	import X from '@lucide/svelte/icons/x';
	import GripVertical from '@lucide/svelte/icons/grip-vertical';
	import FileCode2 from '@lucide/svelte/icons/file-code-2';
	import MousePointerClick from '@lucide/svelte/icons/mouse-pointer-click';
	import ArrowDownUp from '@lucide/svelte/icons/arrow-down-up';
	import Eye from '@lucide/svelte/icons/eye';
	import CodeXml from '@lucide/svelte/icons/code-xml';
	import Info from '@lucide/svelte/icons/info';
	import AlertTriangle from '@lucide/svelte/icons/alert-triangle';
	import HelpCircle from '@lucide/svelte/icons/help-circle';
	import { Separator } from '$lib/shad-components/ui/separator';
	import * as Tooltip from '$lib/shad-components/ui/tooltip';
	import * as Popover from '$lib/shad-components/ui/popover';
	import * as Dialog from '$lib/shad-components/ui/dialog';

	// Filter Engine Integration
	import { useFilterEngineService } from '$lib/hooks/useFilterEngineService';

	// --- Component Props ---
	let {
		selectedPages = $bindable([]),
		selectedEvents = $bindable([]),
		page
	} = $props<{
		selectedPages: SelectedPage[];
		selectedEvents: SelectedEvent[];
		page: any;
	}>();

	// --- Interfaces for this component ---
	interface PageRule {
		id: number;
		type: 'url' | 'datalayer'; // Add rule type
		condition: 'Contains' | 'Equals' | 'StartsWith' | 'EndsWith';
		value: string;
	}
	interface SelectedPage {
		instanceId: string;
		templateId: string;
		name: string;
		customName?: string;
		rules: PageRule[];
		isDefault?: boolean;
	}
	interface EventTemplate {
		id: string;
		name: string;
		isCustom?: boolean;
	}
	interface SelectedEvent {
		instanceId: string;
		templateId: string;
		name: string;
		customName?: string; // Optional custom name for events
		pageInstanceId: string;
		triggerType: 'page-load' | 'datalayer-event' | 'click' | 'scroll' | 'element-visible';
		rules: PageRule[];
		selector?: string;
		scrollDepth?: number;
		customCode?: string;
		dataLayerEventName?: string;
		isDefaultPageLoad?: boolean; // Mark the default page load event
		pageTargeting?: 'all' | 'specific'; // Whether event fires on all pages or specific subset
	}

	// --- Internal State ---
	let availableEvents = $state<EventTemplate[]>([
		{ id: 'view_item', name: 'View Product' },
		{ id: 'view_item_list', name: 'View Product List' },
		{ id: 'add_to_cart', name: 'Add to Cart' }
	]);
	let activeEventInstanceId = $state<string | null>(null);
	let isMobile = $state(false);
	let isDragging = $state(false);
	let allowDuplicates = $state(false);
	let isDropTargetForPage = $state<Record<string, boolean>>({});

	// Custom event creation state
	let isCreateEventModalOpen = $state(false);
	let newEventName = $state('');
	let newEventType = $state<string | undefined>(undefined);
	let showEqualsWarning = $state(false);

	const activeEvent = $derived.by(() => {
		if (!activeEventInstanceId) return null;
		return selectedEvents.find((e: SelectedEvent) => e.instanceId === activeEventInstanceId);
	});

	const triggerTypes = [
		{
			value: 'page-load',
			label: 'Page Load',
			icon: FileCode2,
			description: 'Triggers when the page loads and matches the specified URL conditions'
		},
		{
			value: 'datalayer-event',
			label: 'DataLayer Event',
			icon: CodeXml,
			description: 'Triggers when a specific dataLayer event is pushed to the dataLayer'
		},
		{
			value: 'click',
			label: 'Click',
			icon: MousePointerClick,
			description: 'Triggers when a user clicks on the specified element selector'
		},
		{
			value: 'scroll',
			label: 'Page Scroll',
			icon: ArrowDownUp,
			description: 'Triggers when the user scrolls to a specific depth percentage on the page'
		},
		{
			value: 'element-visible',
			label: 'Element Visible',
			icon: Eye,
			description: 'Triggers when the specified element becomes visible in the viewport'
		}
	];

	const urlConditions = [
		{ value: 'Contains', label: 'Contains' },
		{ value: 'Equals', label: 'Equals' },
		{ value: 'StartsWith', label: 'Starts With' },
		{ value: 'EndsWith', label: 'Ends With' }
	];

	const activeTrigger = $derived(
		activeEvent ? triggerTypes.find((t) => t.value === activeEvent.triggerType) : null
	);

	// Filter Engine Integration
	const filterEngineService = useFilterEngineService();
	const { hasIssues, errors, warnings, firingLogic } = filterEngineService;

	// Convert data to GTM format for validation
	const gtmPages = $derived.by(() => {
		return selectedPages.map((page) => ({
			id: page.instanceId,
			name: page.name,
			rules: page.rules.map((rule) => ({
				id: rule.id.toString(),
				type: rule.type as 'url' | 'datalayer',
				condition: rule.condition as 'Contains' | 'Equals' | 'StartsWith' | 'EndsWith',
				value: rule.value,
				isActive: true
			})),
			isActive: true
		}));
	});

	const gtmEvents = $derived.by(() => {
		return selectedEvents.map((event) => ({
			id: event.instanceId,
			name: event.name,
			triggerType: event.triggerType,
			rules: event.rules.map((rule) => ({
				id: rule.id.toString(),
				type: rule.type as 'url' | 'datalayer',
				condition: rule.condition as 'Contains' | 'Equals' | 'StartsWith' | 'EndsWith',
				value: rule.value,
				isActive: true
			})),
			isActive: true,
			pageInstanceId: event.pageInstanceId
		}));
	});

	// Reactive validation for events in context of pages
	$effect(() => {
		if (gtmPages.length > 0 || gtmEvents.length > 0) {
			filterEngineService.validateEvents(gtmPages, gtmEvents);
		} else {
			filterEngineService.clearValidation();
		}
	});

	// --- DOM Element Maps & Svelte Actions for DnD ---
	let availableEventsMap = new Map<string, HTMLElement>();
	let pageDropAreaMap = new Map<string, HTMLElement>();

	function mapAvailableEventElement(node: HTMLElement, id: string) {
		availableEventsMap.set(id, node);
		return {
			destroy() {
				availableEventsMap.delete(id);
			}
		};
	}
	function mapPageDropAreaElement(node: HTMLElement, id: string) {
		pageDropAreaMap.set(id, node);
		return {
			destroy() {
				pageDropAreaMap.delete(id);
			}
		};
	}

	// --- Core Logic ---

	// Function to create custom events
	function handleCreateNewEvent() {
		if (!newEventName.trim()) {
			toast.error('Event name cannot be empty.');
			return;
		}
		const newCustomEvent: EventTemplate = {
			id: cuid(),
			name: newEventName,
			isCustom: true
		};
		availableEvents = [...availableEvents, newCustomEvent];
		isCreateEventModalOpen = false;
		newEventName = '';
		newEventType = undefined;
		toast.success(`Custom event "${newEventName}" created successfully.`);
	}

	// Ensure only the "All Pages" page has a default Page Load event
	function ensureDefaultPageLoadEvents() {
		// Find the "All Pages" default page
		const allPagesPage = selectedPages.find(
			(page: SelectedPage) => page.templateId === 'all-pages'
		);

		if (allPagesPage) {
			const hasPageLoadEvent = selectedEvents.some(
				(event: SelectedEvent) =>
					event.pageInstanceId === allPagesPage.instanceId && event.isDefaultPageLoad
			);

			if (!hasPageLoadEvent) {
				const defaultPageLoadEvent: SelectedEvent = {
					instanceId: `page-load-${allPagesPage.instanceId}`,
					templateId: 'page-load-default',
					name: 'Page Load',
					pageInstanceId: allPagesPage.instanceId,
					triggerType: 'page-load',
					rules: [], // Default page load events don't need additional rules
					isDefaultPageLoad: true,
					pageTargeting: 'all'
				};
				selectedEvents = [...selectedEvents, defaultPageLoadEvent];
			}
		}
	}

	// Call ensureDefaultPageLoadEvents when selectedPages changes
	$effect(() => {
		if (selectedPages.length > 0) {
			ensureDefaultPageLoadEvents();
		}
	});

	function addEventToPage(eventTemplate: EventTemplate, pageInstanceId: string) {
		const isAlreadySelected = selectedEvents.some(
			(p: SelectedEvent) => p.templateId === eventTemplate.id && p.pageInstanceId === pageInstanceId
		);

		if (!isAlreadySelected || allowDuplicates) {
			const newEventInstance: SelectedEvent = {
				instanceId: cuid(),
				templateId: eventTemplate.id,
				name: eventTemplate.name,
				pageInstanceId: pageInstanceId,
				triggerType: 'page-load',
				rules: [], // Start with empty rules - user can add them if needed
				scrollDepth: 75,
				pageTargeting: 'specific'
			};
			selectedEvents = [...selectedEvents, newEventInstance];
		} else {
			toast.info(`"${eventTemplate.name}" is already in the list for this page.`);
		}
	}

	function deleteSelectedEvent(e: Event, eventToDelete: SelectedEvent) {
		e.stopPropagation();

		// Prevent deletion of default Page Load events
		if (eventToDelete.isDefaultPageLoad) {
			toast.error('The default Page Load event cannot be deleted.');
			return;
		}

		selectedEvents = selectedEvents.filter(
			(e: SelectedEvent) => e.instanceId !== eventToDelete.instanceId
		);
		if (activeEventInstanceId === eventToDelete.instanceId) {
			activeEventInstanceId = null;
		}
	}

	// Function to check for missing configurations
	function hasWarnings(event: SelectedEvent): string[] {
		const warnings: string[] = [];

		// Check event-specific trigger configurations
		if (event.triggerType === 'datalayer-event' && !event.dataLayerEventName?.trim()) {
			warnings.push('DataLayer event name is missing');
		}

		if (event.triggerType === 'click' && !event.selector?.trim()) {
			warnings.push('CSS selector is missing');
		}

		if (event.triggerType === 'element-visible' && !event.selector?.trim()) {
			warnings.push('Element selector is missing');
		}

		// Check page rules
		const currentPage = selectedPages.find((p: any) => p.instanceId === event.pageInstanceId);
		if (currentPage && !currentPage.isDefault) {
			if (!currentPage.rules || currentPage.rules.length === 0) {
				warnings.push('Page has no URL or DataLayer rules defined');
			} else {
				// Check for empty rule values
				for (const rule of currentPage.rules) {
					if (rule.type === 'url' && !rule.value?.trim()) {
						warnings.push('Page URL rule has empty path value');
					}
					if (rule.type === 'datalayer' && !rule.value?.trim()) {
						warnings.push('Page DataLayer rule has empty event name');
					}
				}
			}
		}

		return warnings;
	}

	// Function to get detailed page information for popover
	function getPageDetails(event: SelectedEvent) {
		const currentPage = selectedPages.find((p: any) => p.instanceId === event.pageInstanceId);
		if (!currentPage) return null;

		return {
			name: currentPage.customName || currentPage.name,
			isDefault: currentPage.isDefault,
			rules: currentPage.rules || [],
			hasRules: currentPage.rules && currentPage.rules.length > 0
		};
	}

	function addRule(event: SelectedEvent, ruleType: 'url' | 'datalayer' = 'url') {
		if (!event) return;

		// L2 Validation: Check if event can add URL rules based on page rules
		if (ruleType === 'url') {
			const eventPage = selectedPages.find(
				(p: SelectedPage) => p.instanceId === event.pageInstanceId
			);
			const pageUrlRule = eventPage?.rules?.find((r: PageRule) => r.type === 'url');

			if (pageUrlRule && pageUrlRule.condition === 'Equals') {
				// Show warning instead of toast
				showEqualsWarning = true;
				return;
			}
		}

		const newRule: PageRule =
			ruleType === 'url'
				? { id: Date.now(), type: 'url' as const, condition: 'Contains', value: '' }
				: { id: Date.now(), type: 'datalayer' as const, condition: 'Equals', value: '' };

		event.rules = [...event.rules, newRule];
	}

	function deleteRule(event: SelectedEvent, ruleId: number) {
		if (!event) return;
		event.rules = event.rules.filter((r) => r.id !== ruleId);
	}

	// Validation function for URL inputs in Events tab
	function validateEventUrlInput(value: string): string {
		// Remove leading slash if present
		if (value.startsWith('/')) {
			return value.substring(1);
		}
		return value;
	}

	// --- Lifecycle & DnD Setup ---
	onMount(() => {
		const mediaQuery = window.matchMedia('(max-width: 1023px)');
		isMobile = mediaQuery.matches;
		const listener = (e: MediaQueryListEvent) => (isMobile = e.matches);
		mediaQuery.addEventListener('change', listener);
		return () => mediaQuery.removeEventListener('change', listener);
	});

	$effect(() => {
		if (isMobile) return;

		const unregisterDraggables = availableEvents.map((event) => {
			const el = availableEventsMap.get(event.id);
			if (!el) return () => {};
			return draggable({ element: el, getInitialData: () => ({ type: 'event', event }) });
		});

		const unregisterDropTargets = selectedPages.map((page: SelectedPage) => {
			const el = pageDropAreaMap.get(page.instanceId);
			if (!el) return () => {};
			return dropTargetForElements({
				element: el,
				getData: () => ({ type: 'page-drop-area', pageInstanceId: page.instanceId }),
				canDrop: ({ source }) => source.data.type === 'event',
				onDragEnter: () => (isDropTargetForPage[page.instanceId] = true),
				onDragLeave: () => (isDropTargetForPage[page.instanceId] = false),
				onDrop: ({ source }) => {
					isDropTargetForPage[page.instanceId] = false;
					const eventTemplate = source.data.event as EventTemplate;
					if (eventTemplate) {
						addEventToPage(eventTemplate, page.instanceId);
					}
				}
			});
		});

		const cleanup = combine(
			monitorForElements({
				onDragStart: () => (isDragging = true),
				onDrop: () => (isDragging = false)
			}),
			...unregisterDraggables,
			...unregisterDropTargets
		);
		return () => cleanup();
	});
</script>

<Tooltip.Provider>
	<div class="grid h-full grid-cols-1 gap-6 lg:grid-cols-3">
		<Card.Root class="lg:col-span-1">
			<Card.Header>
				<div class="flex items-center justify-between">
					<Card.Title class="text-lg">Available Events</Card.Title>
					<Button variant="outline" size="sm" onclick={() => (isCreateEventModalOpen = true)}>
						<Plus class="mr-2 h-4 w-4" />
						Create Custom
					</Button>
				</div>
			</Card.Header>
			<Card.Content>
				<div class="relative mb-4">
					<Search class="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
					<Input placeholder="Search Events..." class="pl-9" />
				</div>
				<div class={cn('mt-4 space-y-1', isMobile && 'flex gap-2 overflow-x-auto py-2')}>
					{#each availableEvents as event (event.id)}
						<div
							use:mapAvailableEventElement={event.id}
							class={cn(
								'flex items-center justify-between rounded-md border p-2.5 transition-colors',
								isMobile ? 'bg-secondary/50 w-48 flex-shrink-0' : 'hover:bg-accent',
								isDragging ? 'cursor-grabbing' : 'cursor-grab'
							)}
						>
							<div class="flex items-center gap-2 overflow-hidden">
								<GripVertical class="text-muted-foreground h-5 w-5 flex-shrink-0" />
								<span class="text-foreground truncate text-sm font-medium">{event.name}</span>
								{#if event.isCustom}
									<Badge variant="outline" class="text-xs">Custom</Badge>
								{/if}
							</div>
							{#if isMobile}
								<DropdownMenu.Root>
									<DropdownMenu.Trigger>
										{#snippet child({ props })}
											<Button {...props} size="icon" variant="ghost" class="h-7 w-7 flex-shrink-0">
												<Plus class="h-4 w-4" />
											</Button>
										{/snippet}
									</DropdownMenu.Trigger>
									<DropdownMenu.Content>
										<DropdownMenu.Label>Add to Page</DropdownMenu.Label>
										<DropdownMenu.Separator />
										{#each selectedPages as page (page.instanceId)}
											<DropdownMenu.Item onclick={() => addEventToPage(event, page.instanceId)}>
												{page.name}
											</DropdownMenu.Item>
										{:else}
											<DropdownMenu.Item disabled>No pages selected</DropdownMenu.Item>
										{/each}
									</DropdownMenu.Content>
								</DropdownMenu.Root>
							{/if}
						</div>
					{/each}
				</div>
			</Card.Content>
		</Card.Root>

		<div class="grid h-full grid-cols-1 gap-6 lg:col-span-2 xl:grid-cols-2">
			<div class="flex flex-col space-y-4">
				<h3 class="text-foreground text-lg font-semibold">Selected Events by Page</h3>
				<div class="space-y-4 overflow-y-auto">
					{#each selectedPages as page (page.instanceId)}
						<div use:mapPageDropAreaElement={page.instanceId}>
							<Card.Root
								class={cn(
									'transition-colors',
									isDropTargetForPage[page.instanceId] && 'border-primary bg-accent'
								)}
							>
								<Card.Header>
									<Card.Title class="text-base">{page.name}</Card.Title>
								</Card.Header>
								<Card.Content class="min-h-24">
									{@const eventsForPage = selectedEvents.filter(
										(e: SelectedEvent) => e.pageInstanceId === page.instanceId
									)}
									{#if eventsForPage.length === 0}
										<div
											class="flex h-full items-center justify-center rounded-lg border-2 border-dashed p-4"
										>
											<p class="text-muted-foreground text-center text-sm">Drop events here</p>
										</div>
									{:else}
										<div class="space-y-2">
											{#each eventsForPage as event (event.instanceId)}
												<div
													class={cn(
														'bg-background cursor-pointer rounded-lg border p-3 transition-all',
														activeEventInstanceId === event.instanceId
															? 'border-primary'
															: 'hover:bg-accent/50'
													)}
													onclick={() => (activeEventInstanceId = event.instanceId)}
													role="button"
													tabindex="0"
													onkeydown={(e) =>
														e.key === 'Enter' && (activeEventInstanceId = event.instanceId)}
												>
													<div class="flex items-center justify-between">
														<div class="flex items-center gap-2">
															<span class="text-sm font-medium"
																>{event.customName || event.name}</span
															>
															{#if event.isDefaultPageLoad}
																<Badge variant="secondary" class="text-xs">Default</Badge>
															{:else}
																{@const warnings = hasWarnings(event)}
																{@const pageDetails = getPageDetails(event)}
																{#if warnings.length > 0 || pageDetails}
																	<Popover.Root>
																		<Popover.Trigger>
																			{#if warnings.length > 0}
																				<AlertTriangle
																					class="h-4 w-4 text-amber-500 hover:text-amber-600"
																				/>
																			{:else}
																				<HelpCircle
																					class="h-4 w-4 text-blue-500 hover:text-blue-600"
																				/>
																			{/if}
																		</Popover.Trigger>
																		<Popover.Content side="right" class="w-80">
																			<div class="space-y-3">
																				<div>
																					<h4 class="mb-2 text-sm font-semibold">
																						Event Configuration
																					</h4>
																					<div class="space-y-2 text-xs">
																						<div>
																							<span class="font-medium">Event:</span>
																							{event.customName || event.name}
																						</div>
																						<div>
																							<span class="font-medium">Trigger:</span>
																							{#if event.triggerType === 'page-load'}
																								Page Load
																							{:else if event.triggerType === 'datalayer-event'}
																								DataLayer Event ({event.dataLayerEventName ||
																									'not configured'})
																							{:else if event.triggerType === 'click'}
																								Click ({event.selector || 'not configured'})
																							{:else if event.triggerType === 'scroll'}
																								Scroll ({event.scrollDepth || 0}%)
																							{:else if event.triggerType === 'element-visible'}
																								Element Visible ({event.selector ||
																									'not configured'})
																							{/if}
																						</div>
																					</div>
																				</div>

																				{#if pageDetails}
																					<div>
																						<h4 class="mb-2 text-sm font-semibold">Page Context</h4>
																						<div class="space-y-2 text-xs">
																							<div>
																								<span class="font-medium">Page:</span>
																								{pageDetails.name}
																								{#if pageDetails.isDefault}
																									<Badge variant="secondary" class="ml-1 text-xs"
																										>Default</Badge
																									>
																								{/if}
																							</div>
																							{#if pageDetails.hasRules}
																								<div>
																									<span class="font-medium">Rules:</span>
																									<div class="mt-1 ml-2 space-y-1">
																										{#each pageDetails.rules as rule}
																											<div class="flex items-center gap-2">
																												{#if rule.type === 'url'}
																													<span class="text-blue-600">URL</span>
																													<span>{rule.condition}</span>
																													<span
																														class="rounded bg-gray-100 px-1 font-mono"
																														>{rule.value || 'empty'}</span
																													>
																												{:else if rule.type === 'datalayer'}
																													<span class="text-green-600"
																														>DataLayer</span
																													>
																													<span
																														class="rounded bg-gray-100 px-1 font-mono"
																														>{rule.value || 'empty'}</span
																													>
																												{/if}
																											</div>
																										{/each}
																									</div>
																								</div>
																							{:else}
																								<div class="text-gray-500">
																									No page rules defined
																								</div>
																							{/if}
																						</div>
																					</div>
																				{/if}

																				{#if warnings.length > 0}
																					<div>
																						<h4 class="mb-2 text-sm font-semibold text-amber-600">
																							Configuration Issues
																						</h4>
																						<div class="space-y-1">
																							{#each warnings as warning}
																								<p
																									class="rounded bg-amber-50 p-2 text-xs text-amber-700"
																								>
																									{warning}
																								</p>
																							{/each}
																						</div>
																					</div>
																				{/if}
																			</div>
																		</Popover.Content>
																	</Popover.Root>
																{/if}
															{/if}
														</div>
														{#if !event.isDefaultPageLoad}
															<Button
																variant="ghost"
																size="icon"
																class="h-6 w-6"
																onclick={(e) => deleteSelectedEvent(e, event)}
															>
																<Trash2 class="h-3 w-3" />
															</Button>
														{/if}
													</div>
												</div>
											{/each}
										</div>
									{/if}
								</Card.Content>
							</Card.Root>
						</div>
					{/each}
					{#if selectedPages.length === 0}
						<div
							class="flex h-full min-h-48 items-center justify-center rounded-lg border-2 border-dashed"
						>
							<p class="text-muted-foreground text-center">
								Go to the 'Pages' tab to select pages first.
							</p>
						</div>
					{/if}
				</div>
			</div>

			<div class="relative flex flex-col space-y-4">
				<h3 class="text-foreground text-lg font-semibold">Event Rules</h3>
				<div class="flex-grow">
					{#if activeEvent}
						<div class="space-y-4 rounded-lg border p-4" transition:fly={{ x: 10, duration: 300 }}>
							<h4 class="font-semibold">{activeEvent.customName || activeEvent.name}</h4>

							<!-- Custom Name Input -->
							{#if !activeEvent.isDefaultPageLoad}
								<div class="space-y-2">
									<Label for="custom-name" class="text-xs font-semibold"
										>Custom Event Name (Optional)</Label
									>
									<Input
										id="custom-name"
										placeholder={`e.g., Custom ${activeEvent.name}`}
										bind:value={activeEvent.customName}
									/>
									<p class="text-muted-foreground text-xs">
										Give this event a custom name to distinguish it from other instances.
									</p>
								</div>
								<Separator />
							{/if}

							<div class="space-y-2">
								<Label class="font-semibold">Trigger this event on</Label>
								{#if activeEvent.isDefaultPageLoad}
									<!-- Default Page Load Event - Trigger type is locked -->
									<div class="bg-muted/30 rounded-lg border p-3">
										<div class="flex items-center gap-2">
											<FileCode2 class="h-4 w-4" />
											<span class="font-medium">Page Load</span>
											<Badge variant="secondary" class="text-xs">Locked</Badge>
										</div>
										<p class="text-muted-foreground mt-1 text-xs">
											Default page load events are always triggered on page load.
										</p>
									</div>
								{:else}
									<!-- Regular Event - Full trigger type selection -->
									<Select.Root type="single" bind:value={activeEvent.triggerType}>
										<Select.Trigger class="w-full">
											{#if activeTrigger}
												{@const IconComponent = activeTrigger.icon}
												<div class="flex items-center gap-2">
													<IconComponent class="h-4 w-4" />
													{activeTrigger.label}
												</div>
											{/if}
										</Select.Trigger>
										<Select.Content>
											<Select.Group>
												{#each triggerTypes as trigger (trigger.value)}
													{@const IconComponent = trigger.icon}
													<Select.Item value={trigger.value} label={trigger.label} class="group">
														<div class="flex w-full items-center justify-between">
															<div class="flex items-center gap-2">
																<IconComponent class="h-4 w-4" />
																{trigger.label}
															</div>
															<Tooltip.Root>
																<Tooltip.Trigger>
																	<Info
																		class="text-muted-foreground hover:text-foreground h-3 w-3 opacity-0 transition-colors group-hover:opacity-100"
																	/>
																</Tooltip.Trigger>
																<Tooltip.Content side="right" class="max-w-xs">
																	{trigger.description}
																</Tooltip.Content>
															</Tooltip.Root>
														</div>
													</Select.Item>
												{/each}
											</Select.Group>
										</Select.Content>
									</Select.Root>
								{/if}
							</div>

							<Separator />

							<!-- Page Relationship & Targeting -->
							<div class="space-y-3">
								<Label class="font-semibold">Event Firing Conditions</Label>

								{#if activeEvent}
									{@const currentPage = selectedPages.find(
										(p: SelectedPage) => p.instanceId === activeEvent.pageInstanceId
									)}

									<div class="bg-muted/30 space-y-3 rounded-lg border p-4">
										<div class="flex items-center justify-between">
											<div class="flex items-center gap-2">
												<Info class="text-muted-foreground h-4 w-4" />
												<span class="text-sm font-medium">Event Firing Conditions</span>
											</div>
											<Popover.Root>
												<Popover.Trigger>
													<Info class="h-4 w-4 cursor-pointer text-blue-500 hover:text-blue-600" />
												</Popover.Trigger>
												<Popover.Content side="right" class="w-96">
													<div class="space-y-3">
														<h4 class="text-sm font-semibold">Complete Firing Logic</h4>
														<div class="space-y-2 text-xs">
															<div class="rounded border-l-4 border-blue-400 bg-blue-50 p-3">
																<div class="mb-1 font-medium text-blue-800">Page Context</div>
																<div class="text-blue-700">
																	Page: <span class="rounded bg-white px-1 font-mono"
																		>{currentPage?.customName ||
																			currentPage?.name ||
																			'Unknown'}</span
																	>
																</div>
																{#if currentPage?.rules && currentPage.rules.length > 0}
																	<div class="mt-2">
																		<div class="font-medium text-blue-800">Page Rules:</div>
																		{#each currentPage.rules as rule, i}
																			<div class="mt-1 ml-2 flex items-center gap-2">
																				{#if i > 0}
																					<span class="font-medium text-blue-600">AND</span>
																				{/if}
																				{#if rule.type === 'url'}
																					<span
																						class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800"
																						>URL</span
																					>
																					<span>{rule.condition}</span>
																					<span class="rounded bg-white px-1 font-mono"
																						>{rule.value || 'empty'}</span
																					>
																				{:else if rule.type === 'datalayer'}
																					<span
																						class="rounded bg-green-100 px-2 py-1 text-xs text-green-800"
																						>DataLayer</span
																					>
																					<span class="rounded bg-white px-1 font-mono"
																						>{rule.value || 'empty'}</span
																					>
																				{/if}
																			</div>
																		{/each}
																	</div>
																{:else}
																	<div class="mt-1 text-blue-600">
																		No page rules (fires on all page visits)
																	</div>
																{/if}
															</div>

															<div class="rounded border-l-4 border-purple-400 bg-purple-50 p-3">
																<div class="mb-1 font-medium text-purple-800">Event Trigger</div>
																<div class="text-purple-700">
																	{#if activeEvent.triggerType === 'page-load'}
																		<span
																			class="rounded bg-purple-100 px-2 py-1 text-xs text-purple-800"
																			>Page Load</span
																		>
																		<span class="ml-2">Fires immediately when page loads</span>
																	{:else if activeEvent.triggerType === 'datalayer-event'}
																		<span
																			class="rounded bg-purple-100 px-2 py-1 text-xs text-purple-800"
																			>DataLayer Event</span
																		>
																		<span class="ml-2 rounded bg-white px-1 font-mono"
																			>{activeEvent.dataLayerEventName || 'not configured'}</span
																		>
																	{:else if activeEvent.triggerType === 'click'}
																		<span
																			class="rounded bg-purple-100 px-2 py-1 text-xs text-purple-800"
																			>Click</span
																		>
																		<span class="ml-2 rounded bg-white px-1 font-mono"
																			>{activeEvent.selector || 'not configured'}</span
																		>
																	{:else if activeEvent.triggerType === 'scroll'}
																		<span
																			class="rounded bg-purple-100 px-2 py-1 text-xs text-purple-800"
																			>Scroll</span
																		>
																		<span class="ml-2">{activeEvent.scrollDepth || 0}% of page</span
																		>
																	{:else if activeEvent.triggerType === 'element-visible'}
																		<span
																			class="rounded bg-purple-100 px-2 py-1 text-xs text-purple-800"
																			>Element Visible</span
																		>
																		<span class="ml-2 rounded bg-white px-1 font-mono"
																			>{activeEvent.selector || 'not configured'}</span
																		>
																	{/if}
																</div>
															</div>

															<!-- Event-specific rules if any -->
															{#if activeEvent.rules && activeEvent.rules.length > 0}
																<div class="rounded border-l-4 border-orange-400 bg-orange-50 p-3">
																	<div class="mb-1 font-medium text-orange-800">
																		Event Rules (Additional)
																	</div>
																	<div class="text-orange-700">
																		<div class="mb-2">
																			These rules are also applied (ALL must match):
																		</div>
																		{#each activeEvent.rules as rule}
																			<div class="mt-1 ml-2 flex items-center gap-2">
																				{#if rule.type === 'url'}
																					<span
																						class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800"
																						>URL</span
																					>
																					<span>{rule.condition}</span>
																					<span class="rounded bg-white px-1 font-mono"
																						>{rule.value || 'empty'}</span
																					>
																				{:else if rule.type === 'datalayer'}
																					<span
																						class="rounded bg-purple-100 px-2 py-1 text-xs text-purple-800"
																						>DataLayer</span
																					>
																					<span class="rounded bg-white px-1 font-mono"
																						>{rule.value || 'empty'}</span
																					>
																				{/if}
																			</div>
																		{/each}
																	</div>
																</div>
															{/if}

															<div class="rounded border-l-4 border-green-400 bg-green-50 p-3">
																<div class="mb-1 font-medium text-green-800">
																	Complete Logic Summary
																</div>
																<div class="space-y-2 text-xs text-green-700">
																	<div class="rounded border bg-white p-2">
																		<div class="mb-1 font-medium">This event fires when:</div>
																		<div class="space-y-1">
																			<!-- Page conditions -->
																			{#if currentPage?.isDefault}
																				<div>✓ User visits ANY page on the website</div>
																			{:else if currentPage?.rules && currentPage.rules.length > 0}
																				<div>✓ User visits a page where:</div>
																				{#each currentPage.rules as rule, i}
																					<div class="ml-4">
																						{#if i > 0}AND{/if}
																						{#if rule.type === 'url'}
																							URL path {rule.condition}
																							{#if rule.value?.trim()}
																								"{rule.value}"
																							{:else}
																								<span class="text-amber-600"
																									>(empty - may affect targeting)</span
																								>
																							{/if}
																						{:else if rule.type === 'datalayer'}
																							DataLayer contains event
																							{#if rule.value?.trim()}
																								"{rule.value}"
																							{:else}
																								<span class="text-red-600">(empty - invalid)</span>
																							{/if}
																						{/if}
																					</div>
																				{/each}
																			{:else}
																				<div>✓ User visits ANY page (no page rules defined)</div>
																			{/if}

																			<!-- Event trigger conditions -->
																			<div>✓ AND the trigger condition is met:</div>
																			<div class="ml-4">
																				{#if activeEvent.triggerType === 'page-load'}
																					Page loads
																				{:else if activeEvent.triggerType === 'datalayer-event'}
																					DataLayer event "{activeEvent.dataLayerEventName ||
																						'not configured'}" occurs
																				{:else if activeEvent.triggerType === 'click'}
																					Element "{activeEvent.selector || 'not configured'}" is
																					clicked
																				{:else if activeEvent.triggerType === 'scroll'}
																					User scrolls {activeEvent.scrollDepth || 0}% of the page
																				{:else if activeEvent.triggerType === 'element-visible'}
																					Element "{activeEvent.selector || 'not configured'}"
																					becomes visible
																				{/if}
																			</div>

																			<!-- Event-specific rules -->
																			{#if activeEvent.rules && activeEvent.rules.length > 0}
																				<div>✓ AND additional event rules are met:</div>
																				{#each activeEvent.rules as rule, i}
																					<div class="ml-4">
																						{#if i > 0}AND{/if}
																						{#if rule.type === 'url'}
																							URL path {rule.condition} "{rule.value || 'empty'}"
																						{:else if rule.type === 'datalayer'}
																							DataLayer contains "{rule.value || 'empty'}"
																						{/if}
																					</div>
																				{/each}
																			{/if}
																		</div>
																	</div>

																	<!-- URL Examples -->
																	<div class="rounded border bg-white p-2">
																		<div class="mb-1 font-medium">
																			Example URLs that would trigger:
																		</div>
																		{#if currentPage?.isDefault}
																			{@const baseUrl =
																				page?.data?.project?.subdomain?.full_uri ||
																				'https://example.com'}
																			<div class="font-mono text-xs">{baseUrl}/* (any page)</div>
																		{:else if currentPage?.rules && currentPage.rules.length > 0}
																			{@const baseUrl =
																				page?.data?.project?.subdomain?.full_uri ||
																				'https://example.com'}
																			{@const urlRule = currentPage.rules.find(
																				(r) => r.type === 'url'
																			)}
																			{#if urlRule}
																				{#if urlRule.condition === 'Equals'}
																					<div class="font-mono text-xs">
																						{baseUrl}{urlRule.value ? `/${urlRule.value}` : ''}
																					</div>
																				{:else if urlRule.condition === 'Contains'}
																					<div class="font-mono text-xs">
																						{baseUrl}/page-{urlRule.value || 'X'}-example
																					</div>
																					<div class="font-mono text-xs">
																						{baseUrl}/other/{urlRule.value || 'X'}/path
																					</div>
																				{:else if urlRule.condition === 'StartsWith'}
																					<div class="font-mono text-xs">
																						{baseUrl}/{urlRule.value || 'X'}*
																					</div>
																				{:else if urlRule.condition === 'EndsWith'}
																					<div class="font-mono text-xs">
																						{baseUrl}/*{urlRule.value || 'X'}
																					</div>
																				{:else}
																					<!-- Fallback for any other condition -->
																					<div class="text-muted-foreground font-mono text-xs">
																						{baseUrl}/* (condition: {urlRule.condition})
																					</div>
																				{/if}
																			{:else}
																				<div class="font-mono text-xs">{baseUrl}/* (any page)</div>
																			{/if}
																		{:else}
																			{@const baseUrl =
																				page?.data?.project?.subdomain?.full_uri ||
																				'https://example.com'}
																			<div class="font-mono text-xs">{baseUrl}/* (any page)</div>
																		{/if}
																	</div>
																</div>
															</div>
														</div>
													</div>
												</Popover.Content>
											</Popover.Root>
										</div>

										<!-- Enhanced Summary View -->
										<div class="space-y-2">
											<div class="flex items-center gap-2 text-sm">
												<span class="text-muted-foreground">Triggers on:</span>
												<Badge variant="outline" class="text-xs">
													{currentPage?.customName || currentPage?.name || 'Unknown Page'}
												</Badge>
												{#if currentPage?.isDefault}
													<Badge variant="secondary" class="bg-blue-100 text-xs text-blue-800"
														>All Pages</Badge
													>
												{/if}
												{#if activeEvent.triggerType !== 'page-load'}
													<span class="text-muted-foreground">+</span>
													<Badge variant="secondary" class="text-xs">
														{#if activeEvent.triggerType === 'datalayer-event'}
															DataLayer: {activeEvent.dataLayerEventName || 'not configured'}
														{:else if activeEvent.triggerType === 'click'}
															Click: {activeEvent.selector || 'not configured'}
														{:else if activeEvent.triggerType === 'scroll'}
															Scroll: {activeEvent.scrollDepth || 0}%
														{:else if activeEvent.triggerType === 'element-visible'}
															Element: {activeEvent.selector || 'not configured'}
														{/if}
													</Badge>
												{/if}
											</div>

											<!-- Enhanced Page Rules Summary with Context -->
											{#if currentPage?.isDefault}
												<div class="rounded border-l-2 border-blue-400 bg-blue-50 p-2 text-xs">
													<span class="font-medium text-blue-800">
														This event will trigger on ALL pages across your website
													</span>
												</div>
											{:else if currentPage?.rules && currentPage.rules.length > 0}
												<div class="space-y-1">
													<div class="flex items-center gap-2 text-xs">
														<span class="text-muted-foreground">Page rules (ALL must match):</span>
													</div>
													{#each currentPage.rules as rule, i}
														<div class="flex items-center gap-2 text-xs">
															{#if i > 0}<span class="text-muted-foreground ml-4">AND</span>{/if}
															{#if rule.type === 'url'}
																{@const hasEmptyPath = !rule.value?.trim()}
																<Badge
																	variant="outline"
																	class="text-xs {hasEmptyPath
																		? 'border-amber-400 bg-amber-50'
																		: ''}"
																>
																	Page Path {rule.condition}
																	{hasEmptyPath ? '(empty path)' : `${rule.value}`}
																</Badge>
																{#if hasEmptyPath}
																	<div class="rounded bg-amber-50 px-2 py-1 text-xs text-amber-700">
																		{#if rule.condition === 'equals' || rule.condition === 'ends-with'}
																			⚠️ Will likely only trigger on homepage
																		{:else if rule.condition === 'contains' || rule.condition === 'starts-with'}
																			⚠️ Will trigger on multiple pages - consider adding a path
																		{/if}
																	</div>
																{/if}
															{:else if rule.type === 'datalayer'}
																{@const hasEmptyValue = !rule.value?.trim()}
																<Badge
																	variant="outline"
																	class="text-xs {hasEmptyValue ? 'border-red-400 bg-red-50' : ''}"
																>
																	DataLayer: {hasEmptyValue ? '(empty event name)' : rule.value}
																</Badge>
																{#if hasEmptyValue}
																	<div class="rounded bg-red-50 px-2 py-1 text-xs text-red-700">
																		❌ DataLayer event name is required
																	</div>
																{/if}
															{/if}
														</div>
													{/each}
												</div>
											{:else}
												<div class="rounded border-l-2 border-amber-400 bg-amber-50 p-2 text-xs">
													<span class="font-medium text-amber-800">
														No page rules defined - will trigger on ALL page visits
													</span>
													<div class="mt-1 text-amber-700">
														Consider adding URL or DataLayer rules to target specific pages
													</div>
												</div>
											{/if}
										</div>
									</div>

									{#if !activeEvent.isDefaultPageLoad}
										<div class="space-y-2">
											<Label class="text-xs font-semibold"
												>Narrow trigger conditions (optional)</Label
											>
											<!-- <Select.Root type="single" bind:value={activeEvent.pageTargeting}>
												<Select.Trigger class="w-full">
													{activeEvent.pageTargeting === 'all'
														? 'Fire on all pages'
														: 'Fire only on this page type'}
												</Select.Trigger>
												<Select.Content>
													<Select.Group>
														<Select.Item value="specific">Fire only on this page type</Select.Item>
														<Select.Item value="all">Fire on all pages</Select.Item>
													</Select.Group>
												</Select.Content>
											</Select.Root> -->
											<!-- <p class="text-muted-foreground text-xs">
												{#if activeEvent.pageTargeting === 'all'}
													This event will fire on all pages, regardless of the page conditions
													above.
												{:else}
													This event will only fire when the page conditions above are met.
												{/if}
											</p> -->
										</div>
									{/if}
								{/if}
							</div>

							<Separator />

							{#if activeEvent.triggerType === 'page-load'}
								{#if activeEvent.isDefaultPageLoad}
									<!-- Default Page Load Event - Completely locked like Pages tab -->
									<div class="space-y-3">
										<div class="bg-muted/30 rounded-lg border p-3">
											<div class="mb-2 flex items-center gap-2">
												<Info class="text-muted-foreground h-4 w-4" />
												<span class="text-sm font-medium">Default Page Load Event</span>
											</div>
											<p class="text-muted-foreground text-xs">
												This is the default page load event for all pages. Configuration is locked.
											</p>
										</div>
										{#each activeEvent.rules as rule (rule.id)}
											<div animate:flip={{ duration: 300 }}>
												<Label class="text-xs font-semibold">URL Condition</Label>
												<div class="mt-1 flex items-center gap-2">
													<div
														class="bg-muted w-[150px] flex-shrink-0 rounded-md border px-3 py-2 text-sm"
													>
														{rule.condition}
													</div>
													<Input placeholder="all pages" value="*" disabled class="bg-muted" />
													<!-- No delete button for default page load rules -->
												</div>
											</div>
										{/each}
									</div>
								{:else}
									<!-- Regular Page Load Event - Full functionality -->
									<div class="space-y-3">
										{#each activeEvent.rules as rule, index (rule.id)}
											<div animate:flip={{ duration: 300 }}>
												{#if index > 0}
													<div class="mb-2 flex items-center justify-center">
														<span
															class="bg-muted text-muted-foreground rounded-full px-3 py-1 text-xs font-medium"
														>
															AND
														</span>
													</div>
												{/if}
												<Label class="text-xs font-semibold">
													{rule.type === 'url' ? 'URL Condition' : 'DataLayer Event'}
												</Label>
												<div class="mt-1 flex items-center gap-2">
													{#if rule.type === 'url'}
														<Select.Root
															type="single"
															bind:value={rule.condition}
															onValueChange={(value) => {
																if (!value) return;

																// L2 Validation: Check if the new condition is valid based on page rules
																const eventPage = selectedPages.find(
																	(p: SelectedPage) => p.instanceId === activeEvent.pageInstanceId
																);
																const pageUrlRule = eventPage?.rules?.find(
																	(r: PageRule) => r.type === 'url'
																);

																if (pageUrlRule) {
																	// Validate based on page rule condition
																	if (pageUrlRule.condition === 'Equals') {
																		// Show warning instead of toast
																		showEqualsWarning = true;
																		return;
																	}

																	// For Contains page rule, all event conditions are allowed
																	// For StartsWith page rule, event can narrow down
																	// For EndsWith page rule, event can narrow down
																}

																rule.condition = value;
															}}
														>
															<Select.Trigger class="w-[150px] flex-shrink-0">
																{rule.condition}
															</Select.Trigger>
															<Select.Content>
																<Select.Group>
																	{#each urlConditions as condition (condition.value)}
																		<Select.Item value={condition.value} label={condition.label}
																			>{condition.label}</Select.Item
																		>
																	{/each}
																</Select.Group>
															</Select.Content>
														</Select.Root>
													{:else}
														<!-- DataLayer Event - Fixed condition -->
														<div
															class="bg-muted w-[150px] flex-shrink-0 rounded-md border px-3 py-2 text-sm"
														>
															Equals
														</div>
													{/if}
													<div class="flex-grow">
														{#if rule.type === 'url'}
															{@const eventPage = selectedPages.find(
																(p: SelectedPage) => p.instanceId === activeEvent.pageInstanceId
															)}
															{@const pageRule = eventPage?.rules?.find(
																(r: PageRule) => r.type === 'url'
															)}

															<!-- Show URL input with prefix/suffix layout -->
															<div class="flex items-center gap-1">
																<!-- Prefix - only show when page has StartsWith and event has StartsWith -->
																{#if pageRule?.condition === 'StartsWith' && rule.condition === 'StartsWith'}
																	<span class="text-muted-foreground text-sm">
																		{pageRule.value}
																	</span>
																{:else if rule.condition === 'Equals'}
																	<span class="text-muted-foreground text-sm"> / </span>
																{/if}

																<!-- Input field -->
																<Input
																	placeholder={rule.condition === 'Contains' ||
																	rule.condition === 'EndsWith'
																		? 'search-term'
																		: 'example-path'}
																	bind:value={rule.value}
																	oninput={(e) => {
																		const target = e.target as HTMLInputElement;
																		if (target) {
																			rule.value = validateEventUrlInput(target.value);
																		}
																	}}
																	class="flex-1"
																/>

																<!-- Suffix - only show when page has EndsWith and event has EndsWith -->
																{#if pageRule?.condition === 'EndsWith' && rule.condition === 'EndsWith'}
																	<span class="text-muted-foreground text-sm">
																		{pageRule.value}
																	</span>
																{/if}
															</div>
														{:else}
															<!-- DataLayer Event Input -->
															<Input
																placeholder="dataLayer event name"
																bind:value={rule.value}
																oninput={(e) => {
																	const target = e.target as HTMLInputElement;
																	if (target) {
																		rule.value = target.value;
																	}
																}}
															/>
														{/if}
													</div>
													<Button
														variant="ghost"
														size="icon"
														class="h-8 w-8"
														onclick={() => deleteRule(activeEvent, rule.id)}
													>
														<X class="h-4 w-4" />
													</Button>
												</div>
											</div>
										{/each}
									</div>

									<!-- Filter Engine Validation Results -->
									{#if $hasIssues}
										<div class="space-y-2">
											{#each $errors as error}
												<div class="rounded-lg border border-red-200 bg-red-50 p-3 text-red-800">
													<div class="flex items-start gap-2">
														<X class="mt-0.5 h-4 w-4 flex-shrink-0" />
														<div>
															<p class="text-sm font-medium">Error</p>
															<p class="text-xs">{error.message}</p>
														</div>
													</div>
												</div>
											{/each}

											{#each $warnings as warning}
												<div
													class="rounded-lg border border-amber-200 bg-amber-50 p-3 text-amber-800"
												>
													<div class="flex items-start gap-2">
														<HelpCircle class="mt-0.5 h-4 w-4 flex-shrink-0" />
														<div>
															<p class="text-sm font-medium">Warning</p>
															<p class="text-xs">{warning.message}</p>
														</div>
													</div>
												</div>
											{/each}
										</div>
									{/if}

									<!-- Firing Logic Display -->
									{#if $firingLogic && $firingLogic !== 'No firing conditions defined'}
										<div class="rounded-lg border border-blue-200 bg-blue-50 p-3 text-blue-800">
											<div class="flex items-start gap-2">
												<CodeXml class="mt-0.5 h-4 w-4 flex-shrink-0" />
												<div>
													<p class="text-sm font-medium">Firing Logic</p>
													<p class="text-xs">{$firingLogic}</p>
												</div>
											</div>
										</div>
									{/if}

									<!-- Warning for Equals URL rule -->
									{#if showEqualsWarning && activeEvent}
										{@const eventPage = selectedPages.find(
											(p) => p.instanceId === activeEvent.pageInstanceId
										)}
										{@const pageUrlRule = eventPage?.rules?.find((r) => r.type === 'url')}
										{#if pageUrlRule && pageUrlRule.condition === 'Equals'}
											<div
												class="rounded-lg border border-amber-200 bg-amber-50 p-3 text-amber-800"
											>
												<div class="flex items-center gap-2">
													<Info class="h-4 w-4" />
													<span class="text-sm font-medium">Cannot add URL rules</span>
												</div>
												<p class="mt-1 text-xs">
													The page "{eventPage?.name}" has an "Equals" URL condition. All URL
													filtering is handled at the page level.
												</p>
												<Button
													variant="ghost"
													size="sm"
													class="mt-2 h-6 px-2 text-xs"
													onclick={() => (showEqualsWarning = false)}
												>
													Dismiss
												</Button>
											</div>
										{/if}
									{/if}

									<DropdownMenu.Root>
										<DropdownMenu.Trigger>
											<Button variant="outline" class="w-full">
												<Plus class="mr-2 h-4 w-4" />
												Add Rule
											</Button>
										</DropdownMenu.Trigger>
										<DropdownMenu.Content align="center" class="w-48">
											<DropdownMenu.Item onclick={() => addRule(activeEvent, 'url')}>
												<span class="flex items-center gap-2">
													<span class="text-blue-600">🔗</span>
													URL Path
												</span>
											</DropdownMenu.Item>
											<DropdownMenu.Item onclick={() => addRule(activeEvent, 'url')}>
												<span class="flex items-center gap-2">
													<span class="text-blue-600">🌐</span>
													URL
												</span>
											</DropdownMenu.Item>
											<DropdownMenu.Item onclick={() => addRule(activeEvent, 'datalayer')}>
												<span class="flex items-center gap-2">
													<span class="text-green-600">📊</span>
													DataLayer Event
												</span>
											</DropdownMenu.Item>
										</DropdownMenu.Content>
									</DropdownMenu.Root>
								{/if}
							{:else if activeEvent.triggerType === 'datalayer-event'}
								<div class="space-y-2">
									<Label for="datalayer-event-name" class="font-semibold"
										>DataLayer Event Name</Label
									>
									<Input
										id="datalayer-event-name"
										placeholder="e.g., purchase, add_to_cart, page_view"
										bind:value={activeEvent.dataLayerEventName}
									/>
									<p class="text-muted-foreground text-xs">
										Enter the event name that will be pushed to the dataLayer (e.g., dataLayer.push({`{event: 'purchase'}`}))
									</p>
								</div>
							{:else if activeEvent.triggerType === 'click'}
								<div class="space-y-2">
									<Label for="css-selector" class="font-semibold">CSS Selector</Label>
									<Input
										id="css-selector"
										placeholder="e.g., #buy-button, .product-card a"
										bind:value={activeEvent.selector}
									/>
								</div>
							{:else if activeEvent.triggerType === 'element-visible'}
								<div class="space-y-2">
									<Label for="element-selector" class="font-semibold">Element Selector</Label>
									<Input
										id="element-selector"
										placeholder="e.g., #pricing-section, .newsletter-signup"
										bind:value={activeEvent.selector}
									/>
								</div>
							{:else if activeEvent.triggerType === 'scroll'}
								<div class="space-y-3">
									<Label for="scroll-depth" class="font-semibold"
										>Page Scroll Depth ({activeEvent.scrollDepth ?? 75}%)</Label
									>
									<div class="flex items-center gap-4">
										<input
											type="range"
											min="0"
											max="100"
											class="w-full"
											bind:value={activeEvent.scrollDepth}
										/>
										<Input
											type="number"
											min="0"
											max="100"
											class="w-20"
											bind:value={activeEvent.scrollDepth}
										/>
									</div>
								</div>
							{/if}
						</div>
					{:else}
						<div class="bg-muted/50 flex h-full min-h-48 items-center justify-center rounded-lg">
							<p class="text-muted-foreground text-center font-medium">
								Select an event to configure its rules.
							</p>
						</div>
					{/if}
				</div>
			</div>
		</div>
	</div>

	<!-- Create Custom Event Dialog -->
	<Dialog.Root bind:open={isCreateEventModalOpen}>
		<Dialog.Content class="sm:max-w-md">
			<Dialog.Header>
				<Dialog.Title>Create Custom Event</Dialog.Title>
				<Dialog.Description>
					Create a custom event template that can be reused across different pages.
				</Dialog.Description>
			</Dialog.Header>
			<div class="space-y-4 py-4">
				<div class="space-y-2">
					<Label for="event-name">Event Name</Label>
					<Input
						id="event-name"
						placeholder="e.g., Newsletter Signup, Product View"
						bind:value={newEventName}
					/>
				</div>
			</div>
			<Dialog.Footer>
				<Button variant="outline" onclick={() => (isCreateEventModalOpen = false)}>Cancel</Button>
				<Button onclick={handleCreateNewEvent}>Create Event</Button>
			</Dialog.Footer>
		</Dialog.Content>
	</Dialog.Root>
</Tooltip.Provider>
