import type { Project } from '$lib/interfaces/project';
import type { PageServerLoad } from './$types';
import { redirect } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ url, fetch, cookies, params, locals }) => {
	if (!locals.user) {
		redirect(302, '/');
	}
	const project_id = params.id;

	const getProject = async () => {
		const relativePath = `/spark/api/v1/projects/${project_id}/`;

		// const absoluteUrl = new URL(relativePath, url.origin);
		return fetch(relativePath)
			.then(async (res) => {
				if (!res.ok) {
					return null;
				} else {
					return res.json();
				}
			})
			.then((data) => {
				return data?.data ?? {};
			});
	};

	const project = (await getProject()) as Project;

	if (!project?.id) {
		cookies.set('message', 'Project not found', { path: '/' });
		cookies.set('messageType', 'error', { path: '/' });
		redirect(302, '/dashboard');
	}

	if (project.status === 'archived') {
		cookies.set('message', 'Project is archived', { path: '/' });
		cookies.set('messageType', 'error', { path: '/' });
		redirect(302, `/workspaces/${project.workspace_id}`);
	}

	// const formInitialData = {
	// 	id: String(project.id),
	// 	name: project.name,
	// 	subdomain: project.subdomain?.name ?? '',
	// 	domain: project.subdomain?.domain_name ?? '',
	// 	is_server_side: project.server_supported,
	// 	workspace: project.workspace_id,
	// 	project_reasons: project.project_purposes?.map((p) => String(p.id)) ?? [],
	// 	project_type: String(project.project_type_id ?? ''),
	// 	project_platform: String(project.project_platform_id ?? ''),
	// 	ad_networks:
	// 		project.ad_network?.map((a) => {
	// 			return String(a.ad_network.id);
	// 		}) ?? []
	// };

	const getAllProjectPurposes = async () => {
		const relativePath = '/spark/api/v1/projects/project-purposes/';
		const absoluteUrl = new URL(relativePath, url.origin);
		return fetch(absoluteUrl)
			.then(async (res) => {
				if (!res.ok) {
					return null;
				} else {
					return res.json();
				}
			})
			.then((data) => {
				return data?.data ?? [];
			});
	};
	const getAllProjectTypes = async () => {
		const relativePath = '/spark/api/v1/projects/project-types/';
		const absoluteUrl = new URL(relativePath, url.origin);
		return fetch(absoluteUrl)
			.then(async (res) => {
				if (!res.ok) {
					return null;
				} else {
					return res.json();
				}
			})
			.then((data) => {
				return data?.data ?? [];
			});
	};

	const getWorkspace = async () => {
		const relativePath = `/spark/api/v1/workspaces/${project.workspace_id}/`;

		const absoluteUrl = new URL(relativePath, url.origin);
		return fetch(absoluteUrl)
			.then(async (res) => {
				if (!res.ok) {
					return null;
				} else {
					return res.json();
				}
			})
			.then((data) => {
				return data?.data ?? {};
			});
	};

	const getAdNetworks = async () => {
		const relativePath = `/spark/api/v1/networks/ad-networks/`;
		return fetch(relativePath, { method: 'GET' })
			.then(async (res) => {
				if (!res.ok) {
					return null;
				} else {
					return res.json();
				}
			})
			.then((data) => {
				return data?.data ?? [];
			});
	};

	// const form = await superValidate(formInitialData, zod(createProjectSchema));
	return {
		workspace: await getWorkspace(),
		adNetworks: await getAdNetworks(),
		// form,
		project,
		projectPurposes: await getAllProjectPurposes(),
		projectTypes: await getAllProjectTypes()
	};
};
