<script lang="ts">
	import { onMount } from 'svelte';
	import { globalBreadcrumbs, globalNavLinks } from '$lib/stores/side-nav.svelte';
	import type { PageData } from './$types';

	import { Button } from '$lib/shad-components/ui/button';
	import * as Tabs from '$lib/shad-components/ui/tabs/index.js';
	import * as Card from '$lib/shad-components/ui/card/index.js';
	import * as Alert from '$lib/shad-components/ui/alert/index.js';

	import { goto } from '$app/navigation';
	import { page } from '$app/state';

	import House from '@lucide/svelte/icons/house';
	import Network from '@lucide/svelte/icons/network';
	import FolderOpenDot from '@lucide/svelte/icons/folder-open-dot';
	import Rocket from '@lucide/svelte/icons/rocket';
	import Settings from '@lucide/svelte/icons/settings';
	import Container from '@lucide/svelte/icons/container';
	import Server from '@lucide/svelte/icons/server';
	import AlertTriangle from '@lucide/svelte/icons/alert-triangle';
	import CheckCircle from '@lucide/svelte/icons/check-circle';
	import CreditCard from '@lucide/svelte/icons/credit-card';

	import {
		EditProjectIcon,
		HouseIcon,
		NetworkIcon,
		ProjectIcon
	} from '$lib/shared-ui/layoutExports';

	// Import deploy components
	import GTMAccountSelection from './components/GTMAccountSelection.svelte';
	import ContainerConfiguration from './components/ContainerConfiguration.svelte';
	import StapeConfiguration from './components/StapeConfiguration.svelte';
	import StapeContainerManagement from './components/StapeContainerManagement.svelte';
	import StapeDomainManagement from './components/StapeDomainManagement.svelte';
	import PaymentConfiguration from './components/PaymentConfiguration.svelte';
	import PreDeployChecklist from './components/PreDeployChecklist.svelte';

	// Import deploy data service
	import { deployDataService, type DeploymentData } from '$lib/api/deploy-data';

	let {
		data
	}: {
		data: PageData;
	} = $props();

	// Deployment state management
	let deploymentData = $state<DeploymentData>({
		gtmAccount: null,
		containers: {
			web: null,
			server: null
		},
		stapeConfig: {
			isUseExistingAccount: undefined,
			isEuAccount: undefined,
			userIdentifier: '',
			workspaceIdentifier: '',
			email: '',
			containerName: ''
		},
		containerConfig: {
			useExistingContainer: undefined,
			selectedContainer: null,
			isComplete: false
		},
		domainConfig: {
			useExistingDomain: undefined,
			selectedDomain: null,
			isComplete: false
		},
		paymentConfig: {
			isVerified: false,
			plan: 'starter',
			billingCycle: 'monthly'
		}
	});

	let activeTab = $derived(page.url.searchParams.get('tab') ?? 'gtm-account');
	let canCreateContainers = $state(false);
	let validationErrors = $state<string[]>([]);

	// Derived state for next step
	let nextStep = $derived(
		deployDataService.getNextStep(deploymentData, data.project.server_supported)
	);

	// Load deployment data on mount
	const loadDeploymentData = async () => {
		try {
			const savedData = await deployDataService.loadDeploymentData(data.project.id);
			if (savedData) {
				deploymentData = savedData;
			}
		} catch (error) {
			console.error('Failed to load deployment data:', error);
		}
	};

	// Save deployment data
	const saveDeploymentData = async () => {
		try {
			// Ensure we're saving the complete deployment data structure
			const dataToSave = {
				...deploymentData,
				// Ensure all required fields are present
				gtmAccount: deploymentData.gtmAccount,
				containers: deploymentData.containers,
				stapeConfig: deploymentData.stapeConfig,
				containerConfig: deploymentData.containerConfig || {
					useExistingContainer: undefined,
					selectedContainer: null,
					isComplete: false
				},
				domainConfig: deploymentData.domainConfig || {
					useExistingDomain: undefined,
					selectedDomain: null,
					isComplete: false
				},
				paymentConfig: deploymentData.paymentConfig || {
					isVerified: false,
					plan: 'starter',
					billingCycle: 'monthly'
				}
			};
			await deployDataService.saveDeploymentData(data.project.id, dataToSave);
		} catch (error) {
			console.error('Failed to save deployment data:', error);
		}
	};

	// Handle tab navigation with validation
	const handleTabChange = (newTab: string) => {
		const navigation = deployDataService.canNavigateToStep(
			newTab,
			deploymentData,
			data.project.server_supported
		);

		if (!navigation.canNavigate) {
			validationErrors = [navigation.reason || 'Cannot navigate to this step'];
			return;
		}

		validationErrors = [];
		goto(`?tab=${newTab}`, { replaceState: true });
	};

	// Handle account selection
	const handleAccountSelected = (account: any) => {
		deploymentData.gtmAccount = account;
	};

	// Handle container configuration changes
	const handleContainersConfigured = (containers: any) => {
		deploymentData.containers = containers;
	};

	// Handle Stape configuration changes
	const handleStapeConfigured = (stapeConfig: any) => {
		deploymentData.stapeConfig = stapeConfig;
	};

	// Handle container configuration changes
	const handleContainerConfigured = (containerConfig: any) => {
		deploymentData.containerConfig = containerConfig;
	};

	// Handle domain configuration changes
	const handleDomainConfigured = (domainConfig: any) => {
		deploymentData.domainConfig = domainConfig;
	};

	// Handle payment configuration changes
	const handlePaymentConfigured = (paymentConfig: any) => {
		deploymentData.paymentConfig = paymentConfig;
	};

	// Handle deployment readiness
	const handleDeploymentReady = (isReady: boolean) => {
		// This can be used to enable/disable deploy button
		console.log('Deployment ready:', isReady);
	};

	// Handle save button click
	const handleSaveDeploymentData = async () => {
		try {
			await saveDeploymentData();
			// Show success message or toast
			console.log('Deployment data saved successfully');
		} catch (error) {
			console.error('Failed to save deployment data:', error);
			// Show error message or toast
		}
	};

	onMount(() => {
		globalBreadcrumbs.crumbs = [
			{
				title: HouseIcon,
				href: '/dashboard'
			},
			{
				title: NetworkIcon,
				href: `/workspaces/${data?.workspace?.id}`
			},
			{
				title: ProjectIcon,
				href: `/projects/${data?.project?.id}`
			},
			{
				title: EditProjectIcon,
				href: `/projects/${data?.project?.id}/edit`
			}
		];

		globalNavLinks.links = [
			{
				title: 'Dashboard',
				url: '/dashboard',
				icon: House
			},
			{
				title: 'Workspace',
				url: `/workspaces/${data?.workspace?.id}`,
				icon: Network
			},
			{
				title: 'Project',
				url: `/projects/${data?.project?.id}`,
				icon: FolderOpenDot
			},
			{
				title: 'Deploy Project',
				url: `/projects/${data?.project?.id}/deploy`,
				icon: Rocket
			}
		];

		// Load saved deployment data
		loadDeploymentData();
	});

	// Tab configuration
	const tabs = [
		{
			value: 'gtm-account',
			label: 'GTM Account',
			icon: Settings,
			description: 'Connect your Google Tag Manager account'
		},
		{
			value: 'containers',
			label: 'Containers',
			icon: Container,
			description: 'Configure web and server containers'
		},
		...(data.project.server_supported
			? [
					{
						value: 'stape',
						label: 'Stape Configuration',
						icon: Server,
						description: 'Configure server-side hosting with Stape'
					},
					{
						value: 'stape-containers',
						label: 'Stape Containers',
						icon: Container,
						description: 'Manage Stape containers'
					},
					{
						value: 'stape-domains',
						label: 'Stape Domains',
						icon: Network,
						description: 'Configure custom domains'
					}
				]
			: []),
		{
			value: 'payments',
			label: 'Payments',
			icon: CreditCard,
			description: 'Configure billing and payments'
		},
		{
			value: 'deploy',
			label: 'Deploy',
			icon: Rocket,
			description: 'Review and deploy your configuration'
		}
	];
</script>

<!-- Deployment Status Alert -->
{#if !data.deploymentCheck.eligible}
	<Alert.Root class="mb-6">
		<AlertTriangle class="h-4 w-4" />
		<Alert.Title>Deployment Warnings</Alert.Title>
		<Alert.Description>
			<ul class="mt-2 list-inside list-disc space-y-1">
				{#each data.deploymentCheck.reasons as reason (reason)}
					<li>{reason}</li>
				{/each}
			</ul>
			<p class="mt-2 text-sm">
				You can still proceed with deployment, but these issues should be addressed for optimal
				functionality.
			</p>
		</Alert.Description>
	</Alert.Root>
{/if}

<!-- Validation Errors -->
{#if validationErrors.length > 0}
	<Alert.Root variant="destructive" class="mb-6">
		<AlertTriangle class="h-4 w-4" />
		<Alert.Title>Navigation Error</Alert.Title>
		<Alert.Description>
			{#each validationErrors as error}
				<p>{error}</p>
			{/each}
		</Alert.Description>
	</Alert.Root>
{/if}

<div class="flex h-full w-full flex-col">
	<Tabs.Root value={activeTab} onValueChange={handleTabChange} class="flex h-full w-full flex-col">
		<div
			class="bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky top-14 z-10 border-b backdrop-blur"
		>
			<Tabs.List class="max-w-8xl mx-auto flex h-14 items-center justify-start gap-4 px-4">
				{#each tabs as tab (tab.value)}
					{@const IconComponent = tab.icon}
					<Tabs.Trigger value={tab.value} class="flex items-center gap-2">
						<IconComponent class="h-4 w-4" />
						{tab.label}
					</Tabs.Trigger>
				{/each}
			</Tabs.List>
		</div>

		<main class="flex-1 overflow-y-auto">
			<div class="max-w-8xl mx-auto p-4 lg:p-6">
				<Tabs.Content value="gtm-account" class="h-full">
					<Card.Root>
						<Card.Header>
							<Card.Title class="flex items-center gap-2">
								<Settings class="h-5 w-5" />
								Google Tag Manager Account
							</Card.Title>
							<Card.Description>
								Connect your Google Tag Manager account to manage containers and deploy your
								tracking setup.
							</Card.Description>
						</Card.Header>
						<Card.Content>
							<GTMAccountSelection
								bind:selectedAccount={deploymentData.gtmAccount}
								bind:canCreateContainers
								onAccountSelected={handleAccountSelected}
							/>
						</Card.Content>
					</Card.Root>
				</Tabs.Content>

				<Tabs.Content value="containers" class="h-full">
					<Card.Root>
						<Card.Header>
							<Card.Title class="flex items-center gap-2">
								<Container class="h-5 w-5" />
								Container Configuration
							</Card.Title>
							<Card.Description>
								Configure your web and server containers for tracking deployment.
							</Card.Description>
						</Card.Header>
						<Card.Content>
							<ContainerConfiguration
								selectedAccount={deploymentData.gtmAccount}
								project={data.project}
								onContainersConfigured={handleContainersConfigured}
							/>
						</Card.Content>
					</Card.Root>
				</Tabs.Content>

				{#if data.project.server_supported}
					<Tabs.Content value="stape" class="h-full">
						<Card.Root>
							<Card.Header>
								<Card.Title class="flex items-center gap-2">
									<Server class="h-5 w-5" />
									Stape Configuration
								</Card.Title>
								<Card.Description>
									Configure server-side hosting with Stape for enhanced tracking capabilities.
								</Card.Description>
							</Card.Header>
							<Card.Content>
								<StapeConfiguration
									project={data.project}
									onStapeConfigured={handleStapeConfigured}
								/>
							</Card.Content>
						</Card.Root>
					</Tabs.Content>

					<Tabs.Content value="stape-containers" class="h-full">
						<Card.Root>
							<Card.Header>
								<Card.Title class="flex items-center gap-2">
									<Container class="h-5 w-5" />
									Stape Container Management
								</Card.Title>
								<Card.Description>
									Configure your Stape container for server-side tracking.
								</Card.Description>
							</Card.Header>
							<Card.Content>
								<StapeContainerManagement
									project={data.project}
									stapeConfig={deploymentData.stapeConfig}
									onContainerConfigured={handleContainerConfigured}
								/>
							</Card.Content>
						</Card.Root>
					</Tabs.Content>

					<Tabs.Content value="stape-domains" class="h-full">
						<Card.Root>
							<Card.Header>
								<Card.Title class="flex items-center gap-2">
									<Network class="h-5 w-5" />
									Stape Domain Management
								</Card.Title>
								<Card.Description>
									Configure custom domains for your server-side tracking endpoint.
								</Card.Description>
							</Card.Header>
							<Card.Content>
								<StapeDomainManagement
									project={data.project}
									stapeConfig={deploymentData.stapeConfig}
									containerConfig={deploymentData.containerConfig}
									onDomainConfigured={handleDomainConfigured}
								/>
							</Card.Content>
						</Card.Root>
					</Tabs.Content>
				{/if}

				<Tabs.Content value="payments" class="h-full">
					<Card.Root>
						<Card.Header>
							<Card.Title class="flex items-center gap-2">
								<CreditCard class="h-5 w-5" />
								Payment Configuration
							</Card.Title>
							<Card.Description>
								Configure billing and verify payment information for your deployment.
							</Card.Description>
						</Card.Header>
						<Card.Content>
							<PaymentConfiguration
								project={data.project}
								{deploymentData}
								onPaymentConfigured={handlePaymentConfigured}
							/>
						</Card.Content>
					</Card.Root>
				</Tabs.Content>

				<Tabs.Content value="deploy" class="h-full">
					<Card.Root>
						<Card.Header>
							<Card.Title class="flex items-center gap-2">
								<Rocket class="h-5 w-5" />
								Pre-Deploy Checklist
							</Card.Title>
							<Card.Description>
								Review all configurations and deploy your tracking setup.
							</Card.Description>
						</Card.Header>
						<Card.Content>
							<PreDeployChecklist
								project={data.project}
								{deploymentData}
								onDeploymentReady={handleDeploymentReady}
							/>
						</Card.Content>
					</Card.Root>
				</Tabs.Content>

				<!-- Deployment Actions -->
				{#if activeTab !== 'deploy'}
					<div class="mt-8 flex items-center justify-between border-t pt-6">
						<div class="flex items-center gap-2">
							{#if nextStep === 'deploy'}
								<CheckCircle class="h-5 w-5 text-green-600" />
								<span class="text-sm text-green-600">Ready for deployment</span>
							{:else}
								<AlertTriangle class="h-5 w-5 text-amber-600" />
								<span class="text-sm text-amber-600">Configuration incomplete</span>
							{/if}
						</div>
						<div class="flex gap-2">
							<Button variant="outline" href="/projects/{data.project.id}">Cancel</Button>
							<Button variant="outline" onclick={handleSaveDeploymentData}>Save Progress</Button>
							{#if activeTab === 'gtm-account'}
								<Button
									disabled={!deploymentData.gtmAccount}
									onclick={() => handleTabChange('containers')}
								>
									Next: Configure Containers
								</Button>
							{:else if activeTab === 'containers'}
								{#if data.project.server_supported}
									<Button disabled={!canCreateContainers} onclick={() => handleTabChange('stape')}>
										Next: Configure Stape
									</Button>
								{:else}
									<Button
										disabled={!canCreateContainers}
										onclick={() => handleTabChange('payments')}
									>
										Next: Configure Payments
									</Button>
								{/if}
							{:else if activeTab === 'stape'}
								<Button onclick={() => handleTabChange('stape-containers')}>
									Next: Configure Containers
								</Button>
							{:else if activeTab === 'stape-containers'}
								<Button onclick={() => handleTabChange('stape-domains')}>
									Next: Configure Domains
								</Button>
							{:else if activeTab === 'stape-domains'}
								<Button onclick={() => handleTabChange('payments')}>
									Next: Configure Payments
								</Button>
							{:else if activeTab === 'payments'}
								<Button onclick={() => handleTabChange('deploy')}>Next: Review & Deploy</Button>
							{:else}
								<Button onclick={() => handleTabChange('deploy')}>Review & Deploy</Button>
							{/if}
						</div>
					</div>
				{/if}
			</div>
		</main>
	</Tabs.Root>
</div>
