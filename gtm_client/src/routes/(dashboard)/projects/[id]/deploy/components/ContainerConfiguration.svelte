<script lang="ts">
	// Container Configuration Component
	// Migrated from old-client ContainerCollection.svelte with real API integration

	import { onMount } from 'svelte';
	import { Button } from '$lib/shad-components/ui/button';
	import * as Card from '$lib/shad-components/ui/card/index.js';
	import * as Select from '$lib/shad-components/ui/select/index.js';
	import * as Alert from '$lib/shad-components/ui/alert';
	import { Input } from '$lib/shad-components/ui/input';
	import { Label } from '$lib/shad-components/ui/label';
	import { Separator } from '$lib/shad-components/ui/separator';
	import Plus from '@lucide/svelte/icons/plus';
	import Container from '@lucide/svelte/icons/container';
	import Server from '@lucide/svelte/icons/server';
	import RefreshCw from '@lucide/svelte/icons/refresh-cw';
	import AlertTriangle from '@lucide/svelte/icons/alert-triangle';

	import Info from '@lucide/svelte/icons/info';

	import { gtmAPIService, type GTMAccount, type GTMContainer } from '$lib/api/gtm';

	interface ContainerConfig {
		selectedContainer: GTMContainer | null;
		newContainerName: string;
		isCreatingNew: boolean;
		isLoading: boolean;
		error: string | null;
	}

	let {
		selectedAccount = null,
		project,
		onContainersConfigured = () => {}
	}: {
		selectedAccount?: GTMAccount | null;
		project: any;
		onContainersConfigured?: (containers: {
			web: GTMContainer | null;
			server: GTMContainer | null;
		}) => void;
	} = $props();

	// State management
	let isLoadingContainers = $state(false);
	let containers = $state<GTMContainer[]>([]);
	let error = $state<string | null>(null);

	// Container configurations
	let webConfig = $state<ContainerConfig>({
		selectedContainer: null,
		newContainerName: '',
		isCreatingNew: true,
		isLoading: false,
		error: null
	});

	let serverConfig = $state<ContainerConfig>({
		selectedContainer: null,
		newContainerName: '',
		isCreatingNew: true,
		isLoading: false,
		error: null
	});

	// Derived states
	let webContainers = $derived(
		containers.filter(
			(container) =>
				container.usageContext.includes('web') || container.usageContext.includes('WEB')
		)
	);

	let serverContainers = $derived(
		containers.filter(
			(container) =>
				container.usageContext.includes('server') || container.usageContext.includes('SERVER')
		)
	);

	// Load containers when account is selected
	$effect(() => {
		if (selectedAccount) {
			loadContainers();
		}
	});

	// Load containers from GTM
	const loadContainers = async () => {
		if (!selectedAccount) return;

		isLoadingContainers = true;
		error = null;

		try {
			// Clear cache first
			await gtmAPIService.clearAccountsCache();

			const response = await gtmAPIService.listContainers(selectedAccount.accountId);
			containers = response.container || [];
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to load containers';
			console.error('Error loading containers:', err);
		} finally {
			isLoadingContainers = false;
		}
	};

	// Create new container
	const createContainer = async (config: ContainerConfig, usageContext: string[]) => {
		if (!selectedAccount || !config.newContainerName.trim()) return;

		config.isLoading = true;
		config.error = null;

		try {
			const response = await gtmAPIService.createContainer(
				selectedAccount.accountId,
				config.newContainerName.trim(),
				usageContext
			);

			// Update the config with the new container
			config.selectedContainer = response;

			config.isCreatingNew = false;

			// Refresh containers list
			await loadContainers();

			// Notify parent component
			notifyContainersChange();
		} catch (err) {
			config.error = err instanceof Error ? err.message : 'Failed to create container';
			console.error('Error creating container:', err);
		} finally {
			config.isLoading = false;
		}
	};

	// Handle container selection
	const handleContainerSelect = (config: ContainerConfig, containerId: string) => {
		const container = containers.find((c) => c.containerId === containerId);
		if (container) {
			config.selectedContainer = container;
			config.isCreatingNew = false;
			config.newContainerName = '';
			config.error = null;
			notifyContainersChange();
		}
	};

	// Handle new container name input
	const handleNewContainerName = (config: ContainerConfig, name: string) => {
		config.newContainerName = name;
		config.isCreatingNew = true;
		config.selectedContainer = null;
		config.error = null;
		notifyContainersChange();
	};

	// Notify parent component of changes
	const notifyContainersChange = () => {
		onContainersConfigured({
			web: webConfig.selectedContainer,
			server: serverConfig.selectedContainer
		});
	};

	// Generate default container names
	const generateDefaultNames = (projectName: string) => {
		const safeName = projectName.toLowerCase().replace(/\s+/g, '-');
		webConfig.newContainerName = `${safeName}-web`;
		if (project?.server_supported) {
			serverConfig.newContainerName = `${safeName}-server`;
		}
	};

	onMount(() => {
		// Set default container names if needed
		if (project?.name) {
			generateDefaultNames(project.name);
		}
	});
</script>

<div class="space-y-6">
	<div>
		<h3 class="text-lg font-semibold">Container Configuration</h3>
		<p class="text-muted-foreground text-sm">
			Configure containers for your project deployment. You can create new containers or select
			existing ones.
		</p>
	</div>

	{#if error}
		<Alert.Root variant="destructive">
			<AlertTriangle class="h-4 w-4" />
			<Alert.Title>Error Loading Containers</Alert.Title>
			<Alert.Description>{error}</Alert.Description>
		</Alert.Root>
	{/if}

	{#if !selectedAccount}
		<Alert.Root>
			<Info class="h-4 w-4" />
			<Alert.Title>GTM Account Required</Alert.Title>
			<Alert.Description>
				Please select a GTM account first before configuring containers.
			</Alert.Description>
		</Alert.Root>
	{:else}
		<!-- Web Container Configuration -->
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Container class="h-5 w-5" />
					Web Container
				</Card.Title>
				<Card.Description>
					Configure the container for web tracking. Creating a new container is recommended.
				</Card.Description>
			</Card.Header>
			<Card.Content class="space-y-4">
				{#if isLoadingContainers}
					<div class="flex items-center gap-2">
						<RefreshCw class="h-4 w-4 animate-spin" />
						<span class="text-sm">Loading containers...</span>
					</div>
				{:else}
					<div class="space-y-4">
						<!-- Create New Container Option -->
						<div class="space-y-2">
							<Label for="web-container-name">Create New Web Container</Label>
							<div class="flex gap-2">
								<Input
									id="web-container-name"
									placeholder="Enter container name"
									value={webConfig.newContainerName}
									oninput={(e) =>
										handleNewContainerName(webConfig, (e.target as HTMLInputElement)?.value || '')}
									disabled={webConfig.isLoading}
								/>
								<Button
									onclick={() => createContainer(webConfig, ['web'])}
									disabled={!webConfig.newContainerName.trim() || webConfig.isLoading}
								>
									{#if webConfig.isLoading}
										<RefreshCw class="mr-2 h-4 w-4 animate-spin" />
									{:else}
										<Plus class="mr-2 h-4 w-4" />
									{/if}
									Create
								</Button>
							</div>
							{#if webConfig.isCreatingNew && webConfig.newContainerName}
								<p class="text-sm text-green-600">
									✓ Creating new container: {webConfig.newContainerName}
								</p>
							{/if}
						</div>

						<!-- Or Select Existing -->
						{#if webContainers.length > 0}
							<div class="space-y-2">
								<Separator />
								<Label>Or Select Existing Web Container</Label>
								<Select.Root
									type="single"
									onValueChange={(value) => value && handleContainerSelect(webConfig, value)}
								>
									<Select.Trigger>
										{webConfig.selectedContainer?.name || 'Select existing container...'}
									</Select.Trigger>
									<Select.Content>
										<Select.Group>
											{#each webContainers as container (container.containerId)}
												<Select.Item value={container.containerId}>
													{container.name}
													{container.publicId ? `(${container.publicId})` : ''}
												</Select.Item>
											{/each}
										</Select.Group>
									</Select.Content>
								</Select.Root>
								{#if !webConfig.isCreatingNew && webConfig.selectedContainer}
									<Alert.Root variant="destructive">
										<AlertTriangle class="h-4 w-4" />
										<Alert.Description class="text-sm">
											Using an existing container may overwrite existing configurations. Proceed
											with caution.
										</Alert.Description>
									</Alert.Root>
								{/if}
							</div>
						{/if}

						{#if webConfig.error}
							<Alert.Root variant="destructive">
								<AlertTriangle class="h-4 w-4" />
								<Alert.Description>{webConfig.error}</Alert.Description>
							</Alert.Root>
						{/if}
					</div>
				{/if}
			</Card.Content>
		</Card.Root>

		<!-- Server Container Configuration (if supported) -->
		{#if project?.server_supported}
			<Card.Root>
				<Card.Header>
					<Card.Title class="flex items-center gap-2">
						<Server class="h-5 w-5" />
						Server Container
					</Card.Title>
					<Card.Description>
						Configure the container for server-side tracking. Creating a new container is
						recommended.
					</Card.Description>
				</Card.Header>
				<Card.Content class="space-y-4">
					{#if isLoadingContainers}
						<div class="flex items-center gap-2">
							<RefreshCw class="h-4 w-4 animate-spin" />
							<span class="text-sm">Loading containers...</span>
						</div>
					{:else}
						<div class="space-y-4">
							<!-- Create New Container Option -->
							<div class="space-y-2">
								<Label for="server-container-name">Create New Server Container</Label>
								<div class="flex gap-2">
									<Input
										id="server-container-name"
										placeholder="Enter container name"
										value={serverConfig.newContainerName}
										oninput={(e) =>
											handleNewContainerName(
												serverConfig,
												(e.target as HTMLInputElement)?.value || ''
											)}
										disabled={serverConfig.isLoading}
									/>
									<Button
										onclick={() => createContainer(serverConfig, ['server'])}
										disabled={!serverConfig.newContainerName.trim() || serverConfig.isLoading}
									>
										{#if serverConfig.isLoading}
											<RefreshCw class="mr-2 h-4 w-4 animate-spin" />
										{:else}
											<Plus class="mr-2 h-4 w-4" />
										{/if}
										Create
									</Button>
								</div>
								{#if serverConfig.isCreatingNew && serverConfig.newContainerName}
									<p class="text-sm text-green-600">
										✓ Creating new container: {serverConfig.newContainerName}
									</p>
								{/if}
							</div>

							<!-- Or Select Existing -->
							{#if serverContainers.length > 0}
								<div class="space-y-2">
									<Separator />
									<Label>Or Select Existing Server Container</Label>
									<Select.Root
										type="single"
										onValueChange={(value) => value && handleContainerSelect(serverConfig, value)}
									>
										<Select.Trigger>
											{serverConfig.selectedContainer?.name || 'Select existing container...'}
										</Select.Trigger>
										<Select.Content>
											<Select.Group>
												{#each serverContainers as container (container.containerId)}
													<Select.Item value={container.containerId}>
														{container.name}
														{container.publicId ? `(${container.publicId})` : ''}
													</Select.Item>
												{/each}
											</Select.Group>
										</Select.Content>
									</Select.Root>
									{#if !serverConfig.isCreatingNew && serverConfig.selectedContainer}
										<Alert.Root variant="destructive">
											<AlertTriangle class="h-4 w-4" />
											<Alert.Description class="text-sm">
												Using an existing container may overwrite existing configurations. Proceed
												with caution.
											</Alert.Description>
										</Alert.Root>
									{/if}
								</div>
							{/if}

							{#if serverConfig.error}
								<Alert.Root variant="destructive">
									<AlertTriangle class="h-4 w-4" />
									<Alert.Description>{serverConfig.error}</Alert.Description>
								</Alert.Root>
							{/if}
						</div>
					{/if}
				</Card.Content>
			</Card.Root>
		{/if}
	{/if}
</div>
