<script lang="ts">
	import { But<PERSON> } from '$lib/shad-components/ui/button';
	import * as Card from '$lib/shad-components/ui/card';
	import { Badge } from '$lib/shad-components/ui/badge';
	import * as Alert from '$lib/shad-components/ui/alert';
	import CreditCard from '@lucide/svelte/icons/credit-card';
	import CheckCircle from '@lucide/svelte/icons/check-circle';
	import ExternalLink from '@lucide/svelte/icons/external-link';
	import DollarSign from '@lucide/svelte/icons/dollar-sign';
	import Clock from '@lucide/svelte/icons/clock';

	let {
		project,
		deploymentData,
		onPaymentConfigured
	}: {
		project: any;
		deploymentData: any;
		onPaymentConfigured: (config: any) => void;
	} = $props();

	let paymentConfig = $state({
		isVerified: false,
		plan: 'starter',
		billingCycle: 'monthly',
		estimatedCost: 0
	});

	// Mock payment verification
	const verifyPayment = async () => {
		// Simulate payment verification process
		await new Promise((resolve) => setTimeout(resolve, 2000));

		paymentConfig.isVerified = true;
		paymentConfig.estimatedCost = calculateEstimatedCost();

		onPaymentConfigured(paymentConfig);
	};

	const calculateEstimatedCost = () => {
		// Mock cost calculation based on configuration
		let baseCost = 0;

		// Stape hosting costs
		if (deploymentData.stapeConfig?.isUseExistingAccount === false) {
			baseCost += 20; // New account setup
		}

		// Container costs
		if (deploymentData.containerConfig?.useExistingContainer === false) {
			baseCost += 15; // New container
		}

		// Domain costs
		if (deploymentData.domainConfig?.useExistingDomain === false) {
			baseCost += 10; // Custom domain
		}

		return baseCost;
	};

	// Available plans
	const plans = [
		{
			id: 'starter',
			name: 'Starter',
			price: 29,
			features: ['Up to 100K events/month', '1 Custom domain', 'Basic analytics', 'Email support']
		},
		{
			id: 'professional',
			name: 'Professional',
			price: 79,
			features: [
				'Up to 1M events/month',
				'5 Custom domains',
				'Advanced analytics',
				'Priority support',
				'Custom integrations'
			]
		},
		{
			id: 'enterprise',
			name: 'Enterprise',
			price: 199,
			features: [
				'Unlimited events',
				'Unlimited domains',
				'Enterprise analytics',
				'24/7 phone support',
				'Custom SLA',
				'Dedicated account manager'
			]
		}
	];

	$effect(() => {
		paymentConfig.estimatedCost = calculateEstimatedCost();
	});
</script>

<div class="space-y-6">
	<div>
		<h3 class="text-lg font-semibold">Payment Configuration</h3>
		<p class="text-muted-foreground text-sm">
			Configure billing and verify payment information for your deployment.
		</p>
	</div>

	<!-- Cost Estimation -->
	<Card.Root>
		<Card.Header>
			<Card.Title class="flex items-center gap-2">
				<DollarSign class="h-5 w-5" />
				Estimated Costs
			</Card.Title>
			<Card.Description>Based on your configuration choices</Card.Description>
		</Card.Header>
		<Card.Content>
			<div class="space-y-4">
				<div class="grid gap-4">
					{#if deploymentData.stapeConfig?.isUseExistingAccount === false}
						<div class="flex items-center justify-between">
							<span class="text-sm">Stape Account Setup</span>
							<span class="text-sm font-medium">$20/month</span>
						</div>
					{/if}
					{#if deploymentData.containerConfig?.useExistingContainer === false}
						<div class="flex items-center justify-between">
							<span class="text-sm">New Container</span>
							<span class="text-sm font-medium">$15/month</span>
						</div>
					{/if}
					{#if deploymentData.domainConfig?.useExistingDomain === false}
						<div class="flex items-center justify-between">
							<span class="text-sm">Custom Domain</span>
							<span class="text-sm font-medium">$10/month</span>
						</div>
					{/if}
					<div class="border-t pt-2">
						<div class="flex items-center justify-between font-medium">
							<span>Estimated Total</span>
							<span>${paymentConfig.estimatedCost}/month</span>
						</div>
					</div>
				</div>
			</div>
		</Card.Content>
	</Card.Root>

	<!-- Plan Selection -->
	<Card.Root>
		<Card.Header>
			<Card.Title>Select Plan</Card.Title>
			<Card.Description>Choose the plan that best fits your tracking needs</Card.Description>
		</Card.Header>
		<Card.Content>
			<div class="grid gap-4 md:grid-cols-3">
				{#each plans as plan (plan.id)}
					<div
						class="cursor-pointer rounded-lg border p-4 transition-colors {paymentConfig.plan ===
						plan.id
							? 'border-primary bg-primary/5'
							: 'hover:bg-muted/50'}"
						onclick={() => (paymentConfig.plan = plan.id)}
						role="button"
						tabindex="0"
						onkeypress={(e) => e.key === 'Enter' && (paymentConfig.plan = plan.id)}
					>
						<div class="space-y-3">
							<div>
								<h4 class="font-semibold">{plan.name}</h4>
								<div class="flex items-baseline gap-1">
									<span class="text-2xl font-bold">${plan.price}</span>
									<span class="text-muted-foreground text-sm">/month</span>
								</div>
							</div>
							<ul class="space-y-1">
								{#each plan.features as feature}
									<li class="text-muted-foreground flex items-center gap-2 text-sm">
										<CheckCircle class="h-3 w-3 text-green-600" />
										{feature}
									</li>
								{/each}
							</ul>
							{#if paymentConfig.plan === plan.id}
								<Badge class="w-full justify-center">Selected</Badge>
							{/if}
						</div>
					</div>
				{/each}
			</div>
		</Card.Content>
	</Card.Root>

	<!-- Billing Cycle -->
	<Card.Root>
		<Card.Header>
			<Card.Title>Billing Cycle</Card.Title>
		</Card.Header>
		<Card.Content>
			<div class="flex gap-4">
				<Button
					variant={paymentConfig.billingCycle === 'monthly' ? 'default' : 'outline'}
					onclick={() => (paymentConfig.billingCycle = 'monthly')}
				>
					Monthly
				</Button>
				<Button
					variant={paymentConfig.billingCycle === 'yearly' ? 'default' : 'outline'}
					onclick={() => (paymentConfig.billingCycle = 'yearly')}
				>
					Yearly (Save 20%)
				</Button>
			</div>
		</Card.Content>
	</Card.Root>

	<!-- Payment Verification -->
	<Card.Root>
		<Card.Header>
			<Card.Title class="flex items-center gap-2">
				<CreditCard class="h-5 w-5" />
				Payment Verification
			</Card.Title>
			<Card.Description>Verify your payment method and billing information</Card.Description>
		</Card.Header>
		<Card.Content>
			{#if !paymentConfig.isVerified}
				<div class="space-y-4">
					<Alert.Root>
						<Clock class="h-4 w-4" />
						<Alert.Title>Payment Required</Alert.Title>
						<Alert.Description>
							Please verify your payment method to proceed with deployment. This is a mock
							verification for demonstration purposes.
						</Alert.Description>
					</Alert.Root>

					<div class="space-y-4">
						<div class="grid gap-4 md:grid-cols-2">
							<div class="space-y-2">
								<label for="card-number" class="text-sm font-medium">Card Number</label>
								<div id="card-number" class="bg-muted text-muted-foreground rounded border p-3">
									**** **** **** 1234 (Mock)
								</div>
							</div>
							<div class="space-y-2">
								<label for="expiry" class="text-sm font-medium">Expiry</label>
								<div id="expiry" class="bg-muted text-muted-foreground rounded border p-3">
									12/25 (Mock)
								</div>
							</div>
						</div>

						<Button onclick={verifyPayment} class="w-full">
							<CreditCard class="mr-2 h-4 w-4" />
							Verify Payment (Mock)
						</Button>
					</div>
				</div>
			{:else}
				<Alert.Root>
					<CheckCircle class="h-4 w-4" />
					<Alert.Title>Payment Verified</Alert.Title>
					<Alert.Description>
						Your payment method has been verified and billing is configured.
					</Alert.Description>
				</Alert.Root>

				<div class="mt-4 space-y-2">
					<div class="flex justify-between">
						<span class="text-sm font-medium">Plan:</span>
						<span class="text-sm">{plans.find((p) => p.id === paymentConfig.plan)?.name}</span>
					</div>
					<div class="flex justify-between">
						<span class="text-sm font-medium">Billing:</span>
						<span class="text-sm capitalize">{paymentConfig.billingCycle}</span>
					</div>
					<div class="flex justify-between">
						<span class="text-sm font-medium">Next Charge:</span>
						<span class="text-sm">
							${plans.find((p) => p.id === paymentConfig.plan)?.price}
							{paymentConfig.billingCycle === 'yearly' ? ' × 12 × 0.8' : ''}
							on {new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}
						</span>
					</div>
				</div>
			{/if}
		</Card.Content>
	</Card.Root>

	<!-- Third-party Notice -->
	<Alert.Root>
		<ExternalLink class="h-4 w-4" />
		<Alert.Title>Third-party Billing</Alert.Title>
		<Alert.Description>
			Stape services are billed directly by Stape.io. This configuration helps estimate costs but
			actual billing will be handled by the respective service providers.
		</Alert.Description>
	</Alert.Root>
</div>
