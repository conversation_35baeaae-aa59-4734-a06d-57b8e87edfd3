<script lang="ts">
	// GTM Account Selection Component
	// Migrated from old-client AccountCollection.svelte with real API integration

	import { onMount } from 'svelte';
	import { Button } from '$lib/shad-components/ui/button';
	import * as Select from '$lib/shad-components/ui/select/index.js';
	import * as Alert from '$lib/shad-components/ui/alert';
	import * as Tabs from '$lib/shad-components/ui/tabs';
	import { Badge } from '$lib/shad-components/ui/badge';
	import { Separator } from '$lib/shad-components/ui/separator';

	import ExternalLink from '@lucide/svelte/icons/external-link';
	import RefreshCw from '@lucide/svelte/icons/refresh-cw';
	import AlertTriangle from '@lucide/svelte/icons/alert-triangle';
	import CheckCircle from '@lucide/svelte/icons/check-circle';
	import Info from '@lucide/svelte/icons/info';
	import Settings from '@lucide/svelte/icons/settings';

	// Import GTM API service
	import { gtmAPIService, type GTMAccount, type AccountPermissions } from '$lib/api/gtm';
	import { page } from '$app/state';

	let {
		onAccountSelected = () => {},
		selectedAccount = $bindable(null),
		canCreateContainers = $bindable(false)
	}: {
		onAccountSelected?: (account: GTMAccount | null) => void;
		selectedAccount?: GTMAccount | null;
		canCreateContainers?: boolean;
	} = $props();

	let isLoading = $state(false);
	let accounts = $state<GTMAccount[]>([]);
	let error = $state<string | null>(null);
	let permissions = $state<AccountPermissions | null>(null);

	$effect(() => {
		console.log('permissions:', permissions);
	});

	let isCheckingPermissions = $state(false);
	let activeTab = $state('select-account');

	// Load GTM accounts from the API
	const loadAccounts = async () => {
		isLoading = true;
		error = null;

		try {
			const response = await gtmAPIService.listAccounts();
			accounts = response.account || [];
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to load GTM accounts';
			console.error('Error loading accounts:', err);
		} finally {
			isLoading = false;
		}
	};

	// Check permissions for the selected account
	const checkPermissions = async (accountId: string) => {
		if (!accountId) return;

		isCheckingPermissions = true;
		try {
			// Get current user's email from page data
			const userEmail = page.data.user?.emai;

			permissions = await gtmAPIService.getAccountPermissions(accountId, userEmail);
			// Update canCreateContainers based on permissions
			canCreateContainers =
				permissions?.accountAccess?.permission === 'admin' ||
				permissions?.accountAccess?.permission === 'publish';
		} catch (err) {
			console.error('Error checking permissions:', err);
			permissions = null;
			canCreateContainers = false;
		} finally {
			isCheckingPermissions = false;
		}
	};

	// Refresh accounts (clear cache and reload)
	const refreshAccounts = async () => {
		if (isLoading) return;

		try {
			// Clear cache first
			await gtmAPIService.clearAccountsCache();
		} catch (err) {
			console.error('Error clearing cache:', err);
		}

		await loadAccounts();
	};

	const handleAccountChange = (value: string | string[]) => {
		const accountId = Array.isArray(value) ? value[0] : value;
		const account = accounts.find((acc) => acc.accountId === accountId) || null;
		selectedAccount = account;
		onAccountSelected(account);

		if (account) {
			checkPermissions(account.accountId);
		}
	};

	const openGTMAccountCreation = () => {
		const link = document.createElement('a');
		link.href = 'https://tagmanager.google.com/#/admin/accounts/create';
		link.target = '_blank';
		link.rel = 'noopener noreferrer';
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	};

	// Get permission status for display
	const getPermissionStatus = (permission?: string) => {
		switch (permission) {
			case 'admin':
			case 'publish':
				return { icon: CheckCircle, color: 'text-green-600', status: 'Good' };
			case 'read':
				return { icon: AlertTriangle, color: 'text-red-600', status: 'Limited' };
			default:
				return { icon: AlertTriangle, color: 'text-amber-600', status: 'Unknown' };
		}
	};

	onMount(() => {
		loadAccounts();
	});
</script>

<Tabs.Root bind:value={activeTab} class="w-full">
	<Tabs.List class="grid w-full grid-cols-2">
		<Tabs.Trigger value="select-account">Select GTM Account</Tabs.Trigger>
		<Tabs.Trigger value="create-account">Create New Account</Tabs.Trigger>
	</Tabs.List>

	<Tabs.Content value="select-account" class="space-y-6">
		<div class="space-y-4">
			<div>
				<h3 class="text-lg font-semibold">Select GTM Account</h3>
				<p class="text-muted-foreground text-sm">
					Choose your preferred Google Tag Manager account effortlessly.
				</p>
			</div>

			{#if error}
				<Alert.Root variant="destructive">
					<AlertTriangle class="h-4 w-4" />
					<Alert.Title>Error Loading Accounts</Alert.Title>
					<Alert.Description>{error}</Alert.Description>
				</Alert.Root>
			{/if}

			<div class="space-y-4">
				<div class="space-y-2">
					<label for="gtm-account" class="text-sm font-medium"> Select The GTM Account </label>

					{#if isLoading}
						<div class="flex items-center gap-2 py-2">
							<RefreshCw class="h-4 w-4 animate-spin" />
							<span class="text-muted-foreground text-sm">Loading accounts...</span>
						</div>
					{:else if accounts.length > 0}
						<Select.Root type="single" onValueChange={handleAccountChange}>
							<Select.Trigger>
								{selectedAccount?.name || 'Choose an account...'}
							</Select.Trigger>
							<Select.Content>
								<Select.Group>
									{#each accounts as account (account.accountId)}
										<Select.Item value={account.accountId}>
											{account.name}
										</Select.Item>
									{/each}
								</Select.Group>
							</Select.Content>
						</Select.Root>
					{:else}
						<p class="text-muted-foreground py-2 text-sm">No accounts found.</p>
					{/if}
				</div>

				<Button variant="outline" onclick={refreshAccounts} disabled={isLoading}>
					<RefreshCw class="mr-2 h-4 w-4 {isLoading ? 'animate-spin' : ''}" />
					{isLoading ? 'Loading...' : 'Refresh'}
				</Button>
			</div>

			{#if selectedAccount}
				<Separator />

				<div class="space-y-4">
					<div class="flex items-center gap-2">
						<Settings class="h-4 w-4" />
						<h4 class="font-medium">Account Permissions</h4>
					</div>

					{#if isCheckingPermissions}
						<div class="flex items-center gap-2">
							<RefreshCw class="h-4 w-4 animate-spin" />
							<span class="text-muted-foreground text-sm">Checking permissions...</span>
						</div>
					{:else if permissions}
						<div class="space-y-3">
							{#if permissions.accountAccess}
								{@const status = getPermissionStatus(permissions.accountAccess.permission)}
								{@const IconComponent = status.icon}
								<div class="flex items-center gap-2">
									<IconComponent class="h-4 w-4 {status.color}" />
									<span class="text-sm">
										Account Access:
										<Badge variant={status.status === 'Good' ? 'default' : 'destructive'}>
											{permissions.accountAccess.permission.toUpperCase()}
										</Badge>
									</span>
								</div>
							{/if}

							{#if permissions.accountAccess?.permission === 'read'}
								<Alert.Root variant="destructive">
									<AlertTriangle class="h-4 w-4" />
									<Alert.Title>Limited Permissions</Alert.Title>
									<Alert.Description class="text-sm">
										You only have <strong>READ</strong> permission for this account. You may not be able
										to create containers or workspaces. Contact your GTM administrator to request elevated
										permissions.
									</Alert.Description>
								</Alert.Root>
							{:else if permissions.accountAccess?.permission === 'publish'}
								<Alert.Root>
									<CheckCircle class="h-4 w-4" />
									<Alert.Title>Good Permissions</Alert.Title>
									<Alert.Description class="text-sm">
										You have <strong>PUBLISH</strong> permission. You can create and manage containers
										and workspaces.
									</Alert.Description>
								</Alert.Root>
							{:else if permissions.accountAccess?.permission === 'admin'}
								<Alert.Root>
									<CheckCircle class="h-4 w-4" />
									<Alert.Title>Full Permissions</Alert.Title>
									<Alert.Description class="text-sm">
										You have <strong>ADMIN</strong> permission. You have full access to manage this account.
									</Alert.Description>
								</Alert.Root>
							{:else}
								<Alert.Root>
									<Info class="h-4 w-4" />
									<Alert.Description class="text-sm">
										**Without Admin permission, some account and container actions may be limited.
									</Alert.Description>
								</Alert.Root>
							{/if}
						</div>
					{/if}
				</div>
			{/if}
		</div>
	</Tabs.Content>

	<Tabs.Content value="create-account" class="space-y-6">
		<div class="space-y-4">
			<div>
				<h3 class="text-lg font-semibold">Create New GTM Account</h3>
				<p class="text-muted-foreground text-sm">
					Don't have a Google Tag Manager account yet? Create one now.
				</p>
			</div>

			<div class="bg-muted/30 space-y-4 rounded-lg border p-6 text-center">
				<Settings class="text-muted-foreground mx-auto h-12 w-12" />
				<div class="space-y-2">
					<p class="font-medium">Create Your GTM Account</p>
					<p class="text-muted-foreground text-sm">
						You'll be redirected to Google Tag Manager to create a new account.
					</p>
				</div>
				<Button onclick={openGTMAccountCreation}>
					<ExternalLink class="mr-2 h-4 w-4" />
					Create New Account (google.com)
				</Button>
			</div>

			<Alert.Root>
				<Info class="h-4 w-4" />
				<Alert.Description class="text-sm">
					After creating your account, return here and refresh the account list to select your new
					account.
				</Alert.Description>
			</Alert.Root>
		</div>
	</Tabs.Content>
</Tabs.Root>
