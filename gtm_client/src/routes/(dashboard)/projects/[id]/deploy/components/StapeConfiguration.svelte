<script lang="ts">
	// This component will handle Stape configuration
	// Migrated from old-client GTMStapeCollection.svelte

	import { onMount } from 'svelte';
	import { Button } from '$lib/shad-components/ui/button';
	import * as Card from '$lib/shad-components/ui/card/index.js';
	import * as RadioGroup from '$lib/shad-components/ui/radio-group/index.js';
	import * as Alert from '$lib/shad-components/ui/alert';
	import { Input } from '$lib/shad-components/ui/input';
	import { Label } from '$lib/shad-components/ui/label';
	import { Badge } from '$lib/shad-components/ui/badge';
	import { Separator } from '$lib/shad-components/ui/separator';

	import Server from '@lucide/svelte/icons/server';
	import Globe from '@lucide/svelte/icons/globe';
	import MapPin from '@lucide/svelte/icons/map-pin';
	import Clock from '@lucide/svelte/icons/clock';
	import User from '@lucide/svelte/icons/user';
	import Mail from '@lucide/svelte/icons/mail';
	import AlertTriangle from '@lucide/svelte/icons/alert-triangle';
	import CheckCircle from '@lucide/svelte/icons/check-circle';
	import Info from '@lucide/svelte/icons/info';
	import ExternalLink from '@lucide/svelte/icons/external-link';

	let {
		project,
		onStapeConfigured = () => {}
	}: {
		project: any;
		onStapeConfigured?: (config: any) => void;
	} = $props();

	let stapeConfig = $state({
		isUseExistingAccount: undefined as boolean | undefined,
		isEuAccount: undefined as boolean | undefined,
		userIdentifier: '',
		workspaceIdentifier: '',
		email: '',
		containerName: ''
	});

	// Derived states for validation
	let isAccountTypeSelected = $derived(stapeConfig.isUseExistingAccount !== undefined);
	let isRegionSelected = $derived(stapeConfig.isEuAccount !== undefined);

	// For existing accounts, check if required fields are filled
	let existingAccountFieldsComplete = $derived(
		!stapeConfig.isUseExistingAccount ||
			(stapeConfig.userIdentifier.trim() !== '' && stapeConfig.workspaceIdentifier.trim() !== '')
	);

	// For new accounts, check if email is provided
	let newAccountFieldsComplete = $derived(
		stapeConfig.isUseExistingAccount ||
			(stapeConfig.email.trim() !== '' && stapeConfig.containerName.trim() !== '')
	);

	let isConfigurationComplete = $derived(
		isAccountTypeSelected &&
			isRegionSelected &&
			existingAccountFieldsComplete &&
			newAccountFieldsComplete
	);

	const handleAccountTypeChange = (value: string) => {
		stapeConfig.isUseExistingAccount = value === 'existing';
		if (!stapeConfig.isUseExistingAccount) {
			// Reset existing account fields when switching to new account
			stapeConfig.userIdentifier = '';
			stapeConfig.workspaceIdentifier = '';
			stapeConfig.email = '';
		} else {
			// Reset new account fields when switching to existing account
			stapeConfig.email = '';
			stapeConfig.containerName = '';
		}
		// Update parent when user makes changes (only call when needed)
		if (isConfigurationComplete) {
			onStapeConfigured(stapeConfig);
		}
	};

	const handleRegionChange = (value: string) => {
		stapeConfig.isEuAccount = value === 'eu';
		// Update parent when user makes changes (only call when needed)
		if (isConfigurationComplete) {
			onStapeConfigured(stapeConfig);
		}
	};

	const handleInputChange = () => {
		// Update parent when user makes changes (only call when needed)
		if (isConfigurationComplete) {
			onStapeConfigured(stapeConfig);
		}
	};

	onMount(() => {
		// Set default container name based on project
		if (project?.name && !stapeConfig.containerName) {
			const safeName = project.name.toLowerCase().replace(/\s+/g, '-');
			stapeConfig.containerName = `${safeName}-server`;
		}
	});
</script>

{#if !project.server_supported}
	<div class="py-8 text-center">
		<Server class="text-muted-foreground mx-auto mb-4 h-12 w-12" />
		<p class="text-muted-foreground">Server-side tracking is not enabled for this project</p>
	</div>
{:else}
	<div class="space-y-6">
		<div>
			<h3 class="text-lg font-semibold">Stape Configuration</h3>
			<p class="text-muted-foreground text-sm">
				Configure server-side hosting with Stape for enhanced tracking capabilities.
			</p>
		</div>

		<!-- Account Selection -->
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<User class="h-5 w-5" />
					Account Type
				</Card.Title>
				<Card.Description>
					Choose whether to use an existing Stape account or create a new one.
				</Card.Description>
			</Card.Header>
			<Card.Content>
				<RadioGroup.Root onValueChange={handleAccountTypeChange}>
					<div class="space-y-4">
						<div class="flex items-center space-x-2">
							<RadioGroup.Item value="existing" id="existing" />
							<Label for="existing" class="flex-1">
								<div class="space-y-1">
									<div class="font-medium">Use Existing Account</div>
									<div class="text-muted-foreground text-sm">
										I have an existing Stape account and want to use it
									</div>
								</div>
							</Label>
						</div>
						<div class="flex items-center space-x-2">
							<RadioGroup.Item value="new" id="new" />
							<Label for="new" class="flex-1">
								<div class="space-y-1">
									<div class="font-medium">Create New Account</div>
									<div class="text-muted-foreground text-sm">Create a new Stape account for me</div>
								</div>
							</Label>
						</div>
					</div>
				</RadioGroup.Root>

				{#if stapeConfig.isUseExistingAccount === true}
					<Alert.Root class="mt-4">
						<Info class="h-4 w-4" />
						<Alert.Title>Existing Account</Alert.Title>
						<Alert.Description>
							You'll need to provide your Stape account credentials to link it with this project.
						</Alert.Description>
					</Alert.Root>

					<!-- Existing Account Credentials -->
					<div class="mt-4 space-y-4">
						<div class="space-y-2">
							<Label for="userIdentifier">User Identifier</Label>
							<Input
								id="userIdentifier"
								value={stapeConfig.userIdentifier}
								oninput={(e) => {
									stapeConfig.userIdentifier = (e.target as HTMLInputElement)?.value || '';
									handleInputChange();
								}}
								placeholder="Enter your Stape user identifier"
							/>
						</div>
						<div class="space-y-2">
							<Label for="workspaceIdentifier">Workspace Identifier</Label>
							<Input
								id="workspaceIdentifier"
								value={stapeConfig.workspaceIdentifier}
								oninput={(e) => {
									stapeConfig.workspaceIdentifier = (e.target as HTMLInputElement)?.value || '';
									handleInputChange();
								}}
								placeholder="Enter your Stape workspace identifier"
							/>
						</div>
					</div>
				{:else if stapeConfig.isUseExistingAccount === false}
					<Alert.Root class="mt-4">
						<CheckCircle class="h-4 w-4" />
						<Alert.Title>New Account</Alert.Title>
						<Alert.Description>
							We'll create a new Stape account for you and configure it automatically.
						</Alert.Description>
					</Alert.Root>
				{/if}
			</Card.Content>
		</Card.Root>

		<!-- Region Selection -->
		{#if isAccountTypeSelected}
			<Card.Root>
				<Card.Header>
					<Card.Title class="flex items-center gap-2">
						<Globe class="h-5 w-5" />
						Server Region
					</Card.Title>
					<Card.Description>Select the server region for your Stape hosting.</Card.Description>
				</Card.Header>
				<Card.Content>
					<RadioGroup.Root onValueChange={handleRegionChange}>
						<div class="space-y-4">
							<div class="flex items-center space-x-2">
								<RadioGroup.Item value="global" id="global" />
								<Label for="global" class="flex-1">
									<div class="flex items-center gap-2">
										<MapPin class="h-4 w-4" />
										<div class="space-y-1">
											<div class="font-medium">Global (US)</div>
											<div class="text-muted-foreground text-sm">
												Hosted globally for optimal performance
											</div>
										</div>
									</div>
								</Label>
							</div>
							<div class="flex items-center space-x-2">
								<RadioGroup.Item value="eu" id="eu" />
								<Label for="eu" class="flex-1">
									<div class="flex items-center gap-2">
										<MapPin class="h-4 w-4" />
										<div class="space-y-1">
											<div class="font-medium">Europe (EU)</div>
											<div class="text-muted-foreground text-sm">
												GDPR compliant hosting in European data centers
											</div>
										</div>
									</div>
								</Label>
							</div>
						</div>
					</RadioGroup.Root>
				</Card.Content>
			</Card.Root>
		{/if}

		<!-- New Account Configuration -->
		{#if stapeConfig.isUseExistingAccount === false && isRegionSelected}
			<Card.Root>
				<Card.Header>
					<Card.Title class="flex items-center gap-2">
						<Mail class="h-5 w-5" />
						Account Details
					</Card.Title>
					<Card.Description>Provide details for your new Stape account.</Card.Description>
				</Card.Header>
				<Card.Content class="space-y-4">
					<div class="space-y-2">
						<Label for="email">Email Address</Label>
						<Input
							id="email"
							type="email"
							value={stapeConfig.email}
							oninput={(e) => {
								stapeConfig.email = (e.target as HTMLInputElement)?.value || '';
								handleInputChange();
							}}
							placeholder="Enter your email address"
						/>
						<p class="text-muted-foreground text-xs">
							This will be used to create your new Stape account
						</p>
					</div>
					<div class="space-y-2">
						<Label for="containerName">Container Name</Label>
						<Input
							id="containerName"
							value={stapeConfig.containerName}
							oninput={(e) => {
								stapeConfig.containerName = (e.target as HTMLInputElement)?.value || '';
								handleInputChange();
							}}
							placeholder="Enter a name for your container"
						/>
						<p class="text-muted-foreground text-xs">
							A unique name for your server-side container
						</p>
					</div>
				</Card.Content>
			</Card.Root>
		{/if}

		<!-- Configuration Summary -->
		{#if isConfigurationComplete}
			<Card.Root>
				<Card.Header>
					<Card.Title class="flex items-center gap-2">
						<CheckCircle class="h-5 w-5 text-green-600" />
						Configuration Summary
					</Card.Title>
					<Card.Description>Review your Stape configuration settings.</Card.Description>
				</Card.Header>
				<Card.Content>
					<div class="space-y-3">
						<div class="flex justify-between">
							<span class="text-sm font-medium">Account Type:</span>
							<Badge variant={stapeConfig.isUseExistingAccount ? 'secondary' : 'default'}>
								{stapeConfig.isUseExistingAccount ? 'Existing Account' : 'New Account'}
							</Badge>
						</div>
						<div class="flex justify-between">
							<span class="text-sm font-medium">Region:</span>
							<Badge variant="outline">
								{stapeConfig.isEuAccount ? 'Europe (EU)' : 'Global (US)'}
							</Badge>
						</div>
						{#if stapeConfig.isUseExistingAccount}
							<div class="flex justify-between">
								<span class="text-sm font-medium">User ID:</span>
								<span class="font-mono text-sm">{stapeConfig.userIdentifier}</span>
							</div>
							<div class="flex justify-between">
								<span class="text-sm font-medium">Workspace ID:</span>
								<span class="font-mono text-sm">{stapeConfig.workspaceIdentifier}</span>
							</div>
						{:else}
							<div class="flex justify-between">
								<span class="text-sm font-medium">Email:</span>
								<span class="text-sm">{stapeConfig.email}</span>
							</div>
							<div class="flex justify-between">
								<span class="text-sm font-medium">Container:</span>
								<span class="font-mono text-sm">{stapeConfig.containerName}</span>
							</div>
						{/if}
					</div>

					<Separator class="my-4" />

					<Alert.Root>
						<ExternalLink class="h-4 w-4" />
						<Alert.Title>About Stape</Alert.Title>
						<Alert.Description>
							Stape is a third-party service that provides server-side hosting for Google Tag
							Manager. Hosting and billing are handled directly by Stape.io.
						</Alert.Description>
					</Alert.Root>
				</Card.Content>
			</Card.Root>
		{/if}

		<!-- Validation Errors -->
		{#if !isConfigurationComplete && isAccountTypeSelected}
			<Alert.Root variant="destructive">
				<AlertTriangle class="h-4 w-4" />
				<Alert.Title>Configuration Incomplete</Alert.Title>
				<Alert.Description>
					Please complete all required fields before proceeding to the next step.
				</Alert.Description>
			</Alert.Root>
		{/if}
	</div>
{/if}
