<script lang="ts">
	import { onMount } from 'svelte';
	import { Button } from '$lib/shad-components/ui/button';
	import * as Card from '$lib/shad-components/ui/card';
	import * as RadioGroup from '$lib/shad-components/ui/radio-group';
	import { Input } from '$lib/shad-components/ui/input';
	import { Label } from '$lib/shad-components/ui/label';
	import { Badge } from '$lib/shad-components/ui/badge';
	import * as Alert from '$lib/shad-components/ui/alert';
	import Loader2 from '@lucide/svelte/icons/loader-2';
	import Server from '@lucide/svelte/icons/server';
	import Plus from '@lucide/svelte/icons/plus';
	import ExternalLink from '@lucide/svelte/icons/external-link';
	import {
		stapeAPIService,
		StapeRegion,
		StapeTokenType,
		type StapeContainer,
		type ContainerLocation
	} from '$lib/api/stape';

	let {
		project,
		stapeConfig,
		onContainerConfigured
	}: {
		project: any;
		stapeConfig: any;
		onContainerConfigured: (config: any) => void;
	} = $props();

	let containerConfig = $state({
		useExistingContainer: undefined as boolean | undefined,
		existingContainerIdentifier: '',
		newContainerName: '',
		containerLocation: '',
		selectedContainer: null as any
	});

	let isLoading = $state(false);
	let availableContainers = $state<any[]>([]);
	let availableLocations = $state<any[]>([]);
	let errorMessage = $state('');

	// Derived states for validation
	let isContainerTypeSelected = $derived(containerConfig.useExistingContainer !== undefined);
	let isExistingContainerSelected = $derived(
		!containerConfig.useExistingContainer || containerConfig.selectedContainer !== null
	);
	let isNewContainerConfigured = $derived(
		containerConfig.useExistingContainer ||
			(containerConfig.newContainerName.trim() !== '' && containerConfig.containerLocation !== '')
	);

	let isConfigurationComplete = $derived(
		isContainerTypeSelected && isExistingContainerSelected && isNewContainerConfigured
	);

	const handleContainerTypeChange = (value: string) => {
		containerConfig.useExistingContainer = value === 'existing';
		if (containerConfig.useExistingContainer) {
			// Reset new container fields
			containerConfig.newContainerName = '';
			containerConfig.containerLocation = '';
		} else {
			// Reset existing container fields
			containerConfig.selectedContainer = null;
			containerConfig.existingContainerIdentifier = '';
		}
		// Update parent when user makes changes (only call when needed)
		if (isConfigurationComplete) {
			onContainerConfigured({
				...containerConfig,
				isComplete: isConfigurationComplete
			});
		}
	};

	const handleContainerSelection = (container: any) => {
		containerConfig.selectedContainer = container;
		containerConfig.existingContainerIdentifier = container.identifier;
		// Update parent when user makes changes (only call when needed)
		if (isConfigurationComplete) {
			onContainerConfigured({
				...containerConfig,
				isComplete: isConfigurationComplete
			});
		}
	};

	const handleInputChange = () => {
		// Update parent when user makes changes (only call when needed)
		if (isConfigurationComplete) {
			onContainerConfigured({
				...containerConfig,
				isComplete: isConfigurationComplete
			});
		}
	};

	const fetchContainers = async () => {
		if (!stapeConfig.userIdentifier && !stapeConfig.workspaceIdentifier) {
			return;
		}

		isLoading = true;
		errorMessage = '';

		try {
			const isUserNotWorkspace = stapeConfig.userIdentifier && stapeConfig.userIdentifier !== '';
			const region = stapeConfig.isEuAccount ? StapeRegion.EU : StapeRegion.GLOBAL;
			const tokenType = isUserNotWorkspace ? StapeTokenType.PARTNER : StapeTokenType.NORMAL;

			const options: RequestInit = isUserNotWorkspace
				? { headers: { 'X-Stape-User-Identifier': stapeConfig.userIdentifier || '' } }
				: { headers: { 'X-WORKSPACE': stapeConfig.workspaceIdentifier || '' } };

			const response: any = await stapeAPIService.listContainers(
				isUserNotWorkspace ? stapeConfig.userIdentifier : undefined,
				region,
				tokenType,
				options
			);

			if (!response?.data?.body?.items) {
				throw new Error(response?.message ?? 'Error fetching containers');
			}

			availableContainers = response.data.body.items.map((container: any) => ({
				identifier: container.identifier,
				name: container.name,
				location: container.location || 'Unknown',
				status: container.status || { label: 'Unknown' }
			}));
		} catch (error) {
			errorMessage = 'Failed to fetch containers. Please try again.';
			console.error('Error fetching containers:', error);
		} finally {
			isLoading = false;
		}
	};

	const fetchLocations = async () => {
		isLoading = true;
		try {
			const isUserNotWorkspace = stapeConfig.userIdentifier && stapeConfig.userIdentifier !== '';
			const region = stapeConfig.isEuAccount ? StapeRegion.EU : StapeRegion.GLOBAL;
			const tokenType = isUserNotWorkspace ? StapeTokenType.PARTNER : StapeTokenType.NORMAL;

			const response: any = await stapeAPIService.getContainerZones(region, tokenType);

			if (!response?.data?.body) {
				throw new Error(response?.message ?? 'Error fetching container locations');
			}

			availableLocations = response.data.body.map((location: any) => ({
				id: location.type,
				name: location.label,
				region: location.label
			}));
		} catch (error) {
			errorMessage = 'Failed to fetch locations. Please try again.';
			console.error('Error fetching locations:', error);
		} finally {
			isLoading = false;
		}
	};

	const createContainer = async () => {
		if (!containerConfig.newContainerName || !containerConfig.containerLocation) {
			errorMessage = 'Please fill in all required fields';
			return;
		}

		isLoading = true;
		errorMessage = '';

		try {
			const isUserNotWorkspace = stapeConfig.userIdentifier && stapeConfig.userIdentifier !== '';
			const region = stapeConfig.isEuAccount ? StapeRegion.EU : StapeRegion.GLOBAL;
			const tokenType = isUserNotWorkspace ? StapeTokenType.PARTNER : StapeTokenType.NORMAL;

			const containerData = {
				name: containerConfig.newContainerName,
				code: '',
				codeSettings: {
					domain: project?.subdomain?.domain?.name || '',
					webGtmId: '',
					platform: 'other',
					userIdentifierType: 'cookie',
					userIdentifierValue: '_ga',
					htmlAttribute: '',
					dataLayer: 'dataLayer',
					useCdn: false,
					useCookieKeeper: false,
					useOriginalGtmCode: false,
					useAdblockBypass: false
				},
				zone: { type: containerConfig.containerLocation }
			};

			const response: any = await stapeAPIService.createContainer(containerData, region, tokenType);

			if (!response?.data?.body) {
				throw new Error(response?.message ?? 'Error creating container');
			}

			const newContainer = {
				identifier: response.data.body.identifier,
				name: response.data.body.name,
				status: response.data.body.status || { label: 'Active' },
				location: containerConfig.containerLocation
			};

			containerConfig.selectedContainer = newContainer;
			containerConfig.existingContainerIdentifier = newContainer.identifier;
		} catch (error) {
			errorMessage = 'Failed to create container. Please try again.';
			console.error('Error creating container:', error);
		} finally {
			isLoading = false;
		}
	};

	onMount(() => {
		// Set default container name based on project
		if (project?.name && !containerConfig.newContainerName) {
			const safeName = project.name.toLowerCase().replace(/\s+/g, '-');
			containerConfig.newContainerName = `${safeName}-stape`;
		}

		// Fetch available data
		fetchContainers();
		fetchLocations();
	});
</script>

{#if isLoading}
	<div class="flex items-center justify-center p-8">
		<Loader2 class="mr-2 h-6 w-6 animate-spin" />
		<span>Loading container information...</span>
	</div>
{:else}
	<div class="space-y-6">
		<div>
			<h3 class="text-lg font-semibold">Stape Container Management</h3>
			<p class="text-muted-foreground text-sm">
				Configure your Stape container for server-side tracking.
			</p>
		</div>

		{#if errorMessage}
			<Alert.Root variant="destructive">
				<Alert.Title>Error</Alert.Title>
				<Alert.Description>{errorMessage}</Alert.Description>
			</Alert.Root>
		{/if}

		<!-- Container Type Selection -->
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Server class="h-5 w-5" />
					Container Type
				</Card.Title>
				<Card.Description>
					Choose whether to use an existing container or create a new one.
				</Card.Description>
			</Card.Header>
			<Card.Content>
				<RadioGroup.Root
					value={containerConfig.useExistingContainer === true
						? 'existing'
						: containerConfig.useExistingContainer === false
							? 'new'
							: ''}
					onValueChange={handleContainerTypeChange}
				>
					<div class="space-y-4">
						<div class="flex items-center space-x-2">
							<RadioGroup.Item value="existing" id="existing" />
							<Label for="existing" class="flex-1">
								<div class="space-y-1">
									<div class="font-medium">Use Existing Container</div>
									<div class="text-muted-foreground text-sm">
										Select from your existing Stape containers
									</div>
								</div>
							</Label>
						</div>
						<div class="flex items-center space-x-2">
							<RadioGroup.Item value="new" id="new" />
							<Label for="new" class="flex-1">
								<div class="space-y-1">
									<div class="font-medium">Create New Container</div>
									<div class="text-muted-foreground text-sm">
										Create a new container for this project
									</div>
								</div>
							</Label>
						</div>
					</div>
				</RadioGroup.Root>
			</Card.Content>
		</Card.Root>

		<!-- Existing Container Selection -->
		{#if containerConfig.useExistingContainer === true}
			<Card.Root>
				<Card.Header>
					<Card.Title>Select Container</Card.Title>
					<Card.Description>Choose from your existing containers.</Card.Description>
				</Card.Header>
				<Card.Content>
					{#if availableContainers.length === 0}
						<div class="py-8 text-center">
							<Server class="text-muted-foreground mx-auto h-12 w-12" />
							<h3 class="mt-2 text-sm font-semibold">No containers found</h3>
							<p class="text-muted-foreground mt-1 text-sm">
								No existing containers found for this account.
							</p>
							<Button
								variant="outline"
								class="mt-4"
								onclick={() => (containerConfig.useExistingContainer = false)}
							>
								<Plus class="mr-2 h-4 w-4" />
								Create New Container
							</Button>
						</div>
					{:else}
						<div class="grid gap-4">
							{#each availableContainers as container (container.identifier)}
								<div
									class="cursor-pointer rounded-lg border p-4 transition-colors {containerConfig
										.selectedContainer?.identifier === container.identifier
										? 'border-primary bg-primary/5'
										: 'hover:bg-muted/50'}"
									onclick={() => handleContainerSelection(container)}
									role="button"
									tabindex="0"
									onkeypress={(e) => e.key === 'Enter' && handleContainerSelection(container)}
								>
									<div class="flex items-center justify-between">
										<div>
											<h4 class="font-medium">{container.name}</h4>
											<p class="text-muted-foreground text-sm">
												Location: {container.location}
											</p>
										</div>
										<Badge variant="outline">{container.status.label}</Badge>
									</div>
								</div>
							{/each}
						</div>
					{/if}
				</Card.Content>
			</Card.Root>
		{/if}

		<!-- New Container Configuration -->
		{#if containerConfig.useExistingContainer === false}
			<Card.Root>
				<Card.Header>
					<Card.Title class="flex items-center gap-2">
						<Plus class="h-5 w-5" />
						New Container Details
					</Card.Title>
					<Card.Description>Configure your new Stape container.</Card.Description>
				</Card.Header>
				<Card.Content class="space-y-4">
					<div class="space-y-2">
						<Label for="containerName">Container Name</Label>
						<Input
							id="containerName"
							value={containerConfig.newContainerName}
							oninput={(e) => {
								containerConfig.newContainerName = (e.target as HTMLInputElement)?.value || '';
								handleInputChange();
							}}
							placeholder="Enter container name"
						/>
					</div>
					<div class="space-y-2">
						<Label for="location">Container Location</Label>
						<RadioGroup.Root
							value={containerConfig.containerLocation}
							onValueChange={(value) => {
								containerConfig.containerLocation = value;
								handleInputChange();
							}}
						>
							<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
								{#each availableLocations as location (location.id)}
									<div class="flex items-center space-x-2">
										<RadioGroup.Item value={location.id} id={location.id} />
										<Label for={location.id} class="flex-1">
											<div class="space-y-1">
												<div class="font-medium">{location.name}</div>
												<div class="text-muted-foreground text-xs">
													Region: {location.region}
												</div>
											</div>
										</Label>
									</div>
								{/each}
							</div>
						</RadioGroup.Root>
					</div>
					{#if containerConfig.newContainerName && containerConfig.containerLocation}
						<Button onclick={createContainer} disabled={isLoading}>
							{#if isLoading}
								<Loader2 class="mr-2 h-4 w-4 animate-spin" />
							{:else}
								<Plus class="mr-2 h-4 w-4" />
							{/if}
							Create Container
						</Button>
					{/if}
				</Card.Content>
			</Card.Root>
		{/if}

		<!-- Configuration Summary -->
		{#if isConfigurationComplete}
			<Card.Root>
				<Card.Header>
					<Card.Title>Configuration Summary</Card.Title>
				</Card.Header>
				<Card.Content>
					<div class="space-y-2">
						<div class="flex justify-between">
							<span class="text-sm font-medium">Container Type:</span>
							<Badge variant="outline">
								{containerConfig.useExistingContainer ? 'Existing' : 'New'}
							</Badge>
						</div>
						{#if containerConfig.selectedContainer}
							<div class="flex justify-between">
								<span class="text-sm font-medium">Container:</span>
								<span class="text-sm">{containerConfig.selectedContainer.name}</span>
							</div>
							<div class="flex justify-between">
								<span class="text-sm font-medium">Container ID:</span>
								<span class="font-mono text-sm">{containerConfig.selectedContainer.identifier}</span
								>
							</div>
						{/if}
					</div>
				</Card.Content>
			</Card.Root>
		{/if}

		<Alert.Root>
			<ExternalLink class="h-4 w-4" />
			<Alert.Title>About Stape Containers</Alert.Title>
			<Alert.Description>
				Stape containers host your server-side Google Tag Manager setup. Each container provides
				isolated hosting for your tracking configuration.
			</Alert.Description>
		</Alert.Root>
	</div>
{/if}
