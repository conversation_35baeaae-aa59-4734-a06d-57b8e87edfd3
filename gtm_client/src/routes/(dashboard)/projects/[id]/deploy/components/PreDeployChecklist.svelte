<script lang="ts">
	import { Button } from '$lib/shad-components/ui/button';
	import * as Card from '$lib/shad-components/ui/card';
	import { Badge } from '$lib/shad-components/ui/badge';
	import * as Alert from '$lib/shad-components/ui/alert';
	import CheckCircle from '@lucide/svelte/icons/check-circle';
	import XCircle from '@lucide/svelte/icons/x-circle';
	import AlertTriangle from '@lucide/svelte/icons/alert-triangle';
	import Globe from '@lucide/svelte/icons/globe';
	import Smartphone from '@lucide/svelte/icons/smartphone';
	import Container from '@lucide/svelte/icons/container';
	import Server from '@lucide/svelte/icons/server';
	import CreditCard from '@lucide/svelte/icons/credit-card';
	import Rocket from '@lucide/svelte/icons/rocket';
	import RefreshCw from '@lucide/svelte/icons/refresh-cw';

	let {
		project,
		deploymentData,
		onDeploymentReady
	}: {
		project: any;
		deploymentData: any;
		onDeploymentReady: (isReady: boolean) => void;
	} = $props();

	let checklistState = $state({
		isChecking: false,
		lastChecked: null as Date | null,
		checks: {
			domainAndPlatforms: { status: 'pending', message: '' },
			adNetworks: { status: 'pending', message: '' },
			gtmContainers: { status: 'pending', message: '' },
			serverContainers: { status: 'pending', message: '' },
			stapeContainers: { status: 'pending', message: '' },
			stapeDomains: { status: 'pending', message: '' },
			payments: { status: 'pending', message: '' }
		}
	});

	// Check if all required checks are passing
	let isDeploymentReady = $derived(() => {
		const checks = Object.values(checklistState.checks);
		const requiredChecks = project.server_supported ? checks : checks.slice(0, -3); // Exclude server-specific checks if not supported
		return requiredChecks.every((check) => check.status === 'success');
	});

	// Call parent when checklist is run and readiness changes
	const notifyParentIfReadinessChanged = () => {
		const currentReadiness = isDeploymentReady();
		onDeploymentReady(currentReadiness);
	};

	const runChecklist = async () => {
		checklistState.isChecking = true;

		// Reset all checks
		Object.keys(checklistState.checks).forEach((key) => {
			checklistState.checks[key] = { status: 'checking', message: 'Checking...' };
		});

		// 1. Check domain and platforms
		await new Promise((resolve) => setTimeout(resolve, 500));
		if (project?.subdomain?.domain?.name) {
			checklistState.checks.domainAndPlatforms = {
				status: 'success',
				message: `Domain verified: ${project.subdomain.domain.name}`
			};
		} else {
			checklistState.checks.domainAndPlatforms = {
				status: 'error',
				message: 'No domain configured for this project'
			};
		}

		// 2. Check AD-Networks
		await new Promise((resolve) => setTimeout(resolve, 500));
		// Mock check - in real implementation, this would verify ad network configurations
		checklistState.checks.adNetworks = {
			status: 'success',
			message: 'Ad network configurations verified'
		};

		// 3. Check GTM Containers
		await new Promise((resolve) => setTimeout(resolve, 500));
		if (deploymentData.gtmAccount && deploymentData.containers?.web) {
			checklistState.checks.gtmContainers = {
				status: 'success',
				message: `Web container configured: ${deploymentData.containers.web.name}`
			};
		} else {
			checklistState.checks.gtmContainers = {
				status: 'error',
				message: 'GTM web container not configured'
			};
		}

		// 4. Check Server Containers (if server supported)
		await new Promise((resolve) => setTimeout(resolve, 500));
		if (project.server_supported) {
			if (deploymentData.containers?.server) {
				checklistState.checks.serverContainers = {
					status: 'success',
					message: `Server container configured: ${deploymentData.containers.server.name}`
				};
			} else {
				checklistState.checks.serverContainers = {
					status: 'error',
					message: 'GTM server container not configured'
				};
			}
		} else {
			checklistState.checks.serverContainers = {
				status: 'skipped',
				message: 'Server-side tracking not enabled'
			};
		}

		// 5. Check Stape Containers (if server supported)
		await new Promise((resolve) => setTimeout(resolve, 500));
		if (project.server_supported) {
			if (deploymentData.containerConfig?.isComplete) {
				checklistState.checks.stapeContainers = {
					status: 'success',
					message: 'Stape container configured'
				};
			} else {
				checklistState.checks.stapeContainers = {
					status: 'error',
					message: 'Stape container not configured'
				};
			}
		} else {
			checklistState.checks.stapeContainers = {
				status: 'skipped',
				message: 'Server-side tracking not enabled'
			};
		}

		// 6. Check Stape Domains (if server supported)
		await new Promise((resolve) => setTimeout(resolve, 500));
		if (project.server_supported) {
			if (deploymentData.domainConfig?.isComplete) {
				checklistState.checks.stapeDomains = {
					status: 'success',
					message: 'Stape domain configured'
				};
			} else {
				checklistState.checks.stapeDomains = {
					status: 'error',
					message: 'Stape domain not configured'
				};
			}
		} else {
			checklistState.checks.stapeDomains = {
				status: 'skipped',
				message: 'Server-side tracking not enabled'
			};
		}

		// 7. Verify Payments
		await new Promise((resolve) => setTimeout(resolve, 500));
		if (deploymentData.paymentConfig?.isVerified) {
			checklistState.checks.payments = {
				status: 'success',
				message: 'Payment method verified'
			};
		} else {
			checklistState.checks.payments = {
				status: 'warning',
				message: 'Payment verification recommended'
			};
		}

		checklistState.isChecking = false;
		checklistState.lastChecked = new Date();

		// Notify parent of readiness change
		notifyParentIfReadinessChanged();
	};

	const getStatusIcon = (status: string) => {
		switch (status) {
			case 'success':
				return CheckCircle;
			case 'error':
				return XCircle;
			case 'warning':
				return AlertTriangle;
			case 'checking':
				return RefreshCw;
			default:
				return AlertTriangle;
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'success':
				return 'text-green-600';
			case 'error':
				return 'text-red-600';
			case 'warning':
				return 'text-amber-600';
			case 'checking':
				return 'text-blue-600';
			default:
				return 'text-muted-foreground';
		}
	};

	const checklistItems = [
		{
			key: 'domainAndPlatforms',
			title: 'Domain and Platforms',
			description: 'Verify project domain and platform configurations',
			icon: Globe,
			required: true
		},
		{
			key: 'adNetworks',
			title: 'AD-Networks',
			description: 'Check advertising network configurations',
			icon: Smartphone,
			required: true
		},
		{
			key: 'gtmContainers',
			title: 'GTM Containers',
			description: 'Verify Google Tag Manager container setup',
			icon: Container,
			required: true
		},
		{
			key: 'serverContainers',
			title: 'Server Containers',
			description: 'Check server-side container configuration',
			icon: Server,
			required: project.server_supported
		},
		{
			key: 'stapeContainers',
			title: 'Stape Containers',
			description: 'Verify Stape hosting container setup',
			icon: Server,
			required: project.server_supported
		},
		{
			key: 'stapeDomains',
			title: 'Stape Domains',
			description: 'Check custom domain configuration',
			icon: Globe,
			required: project.server_supported
		},
		{
			key: 'payments',
			title: 'Payment Verification',
			description: 'Verify billing and payment information',
			icon: CreditCard,
			required: false
		}
	];
</script>

<div class="space-y-6">
	<div>
		<h3 class="text-lg font-semibold">Pre-Deploy Checklist</h3>
		<p class="text-muted-foreground text-sm">
			Verify all configurations before deploying your tracking setup.
		</p>
	</div>

	<!-- Checklist Status -->
	<Card.Root>
		<Card.Header>
			<div class="flex items-center justify-between">
				<Card.Title class="flex items-center gap-2">
					{#if isDeploymentReady()}
						<CheckCircle class="h-5 w-5 text-green-600" />
						Ready for Deployment
					{:else}
						<AlertTriangle class="h-5 w-5 text-amber-600" />
						Configuration Incomplete
					{/if}
				</Card.Title>
				<Button
					variant="outline"
					size="sm"
					onclick={runChecklist}
					disabled={checklistState.isChecking}
				>
					{#if checklistState.isChecking}
						<RefreshCw class="mr-2 h-4 w-4 animate-spin" />
						Checking...
					{:else}
						<RefreshCw class="mr-2 h-4 w-4" />
						Run Checks
					{/if}
				</Button>
			</div>
			{#if checklistState.lastChecked}
				<Card.Description>
					Last checked: {checklistState.lastChecked.toLocaleString()}
				</Card.Description>
			{/if}
		</Card.Header>
		<Card.Content>
			<div class="space-y-4">
				{#each checklistItems as item (item.key)}
					{@const check = checklistState.checks[item.key]}
					{@const StatusIcon = getStatusIcon(check.status)}
					<div class="flex items-start gap-3 rounded-lg border p-3">
						<div class="flex-shrink-0">
							<StatusIcon
								class="h-5 w-5 {getStatusColor(check.status)} {check.status === 'checking'
									? 'animate-spin'
									: ''}"
							/>
						</div>
						<div class="min-w-0 flex-1">
							<div class="flex items-center gap-2">
								<h4 class="font-medium">{item.title}</h4>
								{#if item.required}
									<Badge variant="outline" class="text-xs">Required</Badge>
								{:else}
									<Badge variant="secondary" class="text-xs">Optional</Badge>
								{/if}
								{#if check.status === 'skipped'}
									<Badge variant="secondary" class="text-xs">Skipped</Badge>
								{/if}
							</div>
							<p class="text-muted-foreground text-sm">{item.description}</p>
							{#if check.message}
								<p class="mt-1 text-sm {getStatusColor(check.status)}">{check.message}</p>
							{/if}
						</div>
					</div>
				{/each}
			</div>
		</Card.Content>
	</Card.Root>

	<!-- Deployment Actions -->
	<Card.Root>
		<Card.Header>
			<Card.Title>Deploy Project</Card.Title>
			<Card.Description>
				{#if isDeploymentReady()}
					All required checks have passed. You can now deploy your tracking setup.
				{:else}
					Please complete all required configurations before deploying.
				{/if}
			</Card.Description>
		</Card.Header>
		<Card.Content>
			<div class="flex gap-4">
				<Button disabled={!isDeploymentReady()} class="flex-1">
					<Rocket class="mr-2 h-4 w-4" />
					Deploy Project
				</Button>
				<Button variant="outline" href="/projects/{project.id}">Cancel</Button>
			</div>
		</Card.Content>
	</Card.Root>

	{#if !isDeploymentReady()}
		<Alert.Root variant="destructive">
			<AlertTriangle class="h-4 w-4" />
			<Alert.Title>Configuration Required</Alert.Title>
			<Alert.Description>
				Please complete all required configurations in the previous steps before proceeding with
				deployment.
			</Alert.Description>
		</Alert.Root>
	{/if}
</div>
