<script lang="ts">
	import { onMount } from 'svelte';
	import { Button } from '$lib/shad-components/ui/button';
	import * as Card from '$lib/shad-components/ui/card';
	import * as RadioGroup from '$lib/shad-components/ui/radio-group';
	import { Input } from '$lib/shad-components/ui/input';
	import { Label } from '$lib/shad-components/ui/label';
	import { Badge } from '$lib/shad-components/ui/badge';
	import * as Alert from '$lib/shad-components/ui/alert';
	import Loader2 from '@lucide/svelte/icons/loader-2';
	import Globe from '@lucide/svelte/icons/globe';
	import Plus from '@lucide/svelte/icons/plus';
	import ExternalLink from '@lucide/svelte/icons/external-link';
	import AlertTriangle from '@lucide/svelte/icons/alert-triangle';
	import { stapeAPIService, StapeRegion, StapeTokenType, type StapeDomain } from '$lib/api/stape';

	let {
		project,
		stapeConfig,
		containerConfig,
		onDomainConfigured
	}: {
		project: any;
		stapeConfig: any;
		containerConfig: any;
		onDomainConfigured: (config: any) => void;
	} = $props();

	let domainConfig = $state({
		useExistingDomain: undefined as boolean | undefined,
		selectedDomain: null as any,
		newDomainName: '',
		selectedDomainIdentifier: ''
	});

	let isLoading = $state(false);
	let availableDomains = $state<any[]>([]);
	let errorMessage = $state('');

	// Get project subdomain for validation
	let projectSubdomain = $derived(project?.subdomain?.domain?.name || 'example.com');

	// Derived states for validation
	let isDomainTypeSelected = $derived(domainConfig.useExistingDomain !== undefined);
	let isExistingDomainSelected = $derived(
		!domainConfig.useExistingDomain || domainConfig.selectedDomain !== null
	);
	let isNewDomainConfigured = $derived(
		domainConfig.useExistingDomain || domainConfig.newDomainName.trim() !== ''
	);

	let isConfigurationComplete = $derived(
		isDomainTypeSelected && isExistingDomainSelected && isNewDomainConfigured
	);

	const handleDomainTypeChange = (value: string) => {
		domainConfig.useExistingDomain = value === 'existing';
		if (domainConfig.useExistingDomain) {
			// Reset new domain fields
			domainConfig.newDomainName = '';
		} else {
			// Reset existing domain fields
			domainConfig.selectedDomain = null;
			domainConfig.selectedDomainIdentifier = '';
		}
		// Update parent when user makes changes (only call when needed)
		if (isConfigurationComplete) {
			onDomainConfigured({
				...domainConfig,
				isComplete: isConfigurationComplete
			});
		}
	};

	const handleDomainSelection = (domain: any) => {
		if (validateDomainCompatibility(domain.name)) {
			domainConfig.selectedDomain = domain;
			domainConfig.selectedDomainIdentifier = domain.identifier;
			// Update parent when user makes changes (only call when needed)
			if (isConfigurationComplete) {
				onDomainConfigured({
					...domainConfig,
					isComplete: isConfigurationComplete
				});
			}
		}
	};

	const handleInputChange = () => {
		// Update parent when user makes changes (only call when needed)
		if (isConfigurationComplete) {
			onDomainConfigured({
				...domainConfig,
				isComplete: isConfigurationComplete
			});
		}
	};

	const fetchDomains = async () => {
		if (!containerConfig?.selectedContainer?.identifier) {
			return;
		}

		isLoading = true;
		errorMessage = '';

		try {
			const isUserNotWorkspace = stapeConfig.userIdentifier && stapeConfig.userIdentifier !== '';
			const region = stapeConfig.isEuAccount ? StapeRegion.EU : StapeRegion.GLOBAL;
			const tokenType = isUserNotWorkspace ? StapeTokenType.PARTNER : StapeTokenType.NORMAL;

			const options: RequestInit = isUserNotWorkspace
				? { headers: { 'X-Stape-User-Identifier': stapeConfig.userIdentifier || '' } }
				: { headers: { 'X-WORKSPACE': stapeConfig.workspaceIdentifier || '' } };

			const response: any = await stapeAPIService.getContainerDomains(
				containerConfig.selectedContainer.identifier,
				region,
				tokenType,
				options
			);

			if (!response?.data?.body?.items) {
				throw new Error(response?.message ?? 'Error fetching domains');
			}

			availableDomains = response.data.body.items.map((domain: any) => ({
				identifier: domain.identifier,
				name: domain.name,
				status: domain.status || { label: 'Unknown' },
				connectionType: domain.connectionType || 'stape',
				cdnType: domain.cdnType || 'stape'
			}));
		} catch (error) {
			errorMessage = 'Failed to fetch domains. Please try again.';
			console.error('Error fetching domains:', error);
		} finally {
			isLoading = false;
		}
	};

	const createDomain = async () => {
		if (!domainConfig.newDomainName.trim()) {
			errorMessage = 'Please enter a domain name';
			return;
		}

		isLoading = true;
		errorMessage = '';

		try {
			const completeDomainName = `${domainConfig.newDomainName}.${projectSubdomain}`;

			const isUserNotWorkspace = stapeConfig.userIdentifier && stapeConfig.userIdentifier !== '';
			const region = stapeConfig.isEuAccount ? StapeRegion.EU : StapeRegion.GLOBAL;
			const tokenType = isUserNotWorkspace ? StapeTokenType.PARTNER : StapeTokenType.NORMAL;

			const options: RequestInit = isUserNotWorkspace
				? { headers: { 'X-Stape-User-Identifier': stapeConfig.userIdentifier || '' } }
				: { headers: { 'X-WORKSPACE': stapeConfig.workspaceIdentifier || '' } };

			const domainData = {
				name: completeDomainName,
				connectionType: 'stape',
				cdnType: 'stape',
				useCnameRecord: true
			};

			const response: any = await stapeAPIService.createContainerDomain(
				containerConfig.selectedContainer.identifier,
				domainData,
				region,
				tokenType,
				options
			);

			if (!response?.data?.body) {
				throw new Error(response?.message ?? 'Error creating domain');
			}

			const newDomain = {
				identifier: response.data.body.identifier,
				name: response.data.body.name,
				status: response.data.body.status || { label: 'Active' },
				connectionType: response.data.body.connectionType || 'stape',
				cdnType: response.data.body.cdnType || 'stape'
			};

			domainConfig.selectedDomain = newDomain;
			domainConfig.selectedDomainIdentifier = newDomain.identifier;

			// Add to available domains list
			availableDomains = [...availableDomains, newDomain];
		} catch (error) {
			errorMessage = 'Failed to create domain. Please try again.';
			console.error('Error creating domain:', error);
		} finally {
			isLoading = false;
		}
	};

	const validateDomainCompatibility = (domainName: string) => {
		if (!domainName) return true;

		// Extract the base domain from the full domain name
		const parts = domainName.split('.');
		const baseDomain = parts.slice(1).join('.');

		return baseDomain === projectSubdomain;
	};

	onMount(() => {
		// Set default domain name based on project
		if (project?.name && !domainConfig.newDomainName) {
			const safeName = project.name.toLowerCase().replace(/\s+/g, '-');
			domainConfig.newDomainName = `${safeName}-gtm`;
		}

		// Fetch available domains
		fetchDomains();
	});
</script>

{#if isLoading}
	<div class="flex items-center justify-center p-8">
		<Loader2 class="mr-2 h-6 w-6 animate-spin" />
		<span>Loading domain information...</span>
	</div>
{:else}
	<div class="space-y-6">
		<div>
			<h3 class="text-lg font-semibold">Stape Domain Management</h3>
			<p class="text-muted-foreground text-sm">
				Configure a custom domain for your server-side tracking endpoint.
			</p>
		</div>

		{#if errorMessage}
			<Alert.Root variant="destructive">
				<AlertTriangle class="h-4 w-4" />
				<Alert.Title>Error</Alert.Title>
				<Alert.Description>{errorMessage}</Alert.Description>
			</Alert.Root>
		{/if}

		<!-- Project Domain Info -->
		<Alert.Root>
			<Globe class="h-4 w-4" />
			<Alert.Title>Project Domain</Alert.Title>
			<Alert.Description>
				Your project domain is <strong>{projectSubdomain}</strong>. All tracking domains must be
				subdomains of this domain.
			</Alert.Description>
		</Alert.Root>

		<!-- Domain Type Selection -->
		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<Globe class="h-5 w-5" />
					Domain Type
				</Card.Title>
				<Card.Description>
					Choose whether to use an existing domain or create a new one.
				</Card.Description>
			</Card.Header>
			<Card.Content>
				<RadioGroup.Root
					value={domainConfig.useExistingDomain === true
						? 'existing'
						: domainConfig.useExistingDomain === false
							? 'new'
							: ''}
					onValueChange={handleDomainTypeChange}
				>
					<div class="space-y-4">
						<div class="flex items-center space-x-2">
							<RadioGroup.Item value="existing" id="existing" />
							<Label for="existing" class="flex-1">
								<div class="space-y-1">
									<div class="font-medium">Use Existing Domain</div>
									<div class="text-muted-foreground text-sm">
										Select from your existing Stape domains
									</div>
								</div>
							</Label>
						</div>
						<div class="flex items-center space-x-2">
							<RadioGroup.Item value="new" id="new" />
							<Label for="new" class="flex-1">
								<div class="space-y-1">
									<div class="font-medium">Create New Domain</div>
									<div class="text-muted-foreground text-sm">
										Create a new tracking domain for this project
									</div>
								</div>
							</Label>
						</div>
					</div>
				</RadioGroup.Root>
			</Card.Content>
		</Card.Root>

		<!-- Existing Domain Selection -->
		{#if domainConfig.useExistingDomain === true}
			<Card.Root>
				<Card.Header>
					<Card.Title>Select Domain</Card.Title>
					<Card.Description>Choose from your existing domains.</Card.Description>
				</Card.Header>
				<Card.Content>
					{#if availableDomains.length === 0}
						<div class="py-8 text-center">
							<Globe class="text-muted-foreground mx-auto h-12 w-12" />
							<h3 class="mt-2 text-sm font-semibold">No domains found</h3>
							<p class="text-muted-foreground mt-1 text-sm">
								No existing domains found for this container.
							</p>
							<Button
								variant="outline"
								class="mt-4"
								onclick={() => (domainConfig.useExistingDomain = false)}
							>
								<Plus class="mr-2 h-4 w-4" />
								Create New Domain
							</Button>
						</div>
					{:else}
						<div class="grid gap-4">
							{#each availableDomains as domain (domain.identifier)}
								{@const isCompatible = validateDomainCompatibility(domain.name)}
								<div
									class="cursor-pointer rounded-lg border p-4 transition-colors {domainConfig
										.selectedDomain?.identifier === domain.identifier
										? 'border-primary bg-primary/5'
										: isCompatible
											? 'hover:bg-muted/50'
											: 'border-destructive/50 bg-destructive/5'}"
									onclick={() => handleDomainSelection(domain)}
									role="button"
									tabindex="0"
									onkeypress={(e) => e.key === 'Enter' && handleDomainSelection(domain)}
								>
									<div class="flex items-center justify-between">
										<div class="flex-1">
											<h4 class="font-medium">{domain.name}</h4>
											<p class="text-muted-foreground text-sm">
												Type: {domain.connectionType} • CDN: {domain.cdnType}
											</p>
											{#if !isCompatible}
												<p class="text-destructive mt-1 text-sm">
													⚠️ Domain does not match project domain
												</p>
											{/if}
										</div>
										<Badge variant={isCompatible ? 'outline' : 'destructive'}>
											{domain.status.label}
										</Badge>
									</div>
								</div>
							{/each}
						</div>
					{/if}
				</Card.Content>
			</Card.Root>
		{/if}

		<!-- New Domain Configuration -->
		{#if domainConfig.useExistingDomain === false}
			<Card.Root>
				<Card.Header>
					<Card.Title class="flex items-center gap-2">
						<Plus class="h-5 w-5" />
						New Domain Configuration
					</Card.Title>
					<Card.Description>Create a new tracking domain.</Card.Description>
				</Card.Header>
				<Card.Content class="space-y-4">
					<div class="space-y-2">
						<Label for="domainName">Subdomain</Label>
						<div class="flex items-center gap-2">
							<Input
								id="domainName"
								value={domainConfig.newDomainName}
								oninput={(e) => {
									domainConfig.newDomainName = (e.target as HTMLInputElement)?.value || '';
									handleInputChange();
								}}
								placeholder="tracking"
								class="flex-1"
							/>
							<span class="text-muted-foreground">.{projectSubdomain}</span>
						</div>
						<p class="text-muted-foreground text-xs">
							We recommend using unobvious subdomains without direct references to GTM, Stape,
							analytics, tracking, etc.
						</p>
						{#if domainConfig.newDomainName}
							<div class="bg-muted mt-2 rounded p-2">
								<p class="text-sm">
									<strong>Full domain:</strong>
									{domainConfig.newDomainName}.{projectSubdomain}
								</p>
							</div>
						{/if}
					</div>
					{#if domainConfig.newDomainName.trim()}
						<Button onclick={createDomain} disabled={isLoading}>
							{#if isLoading}
								<Loader2 class="mr-2 h-4 w-4 animate-spin" />
							{:else}
								<Plus class="mr-2 h-4 w-4" />
							{/if}
							Create Domain
						</Button>
					{/if}
				</Card.Content>
			</Card.Root>
		{/if}

		<!-- Configuration Summary -->
		{#if isConfigurationComplete}
			<Card.Root>
				<Card.Header>
					<Card.Title>Domain Configuration Summary</Card.Title>
				</Card.Header>
				<Card.Content>
					<div class="space-y-2">
						<div class="flex justify-between">
							<span class="text-sm font-medium">Domain Type:</span>
							<Badge variant="outline">
								{domainConfig.useExistingDomain ? 'Existing' : 'New'}
							</Badge>
						</div>
						{#if domainConfig.selectedDomain}
							<div class="flex justify-between">
								<span class="text-sm font-medium">Domain:</span>
								<span class="font-mono text-sm">{domainConfig.selectedDomain.name}</span>
							</div>
							<div class="flex justify-between">
								<span class="text-sm font-medium">Status:</span>
								<Badge variant="outline">{domainConfig.selectedDomain.status.label}</Badge>
							</div>
						{/if}
					</div>
				</Card.Content>
			</Card.Root>
		{/if}

		<Alert.Root>
			<ExternalLink class="h-4 w-4" />
			<Alert.Title>About Custom Domains</Alert.Title>
			<Alert.Description>
				Custom domains allow you to serve tracking scripts from your own domain, improving data
				collection reliability and avoiding ad blockers.
			</Alert.Description>
		</Alert.Root>
	</div>
{/if}
