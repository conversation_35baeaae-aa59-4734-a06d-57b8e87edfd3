import { error, redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ fetch, params, locals, depends }) => {
	const { id } = params;

	depends(`project:${id}`);

	// Get project data
	const projectResponse = await fetch(`/spark/api/v1/projects/${id}/`);

	if (!projectResponse.ok) {
		throw error(404, 'Project not found');
	}

	const projectData = await projectResponse.json();
	const project = projectData.data;

	// Get workspace data
	const workspaceResponse = await fetch(`/spark/api/v1/workspaces/${project.workspace_id}/`);

	if (!workspaceResponse.ok) {
		throw error(404, 'Workspace not found');
	}

	const workspaceData = await workspaceResponse.json();
	const workspace = workspaceData.data;

	// Pre-deployment checks
	const canDeploy = checkDeploymentEligibility(project);

	if (!canDeploy.eligible) {
		// For now, we'll allow access but show warnings
		// In the future, you might want to redirect or block access
		console.warn('Project not ready for deployment:', canDeploy.reasons);
	}

	return {
		project,
		workspace,
		deploymentCheck: canDeploy
	};
};

/**
 * Check if a project is eligible for deployment
 * Returns true for now, but can be extended with actual validation logic
 */
function checkDeploymentEligibility(project: any) {
	const reasons: string[] = [];

	// Check if project has pages configured
	if (!project.raw_data?.pages || project.raw_data.pages.length === 0) {
		reasons.push('No pages configured');
	}

	// Check if project has events configured
	if (!project.raw_data?.events || project.raw_data.events.length === 0) {
		reasons.push('No events configured');
	}

	// Check if project has ad networks configured
	if (!project.ad_network || project.ad_network.length === 0) {
		reasons.push('No ad networks configured');
	}

	// For now, always return eligible = true
	// You can change this logic based on your requirements
	return {
		eligible: true, // Change to reasons.length === 0 for strict validation
		reasons
	};
}
