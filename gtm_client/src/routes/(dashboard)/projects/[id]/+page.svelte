<script lang="ts">
	import { onDestroy, onMount } from 'svelte';
	import type { PageData } from './$types';
	import { globalBreadcrumbs, globalNavLinks } from '$lib/stores/side-nav.svelte';

	// ShadCN & Lucide Components
	import * as Card from '$lib/shad-components/ui/card';
	import * as Tabs from '$lib/shad-components/ui/tabs';
	import { Button } from '$lib/shad-components/ui/button';
	import { Badge } from '$lib/shad-components/ui/badge';
	import * as Sheet from '$lib/shad-components/ui/sheet';
	import { Checkbox } from '$lib/shad-components/ui/checkbox';
	import Edit from '@lucide/svelte/icons/edit';
	import Settings2 from '@lucide/svelte/icons/settings-2';
	import Tags from '@lucide/svelte/icons/tags';
	import Rocket from '@lucide/svelte/icons/rocket';
	import PanelLeftOpen from '@lucide/svelte/icons/panel-left-open';
	import FileText from '@lucide/svelte/icons/file-text';
	import { setSlots } from '../../layout.slots.svelte';
	import { HouseIcon, NetworkIcon, ProjectIcon } from '$lib/shared-ui/layoutExports';
	import { page } from '$app/state';
	import FolderOpenDot from '@lucide/svelte/icons/folder-open-dot';
	import House from '@lucide/svelte/icons/house';
	import Network from '@lucide/svelte/icons/network';
	import FileCode2 from '@lucide/svelte/icons/file-code-2';
	import CodeXml from '@lucide/svelte/icons/code-xml';
	import MousePointerClick from '@lucide/svelte/icons/mouse-pointer-click';
	import ArrowDownUp from '@lucide/svelte/icons/arrow-down-up';
	import Eye from '@lucide/svelte/icons/eye';
	import Zap from '@lucide/svelte/icons/zap';

	// Type definitions to ensure data consistency with the wizard
	interface PageRule {
		id: number;
		type: 'url' | 'datalayer'; // Add rule type
		condition: 'Contains' | 'Equals' | 'StartsWith' | 'EndsWith';
		value: string;
	}
	interface SelectedPage {
		instanceId: string;
		name: string;
		rules: PageRule[];
		isDefault?: boolean;
	}
	interface SelectedEvent {
		instanceId: string;
		name: string;
		pageInstanceId: string;
		triggerType: 'page-load' | 'datalayer-event' | 'click' | 'scroll' | 'element-visible';
		rules: PageRule[];
		selector?: string;
		scrollDepth?: number;
		customCode?: string;
		dataLayerEventName?: string;
		isDefaultPageLoad?: boolean;
		pageTargeting?: 'all' | 'specific'; // Whether event fires on all pages or specific subset
	}

	let { data }: { data: PageData } = $props();
	const { project, workspace } = data;

	let drawerOpen = $state(false);

	// Mock completion data for the drawer
	const completionSteps = [
		{ id: 'details', label: 'Project Details Verified', done: true },
		{ id: 'pages', label: 'Pages Configured', done: true },
		{ id: 'events', label: 'Events Added', done: true },
		{ id: 'ad-networks', label: 'Ad Networks Linked', done: project.ad_network.length > 0 },
		{ id: 'deploy', label: 'Ready to Deploy', done: false }
	];

	// Extracted data for "Tags & Triggers" tab from the project raw_data
	let wizardData = $derived.by(() => {
		const rawData = project?.raw_data;
		if (!rawData) return null;
		return {
			droppedPages: (rawData.pages as SelectedPage[]) ?? [],
			droppedEvents: (rawData.events as SelectedEvent[]) ?? []
		};
	});

	// Correctly filters events based on the page's unique instanceId
	const getEventsForPage = (pageInstanceId: string) => {
		return (
			wizardData?.droppedEvents?.filter((event) => event.pageInstanceId === pageInstanceId) ?? []
		);
	};

	// Helper function to get trigger icon and label
	const getTriggerInfo = (triggerType: string) => {
		const triggerMap = {
			'page-load': { icon: FileCode2, label: 'Page Load', color: 'bg-blue-100 text-blue-800' },
			'datalayer-event': {
				icon: CodeXml,
				label: 'DataLayer Event',
				color: 'bg-purple-100 text-purple-800'
			},
			click: { icon: MousePointerClick, label: 'Click', color: 'bg-green-100 text-green-800' },
			scroll: { icon: ArrowDownUp, label: 'Page Scroll', color: 'bg-orange-100 text-orange-800' },
			'element-visible': {
				icon: Eye,
				label: 'Element Visible',
				color: 'bg-indigo-100 text-indigo-800'
			}
		};
		return (
			triggerMap[triggerType] || { icon: Zap, label: 'Unknown', color: 'bg-gray-100 text-gray-800' }
		);
	};

	onMount(() => {
		globalBreadcrumbs.crumbs = [
			{
				title: HouseIcon,
				href: '/dashboard'
			},
			{
				title: NetworkIcon,
				href: `/workspaces/${data?.workspace?.id}`
			},
			{
				title: ProjectIcon,
				href: `/projects/${data?.project?.id}`
			}
		];

		globalNavLinks.links = [
			{
				title: 'Dashboard',
				url: '/dashboard',

				icon: House
			},
			{
				title: 'Workspace',
				url: `/workspaces/${data?.workspace?.id}`,
				icon: Network
			},
			{
				title: 'Project',
				url: `/projects/${data?.project?.id}`,
				icon: FolderOpenDot
			}
		];

		setSlots({
			ActionArea,
			Footer: () => {}
		});
	});
</script>

{#snippet ActionArea()}
	<div class="flex flex-col-reverse gap-4 lg:flex-row">
		<div class="flex items-center gap-2">
			<Button variant="outline" href={`/projects/${project.id}/edit`}>
				<Edit class="mr-2 h-4 w-4" />
				Edit Wizard
			</Button>
			<Button href={`/projects/${page.params.id}/deploy`} class="hidden lg:flex">
				<Rocket class="mr-2 h-4 w-4" />
				Deploy Project
			</Button>
			<Button variant="ghost" size="icon" class="lg:hidden" onclick={() => (drawerOpen = true)}>
				<PanelLeftOpen class="h-5 w-5" />
			</Button>
		</div>
	</div>
{/snippet}

<div class="flex h-full w-full">
	<Sheet.Root bind:open={drawerOpen}>
		<Sheet.Content side="left" class="p-4 lg:hidden">
			<Sheet.Header>
				<Sheet.Title>Setup Progress</Sheet.Title>
			</Sheet.Header>
			<div class="mt-4 space-y-4">
				{#each completionSteps as step (step.id)}
					<div class="flex items-center space-x-2">
						<Checkbox id={`check-drawer-${step.id}`} checked={step.done} disabled />
						<label for={`check-drawer-${step.id}`} class="text-sm font-medium">{step.label}</label>
					</div>
				{/each}
			</div>
		</Sheet.Content>
	</Sheet.Root>

	<div class="flex-1 p-4 lg:p-6">
		<header class="mb-6 flex items-center justify-between">
			<div>
				<h1 class="text-3xl font-bold tracking-tight">{project.name}</h1>
				<p class="text-muted-foreground">Overview of your project configuration and status.</p>
			</div>
		</header>

		<Tabs.Root value="details" class="w-full">
			<Tabs.List class="grid w-full grid-cols-3">
				<Tabs.Trigger value="details"><FileText class="mr-2 h-4 w-4" />Details</Tabs.Trigger>
				<Tabs.Trigger value="ad_networks"
					><Settings2 class="mr-2 h-4 w-4" />Ad Networks</Tabs.Trigger
				>
				<Tabs.Trigger value="tags_triggers"
					><Tags class="mr-2 h-4 w-4" />Tags & Triggers</Tabs.Trigger
				>
			</Tabs.List>

			<Tabs.Content value="details" class="mt-6">
				<Card.Root>
					<Card.Header>
						<Card.Title>Project Details</Card.Title>
					</Card.Header>
					<Card.Content>
						<dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
							<div class="sm:col-span-1">
								<dt class="text-muted-foreground text-sm font-medium">Name</dt>
								<dd class="text-foreground mt-1 text-sm">{project.name}</dd>
							</div>
							<div class="sm:col-span-1">
								<dt class="text-muted-foreground text-sm font-medium">Status</dt>
								<dd class="text-foreground mt-1 text-sm">
									<Badge class="capitalize">{project.status}</Badge>
								</dd>
							</div>
							<div class="sm:col-span-2">
								<dt class="text-muted-foreground text-sm font-medium">Full Domain</dt>
								<dd class="text-foreground mt-1 text-sm">{project.subdomain?.full_uri ?? 'N/A'}</dd>
							</div>
						</dl>
					</Card.Content>
				</Card.Root>
			</Tabs.Content>

			<Tabs.Content value="ad_networks" class="mt-6">
				<Card.Root>
					<Card.Header>
						<Card.Title>Ad Networks</Card.Title>
					</Card.Header>
					<Card.Content class="grid grid-cols-1 gap-4 md:grid-cols-2">
						{#each project.ad_network ?? [] as network (network.ad_network.id)}
							<Card.Root class="flex flex-col">
								<Card.Header class="flex flex-row items-center gap-4 space-y-0 pb-2">
									<img src={network.ad_network?.icon?.full_url} alt="icon" class="h-8 w-8" />
									<Card.Title>{network.ad_network.name}</Card.Title>
								</Card.Header>
								<Card.Content class="flex-grow">
									<dl>
										{#each Object.entries(network.field_values ?? {}) as [key, value] (key)}
											<div class="mt-2">
												<dt class="text-muted-foreground text-xs font-medium uppercase">{key}</dt>
												<dd class="text-foreground font-mono text-sm">{value}</dd>
											</div>
										{/each}
									</dl>
								</Card.Content>
							</Card.Root>
						{/each}
					</Card.Content>
				</Card.Root>
			</Tabs.Content>

			<Tabs.Content value="tags_triggers" class="mt-6">
				<Card.Root>
					<Card.Header>
						<Card.Title>Tags & Triggers Summary</Card.Title>
						<p class="text-muted-foreground text-sm">
							Overview of configured pages, events, and their trigger conditions
						</p>
					</Card.Header>
					<Card.Content>
						{#if wizardData && wizardData.droppedPages.length > 0}
							<!-- Mobile: Stack layout -->
							<div class="space-y-4 lg:hidden">
								{#each wizardData.droppedPages as page (page.instanceId)}
									<Card.Root class="bg-muted/50">
										<Card.Header class="pb-3">
											<Card.Title class="text-base">{page.name}</Card.Title>
											<div class="flex flex-wrap gap-1">
												{#each page.rules as rule (rule.id)}
													<Badge variant="outline" class="text-xs">
														{#if rule.type === 'url'}
															URL {rule.condition}: {rule.value || 'Any'}
														{:else if rule.type === 'datalayer'}
															DataLayer: {rule.value || 'Any'}
														{:else}
															{rule.condition}: {rule.value || 'Any'}
														{/if}
													</Badge>
												{/each}
												{#if page.isDefault}
													<Badge variant="secondary" class="text-xs">Default Page</Badge>
												{/if}
											</div>
										</Card.Header>
										<Card.Content class="space-y-2">
											{@const events = getEventsForPage(page.instanceId)}
											{#if events.length > 0}
												{#each events as event (event.instanceId)}
													{@const triggerInfo = getTriggerInfo(event.triggerType)}
													{@const IconComponent = triggerInfo.icon}
													<div class="bg-background rounded-md border p-3">
														<div class="flex items-center justify-between">
															<div class="flex items-center gap-2">
																<span class="text-sm font-medium">{event.name}</span>
																{#if event.isDefaultPageLoad}
																	<Badge variant="secondary" class="text-xs">Default</Badge>
																{/if}
																{#if event.pageTargeting === 'all'}
																	<Badge variant="outline" class="text-xs">All Pages</Badge>
																{/if}
															</div>
															<Badge class={triggerInfo.color}>
																<IconComponent class="mr-1 h-3 w-3" />
																{triggerInfo.label}
															</Badge>
														</div>
														{#if event.triggerType === 'click' && event.selector}
															<p class="text-muted-foreground mt-1 text-xs">
																Selector: {event.selector}
															</p>
														{:else if event.triggerType === 'scroll' && event.scrollDepth}
															<p class="text-muted-foreground mt-1 text-xs">
																Scroll Depth: {event.scrollDepth}%
															</p>
														{:else if event.triggerType === 'datalayer-event' && event.dataLayerEventName}
															<p class="text-muted-foreground mt-1 text-xs">
																Event: {event.dataLayerEventName}
															</p>
														{/if}
														{#if event.rules && event.rules.length > 0}
															<div class="mt-2 flex flex-wrap gap-1">
																{#each event.rules as rule (rule.id)}
																	<Badge variant="secondary" class="text-xs">
																		{#if rule.type === 'url'}
																			URL {rule.condition}: {rule.value || 'Any'}
																		{:else if rule.type === 'datalayer'}
																			DataLayer: {rule.value || 'Any'}
																		{:else}
																			{rule.condition}: {rule.value || 'Any'}
																		{/if}
																	</Badge>
																{/each}
															</div>
														{/if}
													</div>
												{/each}
											{:else}
												<p class="text-muted-foreground text-xs">No events configured.</p>
											{/if}
										</Card.Content>
									</Card.Root>
								{/each}
							</div>

							<!-- Desktop: Horizontal scroll layout -->
							<div class="hidden lg:block">
								<div class="overflow-x-auto pb-4">
									<div class="flex w-max space-x-4">
										{#each wizardData.droppedPages as page (page.instanceId)}
											<div class="w-80 flex-shrink-0">
												<Card.Root class="bg-muted/50 h-full">
													<Card.Header class="pb-3">
														<Card.Title class="text-base">{page.name}</Card.Title>
														<div class="flex flex-wrap gap-1">
															{#each page.rules as rule (rule.id)}
																<Badge variant="outline" class="text-xs">
																	{#if rule.type === 'url'}
																		URL {rule.condition}: {rule.value || 'Any'}
																	{:else if rule.type === 'datalayer'}
																		DataLayer: {rule.value || 'Any'}
																	{:else}
																		{rule.condition}: {rule.value || 'Any'}
																	{/if}
																</Badge>
															{/each}
															{#if page.isDefault}
																<Badge variant="secondary" class="text-xs">Default Page</Badge>
															{/if}
														</div>
													</Card.Header>
													<Card.Content class="space-y-3">
														{@const events = getEventsForPage(page.instanceId)}
														{#if events.length > 0}
															{#each events as event (event.instanceId)}
																{@const triggerInfo = getTriggerInfo(event.triggerType)}
																{@const IconComponent = triggerInfo.icon}
																<div class="bg-background rounded-md border p-3">
																	<div class="mb-2 flex items-center justify-between">
																		<div class="flex items-center gap-2">
																			<span class="text-sm font-medium">{event.name}</span>
																			{#if event.isDefaultPageLoad}
																				<Badge variant="secondary" class="text-xs">Default</Badge>
																			{/if}
																			{#if event.pageTargeting === 'all'}
																				<Badge variant="outline" class="text-xs">All Pages</Badge>
																			{/if}
																		</div>
																		<Badge class={triggerInfo.color}>
																			<IconComponent class="mr-1 h-3 w-3" />
																			{triggerInfo.label}
																		</Badge>
																	</div>
																	{#if event.triggerType === 'click' && event.selector}
																		<p class="text-muted-foreground text-xs">
																			Selector: {event.selector}
																		</p>
																	{:else if event.triggerType === 'scroll' && event.scrollDepth}
																		<p class="text-muted-foreground text-xs">
																			Scroll Depth: {event.scrollDepth}%
																		</p>
																	{:else if event.triggerType === 'datalayer-event' && event.dataLayerEventName}
																		<p class="text-muted-foreground text-xs">
																			Event: {event.dataLayerEventName}
																		</p>
																	{:else if event.triggerType === 'element-visible' && event.selector}
																		<p class="text-muted-foreground text-xs">
																			Element: {event.selector}
																		</p>
																	{/if}
																	{#if event.rules && event.rules.length > 0}
																		<div class="mt-2 flex flex-wrap gap-1">
																			{#each event.rules as rule (rule.id)}
																				<Badge variant="secondary" class="text-xs">
																					{#if rule.type === 'url'}
																						URL {rule.condition}: {rule.value || 'Any'}
																					{:else if rule.type === 'datalayer'}
																						DataLayer: {rule.value || 'Any'}
																					{:else}
																						{rule.condition}: {rule.value || 'Any'}
																					{/if}
																				</Badge>
																			{/each}
																		</div>
																	{/if}
																</div>
															{/each}
														{:else}
															<p class="text-muted-foreground text-xs">No events configured.</p>
														{/if}
													</Card.Content>
												</Card.Root>
											</div>
										{/each}
									</div>
								</div>
							</div>
						{:else}
							<div class="py-8 text-center">
								<Tags class="text-muted-foreground mx-auto mb-4 h-12 w-12" />
								<p class="text-muted-foreground text-sm">No page or event configurations found.</p>
								<p class="text-muted-foreground mt-1 text-xs">
									Configure pages and events in the wizard to see them here.
								</p>
							</div>
						{/if}
					</Card.Content>
				</Card.Root>
			</Tabs.Content>
		</Tabs.Root>
	</div>

	<aside class="hidden lg:flex lg:w-64 lg:flex-col lg:border-l lg:p-4">
		<h3 class="text-lg font-semibold">Setup Progress</h3>
		<div class="mt-4 space-y-4">
			{#each completionSteps as step (step.id)}
				<div class="flex items-center space-x-2">
					<Checkbox id={`check-desktop-${step.id}`} checked={step.done} disabled />
					<label for={`check-desktop-${step.id}`} class="text-sm font-medium">{step.label}</label>
				</div>
			{/each}
		</div>
		<Button href={`/projects/${page.params.id}/deploy`} class="mt-auto mb-6">
			<Rocket class="mr-2 h-4 w-4" />
			Deploy Project
		</Button>
	</aside>
</div>
