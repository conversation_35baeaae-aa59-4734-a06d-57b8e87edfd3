<script lang="ts">
	import { page } from '$app/state';
	import { setGlobalUIContext } from '$lib/stores/globalContext.svelte';
	import { onDestroy } from 'svelte';

	let { children } = $props();

	// Sets and updates the global context to be in sync with all the current changes
	$effect(() => {
		setGlobalUIContext({
			currentWorkspaceId: page.data.workspace.id,
			currentWorkspaceInformation: page.data.workspace,
			extra: {
				adNetworks: page.data.adNetworks,
				projectPurposes: page.data.projectPurposes,
				projectTypes: page.data.projectTypes
			}
		});
	});

	onDestroy(() => {
		setGlobalUIContext({
			currentWorkspaceId: undefined,
			currentWorkspaceInformation: undefined,
			extra: {
				adNetworks: undefined,
				projectPurposes: undefined,
				projectTypes: undefined
			}
		});
	});
</script>

{@render children()}
