<script lang="ts">
	import { onMount } from 'svelte';
	import { globalBreadcrumbs, globalNavLinks } from '$lib/stores/side-nav.svelte';

	import Dashboard from '@lucide/svelte/icons/layout-dashboard';
	import type { PageData } from './$types';

	import { zodClient } from 'sveltekit-superforms/adapters';
	import { createProjectSchema } from './formSchemas';
	import { superForm } from 'sveltekit-superforms';
	import * as Form from '$lib/shad-components/ui/form/index.js';
	import { Input } from '$lib/shad-components/ui/input/index.js';
	import { Checkbox } from '$lib/shad-components/ui/checkbox/index.js';
	import Separator from '$lib/shad-components/ui/separator/separator.svelte';
	import * as RadioGroup from '$lib/shad-components/ui/radio-group/index.js';
	import { watch } from 'runed';
	import { slide } from 'svelte/transition';
	import { Button } from '$lib/shad-components/ui/button';
	import { toast } from 'svelte-sonner';

	import { goto } from '$app/navigation';
	import { HouseIcon, NetworkIcon } from '$lib/shared-ui/layoutExports';
	import House from '@lucide/svelte/icons/house';
	import Network from '@lucide/svelte/icons/network';

	let {
		data
	}: {
		data: PageData;
	} = $props();

	onMount(() => {
		globalBreadcrumbs.crumbs = [
			{
				title: HouseIcon,
				href: '/dashboard'
			},
			{
				title: NetworkIcon,
				href: `/workspaces/${data?.workspace?.id}`
			},
			{
				title: 'Create new project',
				href: `/projects/create?workspaceId=${data?.workspace?.id}`
			}
		];

		globalNavLinks.links = [
			{
				title: 'Dashboard',
				url: '/dashboard',

				icon: House
			},
			{
				title: 'Workspace',
				url: `/workspaces/${data?.workspace?.id}`,
				icon: Network
			}
		];
	});

	// form
	const form = superForm(data.form!, {
		dataType: 'json',
		validators: zodClient(createProjectSchema),
		onResult(event) {
			if (event.result.type !== 'success') {
				return;
			}

			if (event.result.type !== 'success') {
				return;
			}

			toast.success('Project Created', {
				description: 'Redirecting you to Pages.'
			});
			const projectId = event?.result?.data?.data?.id;

			if (projectId) {
				setTimeout(() => {
					goto(`/projects/${projectId}/edit?view=pages`);
				}, 2000);
			} else {
				toast.error('Error', {
					description: 'Error occurred while creating the project.'
				});
			}
		},
		onError(event) {
			toast.error('Submission Error', {
				description: event.result.error.message || 'Please check the form for errors.'
			});
		}
	});

	const { form: formData, enhance, submitting, submit, errors } = form;

	$effect(() => {
		if ($errors && $errors._errors) {
			$errors._errors.forEach((error) =>
				toast.error('Error occured.', {
					description: error
				})
			);
		}
	});

	const projectPurposes = Array.isArray(data.projectPurposes) ? data.projectPurposes : [];
	const projectTypes = Array.isArray(data.projectTypes) ? data.projectTypes : [];

	// Functions to manage the array of selected project reason IDs (as strings)
	function addProjectReason(id: string) {
		if (!$formData.project_reasons.includes(id)) {
			$formData.project_reasons = [...$formData.project_reasons, id];
		}
	}

	function removeProjectReason(id: string) {
		$formData.project_reasons = $formData.project_reasons.filter((reasonId) => reasonId !== id);
	}

	let platformsFetching = $state(false);
	let controller = new AbortController();

	const getProjectPlatforms = async () => {
		if (!$formData.project_type) {
			projectPlatforms = [];
			return [];
		}

		if (platformsFetching) {
			controller.abort();
			controller = new AbortController();
		}
		const signal = controller.signal;

		platformsFetching = true;
		try {
			existingProjectType = $formData.project_type;
			const relativePath = `/spark/api/v1/projects/project-platform/by-project-type/${$formData.project_type}`;
			const response = await fetch(relativePath, { method: 'GET', signal });
			if (!response.ok) {
				console.error('Failed to fetch platforms:', response.statusText);
				return [];
			}
			// $formData.projectPlatform = '';
			const responseData = await response.json();
			return responseData?.data ?? [];
		} catch (err: any) {
			if (err.name === 'AbortError') {
				console.log('Fetch aborted for project platforms');
			} else {
				console.error('Error fetching project platforms:', err);
			}
			return [];
		} finally {
			platformsFetching = false;
		}
	};

	let projectPlatforms = $state([]);
	let existingProjectType = $state('');
	$formData.workspace = data.workspace?.id;

	watch(
		() => [$formData.project_type],
		() => {
			if ($formData.project_type !== existingProjectType) {
				getProjectPlatforms().then((platforms) => {
					projectPlatforms = platforms;
				});
			}
		}
	);

	let currentStep = $state(1);
	const TOTAL_STEPS = 6;

	async function advanceToStep(nextStep: number) {
		if (nextStep <= TOTAL_STEPS && nextStep !== currentStep) {
			currentStep = nextStep;
		}
	}

	function scrollToComfortableView(sectionId: string) {
		requestAnimationFrame(() => {
			const element = document.getElementById(sectionId);

			if (element && element.offsetHeight > 0) {
				const desiredPaddingBottom = 75;
				const elementRect = element.getBoundingClientRect();

				const targetScrollY =
					elementRect.bottom + window.scrollY - window.innerHeight + desiredPaddingBottom;

				const maxScrollY = document.documentElement.scrollHeight - window.innerHeight;
				let finalScrollY = Math.max(0, Math.min(targetScrollY, maxScrollY));

				const isComfortablyInView =
					elementRect.top >= 0 &&
					elementRect.bottom <= window.innerHeight - desiredPaddingBottom + 10 &&
					elementRect.top < elementRect.bottom;

				if (
					isComfortablyInView &&
					targetScrollY <= window.scrollY + 5 &&
					targetScrollY >= window.scrollY - 5
				) {
					// If target is very close to current
					// console.log(`Element ${sectionId} already comfortably in view. Minor or no scroll needed.`);
					// You might still want a very gentle scroll if it just appeared,
					// but if the calculation results in minimal change, it might be okay.
					// For now, we'll proceed with the calculated scroll,
					// as even minor adjustments can be part of bringing it to the "perfect" spot.
				}

				window.scrollTo({
					top: finalScrollY,
					behavior: 'smooth'
				});
			} else if (element) {
				element.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
			}
		});
	}

	function addAdNetwork(id: string) {
		if (!$formData.ad_networks?.includes(id)) {
			$formData.ad_networks = [...$formData.ad_networks, id];
		}
	}

	function removeAdNetwork(id: string) {
		$formData.ad_networks = $formData.ad_networks.filter((networkId) => networkId !== id);
	}
</script>

<div class="flex w-full flex-col items-center justify-center">
	<form use:enhance method="POST" action="?/create_project" class="flex w-full max-w-2xl flex-col">
		<input type="hidden" bind:value={$formData.workspace} name="workspace" />

		<div class=" my-6 w-full">
			<h1 class="text-left text-2xl font-bold">Create Project</h1>
			<p class="text-left">Fill out the form below to create a new project.</p>
		</div>
		<section id="step1-basic-info" class="w-full">
			{#if currentStep >= 1}
				<div
					class="step-content py-4"
					transition:slide|local={{ duration: 300, axis: 'y' }}
					role="region"
					aria-labelledby="step1-heading"
					onintroend={() => scrollToComfortableView('step1-basic-info')}
				>
					<h3 class="mb-4 text-lg font-semibold">Step 1: Basic Project Information</h3>
					<div class="form-group mt-4 w-full md:w-3/4">
						<Form.Field {form} name="name" class="w-full">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Project name</Form.Label>
									<Input {...props} bind:value={$formData.name} disabled={$submitting} required />
								{/snippet}
							</Form.Control>
							<Form.Description>Choose a clear and concise name for your project.</Form.Description>
							<Form.FieldErrors />
						</Form.Field>
					</div>
					<div class="form-group mt-4 flex w-full flex-col gap-4 md:flex-row md:gap-6">
						<Form.Field {form} name="subdomain" class="w-full md:w-1/2">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Subdomain</Form.Label>
									<Input
										{...props}
										bind:value={$formData.subdomain}
										disabled={$submitting}
										required
									/>
								{/snippet}
							</Form.Control>

							<Form.FieldErrors />
						</Form.Field>
						<Form.Field {form} name="domain" class="w-full md:w-1/2">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label>Domain</Form.Label>
									<Input {...props} bind:value={$formData.domain} disabled={$submitting} required />
								{/snippet}
							</Form.Control>

							<Form.FieldErrors />
						</Form.Field>
					</div>
				</div>

				{#if currentStep === 1}
					<div class="mt-6 flex justify-end pb-8">
						<Button type="button" onclick={() => advanceToStep(2)}>Continue</Button>
					</div>
				{/if}
			{/if}
		</section>

		<section id="step2-server-config" class="w-full">
			{#if currentStep >= 2}
				<div
					class="step-content py-4"
					transition:slide|local={{ duration: 300, axis: 'y' }}
					role="region"
					aria-labelledby="step2-heading"
					onintroend={() => scrollToComfortableView('step2-server-config')}
				>
					<h3 class="mb-4 text-lg font-semibold">Step 2: Server Configuration</h3>

					<Form.Field
						{form}
						name="is_server_side"
						class="flex w-full flex-row items-center space-x-3 rounded-md p-4"
					>
						<Form.Control>
							{#snippet children({ props })}
								<Checkbox {...props} bind:checked={$formData.is_server_side} />
								<div class="space-y-1 leading-none">
									<Form.Label>Use Stape Server Side Tracking</Form.Label>
									<Form.Description>
										You can find more information about Stape <a
											href="https://stape.io"
											target="_blank"
											rel="noopener noreferrer"
											class="underline">here</a
										>.
									</Form.Description>
								</div>
							{/snippet}
						</Form.Control>
					</Form.Field>
					{#if currentStep === 2}
						<div class="mt-6 flex justify-end pb-8">
							<Button type="button" onclick={() => advanceToStep(3)}>Continue</Button>
						</div>
					{/if}
					<Separator class="my-8" />
				</div>
			{/if}
		</section>

		<section id="step3-project-purpose" class="w-full">
			{#if currentStep >= 3}
				<div
					class="step-content py-4"
					transition:slide|local={{ duration: 300, axis: 'y' }}
					role="region"
					aria-labelledby="step3-heading"
					onintroend={() => scrollToComfortableView('step3-project-purpose')}
				>
					<Form.Fieldset {form} name="project_reasons" class="w-full">
						<div class="mb-4">
							<Form.Legend class="text-lg font-semibold">Step 3: Project Purpose(s)</Form.Legend>
							<Form.Description
								>Select the purpose(s) for this project. At least one selection is required.</Form.Description
							>
						</div>
						{#if projectPurposes.length > 0}
							<div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-2">
								{#each projectPurposes as purpose (purpose.id)}
									{@const purposeIdStr = String(purpose.id)}
									{@const checked = $formData.project_reasons.includes(purposeIdStr)}
									<div class="flex flex-row items-start space-x-3">
										<Form.Control>
											{#snippet children({ props: checkboxProps })}
												<Checkbox
													{...checkboxProps}
													id={`reason-${purposeIdStr}`}
													aria-labelledby={`reason-label-${purposeIdStr}`}
													aria-describedby={purpose.description
														? `reason-desc-${purposeIdStr}`
														: undefined}
													{checked}
													onCheckedChange={(isChecked) => {
														if (isChecked === true) {
															// Explicitly check against true
															addProjectReason(purposeIdStr);
														} else {
															removeProjectReason(purposeIdStr);
														}
													}}
												/>
											{/snippet}
										</Form.Control>
										<div class="grid gap-1.5 leading-none">
											<label
												for={`reason-${purposeIdStr}`}
												id={`reason-label-${purposeIdStr}`}
												class="cursor-pointer text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
											>
												{purpose.name}
											</label>
											{#if purpose.description}
												<p id={`reason-desc-${purposeIdStr}`} class="text-muted-foreground text-sm">
													{purpose.description}
												</p>
											{/if}
										</div>
									</div>
								{/each}
							</div>
						{:else}
							<p class="text-muted-foreground text-sm">No project purposes available to select.</p>
						{/if}
						<Form.FieldErrors class="mt-3" />
					</Form.Fieldset>
				</div>

				{#if currentStep === 3}
					<div class="mt-6 flex justify-end pb-8">
						<Button type="button" onclick={() => advanceToStep(4)}>Continue</Button>
					</div>
				{/if}
				<Separator class="my-8" />
			{/if}
		</section>

		<section id="step4-project-type" class="w-full">
			{#if currentStep >= 4}
				<div
					class="step-content py-4"
					transition:slide|local={{ duration: 300, axis: 'y' }}
					role="region"
					aria-labelledby="step4-heading"
					onintroend={() => scrollToComfortableView('step4-project-type')}
				>
					<Form.Fieldset {form} name="project_type" class="w-full">
						<div class="mb-4">
							<Form.Legend class="text-lg font-semibold">Step 4: Project Type</Form.Legend>
							<Form.Description>Select the type of project you are creating.</Form.Description>
						</div>
						{#if projectTypes.length > 0}
							<RadioGroup.Root
								bind:value={$formData.project_type}
								class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"
								name="project_type"
							>
								{#each projectTypes as pType (pType.id)}
									{@const typeIdStr = String(pType.id)}
									<div class="relative">
										<Form.Control>
											{#snippet children({ props: controlProps })}
												<RadioGroup.Item
													{...controlProps}
													value={typeIdStr}
													id={`type-${typeIdStr}`}
													class="peer absolute inset-0 z-10 h-full w-full cursor-pointer opacity-0"
												/>

												<Form.Label
													for={`type-${typeIdStr}`}
													aria-hidden="true"
													class="bg-popover border-muted hover:border-primary/50 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary peer-data-[state=checked]:bg-primary/10 peer-data-[state=checked]:ring-primary flex h-full
														   flex-col items-center justify-start
														   rounded-md border-2 p-4 text-center
														   transition-colors duration-150 ease-in-out peer-data-[state=checked]:ring-0 peer-data-[state=checked]:ring-offset-2"
												>
													{#if pType.icon?.full_url}
														<img
															src={pType.icon.full_url}
															alt=""
															class="mb-3 h-10 w-10 transition-transform duration-150 ease-in-out peer-data-[state=checked]:scale-110"
														/>
													{/if}
													<span class="mb-1 text-sm font-semibold">{pType.name}</span>
													{#if pType.description}
														<p class="text-muted-foreground text-xs">{pType.description}</p>
													{/if}
												</Form.Label>
											{/snippet}
										</Form.Control>
									</div>
								{/each}
							</RadioGroup.Root>
						{:else}
							<p class="text-muted-foreground text-sm">No project types available to select.</p>
						{/if}
						<Form.FieldErrors class="mt-3" />
					</Form.Fieldset>
					{#if currentStep === 4}
						<div class="mt-6 flex justify-end pb-8">
							<Button
								type="button"
								onclick={() => advanceToStep(5)}
								disabled={!$formData.project_type}>Continue</Button
							>
						</div>
					{/if}
					<Separator class="my-8" />
				</div>
			{/if}
		</section>

		<section id="step5-project-platform" class="w-full">
			{#if currentStep >= 5}
				<div
					class="step-content py-4"
					transition:slide|local={{ duration: 300, axis: 'y' }}
					role="region"
					aria-labelledby="step5-heading"
					onintroend={() => scrollToComfortableView('step5-project-platform')}
				>
					<Form.Fieldset {form} name="project_platform" class="w-full">
						<div class="mb-4">
							<Form.Legend class="text-lg font-semibold">Step 5: Project Platform</Form.Legend>
							<Form.Description
								>Select the specific platform for your chosen project type.</Form.Description
							>
						</div>
						{#if projectPlatforms.length > 0}
							<RadioGroup.Root
								bind:value={$formData.project_platform}
								class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"
								name="project_platform"
							>
								{#each projectPlatforms as pPlatform (pPlatform.id)}
									{@const typeIdStr = String(pPlatform.id)}
									<div class="relative">
										<Form.Control>
											{#snippet children({ props: controlProps })}
												<RadioGroup.Item
													{...controlProps}
													value={typeIdStr}
													id={`type-${typeIdStr}`}
													class="peer absolute inset-0 z-10 h-full w-full cursor-pointer opacity-0"
												/>

												<Form.Label
													for={`type-${typeIdStr}`}
													aria-hidden="true"
													class="bg-popover border-muted hover:border-primary/50 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary peer-data-[state=checked]:bg-primary/10 peer-data-[state=checked]:ring-primary flex h-full
												   flex-col items-center justify-start
												   rounded-md border-2 p-4 text-center
												   transition-colors duration-150 ease-in-out peer-data-[state=checked]:ring-0 peer-data-[state=checked]:ring-offset-2"
												>
													{#if pPlatform.icon?.full_url}
														<img
															src={pPlatform.icon.full_url}
															alt=""
															class="mb-3 h-10 w-10 transition-transform duration-150 ease-in-out peer-data-[state=checked]:scale-110"
														/>
													{/if}
													<span class="mb-1 text-sm font-semibold">{pPlatform.name}</span>
													{#if pPlatform.description}
														<p class="text-muted-foreground text-xs">{pPlatform.description}</p>
													{/if}
												</Form.Label>
											{/snippet}
										</Form.Control>
									</div>
								{/each}
							</RadioGroup.Root>
						{:else}
							<p class="text-muted-foreground text-sm">
								{#if !$formData.project_platform}
									Please select a project type first.
								{:else}
									No project platforms available to select.
								{/if}
							</p>
						{/if}
						<Form.FieldErrors class="mt-3" />
					</Form.Fieldset>

					{#if currentStep === 5}
						<div class="mt-6 flex justify-end pb-8">
							<Button type="button" onclick={() => advanceToStep(6)}>Continue</Button>
						</div>
					{/if}
				</div>
			{/if}
		</section>

		<section id="step6-ad-network" class="w-full">
			{#if currentStep >= 6}
				<div
					class="step-content py-4"
					transition:slide|local={{ duration: 300, axis: 'y' }}
					role="region"
					aria-labelledby="step6-heading"
					onintroend={() => scrollToComfortableView('step6-ad-network')}
				>
					<Form.Fieldset {form} name="ad_networks" class="w-full">
						<div class="mb-4">
							<Form.Legend class="text-lg font-semibold">Step 6: Project Ad-Networks</Form.Legend>
							<Form.Description
								>Select the ad networks for your project. At least one is required.</Form.Description
							>
						</div>
						{#if data.adNetworks.length > 0}
							<div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
								{#each data.adNetworks as pType (pType.id)}
									{@const typeIdStr = String(pType.id)}
									{@const checked = $formData?.ad_networks?.includes(typeIdStr)}

									<div class="relative">
										<Form.Control>
											{#snippet children({ props: checkboxProps })}
												<Checkbox
													{...checkboxProps}
													id={`ad-network-${typeIdStr}`}
													aria-labelledby={`ad-network-label-${typeIdStr}`}
													class="peer absolute z-[-1] opacity-0"
													{checked}
													onCheckedChange={(isChecked) => {
														if (isChecked) {
															addAdNetwork(typeIdStr);
														} else {
															removeAdNetwork(typeIdStr);
														}
													}}
												/>
											{/snippet}
										</Form.Control>

										<label
											for={`ad-network-${typeIdStr}`}
											id={`ad-network-label-${typeIdStr}`}
											class="bg-popover border-muted hover:border-primary/50 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary peer-data-[state=checked]:bg-primary/10 peer-data-[state=checked]:ring-primary flex h-full cursor-pointer
                                                   flex-col items-center justify-start
                                                   rounded-md border-2 p-4 text-center
                                                   transition-colors duration-150 ease-in-out peer-data-[state=checked]:ring-0 peer-data-[state=checked]:ring-offset-2"
										>
											{#if pType.icon?.full_url}
												<img
													src={pType.icon.full_url}
													alt=""
													class="mb-3 h-10 w-10 transition-transform duration-150 ease-in-out peer-data-[state=checked]:scale-110"
												/>
											{/if}
											<span class="mb-1 text-sm font-semibold">{pType.name}</span>
											{#if pType.description}
												<p class="text-muted-foreground text-xs">{pType.description}</p>
											{/if}
										</label>
									</div>
								{/each}
							</div>
						{:else}
							<p class="text-muted-foreground text-sm">No ad networks available to select.</p>
						{/if}
						<Form.FieldErrors class="mt-3" />
					</Form.Fieldset>
					{#if currentStep === TOTAL_STEPS}
						<div class="mt-8 flex justify-end pb-24">
							<Form.Button onclick={submit} disabled={$submitting}>
								{#if $submitting}
									Submitting...
								{:else}
									Create Project
								{/if}
							</Form.Button>
						</div>
					{/if}
					<Separator class="my-8" />
				</div>
			{/if}
		</section>
	</form>
</div>

<style>
	.form-group {
		margin-bottom: 1rem;
		display: flex;
		flex-direction: column;
	}

	@media (min-width: 768px) {
		.form-group {
			flex-direction: row;
		}
	}
</style>
