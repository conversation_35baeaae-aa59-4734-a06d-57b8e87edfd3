import { zod4 } from 'sveltekit-superforms/adapters';
import type { PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms';
import { callViaRouteName } from '$lib/server/repl';

import { createWorkspaceSchema } from './formSchemas';
import { redirect } from '@sveltejs/kit';

export const actions = callViaRouteName([
	{
		name: 'create_workspace',
		method: 'POST',
		allowCookies: true,
		schema: createWorkspaceSchema,
		namespace: 'workspaces'
	}
]);

export const load: PageServerLoad = async ({ url, fetch, locals, depends }) => {
	depends('app:user');
	depends('app:workspaces');
	depends('app:currentOrganization');

	if (!locals.user) {
		redirect(302, '/');
	}
	const offset = url.searchParams.get('offset') ?? '0';
	const limit = url.searchParams.get('limit') ?? '10';
	const search = url.searchParams.get('search') ?? '';
	const status = url.searchParams.get('status') ?? '';

	const currentOrganizationID = locals.currentOrganizationID;

	const getWorkspaces = async () => {
		const relativePath = '/spark/api/v1/workspaces/';

		const absoluteUrl = new URL(relativePath, url.origin);

		absoluteUrl.searchParams.set('offset', offset);
		absoluteUrl.searchParams.set('limit', limit);
		absoluteUrl.searchParams.set('search', search);
		absoluteUrl.searchParams.set('status', status);

		if (currentOrganizationID) {
			absoluteUrl.searchParams.set('organization_id', currentOrganizationID);
		}

		return fetch(absoluteUrl).then(async (res) => {
			if (!res.ok) {
				console.error('Failed to fetch workspaces:', await res.text());
				return {
					error: 'Failed to load workspaces'
				};
			} else {
				return res.json();
			}
		});
	};
	return {
		user: locals.user,
		form: await superValidate(zod4(createWorkspaceSchema)),
		workspaces: await getWorkspaces(),
		organizations: locals.organizations,
		currentOrganization: locals.currentOrganization
	};
};
