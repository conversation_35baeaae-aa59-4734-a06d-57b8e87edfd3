<script lang="ts">
	import { type SuperValidated, type Infer, superForm } from 'sveltekit-superforms';
	import LoaderCircleIcon from '@lucide/svelte/icons/loader-circle';

	import { zodClient } from 'sveltekit-superforms/adapters';
	import { toast } from 'svelte-sonner';

	import * as Dialog from '$lib/shad-components/ui/dialog/index.js';
	import * as Form from '$lib/shad-components/ui/form/index.js';
	import { Input } from '$lib/shad-components/ui/input/index.js';
	import * as TextArea from '$lib/shad-components/ui/textarea/index.js';
	import CircleAlertIcon from '@lucide/svelte/icons/circle-alert';
	import * as Alert from '$lib/shad-components/ui/alert/index.js';

	import { createWorkspaceSchema, type CreateWorkspaceSchema } from './formSchemas';
	import Button from '$lib/shad-components/ui/button/button.svelte';
	import { goto } from '$app/navigation';
	import type { Workspace } from '$lib/interfaces/workspace';
	import { page } from '$app/state';
	import type { Organization } from '$lib/api/organizations';
	import { currentOrganizationPersistedStore } from '$lib/stores/organization.svelte';

	let {
		initialSuperForm,
		onSuccessClose
	}: {
		initialSuperForm: SuperValidated<Infer<CreateWorkspaceSchema>>;
		onSuccessClose?: () => void;
	} = $props();

	const isEditing = $derived(!!initialSuperForm.data.id);

	let modalCloseButton: HTMLButtonElement = $state('');

	const form = superForm(initialSuperForm, {
		dataType: 'form',
		validators: zodClient(createWorkspaceSchema),

		onResult(event) {
			if (event.result.type !== 'success') {
				return;
			}
			const actionVerb = isEditing ? 'Updated' : 'Created';
			toast.success(`Workspace ${actionVerb}`, {
				description: isEditing
					? 'Your changes have been saved.'
					: "Redirecting you to the workspace's page."
			});

			onSuccessClose?.();

			if (!isEditing && event.result.data?.data?.id) {
				const newWorkspaceId = event.result.data.data.id;
				setTimeout(() => {
					goto(`/workspaces/${newWorkspaceId}`);
				}, 2000);
			}
			modalCloseButton?.click();
		},
		onError(event) {
			toast.error('Submission Error', {
				description: event.result.error.message || 'Please check the form for errors.'
			});
		}
	});

	const { form: formData, enhance, submitting, submit, errors } = form;

	let currentOrganization: Organization = $derived(currentOrganizationPersistedStore.current);
</script>

<form use:enhance method="POST" action="?/create_workspace">
	{#if $formData.id}
		<input hidden bind:value={$formData.id} name="id" type="text" />
	{/if}

	<input hidden value={currentOrganization?.id} name="organization_id" type="text" />

	<Dialog.Header>
		<Dialog.Title>
			{isEditing ? 'Edit Workspace' : 'Create Workspace'}
		</Dialog.Title>
		<Dialog.Description>
			{#if $errors._errors}
				{#each $errors._errors as error (error)}
					<Alert.Root variant="destructive">
						<CircleAlertIcon class="size-4" />
						<Alert.Title>Error</Alert.Title>
						<Alert.Description>{error}</Alert.Description>
					</Alert.Root>
				{/each}
			{:else if $formData.id}
				Edit Workspace. Click save when you're done.
			{:else}
				Create a new Workspace. Click save when you're done.
			{/if}
		</Dialog.Description>
	</Dialog.Header>

	<div class="mt-6 flex flex-col gap-10">
		<Form.Field {form} name="title">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>Workspace Name</Form.Label>
					<Input {...props} bind:value={$formData.title} disabled={$submitting} />
				{/snippet}
			</Form.Control>
			<Form.Description>Workspace name.</Form.Description>
			<Form.FieldErrors />
		</Form.Field>
		<Form.Field {form} name="description">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>Workspace Description</Form.Label>
					<TextArea.Textarea {...props} disabled={$submitting} bind:value={$formData.description} />
				{/snippet}
			</Form.Control>
			<Form.Description>Workspace Description.</Form.Description>
			<Form.FieldErrors />
		</Form.Field>
	</div>

	<!-- <input hidden bind:value={localForm.id} name="id" type="text" /> -->
</form>

<Dialog.Footer class="gap-2 pt-6 sm:gap-4">
	<div class="w-1/2">
		<Dialog.Close bind:ref={modalCloseButton} class="w-full">
			<Button class="w-full" disabled={$submitting} variant="outline">Cancel</Button>
		</Dialog.Close>
	</div>
	<div class="w-1/2">
		<Button
			class="animate-in fade-in zoom-in w-full"
			disabled={$submitting}
			onclick={() => submit()}
		>
			{#if $submitting}
				<LoaderCircleIcon class="mr-2 size-4 animate-spin" />
				{isEditing ? 'Updating...' : 'Creating...'}
			{:else}
				{isEditing ? 'Save Changes' : 'Create Workspace'}
			{/if}
		</Button>
	</div>
</Dialog.Footer>
