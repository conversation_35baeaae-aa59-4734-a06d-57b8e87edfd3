<script lang="ts">
	import { page } from '$app/state';
	import { onMount } from 'svelte';
	import { SvelteURLSearchParams } from 'svelte/reactivity';

	import Plus from '@lucide/svelte/icons/plus';

	import * as Dialog from '$lib/shad-components/ui/dialog/index.js';
	import Pagination from '$lib/shared-ui/Pagination.svelte';
	import LoaderCircleIcon from '@lucide/svelte/icons/loader-circle';

	import { setSlots } from '../layout.slots.svelte';
	import CreateEditWorkspaceModal from './CreateEditWorkspaceModal.svelte';
	import Button from '$lib/shad-components/ui/button/button.svelte';
	import * as Select from '$lib/shad-components/ui/select/index.js';

	import { type PageData } from './$types';
	import Workspacecard from './Workspacecard.svelte';
	import SearchForm from '$lib/shad-components/search-form.svelte';
	import { globalBreadcrumbs, globalNavLinks } from '$lib/stores/side-nav.svelte';

	import House from '@lucide/svelte/icons/house';
	import { HouseIcon } from '$lib/shared-ui/layoutExports';
	import { goto } from '$app/navigation';

	let { data }: { data: PageData } = $props();

	let workspaceItems = $derived(data?.workspaces?.data?.items ?? []);
	let workspaceData = $derived(data?.workspaces?.data);

	let limit = $derived(workspaceData?.limit ?? 10);
	let offset = $derived(workspaceData?.offset ?? 0);
	let total_items = $derived(workspaceData?.total_items ?? 0);

	let currentPage = $derived(Math.floor(offset / limit) + 1);
	let currentOrganizationId = $derived(page.data?.currentOrganization?.id ?? '');

	// Initialize filters from URL params
	let search = $state(page.url.searchParams.get('search') ?? '');
	let selectedWorkspaceStatus = $state(page.url.searchParams.get('status') ?? '');

	// Handle page changes
	function handlePageChange(newPage: number) {
		const params = new SvelteURLSearchParams(page.url.searchParams);
		params.set('offset', ((newPage - 1) * limit).toString());
		goto(`/dashboard?${params.toString()}`, { replaceState: true });
	}

	// Apply all filters and reset to first page
	function applyFilters() {
		const params = new SvelteURLSearchParams();

		if (search.trim()) {
			params.append('search', search.trim());
		}
		if (selectedWorkspaceStatus) {
			params.append('status', selectedWorkspaceStatus);
		}
		if (currentOrganizationId) {
			params.append('organization_id', currentOrganizationId);
		}

		// Reset to first page when filtering
		params.append('offset', '0');
		params.append('limit', limit.toString());

		// Navigate to update the URL and trigger server reload
		goto(`/dashboard?${params.toString()}`, { replaceState: true });
	}

	setSlots({
		ActionArea,
		Footer: PaginationFooter
	});

	onMount(() => {
		globalBreadcrumbs.crumbs = [
			{
				title: HouseIcon,
				href: '/dashboard'
			}
		];

		globalNavLinks.links = [
			{
				title: 'Dashboard',
				url: '/dashboard',
				icon: House
			}
		];
	});

	const status = [
		{ value: 'all', label: 'All' },
		{ value: 'archived', label: 'Archived' },
		{ value: 'draft', label: 'Draft' },
		{ value: 'deployed', label: 'Deployed' }
	];

	let workspaceFetching = $state(false);

	const triggerContent = $derived(
		status.find((f) => f.value === selectedWorkspaceStatus)?.label ?? 'Workspace Status'
	);

	const handleSearch = (e: Event) => {
		e.preventDefault();
		const form = e.target as HTMLFormElement;
		const formData = new FormData(form);
		search = formData.get('search') as string;
		applyFilters();
	};

	// Watch for organization changes and reset filters
	$effect(() => {
		// When organization changes, reset to first page with current filters
		if (currentOrganizationId) {
			const currentOrgFromUrl = page.url.searchParams.get('organization_id');
			if (currentOrgFromUrl !== currentOrganizationId) {
				applyFilters();
			}
		}
	});
</script>

{#snippet ActionArea()}
	<div class="flex flex-col-reverse gap-4 lg:flex-row">
		<div class="flex gap-4">
			{#if workspaceFetching}
				<div class="my-auto flex h-full items-center justify-center">
					<LoaderCircleIcon class="animate-spin" />
				</div>
			{/if}
			<Select.Root
				type="single"
				disabled={workspaceFetching}
				name="workspace_status"
				bind:value={selectedWorkspaceStatus}
				onValueChange={() => {
					applyFilters();
				}}
			>
				<Select.Trigger class="w-[180px]">
					{triggerContent}
				</Select.Trigger>
				<Select.Content>
					<Select.Group>
						<Select.Label>Workspace Status</Select.Label>
						{#each status as fruit (fruit.value)}
							<Select.Item value={fruit.value} label={fruit.label}>
								{fruit.label}
							</Select.Item>
						{/each}
					</Select.Group>
				</Select.Content>
			</Select.Root>

			<Dialog.Root>
				<Dialog.Trigger disabled={workspaceFetching}>
					<Button>
						<Plus />
						New Workspace
					</Button>
				</Dialog.Trigger>
				<Dialog.Content class="sm:max-w-[425px]">
					<svelte:boundary>
						<CreateEditWorkspaceModal initialSuperForm={data.form} />
						{#snippet failed(error, reset)}
							{console.error(error)}
							<button onclick={reset}>oop! Something went wrong. Try again</button>
						{/snippet}
					</svelte:boundary>
				</Dialog.Content>
			</Dialog.Root>
		</div>

		<SearchForm onsubmit={handleSearch} class="w-full  sm:w-auto" />
	</div>
{/snippet}

{#snippet PaginationFooter()}
	<Pagination
		fetching={workspaceFetching}
		{total_items}
		{limit}
		page={currentPage}
		onPageChange={handlePageChange}
	/>
{/snippet}

<div class="workspaces m-4 md:m-12">
	{#if workspaceItems?.length > 0}
		{#each workspaceItems as workspace, index (workspace?.id)}
			<Workspacecard
				bind:operationInProgress={workspaceFetching}
				bind:workspace={workspaceItems[index]}
				{data}
			/>
		{/each}
	{/if}
</div>

<style>
	.workspaces {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
		grid-gap: 2rem;
		overflow: hidden;
		overflow-y: auto;
	}

	@media screen and (min-width: 1200px) {
		.workspaces {
			grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
		}
	}

	@media screen and (min-width: 1600px) {
		.workspaces {
			grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
		}
	}
</style>
