<script lang="ts">
	import { Button } from '$lib/shad-components/ui/button';
	import * as Dialog from '$lib/shad-components/ui/dialog';
	import { Badge } from '$lib/shad-components/ui/badge';
	import * as Avatar from '$lib/shad-components/ui/avatar';
	import * as Card from '$lib/shad-components/ui/card';
	import { Separator } from '$lib/shad-components/ui/separator';
	import { type PageData } from './$types';
	import CreateEditWorkspaceModal from './CreateEditWorkspaceModal.svelte';
	import { type Workspace } from '$lib/interfaces/workspace';
	import type { SuperValidated, Infer } from 'sveltekit-superforms';
	import type { CreateWorkspaceSchema } from './formSchemas';
	import * as AlertDialog from '$lib/shad-components/ui/alert-dialog/index.js';
	import { buttonVariants } from '$lib/shad-components/ui/button/index.js';
	import { toast } from 'svelte-sonner';
	import { goto } from '$app/navigation';
	import { invalidate } from '$app/navigation';

	let {
		workspace = $bindable(),
		class: additionalClasses = '',
		data,
		operationInProgress = $bindable(false)
	}: { workspace: any; class?: string; data: PageData; operationInProgress: boolean } = $props();

	let isArchived = $derived(workspace?.status === 'archived');

	const makeSureThereIsAZeroInFront = (num: number): string => {
		return num < 10 ? `0${num}` : `${num}`;
	};

	const dateWithSlashes = (dateString: string | undefined): string => {
		if (!dateString) return '';
		return dateString.split('T')[0].split('-').reverse().join('/');
	};

	const onlyDate = (dateString: string | undefined): string => {
		if (!dateString) return '';
		return dateWithSlashes(dateString.split('T')[0]);
	};

	function prepareEditSuperForm(
		workspaceToEdit: Workspace,
		baseCreationForm: SuperValidated<Infer<CreateWorkspaceSchema>>
	): SuperValidated<Infer<CreateWorkspaceSchema>> {
		const formData: Infer<CreateWorkspaceSchema> = {
			id: String(workspaceToEdit.id), // Important: ID for editing
			title: workspaceToEdit.title,
			description: workspaceToEdit.description ?? '' // Ensure schema compliance
			// Ensure all fields defined in CreateWorkspaceSchema are present here
		};

		return {
			id: workspaceToEdit.id,
			...formData,
			data: formData,
			errors: {}, // Start with no errors for an edit form
			constraints: baseCreationForm.constraints, // Reuse constraints
			message: undefined, // Clear any previous messages
			tainted: undefined // Consider if you want to reset tainted state
		};
	}

	const handleArchive = async () => {
		if (operationInProgress) return;
		operationInProgress = true;

		try {
			const response = await fetch(`/spark/api/v1/workspaces/${workspace.id}/`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ data: { status: 'archived' } })
			});

			if (response.ok) {
				workspace.status = 'archived';
				toast.info('Archive operation successful');
				closeDeleteModal?.click();
				// Invalidate the workspaces data to trigger a refresh
				await invalidate('app:workspaces');
			} else {
				throw new Error('Archive failed');
			}
		} catch (err) {
			console.error(err);
			toast.error('Archive operation failed');
		} finally {
			operationInProgress = false;
		}
	};

	const handleDelete = async () => {
		if (operationInProgress) return;
		operationInProgress = true;

		try {
			const response = await fetch(`/spark/api/v1/workspaces/${workspace.id}/`, {
				method: 'DELETE'
			});

			if (response.ok) {
				workspace = null;
				toast.info('Delete operation successful');
				// Close the modal
				closeDeleteModal?.click();
				// Invalidate the workspaces data to trigger a refresh
				await invalidate('app:workspaces');
			} else {
				throw new Error('Delete failed');
			}
		} catch (err) {
			console.error(err);
			toast.error('Delete operation failed');
		} finally {
			operationInProgress = false;
		}
	};

	const restoreWorkspace = async () => {
		if (operationInProgress) return;
		operationInProgress = true;

		try {
			const response = await fetch(`/spark/api/v1/workspaces/${workspace.id}/`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ data: { status: 'draft' } })
			});

			if (response.ok) {
				workspace.status = 'draft';
				toast.info('Restore operation successful');
				// Invalidate the workspaces data to trigger a refresh
				await invalidate('app:workspaces');
			} else {
				throw new Error('Restore failed');
			}
		} catch (err) {
			console.error(err);
			toast.error('Restore operation failed');
		} finally {
			operationInProgress = false;
		}
	};

	let closeDeleteModal = $state(null);
</script>

{#snippet RefreshIcon(props = { class: 'h-4 w-4' })}
	<svg
		width="20"
		height="20"
		viewBox="0 0 20 20"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		class={props.class}
	>
		<path
			d="M1.66699 8.33333C1.66699 8.33333 3.33781 6.05685 4.69519 4.69854C6.05257 3.34022 7.92832 2.5 10.0003 2.5C14.1425 2.5 17.5003 5.85786 17.5003 10C17.5003 14.1421 14.1425 17.5 10.0003 17.5C6.58108 17.5 3.69625 15.2119 2.79346 12.0833M1.66699 8.33333V3.33333M1.66699 8.33333H6.66699"
			stroke="currentColor"
			stroke-width="1.67"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</svg>
{/snippet}

{#snippet NavIcon(props = { class: 'h-5 w-5' })}
	<svg
		width="24"
		height="24"
		viewBox="0 0 24 24"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		class={props.class}
	>
		<path
			d="M6 18L18 6M18 6H10M18 6V14"
			stroke="currentColor"
			stroke-width="1.67"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</svg>
{/snippet}

{#snippet CopyIcon(props = { class: 'h-4 w-4' })}
	<svg
		width="17"
		height="18"
		viewBox="0 0 17 18"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		class={props.class}
	>
		<path
			d="M7.625 1.5021C7.11871 1.50896 6.81477 1.53827 6.56901 1.66349C6.28677 1.8073 6.0573 2.03677 5.91349 2.31901C5.78827 2.56477 5.75896 2.86871 5.7521 3.375M14.375 1.5021C14.8813 1.50896 15.1852 1.53827 15.431 1.66349C15.7132 1.8073 15.9427 2.03677 16.0865 2.31901C16.2117 2.56477 16.241 2.86871 16.2479 3.37499M16.2479 10.125C16.241 10.6313 16.2117 10.9352 16.0865 11.181C15.9427 11.4632 15.7132 11.6927 15.431 11.8365C15.1852 11.9617 14.8813 11.991 14.375 11.9979M16.25 5.99999V7.49999M10.25 1.5H11.75M3.65 16.5H9.35C10.1901 16.5 10.6101 16.5 10.931 16.3365C11.2132 16.1927 11.4427 15.9632 11.5865 15.681C11.75 15.3601 11.75 14.9401 11.75 14.1V8.4C11.75 7.55992 11.75 7.13988 11.5865 6.81901C11.4427 6.53677 11.2132 6.3073 10.931 6.16349C10.6101 6 10.1901 6 9.35 6H3.65C2.80992 6 2.38988 6 2.06901 6.16349C1.78677 6.3073 1.5573 6.53677 1.41349 6.81901C1.25 7.13988 1.25 7.55992 1.25 8.4V14.1C1.25 14.9401 1.25 15.3601 1.41349 15.681C1.5573 15.9632 1.78677 16.1927 2.06901 16.3365C2.38988 16.5 2.80992 16.5 3.65 16.5Z"
			stroke="currentColor"
			stroke-width="1.5"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</svg>
{/snippet}

{#snippet EditIcon(props = { class: 'h-4 w-4' })}
	<svg
		width="17"
		height="18"
		viewBox="0 0 17 18"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		class={props.class}
	>
		<path
			d="M7.5 3.00017H4.35C3.08988 3.00017 2.45982 3.00017 1.97852 3.24541C1.55516 3.46112 1.21095 3.80533 0.995235 4.22869C0.75 4.70999 0.75 5.34005 0.75 6.60017V12.9002C0.75 14.1603 0.75 14.7903 0.995235 15.2716C1.21095 15.695 1.55516 16.0392 1.97852 16.2549C2.45982 16.5002 3.08988 16.5002 4.35 16.5002H10.65C11.9101 16.5002 12.5402 16.5002 13.0215 16.2549C13.4448 16.0392 13.789 15.695 14.0048 15.2716C14.25 14.7903 14.25 14.1603 14.25 12.9002V9.75017M5.24998 12.0002H6.50589C6.87277 12.0002 7.05622 12.0002 7.22885 11.9587C7.3819 11.922 7.52822 11.8614 7.66243 11.7791C7.8138 11.6864 7.94352 11.5567 8.20294 11.2972L15.375 4.12517C15.9963 3.50385 15.9963 2.49649 15.375 1.87517C14.7537 1.25385 13.7463 1.25385 13.125 1.87517L5.95293 9.04723C5.6935 9.30665 5.56378 9.43637 5.47102 9.58774C5.38878 9.72195 5.32817 9.86827 5.29143 10.0213C5.24998 10.194 5.24998 10.3774 5.24998 10.7443V12.0002Z"
			stroke="currentColor"
			stroke-width="1.5"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</svg>
{/snippet}

{#snippet ArchiveIcon(props = { class: 'h-4 w-4' })}
	<svg
		width="17"
		height="16"
		viewBox="0 0 17 16"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		class={props.class}
	>
		<path
			d="M2.75 4.99745C2.62699 4.99427 2.53767 4.98715 2.45736 4.97118C1.86233 4.85282 1.39718 4.38767 1.27882 3.79264C1.25 3.64774 1.25 3.47349 1.25 3.125C1.25 2.77651 1.25 2.60226 1.27882 2.45736C1.39718 1.86233 1.86233 1.39718 2.45736 1.27882C2.60226 1.25 2.77651 1.25 3.125 1.25H14.375C14.7235 1.25 14.8977 1.25 15.0426 1.27882C15.6377 1.39718 16.1028 1.86233 16.2212 2.45736C16.25 2.60226 16.25 2.77651 16.25 3.125C16.25 3.47349 16.25 3.64774 16.2212 3.79264C16.1028 4.38767 15.6377 4.85282 15.0426 4.97118C14.9623 4.98715 14.873 4.99427 14.75 4.99745M7.25 8.75H10.25M2.75 5H14.75V11.15C14.75 12.4101 14.75 13.0402 14.5048 13.5215C14.289 13.9448 13.9448 14.289 13.5215 14.5048C13.0402 14.75 12.4101 14.75 11.15 14.75H6.35C5.08988 14.75 4.45982 14.75 3.97852 14.5048C3.55516 14.289 3.21095 13.9448 2.99524 13.5215C2.75 13.0402 2.75 12.4101 2.75 11.15V5Z"
			stroke="currentColor"
			stroke-width="1.5"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</svg>
{/snippet}

{#snippet DeleteIcon(props = { class: 'h-4 w-4' })}
	<svg
		width="16"
		height="18"
		viewBox="0 0 16 18"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		class={props.class}
	>
		<path
			d="M11.25 4.5V3.9C11.25 3.05992 11.25 2.63988 11.0865 2.31901C10.9427 2.03677 10.7132 1.8073 10.431 1.66349C10.1101 1.5 9.69008 1.5 8.85 1.5H7.65C6.80992 1.5 6.38988 1.5 6.06901 1.66349C5.78677 1.8073 5.5573 2.03677 5.41349 2.31901C5.25 2.63988 5.25 3.05992 5.25 3.9V4.5M6.75 8.625V12.375M9.75 8.625V12.375M1.5 4.5H15M13.5 4.5V12.9C13.5 14.1601 13.5 14.7902 13.2548 15.2715C13.039 15.6948 12.6948 16.039 12.2715 16.2548C11.7902 16.5 11.1601 16.5 9.9 16.5H6.6C5.33988 16.5 4.70982 16.5 4.22852 16.2548C3.80516 16.039 3.46095 15.6948 3.24524 15.2715C3 14.7902 3 14.1601 3 12.9V4.5"
			stroke="currentColor"
			stroke-width="1.5"
			stroke-linecap="round"
			stroke-linejoin="round"
		/>
	</svg>
{/snippet}

{#snippet Edit(workspace: Workspace)}
	<Dialog.Root>
		<Dialog.Trigger
			class={[
				buttonVariants({ variant: 'outline' }),
				'group-hover:border-primary hover:bg-primary/5 flex-1'
			]}
		>
			{@render EditIcon({ class: 'h-4 w-4 mr-1 md:mr-2' })}
			<span class="hidden sm:inline">Edit</span>
		</Dialog.Trigger>
		<Dialog.Content class="sm:max-w-[425px]">
			<svelte:boundary>
				<CreateEditWorkspaceModal initialSuperForm={prepareEditSuperForm(workspace, data.form)} />
				{#snippet failed(error, reset)}
					{console.error(error)}
					<button onclick={reset}>oop! Something went wrong. Try again</button>
				{/snippet}
			</svelte:boundary>
		</Dialog.Content>
	</Dialog.Root>
{/snippet}

{#snippet Delete(workspace: Workspace, perm: boolean = false)}
	<AlertDialog.Root>
		<AlertDialog.Trigger
			class={[
				buttonVariants({ variant: 'outline', size: 'sm' }),
				'group-hover:border-destructive group-hover:text-destructive hover:bg-destructive/10 text-destructive/90 border-destructive/50 flex-1'
			]}
		>
			{@render DeleteIcon({ class: 'h-4 w-4 mr-1 md:mr-2' })}
			<span class="hidden sm:inline">Delete {`${perm ? 'permanently' : ''}`}</span>
		</AlertDialog.Trigger>
		<AlertDialog.Content>
			<AlertDialog.Header>
				<AlertDialog.Title>Delete Workspace</AlertDialog.Title>
				<AlertDialog.Description>
					This action cannot be undone. This will permanently delete your workspace and remove your
					data from our servers.
				</AlertDialog.Description>
			</AlertDialog.Header>
			<AlertDialog.Footer>
				<div class="flex w-full justify-between gap-2">
					{#if !perm}
						<Button onclick={handleArchive}>
							{@render ArchiveIcon({ class: 'h-4 w-4 mr-1 md:mr-2' })}
							<span class="hidden sm:inline">Archive instead</span>
						</Button>
					{/if}
					<div>
						<AlertDialog.Cancel bind:ref={closeDeleteModal}>Cancel</AlertDialog.Cancel>
						<AlertDialog.Action
							onclick={handleDelete}
							class={buttonVariants({ variant: 'destructive', size: 'sm' })}
							>Continue</AlertDialog.Action
						>
					</div>
				</div>
			</AlertDialog.Footer>
		</AlertDialog.Content>
	</AlertDialog.Root>
{/snippet}

{#if workspace}
	<Card.Root
		class="group h-max rounded-2xl shadow-sm transition-all {isArchived
			? 'bg-muted/60 border-muted-foreground/30 hover:border-muted-foreground/50'
			: 'border-border bg-gradient-to-br from-blue-500/10 via-blue-600/10 to-blue-700/20 hover:shadow-md dark:from-blue-500/5 dark:via-blue-600/5 dark:to-blue-700/10'} {additionalClasses}"
	>
		<Card.Header class="flex flex-row items-center justify-between p-4">
			<div class="flex -space-x-3">
				{#each [1, 2, 3] as _, i}
					<Avatar.Root
						class="border-background 20 h-11 w-11 border-2 transition-colors {isArchived
							? '!border-muted-foreground/20'
							: ''}"
					>
						<Avatar.Fallback
							class={isArchived
								? 'bg-muted-foreground/10 text-muted-foreground'
								: 'bg-background text-foreground'}
						>
							WIX
						</Avatar.Fallback>
					</Avatar.Root>
				{/each}
			</div>
			<div class="navigation">
				{#if isArchived}
					<Button
						onclick={restoreWorkspace}
						variant="outline"
						size="sm"
						class="group-hover:text-primary text-xs {isArchived
							? 'border-muted-foreground/50 text-muted-foreground hover:bg-muted hover:text-foreground'
							: 'text-primary border-primary/50 hover:bg-primary/5 hover:border-primary'}"
					>
						{@render RefreshIcon({ class: 'h-4 w-4 mr-2' })}
						Restore Workspace
					</Button>
				{:else}
					<Button
						onclick={() => goto(`/workspaces/${workspace.id}/`)}
						variant="outline"
						size="icon"
						class="border-primary/50 text-primary group-hover:bg-primary/5 group-hover:text-primary h-11 w-11 rounded-full transition-colors"
					>
						<span class="sr-only">Navigate to workspace</span>
						{@render NavIcon({ class: 'h-5 w-5' })}
					</Button>
				{/if}
			</div>
		</Card.Header>

		<Card.Content class="p-4 pt-0">
			<div class="title mb-2">
				<Card.Title
					class="text-2xl font-semibold {isArchived
						? 'text-muted-foreground group-hover:text-foreground/80'
						: 'text-card-foreground group-hover:text-primary'} transition-colors"
				>
					{workspace.title}
				</Card.Title>
				<Badge
					variant={isArchived ? 'outline' : 'default'}
					class="50 ml-2 whitespace-nowrap {isArchived
						? 'border-muted-foreground/50 text-muted-foreground group-hover:text-foreground/80'
						: 'bg-primary/10 text-primary group-hover:text-primary group-hover:bg-primary/20'} transition-colors"
				>
					{makeSureThereIsAZeroInFront(workspace?.project_count ?? 0)} Projects
				</Badge>
			</div>

			<div
				class="updated mb-4 flex items-center gap-2 text-sm {isArchived
					? 'text-muted-foreground/80'
					: 'text-muted-foreground'} group-hover:{isArchived
					? 'text-foreground/70'
					: 'text-foreground/80'} transition-colors"
			>
				{@render RefreshIcon({ class: 'h-5 w-5 opacity-70' })}
				<span>
					{isArchived ? 'ARCHIVED' : 'UPDATED'}:
					<span class="font-medium transition-all group-hover:font-semibold"
						>{onlyDate(workspace.updated_at)}</span
					>
				</span>
			</div>

			<div class="pills flex gap-2">
				<Badge
					variant="outline"
					class="border-green-500/50 bg-green-500/10 text-green-700 dark:text-green-400 {isArchived
						? '!bg-muted !text-muted-foreground !border-muted-foreground/30'
						: ''} group-hover:{isArchived ? '' : 'border-green-500 bg-green-500/20'}"
					>Deployed x 3</Badge
				>
				<Badge
					variant="outline"
					class="border-purple-500/50 bg-purple-500/10 text-purple-700 dark:text-purple-400 {isArchived
						? '!bg-muted !text-muted-foreground !border-muted-foreground/30'
						: ''} group-hover:{isArchived ? '' : 'border-purple-500 bg-purple-500/20'}"
					>Archived x 3</Badge
				>
				<Badge
					variant="secondary"
					class={isArchived
						? '!bg-muted !text-muted-foreground !border-muted-foreground/30 group-hover:!border-foreground/20'
						: 'group-hover:bg-muted/80'}>Draft x 3</Badge
				>
			</div>
		</Card.Content>

		<Separator class="mx-4 my-0 {isArchived ? 'bg-muted-foreground/30' : 'bg-border'}" />

		<Card.Footer class="p-4">
			<div
				class="flex w-full items-center {isArchived ? 'justify-center' : 'justify-between'} gap-2"
			>
				{#if isArchived}
					{@render Delete(workspace, true)}
				{:else}
					<Button
						variant="outline"
						size="sm"
						class="group-hover:text-primary group-hover:border-primary hover:bg-primary/5 flex-1"
					>
						{@render CopyIcon({ class: 'h-4 w-4 mr-1 md:mr-2' })}
						<span class="hidden sm:inline">Copy</span>
					</Button>
					{@render Edit(workspace)}
					<Button
						onclick={handleArchive}
						variant="outline"
						size="sm"
						class="hover:bg-muted/80 group-hover:text-primary group-hover:border-primary flex-1"
					>
						{@render ArchiveIcon({ class: 'h-4 w-4 mr-1 md:mr-2' })}
						<span class="hidden sm:inline">Archive</span>
					</Button>
					{@render Delete(workspace)}
				{/if}
			</div>
		</Card.Footer>
	</Card.Root>
{/if}
