import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';

interface AuditLog {
	id: string;
	user: {
		id: number;
		username: string;
		email: string;
		first_name: string;
		last_name: string;
	} | null;
	organization_id: string | null;
	content_type: {
		id: number;
		app_label: string;
		model: string;
	};
	object_id: string;
	action: string;
	description: string;
	old_values: Record<string, any> | null;
	new_values: Record<string, any> | null;
	changed_fields: string[];
	ip_address: string | null;
	user_agent: string;
	metadata: Record<string, any>;
	created_at: string;
	updated_at: string;
}

interface AuditLogsResponse {
	data: AuditLog[];
	total_items: number;
	offset: number;
	limit: number;
}

export const load: PageServerLoad = async ({ fetch, locals, url }) => {
	// Check if user has a current organization
	if (!locals.currentOrganization) {
		throw error(404, 'No organization selected');
	}

	// Get query parameters for pagination and filtering
	const offset = parseInt(url.searchParams.get('offset') || '0');
	const limit = parseInt(url.searchParams.get('limit') || '20');
	const search = url.searchParams.get('search') || '';
	const action = url.searchParams.get('action') || '';
	const user = url.searchParams.get('user') || '';

	try {
		// Build query parameters
		const params = new URLSearchParams({
			offset: offset.toString(),
			limit: limit.toString()
		});

		if (search.trim()) {
			params.append('search', search.trim());
		}
		if (action) {
			params.append('action', action);
		}
		if (user) {
			params.append('user', user);
		}

		// Fetch audit logs from the API using same-origin proxy
		const relativePath = `/spark/api/v1/audit/organizations/${locals.currentOrganization.id}/audit-logs/?${params}`;
		const absoluteUrl = new URL(relativePath, url.origin);
		const response = await fetch(absoluteUrl);

		if (!response.ok) {
			if (response.status === 403) {
				throw error(403, 'You do not have permission to view audit logs for this organization');
			}
			if (response.status === 404) {
				throw error(404, 'Organization not found');
			}
			throw error(response.status, 'Failed to fetch audit logs');
		}

		const auditData: AuditLogsResponse = await response.json();

		return {
			auditLogs: auditData.data || [],
			totalItems: auditData.total_items || 0,
			currentPage: Math.floor(offset / limit) + 1,
			limit: limit,
			filters: {
				search,
				action,
				user
			}
		};
	} catch (err) {
		console.error('Error fetching audit logs:', err);

		// If it's already a SvelteKit error, re-throw it
		if (err && typeof err === 'object' && 'status' in err) {
			throw err;
		}

		// Otherwise, throw a generic error
		throw error(500, 'Failed to load audit logs');
	}
};
