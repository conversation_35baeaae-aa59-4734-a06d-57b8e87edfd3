<script lang="ts">
	import { page } from '$app/state';
	import { goto } from '$app/navigation';

	import * as Card from '$lib/shad-components/ui/card/index.js';
	import * as Table from '$lib/shad-components/ui/table/index.js';
	import * as Badge from '$lib/shad-components/ui/badge/index.js';
	import * as Dialog from '$lib/shad-components/ui/dialog/index.js';
	import { Button } from '$lib/shad-components/ui/button';
	import { Input } from '$lib/shad-components/ui/input';
	import * as Select from '$lib/shad-components/ui/select/index.js';

	import Pagination from '$lib/shared-ui/Pagination.svelte';

	import Eye from '@lucide/svelte/icons/eye';
	import Search from '@lucide/svelte/icons/search';
	import Filter from '@lucide/svelte/icons/filter';
	import User from '@lucide/svelte/icons/user';
	import Activity from '@lucide/svelte/icons/activity';

	interface AuditLog {
		id: string;
		user: {
			id: number;
			username: string;
			email: string;
			first_name: string;
			last_name: string;
		} | null;
		organization_id: string | null;
		content_type: {
			id: number;
			app_label: string;
			model: string;
		};
		object_id: string;
		action: string;
		description: string;
		old_values: Record<string, any> | null;
		new_values: Record<string, any> | null;
		changed_fields: string[];
		ip_address: string | null;
		user_agent: string;
		metadata: Record<string, any>;
		created_at: string;
		updated_at: string;
	}

	// Get server data
	let auditLogs: AuditLog[] = $state(page.data.auditLogs || []);
	let totalItems = $state(page.data.totalItems || 0);
	let currentPage = $state(page.data.currentPage || 1);
	let limit = $state(page.data.limit || 20);

	// UI state
	let selectedLog: AuditLog | null = $state(null);
	let showDetailDialog = $state(false);

	// Filters - initialize from server data
	let searchQuery = $state(page.data.filters?.search || '');
	let actionFilter = $state<string>(page.data.filters?.action || '');
	let userFilter = $state<string>(page.data.filters?.user || '');

	const actionTypes = [
		{ value: '', label: 'All Actions' },
		{ value: 'create', label: 'Create' },
		{ value: 'update', label: 'Update' },
		{ value: 'delete', label: 'Delete' },
		{ value: 'view', label: 'View' },
		{ value: 'deploy', label: 'Deploy' },
		{ value: 'archive', label: 'Archive' },
		{ value: 'restore', label: 'Restore' }
	];

	function handlePageChange(newPage: number) {
		const params = new URLSearchParams(page.url.searchParams);
		params.set('offset', ((newPage - 1) * limit).toString());
		goto(`/teams/audit?${params.toString()}`);
	}

	function applyFilters() {
		const params = new URLSearchParams();

		if (searchQuery.trim()) {
			params.append('search', searchQuery.trim());
		}
		if (actionFilter) {
			params.append('action', actionFilter);
		}
		if (userFilter.trim()) {
			params.append('user', userFilter.trim());
		}

		// Reset to first page when filtering
		params.append('offset', '0');
		params.append('limit', limit.toString());

		// Navigate to update the URL and trigger server reload
		goto(`/teams/audit?${params.toString()}`);
	}

	function handleSearchKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			applyFilters();
		}
	}

	async function viewLogDetail(log: AuditLog) {
		selectedLog = log;
		showDetailDialog = true;
	}

	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleString();
	}

	function formatUser(user: AuditLog['user']) {
		if (!user) return 'System';
		return user.first_name && user.last_name
			? `${user.first_name} ${user.last_name}`
			: user.username || user.email;
	}

	function getActionBadgeVariant(action: string) {
		switch (action.toLowerCase()) {
			case 'create':
				return 'default';
			case 'update':
				return 'secondary';
			case 'delete':
				return 'destructive';
			case 'deploy':
				return 'default';
			default:
				return 'outline';
		}
	}

	function formatObjectType(contentType: AuditLog['content_type']) {
		return contentType.model.charAt(0).toUpperCase() + contentType.model.slice(1);
	}
</script>

<div class="container mx-auto space-y-6 p-6">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold">Audit Logs</h1>
			<p class="text-muted-foreground">Track all changes and activities in your organization</p>
		</div>
		<div class="flex items-center gap-2">
			<Activity class="h-5 w-5" />
			<span class="text-sm font-medium">
				{page.data.currentOrganization?.name || 'Organization'}
			</span>
		</div>
	</div>

	<!-- Filters -->
	<Card.Root>
		<Card.Header>
			<Card.Title class="flex items-center gap-2">
				<Filter class="h-4 w-4" />
				Filters
			</Card.Title>
		</Card.Header>
		<Card.Content>
			<div class="grid grid-cols-1 gap-4 md:grid-cols-4">
				<div class="relative">
					<Search class="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
					<Input
						placeholder="Search descriptions..."
						bind:value={searchQuery}
						class="pl-9"
						onkeydown={handleSearchKeydown}
					/>
				</div>
				<select
					bind:value={actionFilter}
					class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
					onchange={applyFilters}
				>
					{#each actionTypes as action}
						<option value={action.value}>{action.label}</option>
					{/each}
				</select>
				<Input
					placeholder="Filter by user..."
					bind:value={userFilter}
					onkeydown={handleSearchKeydown}
				/>
				<Button onclick={applyFilters} variant="outline">
					<Filter class="mr-2 h-4 w-4" />
					Apply Filters
				</Button>
			</div>
		</Card.Content>
	</Card.Root>

	<!-- Audit Logs Table -->
	<Card.Root>
		<Card.Header>
			<Card.Title>Activity Log</Card.Title>
			<Card.Description>
				{totalItems} total entries
			</Card.Description>
		</Card.Header>
		<Card.Content>
			{#if auditLogs.length === 0}
				<div class="py-8 text-center">
					<p class="text-muted-foreground">No audit logs found</p>
				</div>
			{:else}
				<Table.Root>
					<Table.Header>
						<Table.Row>
							<Table.Head>Date</Table.Head>
							<Table.Head>User</Table.Head>
							<Table.Head>Action</Table.Head>
							<Table.Head>Object</Table.Head>
							<Table.Head>Description</Table.Head>
							<Table.Head>IP Address</Table.Head>
							<Table.Head class="w-[100px]">Actions</Table.Head>
						</Table.Row>
					</Table.Header>
					<Table.Body>
						{#each auditLogs as log (log.id)}
							<Table.Row>
								<Table.Cell class="font-mono text-sm">
									{formatDate(log.created_at)}
								</Table.Cell>
								<Table.Cell>
									<div class="flex items-center gap-2">
										<User class="h-4 w-4" />
										{formatUser(log.user)}
									</div>
								</Table.Cell>
								<Table.Cell>
									<Badge.Badge variant={getActionBadgeVariant(log.action)}>
										{log.action}
									</Badge.Badge>
								</Table.Cell>
								<Table.Cell>
									{formatObjectType(log.content_type)}
								</Table.Cell>
								<Table.Cell class="max-w-[300px] truncate">
									{log.description}
								</Table.Cell>
								<Table.Cell class="font-mono text-sm">
									{log.ip_address || 'N/A'}
								</Table.Cell>
								<Table.Cell>
									<Button variant="ghost" size="sm" onclick={() => viewLogDetail(log)}>
										<Eye class="h-4 w-4" />
									</Button>
								</Table.Cell>
							</Table.Row>
						{/each}
					</Table.Body>
				</Table.Root>
			{/if}
		</Card.Content>
	</Card.Root>

	<!-- Pagination -->
	{#if totalItems > limit}
		<Pagination
			fetching={false}
			total_items={totalItems}
			{limit}
			page={currentPage}
			onPageChange={handlePageChange}
		/>
	{/if}
</div>

<!-- Detail Dialog -->
<Dialog.Root bind:open={showDetailDialog}>
	<Dialog.Content class="max-w-4xl">
		<Dialog.Header>
			<Dialog.Title>Audit Log Details</Dialog.Title>
		</Dialog.Header>
		{#if selectedLog}
			<div class="space-y-6">
				<!-- Basic Info -->
				<div class="grid grid-cols-2 gap-4">
					<div>
						<h4 class="font-semibold">User</h4>
						<p>{formatUser(selectedLog.user)}</p>
					</div>
					<div>
						<h4 class="font-semibold">Date</h4>
						<p>{formatDate(selectedLog.created_at)}</p>
					</div>
					<div>
						<h4 class="font-semibold">Action</h4>
						<Badge.Badge variant={getActionBadgeVariant(selectedLog.action)}>
							{selectedLog.action}
						</Badge.Badge>
					</div>
					<div>
						<h4 class="font-semibold">Object Type</h4>
						<p>{formatObjectType(selectedLog.content_type)}</p>
					</div>
				</div>

				<!-- Description -->
				<div>
					<h4 class="font-semibold">Description</h4>
					<p>{selectedLog.description}</p>
				</div>

				<!-- Changes -->
				{#if selectedLog.changed_fields.length > 0}
					<div>
						<h4 class="font-semibold">Changed Fields</h4>
						<div class="flex flex-wrap gap-2">
							{#each selectedLog.changed_fields as field}
								<Badge.Badge variant="outline">{field}</Badge.Badge>
							{/each}
						</div>
					</div>
				{/if}

				<!-- Old/New Values -->
				{#if selectedLog.old_values || selectedLog.new_values}
					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						{#if selectedLog.old_values}
							<div>
								<h4 class="font-semibold">Previous Values</h4>
								<pre class="bg-muted max-h-40 overflow-auto rounded p-3 text-sm">{JSON.stringify(
										selectedLog.old_values,
										null,
										2
									)}</pre>
							</div>
						{/if}
						{#if selectedLog.new_values}
							<div>
								<h4 class="font-semibold">New Values</h4>
								<pre class="bg-muted max-h-40 overflow-auto rounded p-3 text-sm">{JSON.stringify(
										selectedLog.new_values,
										null,
										2
									)}</pre>
							</div>
						{/if}
					</div>
				{/if}

				<!-- Technical Details -->
				<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
					<div>
						<h4 class="font-semibold">IP Address</h4>
						<p class="font-mono text-sm">{selectedLog.ip_address || 'N/A'}</p>
					</div>
					<div>
						<h4 class="font-semibold">User Agent</h4>
						<p class="truncate font-mono text-sm">{selectedLog.user_agent || 'N/A'}</p>
					</div>
				</div>

				<!-- Metadata -->
				{#if Object.keys(selectedLog.metadata).length > 0}
					<div>
						<h4 class="font-semibold">Metadata</h4>
						<pre class="bg-muted max-h-40 overflow-auto rounded p-3 text-sm">{JSON.stringify(
								selectedLog.metadata,
								null,
								2
							)}</pre>
					</div>
				{/if}
			</div>
		{/if}
	</Dialog.Content>
</Dialog.Root>
