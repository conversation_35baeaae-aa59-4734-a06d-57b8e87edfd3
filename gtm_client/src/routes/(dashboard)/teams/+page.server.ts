import type { PageServerLoad } from './$types';

import { redirect } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ url, fetch, locals, depends }) => {
	depends('app:user');
	depends('app:currentOrganization');
	depends('app:organizations');
	depends('app:invitations');

	if (!locals.user) {
		redirect(302, '/');
	}

	const currentOrganizationID = locals.currentOrganizationID;

	const getOrganizationMembers = async () => {
		const relativePath = `/spark/api/v1/organizations/${currentOrganizationID}/members/`;
		const absoluteUrl = new URL(relativePath, url.origin);
		return fetch(absoluteUrl)
			.then(async (res) => {
				if (!res.ok) {
					console.error('Failed to fetch organization members:', await res.text());
					return {
						error: 'Failed to load organization members'
					};
				} else {
					return res.json();
				}
			})
			.then((data) => {
				return data?.data ?? [];
			});
	};

	const getOrganizationInvitations = async () => {
		const relativePath = `/spark/api/v1/organizations/${currentOrganizationID}/invitations/`;
		const absoluteUrl = new URL(relativePath, url.origin);
		return fetch(absoluteUrl)
			.then(async (res) => {
				if (!res.ok) {
					console.error('Failed to fetch organization invitations:', await res.text());
					return {
						error: 'Failed to load organization invitations'
					};
				} else {
					return res.json();
				}
			})
			.then((data) => {
				return data?.data ?? [];
			});
	};

	return {
		user: locals.user,
		members: await getOrganizationMembers(),
		invitations: await getOrganizationInvitations()
	};
};
