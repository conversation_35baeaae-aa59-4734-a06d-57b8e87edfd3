<script lang="ts">
	import * as Card from '$lib/shad-components/ui/card';
	import * as Alert from '$lib/shad-components/ui/alert';
	import AlertTriangleIcon from '@lucide/svelte/icons/alert-triangle';
	import Button from '$lib/shad-components/ui/button/button.svelte';
	import XIcon from '@lucide/svelte/icons/x';
	import * as AlertDialog from '$lib/shad-components/ui/alert-dialog';
	import { organizationAPIService, type Organization } from '$lib/api/organizations';
	import TrashIcon from '@lucide/svelte/icons/trash-2';

	import { toast } from 'svelte-sonner';
	import {
		currentOrganizationPersistedStore,
		organizationsPersistedStore
	} from '$lib/stores/organization.svelte';

	let { error = $bindable(), clearError, handleOrganizationSwitch } = $props();
	let currentOrganization = $derived(currentOrganizationPersistedStore.current);
	let organizations: Organization[] = $derived(organizationsPersistedStore.current);

	const openDeleteDialog = async () => {
		if (!currentOrganization) return;

		// Get workspace count for the current organization
		try {
			// TODO: Replace with actual workspace count API call
			// For now, we'll use a placeholder
			workspaceCount = 0; // This should be fetched from an API
			deleteDialogOpen = true;
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to load workspace information';
		}
	};
	// Delete organization state
	let isDeletingOrganization = $state(false);
	let deleteDialogOpen = $state(false);
	let workspaceCount = $state(0);

	const deleteOrganization = async () => {
		if (!currentOrganization) return;

		isDeletingOrganization = true;
		error = null;

		try {
			const personalOrg = organizations.find((org) => org.type === 'personal');

			const result = await organizationAPIService.deleteOrganization(currentOrganization.id);

			if (personalOrg) {
				await handleOrganizationSwitch(personalOrg);
			}
			// Close dialog
			deleteDialogOpen = false;

			// Show success message (you could add a toast notification here)
			toast.message('Organization deleted successfully');
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to delete organization';
		} finally {
			isDeletingOrganization = false;
		}
	};
</script>

<Card.Root>
	<Card.Header>
		<Card.Title class="text-destructive">Danger Zone</Card.Title>
		<Card.Description>Irreversible and destructive actions for this organization.</Card.Description>
	</Card.Header>
	<Card.Content class="space-y-4">
		{#if error}
			<Alert.Root variant="destructive" class="relative">
				<AlertTriangleIcon class="h-4 w-4" />
				<Alert.Title>Error</Alert.Title>
				<Alert.Description>{error}</Alert.Description>
				<Button
					variant="ghost"
					size="sm"
					class="absolute top-2 right-2 h-6 w-6 p-0"
					onclick={clearError}
				>
					<XIcon class="h-4 w-4" />
				</Button>
			</Alert.Root>
		{/if}

		<div class="border-destructive rounded-lg border p-4">
			<div class="flex items-start justify-between">
				<div class="space-y-1">
					<h4 class="text-sm font-medium">Delete Organization</h4>
					<p class="text-muted-foreground text-sm">
						Permanently delete this team organization and all associated data.
						{#if workspaceCount > 0}
							All {workspaceCount} workspace(s) will be transferred to your personal account.
						{/if}
					</p>
				</div>
				<Button variant="destructive" onclick={openDeleteDialog}>
					<TrashIcon class="mr-2 h-4 w-4" />
					Delete Organization
				</Button>
			</div>
		</div>
	</Card.Content>
</Card.Root>

<!-- Delete Organization Confirmation Dialog -->
<AlertDialog.Root bind:open={deleteDialogOpen}>
	<AlertDialog.Content>
		<AlertDialog.Header>
			<AlertDialog.Title>Delete Organization</AlertDialog.Title>
			<AlertDialog.Description>
				Are you sure you want to delete "{currentOrganization?.name}"? This action cannot be undone.
				{#if workspaceCount > 0}
					<br /><br />
					<strong>Important:</strong>
					{workspaceCount} workspace(s) will be automatically transferred to your personal account before
					deletion.
				{/if}
			</AlertDialog.Description>
		</AlertDialog.Header>
		<AlertDialog.Footer>
			<AlertDialog.Cancel>Cancel</AlertDialog.Cancel>
			<AlertDialog.Action onclick={deleteOrganization} disabled={isDeletingOrganization}>
				{#if isDeletingOrganization}
					<div class="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-current"></div>
					Deleting...
				{:else}
					Delete Organization
				{/if}
			</AlertDialog.Action>
		</AlertDialog.Footer>
	</AlertDialog.Content>
</AlertDialog.Root>
