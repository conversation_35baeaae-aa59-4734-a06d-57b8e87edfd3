<script lang="ts">
	import * as Card from '$lib/shad-components/ui/card';
	import * as Table from '$lib/shad-components/ui/table';
	import * as Select from '$lib/shad-components/ui/select';
	import * as Alert from '$lib/shad-components/ui/alert';
	import AlertTriangleIcon from '@lucide/svelte/icons/alert-triangle';
	import Label from '$lib/shad-components/ui/label/label.svelte';
	import Badge from '$lib/shad-components/ui/badge/badge.svelte';
	import Button from '$lib/shad-components/ui/button/button.svelte';
	import * as Dialog from '$lib/shad-components/ui/dialog';
	import XIcon from '@lucide/svelte/icons/x';
	import UsersIcon from '@lucide/svelte/icons/users';
	import UserPlusIcon from '@lucide/svelte/icons/user-plus';
	import SettingsIcon from '@lucide/svelte/icons/settings';
	import TrashIcon from '@lucide/svelte/icons/trash-2';
	import { organizationAPIService, type OrganizationMember } from '$lib/api/organizations';
	import { page } from '$app/state';
	import { currentOrganizationPersistedStore } from '$lib/stores/organization.svelte';

	import { invalidateWithLoading } from '$lib/utils/navigation';

	let {
		error = $bindable(),
		openInviteDialog,
		clearError,
		getRoleBadgeVariant,
		formatDate
	} = $props();
	let members = $derived(page.data.members);
	let currentOrganization = $derived(currentOrganizationPersistedStore.current);
	let currentUser = $derived(page.data.user);
	let isUpdatingMember = $state(false);
	let memberSettingsDialogOpen = $state(false);
	let selectedMember = $state<OrganizationMember | null>(null);
	let deleteConfirmDialogOpen = $state(false);
	let memberToDelete = $state<OrganizationMember | null>(null);
	let isDeletingMember = $state(false);

	// Get current user's membership to determine permissions
	let currentUserMembership = $derived(
		members?.find((member) => member.user.email === currentUser?.email)
	);

	// Check if current user can delete members (owner or admin with delete permission)
	let canDeleteMembers = $derived(
		currentUserMembership?.role === 'owner' ||
			currentUserMembership?.role === 'admin' ||
			currentOrganization?.owner?.email === currentUser?.email ||
			currentUserMembership?.can_delete
	);

	const getPermissionsList = (member: OrganizationMember) => {
		const permissions = [];
		if (member.can_view) permissions.push('View');
		if (member.can_create) permissions.push('Create');
		if (member.can_edit) permissions.push('Edit');
		if (member.can_delete) permissions.push('Delete');
		return permissions.join(', ') || 'No permissions';
	};

	const openMemberSettings = (member: OrganizationMember) => {
		selectedMember = member;
		memberSettingsDialogOpen = true;
		clearError();
	};

	const updateMemberPermissions = async () => {
		if (!currentOrganization || !selectedMember) return;

		isUpdatingMember = true;
		error = null;

		try {
			await organizationAPIService.updateMember(currentOrganization.id, selectedMember.id, {
				role: selectedMember.role === 'owner' ? 'admin' : selectedMember.role,
				can_view: selectedMember.can_view,
				can_create: selectedMember.can_create,
				can_edit: selectedMember.can_edit,
				can_delete: selectedMember.can_delete
			});

			// Reload members
			// await organizationStore.loadMembers(currentOrganization.id);
			await invalidateWithLoading('app:members');
			await invalidateWithLoading('app:invitations');
			await invalidateWithLoading('app:currentOrganization');

			// Close dialog
			memberSettingsDialogOpen = false;
			selectedMember = null;
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to update member permissions';
		} finally {
			isUpdatingMember = false;
		}
	};

	const openDeleteConfirmation = (member: OrganizationMember) => {
		memberToDelete = member;
		deleteConfirmDialogOpen = true;
		clearError();
	};

	const deleteMember = async () => {
		if (!currentOrganization || !memberToDelete) return;

		isDeletingMember = true;
		error = null;

		try {
			await organizationAPIService.removeMember(currentOrganization.id, memberToDelete.id);

			// Reload members
			await invalidateWithLoading('app:members');
			await invalidateWithLoading('app:invitations');
			await invalidateWithLoading('app:currentOrganization');

			// Close dialog
			deleteConfirmDialogOpen = false;
			memberToDelete = null;
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to remove member';
		} finally {
			isDeletingMember = false;
		}
	};

	const canDeleteMember = (member: OrganizationMember) => {
		// Can't delete yourself
		if (member.user.email === currentUser?.email) return false;

		// Can't delete the owner
		if (member.role === 'owner') return false;

		// Only owners and admins with delete permission can delete members
		return canDeleteMembers;
	};
</script>

<Card.Root>
	<Card.Header>
		<Card.Title>Team Members</Card.Title>
		<Card.Description>Manage your organization members and their permissions.</Card.Description>
	</Card.Header>
	<Card.Content>
		{#if members?.length === 0}
			<div class="py-8 text-center">
				<UsersIcon class="text-muted-foreground mx-auto mb-4 h-12 w-12" />
				<h3 class="mb-2 text-lg font-medium">No members yet</h3>
				<p class="text-muted-foreground mb-4">Invite team members to start collaborating.</p>
				<Button onclick={openInviteDialog}>
					<UserPlusIcon class="mr-2 h-4 w-4" />
					Invite First Member
				</Button>
			</div>
		{:else}
			<Table.Root>
				<Table.Header>
					<Table.Row>
						<Table.Head>Member</Table.Head>
						<Table.Head>Role</Table.Head>
						<Table.Head>Permissions</Table.Head>
						<Table.Head>Joined</Table.Head>
						<Table.Head>Actions</Table.Head>
					</Table.Row>
				</Table.Header>
				<Table.Body>
					{#each members as member (member.id)}
						<Table.Row>
							<Table.Cell>
								<div class="flex items-center gap-3">
									<div class="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full">
										<span class="text-sm font-medium">
											{member.user.first_name?.[0] || member.user.email[0].toUpperCase()}
										</span>
									</div>
									<div>
										<div class="font-medium">
											{member.user.first_name}
											{member.user.last_name}
										</div>
										<div class="text-muted-foreground text-sm">
											{member.user.email}
										</div>
									</div>
								</div>
							</Table.Cell>
							<Table.Cell>
								<Badge variant={getRoleBadgeVariant(member.role)}>
									{member.role}
								</Badge>
							</Table.Cell>
							<Table.Cell>
								<span class="text-muted-foreground text-sm">
									{getPermissionsList(member)}
								</span>
							</Table.Cell>
							<Table.Cell>
								<span class="text-muted-foreground text-sm">
									{formatDate(member.joined_at)}
								</span>
							</Table.Cell>
							<Table.Cell>
								<div class="flex items-center gap-2">
									{#if member.role !== 'owner'}
										<Button variant="ghost" size="sm" onclick={() => openMemberSettings(member)}>
											<SettingsIcon class="h-4 w-4" />
										</Button>
									{/if}
									{#if canDeleteMember(member)}
										<Button
											variant="ghost"
											size="sm"
											onclick={() => openDeleteConfirmation(member)}
											class="text-red-600 hover:bg-red-50 hover:text-red-700"
										>
											<TrashIcon class="h-4 w-4" />
										</Button>
									{/if}
								</div>
							</Table.Cell>
						</Table.Row>
					{/each}
				</Table.Body>
			</Table.Root>
		{/if}
	</Card.Content>
</Card.Root>

<!-- Member Settings Dialog -->
<Dialog.Root bind:open={memberSettingsDialogOpen}>
	<Dialog.Content class="sm:max-w-md">
		<Dialog.Header>
			<Dialog.Title>Member Settings</Dialog.Title>
			<Dialog.Description>
				Manage permissions for {selectedMember?.user.first_name}
				{selectedMember?.user.last_name}
			</Dialog.Description>
		</Dialog.Header>

		{#if selectedMember}
			<div class="space-y-4">
				{#if error}
					<Alert.Root variant="destructive" class="relative">
						<AlertTriangleIcon class="h-4 w-4" />
						<Alert.Title>Error</Alert.Title>
						<Alert.Description>{error}</Alert.Description>
						<Button
							variant="ghost"
							size="sm"
							class="absolute top-2 right-2 h-6 w-6 p-0"
							onclick={clearError}
						>
							<XIcon class="h-4 w-4" />
						</Button>
					</Alert.Root>
				{/if}

				<div class="space-y-2">
					<Label>Member Information</Label>
					<div class="rounded-lg border p-3">
						<div class="flex items-center gap-3">
							<div class="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full">
								<span class="text-sm font-medium">
									{selectedMember.user.first_name?.[0] ||
										selectedMember.user.email[0].toUpperCase()}
								</span>
							</div>
							<div>
								<div class="font-medium">
									{selectedMember.user.first_name}
									{selectedMember.user.last_name}
								</div>
								<div class="text-muted-foreground text-sm">
									{selectedMember.user.email}
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="space-y-2">
					<Label>Role</Label>
					<Select.Root
						type="single"
						value={selectedMember.role}
						onValueChange={(value: string) => {
							if (selectedMember && value) {
								selectedMember.role = value as 'admin' | 'member';
							}
						}}
						disabled={isUpdatingMember}
					>
						<Select.Trigger>
							{selectedMember.role === 'admin' ? 'Admin' : 'Member'}
						</Select.Trigger>
						<Select.Content>
							<Select.Item value="member">Member</Select.Item>
							<Select.Item value="admin">Admin</Select.Item>
						</Select.Content>
					</Select.Root>
				</div>

				<div class="space-y-2">
					<Label>Permissions</Label>
					<div class="space-y-2">
						<div class="flex items-center space-x-2">
							<input
								type="checkbox"
								id="can_view"
								bind:checked={selectedMember.can_view}
								disabled={isUpdatingMember}
								class="rounded"
							/>
							<Label for="can_view" class="text-sm">View workspaces and projects</Label>
						</div>
						<div class="flex items-center space-x-2">
							<input
								type="checkbox"
								id="can_create"
								bind:checked={selectedMember.can_create}
								disabled={isUpdatingMember}
								class="rounded"
							/>
							<Label for="can_create" class="text-sm">Create new workspaces and projects</Label>
						</div>
						<div class="flex items-center space-x-2">
							<input
								type="checkbox"
								id="can_edit"
								bind:checked={selectedMember.can_edit}
								disabled={isUpdatingMember}
								class="rounded"
							/>
							<Label for="can_edit" class="text-sm">Edit existing workspaces and projects</Label>
						</div>
						<div class="flex items-center space-x-2">
							<input
								type="checkbox"
								id="can_delete"
								bind:checked={selectedMember.can_delete}
								disabled={isUpdatingMember}
								class="rounded"
							/>
							<Label for="can_delete" class="text-sm">Delete workspaces and projects</Label>
						</div>
					</div>
				</div>
			</div>

			<Dialog.Footer>
				<Button
					variant="outline"
					onclick={() => {
						memberSettingsDialogOpen = false;
						selectedMember = null;
					}}
					disabled={isUpdatingMember}
				>
					Cancel
				</Button>
				<Button onclick={updateMemberPermissions} disabled={isUpdatingMember}>
					{#if isUpdatingMember}
						<div class="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-current"></div>
						Updating...
					{:else}
						Update Permissions
					{/if}
				</Button>
			</Dialog.Footer>
		{/if}
	</Dialog.Content>
</Dialog.Root>

<!-- Delete Member Confirmation Dialog -->
<Dialog.Root bind:open={deleteConfirmDialogOpen}>
	<Dialog.Content class="sm:max-w-md">
		<Dialog.Header>
			<Dialog.Title>Remove Member</Dialog.Title>
			<Dialog.Description>
				Are you sure you want to remove {memberToDelete?.user.first_name}
				{memberToDelete?.user.last_name} from this organization? This action cannot be undone.
			</Dialog.Description>
		</Dialog.Header>

		{#if error}
			<Alert.Root variant="destructive" class="relative">
				<AlertTriangleIcon class="h-4 w-4" />
				<Alert.Title>Error</Alert.Title>
				<Alert.Description>{error}</Alert.Description>
				<Button
					variant="ghost"
					size="sm"
					class="absolute top-2 right-2 h-6 w-6 p-0"
					onclick={clearError}
				>
					<XIcon class="h-4 w-4" />
				</Button>
			</Alert.Root>
		{/if}

		{#if memberToDelete}
			<div class="space-y-4">
				<div class="rounded-lg border border-red-200 bg-red-50 p-3">
					<div class="flex items-center gap-3">
						<div class="flex h-8 w-8 items-center justify-center rounded-full bg-red-100">
							<span class="text-sm font-medium text-red-700">
								{memberToDelete.user.first_name?.[0] || memberToDelete.user.email[0].toUpperCase()}
							</span>
						</div>
						<div>
							<div class="font-medium text-red-900">
								{memberToDelete.user.first_name}
								{memberToDelete.user.last_name}
							</div>
							<div class="text-sm text-red-700">
								{memberToDelete.user.email}
							</div>
							<div class="text-xs text-red-600">
								Role: {memberToDelete.role}
							</div>
						</div>
					</div>
				</div>
			</div>
		{/if}

		<Dialog.Footer>
			<Button
				variant="outline"
				onclick={() => {
					deleteConfirmDialogOpen = false;
					memberToDelete = null;
				}}
				disabled={isDeletingMember}
			>
				Cancel
			</Button>
			<Button variant="destructive" onclick={deleteMember} disabled={isDeletingMember}>
				{#if isDeletingMember}
					<div class="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-current"></div>
					Removing...
				{:else}
					Remove Member
				{/if}
			</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
