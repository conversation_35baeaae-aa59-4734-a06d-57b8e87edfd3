<script lang="ts">
	import * as Card from '$lib/shad-components/ui/card';
	import * as Table from '$lib/shad-components/ui/table';

	import * as Alert from '$lib/shad-components/ui/alert';
	import AlertTriangleIcon from '@lucide/svelte/icons/alert-triangle';

	import Badge from '$lib/shad-components/ui/badge/badge.svelte';
	import Button from '$lib/shad-components/ui/button/button.svelte';

	import XIcon from '@lucide/svelte/icons/x';

	import UserPlusIcon from '@lucide/svelte/icons/user-plus';
	import CheckCircleIcon from '@lucide/svelte/icons/check-circle';
	import ClockIcon from '@lucide/svelte/icons/clock';

	import MailIcon from '@lucide/svelte/icons/mail';
	import { organizationAPIService } from '$lib/api/organizations';
	import { page } from '$app/state';
	import { invalidateWithLoading } from '$lib/utils/navigation';
	import { currentOrganizationPersistedStore } from '$lib/stores/organization.svelte';

	let {
		error = $bindable(),
		clearError,
		getRoleBadgeVariant,
		openInviteDialog,
		formatDate
	} = $props();
	let currentOrganization = $derived(currentOrganizationPersistedStore.current);
	let invitations = $derived(page.data?.invitations ?? []);

	const getStatusBadgeVariant = (status: string) => {
		switch (status) {
			case 'pending':
				return 'secondary';
			case 'accepted':
				return 'default';
			case 'declined':
				return 'destructive';
			case 'expired':
				return 'outline';
			default:
				return 'outline';
		}
	};

	const getStatusIcon = (status: string) => {
		switch (status) {
			case 'pending':
				return ClockIcon;
			case 'accepted':
				return CheckCircleIcon;
			case 'declined':
				return XIcon;
			case 'expired':
				return AlertTriangleIcon;
			default:
				return ClockIcon;
		}
	};

	const resendInvitation = async (invitationId: string) => {
		if (!currentOrganization) return;

		try {
			await organizationAPIService.resendInvitation(currentOrganization.id, invitationId);
			await invalidateWithLoading('app:invitations');
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to resend invitation';
		}
	};

	const cancelInvitation = async (invitationId: string) => {
		if (!currentOrganization) return;

		try {
			await organizationAPIService.cancelInvitation(currentOrganization.id, invitationId);
			await invalidateWithLoading('app:invitations');
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to cancel invitation';
		}
	};
</script>

<Card.Root>
	<Card.Header>
		<Card.Title>Pending Invitations</Card.Title>
		<Card.Description
			>Manage pending invitations. Cancelled and processed invitations are automatically hidden.</Card.Description
		>
	</Card.Header>
	<Card.Content>
		{#if error}
			<Alert.Root variant="destructive" class="relative">
				<AlertTriangleIcon class="h-4 w-4" />
				<Alert.Title>Error</Alert.Title>
				<Alert.Description>{error}</Alert.Description>
				<Button
					variant="ghost"
					size="sm"
					class="absolute top-2 right-2 h-6 w-6 p-0"
					onclick={clearError}
				>
					<XIcon class="h-4 w-4" />
				</Button>
			</Alert.Root>
		{:else if invitations.length === 0}
			<div class="py-8 text-center">
				<MailIcon class="text-muted-foreground mx-auto mb-4 h-12 w-12" />
				<h3 class="mb-2 text-lg font-medium">No invitations sent</h3>
				<p class="text-muted-foreground mb-4">Invite team members to get started.</p>
				<Button onclick={openInviteDialog}>
					<UserPlusIcon class="mr-2 h-4 w-4" />
					Send Invitation
				</Button>
			</div>
		{:else}
			<Table.Root>
				<Table.Header>
					<Table.Row>
						<Table.Head>Email</Table.Head>
						<Table.Head>Role</Table.Head>
						<Table.Head>Status</Table.Head>
						<Table.Head>Invited By</Table.Head>
						<Table.Head>Expires</Table.Head>
						<Table.Head>Actions</Table.Head>
					</Table.Row>
				</Table.Header>
				<Table.Body>
					{#each invitations as invitation (invitation.id)}
						{@const StatusIcon = getStatusIcon(invitation.status)}
						<Table.Row>
							<Table.Cell>
								<div class="font-medium">{invitation.email}</div>
							</Table.Cell>
							<Table.Cell>
								<Badge variant={getRoleBadgeVariant(invitation.role)}>
									{invitation.role}
								</Badge>
							</Table.Cell>
							<Table.Cell>
								<div class="flex items-center gap-2">
									<StatusIcon class="h-4 w-4" />
									<Badge variant={getStatusBadgeVariant(invitation.status)}>
										{invitation.status}
									</Badge>
								</div>
							</Table.Cell>
							<Table.Cell>
								<span class="text-muted-foreground text-sm">
									{invitation.invited_by.first_name}
									{invitation.invited_by.last_name}
								</span>
							</Table.Cell>
							<Table.Cell>
								<span class="text-muted-foreground text-sm">
									{formatDate(invitation.expires_at)}
								</span>
							</Table.Cell>
							<Table.Cell>
								{#if invitation.status === 'pending'}
									<div class="flex items-center gap-2">
										<Button
											variant="ghost"
											size="sm"
											onclick={() => resendInvitation(invitation.id)}
										>
											Resend
										</Button>
										<Button
											variant="ghost"
											size="sm"
											onclick={() => cancelInvitation(invitation.id)}
										>
											Cancel
										</Button>
									</div>
								{/if}
							</Table.Cell>
						</Table.Row>
					{/each}
				</Table.Body>
			</Table.Root>
		{/if}
	</Card.Content>
</Card.Root>
