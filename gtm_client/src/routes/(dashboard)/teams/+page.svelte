<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import { globalBreadcrumbs, globalNavLinks } from '$lib/stores/side-nav.svelte';
	import { HouseIcon } from '$lib/shared-ui/layoutExports';

	import House from '@lucide/svelte/icons/house';

	import * as Card from '$lib/shad-components/ui/card';
	import * as Tabs from '$lib/shad-components/ui/tabs';
	import * as Alert from '$lib/shad-components/ui/alert';

	import * as Dialog from '$lib/shad-components/ui/dialog';
	import * as Select from '$lib/shad-components/ui/select';
	import Button from '$lib/shad-components/ui/button/button.svelte';
	import Input from '$lib/shad-components/ui/input/input.svelte';
	import Label from '$lib/shad-components/ui/label/label.svelte';
	import Textarea from '$lib/shad-components/ui/textarea/textarea.svelte';

	import UsersIcon from '@lucide/svelte/icons/users';
	import UserPlusIcon from '@lucide/svelte/icons/user-plus';
	import MailIcon from '@lucide/svelte/icons/mail';
	import SettingsIcon from '@lucide/svelte/icons/settings';
	import AlertTriangleIcon from '@lucide/svelte/icons/alert-triangle';
	import ActivityIcon from '@lucide/svelte/icons/activity';

	import XIcon from '@lucide/svelte/icons/x';

	import {
		organizationAPIService,
		type Organization,
		type OrganizationInvitation
	} from '$lib/api/organizations';
	import { invalidate } from '$app/navigation';
	import Members from './Members.svelte';
	import Invitations from './Invitations.svelte';
	import Settings from './Settings.svelte';
	import { invalidateWithLoading } from '$lib/utils/navigation';
	import { currentOrganizationPersistedStore } from '$lib/stores/organization.svelte';

	let currentOrganization = $derived(currentOrganizationPersistedStore.current);
	let currentUser = $derived(page.data.user);

	let members = $derived(page.data.members);

	let activeTab = $state('members');
	let invitations = $derived<OrganizationInvitation[]>(page.data.invitations ?? []);

	let error = $state<string | null>(null);

	// Function to clear errors
	const clearError = () => {
		error = null;
	};

	// Clear error when switching tabs
	$effect(() => {
		// Watch activeTab changes and clear errors
		if (activeTab) {
			clearError();
		}
	});

	// Create team state
	let isCreatingTeam = $state(false);
	let teamName = $state('');

	// Invite member state
	let inviteDialogOpen = $state(false);
	let isInviting = $state(false);
	let inviteEmail = $state('');
	let inviteRole = $state<'admin' | 'member'>('member');
	let inviteMessage = $state('');

	const getRoleBadgeVariant = (role: string) => {
		switch (role) {
			case 'owner':
				return 'default';
			case 'admin':
				return 'secondary';
			case 'member':
				return 'outline';
			default:
				return 'outline';
		}
	};

	onMount(() => {
		// Set up navigation
		globalBreadcrumbs.crumbs = [
			{
				title: HouseIcon,
				href: '/dashboard'
			},
			{
				title: 'Teams',
				href: '/teams'
			}
		];

		globalNavLinks.links = [
			{
				title: 'Dashboard',
				url: '/dashboard',
				icon: House
			}
		];
	});

	const openInviteDialog = () => {
		inviteDialogOpen = true;
		// Reset form
		inviteEmail = '';
		inviteRole = 'member';
		inviteMessage = '';
		clearError();
	};

	const inviteMember = async () => {
		if (!currentOrganization || !inviteEmail.trim()) return;

		// Check if user is trying to invite themselves
		if (currentUser && inviteEmail.trim().toLowerCase() === currentUser.email.toLowerCase()) {
			error = 'You cannot invite yourself to the organization';
			return;
		}

		isInviting = true;
		error = null;

		try {
			// Set permissions based on role
			const permissions = {
				viewer: { can_view: true, can_create: false, can_edit: false, can_delete: false },
				member: { can_view: true, can_create: true, can_edit: true, can_delete: false },
				admin: { can_view: true, can_create: true, can_edit: true, can_delete: true }
			};

			await organizationAPIService.inviteUser(currentOrganization.id, {
				email: inviteEmail.trim(),
				role: inviteRole,
				...permissions[inviteRole as keyof typeof permissions]
			});
			await invalidateWithLoading('app:invitations');

			// Close dialog and reset form
			inviteDialogOpen = false;
			inviteEmail = '';
			inviteRole = 'member';
			inviteMessage = '';
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to send invitation';
		} finally {
			isInviting = false;
		}
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	};

	const handleOrganizationSwitch = async (org: Organization) => {
		try {
			const response = await fetch('/local/organization/set', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(org)
			});

			if (response.ok) {
				await invalidateWithLoading('app:currentOrganization');

				// await goto('/dashboard', { invalidateAll: true, replaceState: true });
			} else {
				console.error('Failed to set organization on server');
			}
		} catch (error) {
			console.error('Network error while setting organization:', error);
		}
	};

	const createTeam = async () => {
		if (!teamName.trim()) {
			error = 'Team name is required';
			return;
		}

		isCreatingTeam = true;
		error = null;

		try {
			const newOrganization = await organizationAPIService.createOrganization({
				name: teamName.trim()
			});

			await handleOrganizationSwitch(newOrganization);
			await invalidateWithLoading('app:currentOrganization');

			// Reset form
			teamName = '';
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to create team';
		} finally {
			isCreatingTeam = false;
		}
	};
</script>

<div class="container mx-auto space-y-6 p-6">
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">Team Management</h1>
			<p class="text-muted-foreground">Manage your organization members and invitations</p>
		</div>

		{#if currentOrganization && currentOrganization.type !== 'personal'}
			<Button onclick={openInviteDialog}>
				<UserPlusIcon class="mr-2 h-4 w-4" />
				Invite Member
			</Button>
		{/if}
	</div>

	{#if !currentOrganization}
		<Alert.Root variant="destructive">
			<AlertTriangleIcon class="h-4 w-4" />
			<Alert.Title>No Organization Selected</Alert.Title>
			<Alert.Description>
				Please select an organization from the sidebar to manage team members.
			</Alert.Description>
		</Alert.Root>
	{:else if currentOrganization.type === 'personal'}
		<Alert.Root>
			<UsersIcon class="h-4 w-4" />
			<Alert.Title>Personal Account</Alert.Title>
			<Alert.Description>
				This is your personal account. Create a team organization to invite and manage members.
			</Alert.Description>
		</Alert.Root>

		<Card.Root>
			<Card.Header>
				<Card.Title class="flex items-center gap-2">
					<SettingsIcon class="h-5 w-5" />
					Create Team Organization
				</Card.Title>
				<Card.Description>
					Collaborate with others by creating a team organization.
				</Card.Description>
			</Card.Header>
			<Card.Content class="space-y-4">
				{#if error}
					<Alert.Root variant="destructive" class="relative">
						<AlertTriangleIcon class="h-4 w-4" />
						<Alert.Title>Error</Alert.Title>
						<Alert.Description>{error}</Alert.Description>
						<Button
							variant="ghost"
							size="sm"
							class="absolute top-2 right-2 h-6 w-6 p-0"
							onclick={clearError}
						>
							<XIcon class="h-4 w-4" />
						</Button>
					</Alert.Root>
				{/if}

				<div class="space-y-2">
					<Label for="team-name">Team Name</Label>
					<Input
						id="team-name"
						type="text"
						bind:value={teamName}
						placeholder="Enter team name..."
						disabled={isCreatingTeam}
					/>
				</div>

				<Button onclick={createTeam} disabled={isCreatingTeam || !teamName.trim()}>
					{#if isCreatingTeam}
						<div class="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-current"></div>
						Creating...
					{:else}
						<UsersIcon class="mr-2 h-4 w-4" />
						Create Team
					{/if}
				</Button>
			</Card.Content>
		</Card.Root>
	{:else}
		<svelte:boundary onerror={(e) => console.error('Tab error:', e)}>
			<Tabs.Root bind:value={activeTab} class="w-full">
				<Tabs.List class="grid w-full grid-cols-3">
					<Tabs.Trigger value="members">
						<UsersIcon class="mr-2 h-4 w-4" />
						Members ({members.length})
					</Tabs.Trigger>
					<Tabs.Trigger value="invitations">
						<MailIcon class="mr-2 h-4 w-4" />
						Invitations ({invitations.length})
					</Tabs.Trigger>
					<!-- <Tabs.Trigger value="audit">
						<ActivityIcon class="mr-2 h-4 w-4" />
						Audit
					</Tabs.Trigger> -->
					<Tabs.Trigger value="settings">
						<SettingsIcon class="mr-2 h-4 w-4" />
						Settings
					</Tabs.Trigger>
				</Tabs.List>

				<Tabs.Content value="members" class="space-y-4">
					<Members bind:error {openInviteDialog} {clearError} {getRoleBadgeVariant} {formatDate} />
				</Tabs.Content>

				<Tabs.Content value="invitations" class="space-y-4">
					<Invitations
						bind:error
						{openInviteDialog}
						{clearError}
						{getRoleBadgeVariant}
						{formatDate}
					/>
				</Tabs.Content>

				<!-- <Tabs.Content value="audit" class="space-y-4">
					<div class="py-8 text-center">
						<ActivityIcon class="text-muted-foreground mx-auto mb-4 h-12 w-12" />
						<h3 class="mb-2 text-lg font-semibold">Audit Logs</h3>
						<p class="text-muted-foreground mb-4">
							Track all changes and activities in your organization
						</p>
						<Button onclick={() => window.open('/teams/audit', '_blank')}>View Audit Logs</Button>
					</div>
				</Tabs.Content> -->

				<Tabs.Content value="settings" class="space-y-4">
					<Settings {handleOrganizationSwitch} bind:error {clearError} />
				</Tabs.Content>
			</Tabs.Root>

			{#snippet failed(error, reset)}
				<Alert.Root variant="destructive" class="relative">
					<AlertTriangleIcon class="h-4 w-4" />
					<Alert.Title>Something went wrong</Alert.Title>
					<Alert.Description>
						An error occurred while loading the team management interface. Please try again.
					</Alert.Description>
					<Button variant="outline" size="sm" class="absolute top-2 right-2" onclick={reset}>
						Try Again
					</Button>
				</Alert.Root>
			{/snippet}
		</svelte:boundary>

		<!-- Invite Member Dialog -->
		<Dialog.Root bind:open={inviteDialogOpen}>
			<Dialog.Content class="sm:max-w-md">
				<Dialog.Header>
					<Dialog.Title>Invite Team Member</Dialog.Title>
					<Dialog.Description>
						Send an invitation to join "{currentOrganization?.name}".
					</Dialog.Description>
				</Dialog.Header>

				<div class="space-y-4">
					{#if error}
						<Alert.Root variant="destructive" class="relative">
							<AlertTriangleIcon class="h-4 w-4" />
							<Alert.Title>Error</Alert.Title>
							<Alert.Description>{error}</Alert.Description>
							<Button
								variant="ghost"
								size="sm"
								class="absolute top-2 right-2 h-6 w-6 p-0"
								onclick={clearError}
							>
								<XIcon class="h-4 w-4" />
							</Button>
						</Alert.Root>
					{/if}

					<div class="space-y-2">
						<Label for="invite-email">Email Address</Label>
						<Input
							id="invite-email"
							type="email"
							placeholder="<EMAIL>"
							bind:value={inviteEmail}
							disabled={isInviting}
						/>
					</div>

					<div class="space-y-2">
						<Label for="invite-role">Role</Label>
						<Select.Root
							type="single"
							onValueChange={(value: string) => {
								if (value) inviteRole = value as 'admin' | 'member';
							}}
						>
							<Select.Trigger>
								{inviteRole === 'admin' ? 'Admin' : 'Member'}
							</Select.Trigger>
							<Select.Content>
								<Select.Item value="member">Member</Select.Item>
								<Select.Item value="admin">Admin</Select.Item>
							</Select.Content>
						</Select.Root>
					</div>

					<div class="space-y-2">
						<Label for="invite-message">Personal Message (Optional)</Label>
						<Textarea
							id="invite-message"
							placeholder="Add a personal message to the invitation..."
							bind:value={inviteMessage}
							disabled={isInviting}
							rows={3}
						/>
					</div>
				</div>

				<Dialog.Footer>
					<Button
						variant="outline"
						onclick={() => (inviteDialogOpen = false)}
						disabled={isInviting}
					>
						Cancel
					</Button>
					<Button onclick={inviteMember} disabled={isInviting || !inviteEmail.trim()}>
						{#if isInviting}
							<div class="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-current"></div>
							Sending...
						{:else}
							Send Invitation
						{/if}
					</Button>
				</Dialog.Footer>
			</Dialog.Content>
		</Dialog.Root>
	{/if}
</div>
