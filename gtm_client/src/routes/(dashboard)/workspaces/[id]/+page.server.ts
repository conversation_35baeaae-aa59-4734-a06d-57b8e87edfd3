import type { PageServerLoad } from './$types';
import { redirect } from '@sveltejs/kit';
import type { Workspace } from '$lib/interfaces/workspace';

export const load: PageServerLoad = async ({ url, fetch, params, cookies, locals }) => {
	if (!locals.user) {
		redirect(302, '/');
	}
	const getWorkspace = async () => {
		const relativePath = `/spark/api/v1/workspaces/${params.id}/`;

		const absoluteUrl = new URL(relativePath, url.origin);
		return fetch(absoluteUrl)
			.then(async (res) => {
				if (!res.ok) {
					return null;
				} else {
					return res.json();
				}
			})
			.then((data) => {
				return data?.data ?? {};
			});
	};
	const getProjectsAsync = async () => {
		const relativePath = `/spark/api/v1/projects/workspaces/${params.id}/projects/`;
		const absoluteUrl = new URL(relativePath, url.origin);
		const searchParams = url.searchParams;
		absoluteUrl.searchParams.set('offset', searchParams.get('offset') ?? '0');
		absoluteUrl.searchParams.set('limit', searchParams.get('limit') ?? '10');
		absoluteUrl.searchParams.set('status', searchParams.get('status') ?? '');
		absoluteUrl.searchParams.set('search', searchParams.get('search') ?? '');

		return fetch(absoluteUrl)
			.then(async (res) => {
				if (!res.ok) {
					const _ = await res.json();
					console.log(_);
					return null;
				} else {
					return res.json();
				}
			})
			.then((data) => {
				return data?.data ?? {};
			})
			.catch((error) => {
				console.error(error);
			});
	};

	const workspace = (await getWorkspace()) as Workspace;

	if (!workspace) {
		cookies.set('message', 'Workspace not found', { path: '/' });
		cookies.set('messageType', 'error', { path: '/' });
		redirect(302, '/dashboard');
		return;
	}

	return {
		user: locals.user,
		workspace,
		projects: await getProjectsAsync()
	};
};
