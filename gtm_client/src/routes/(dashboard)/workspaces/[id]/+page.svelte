<script lang="ts">
	import { page } from '$app/state';
	import { onMount } from 'svelte';
	import { watch } from 'runed';

	import Plus from '@lucide/svelte/icons/plus';
	import Loader2Icon from '@lucide/svelte/icons/loader-2';
	import MoreHorizontal from '@lucide/svelte/icons/more-horizontal';
	import Dashboard from '@lucide/svelte/icons/layout-dashboard';
	import Pagination from '$lib/shared-ui/Pagination.svelte';
	import LoaderCircleIcon from '@lucide/svelte/icons/loader-circle';
	import SearchXIcon from '@lucide/svelte/icons/search-x';

	import Button from '$lib/shad-components/ui/button/button.svelte';
	import * as Select from '$lib/shad-components/ui/select/index.js';
	import * as Table from '$lib/shad-components/ui/table/index.js';
	import { Badge } from '$lib/shad-components/ui/badge';
	import * as DropdownMenu from '$lib/shad-components/ui/dropdown-menu';
	import * as AlertDialog from '$lib/shad-components/ui/alert-dialog/index.js';
	import { buttonVariants } from '$lib/shad-components/ui/button/index.js';

	import { type PageData } from './$types';
	import { toast } from 'svelte-sonner';

	import { setSlots } from '../../layout.slots.svelte';
	import { globalBreadcrumbs, globalNavLinks } from '$lib/stores/side-nav.svelte';
	import SearchForm from '$lib/shad-components/search-form.svelte';
	import { goto } from '$app/navigation';
	import { HouseIcon, NetworkIcon } from '$lib/shared-ui/layoutExports';

	import House from '@lucide/svelte/icons/house';
	import Network from '@lucide/svelte/icons/network';

	let { data }: { data: PageData } = $props();

	let projectItems = $state(data?.projects?.items ?? []);
	let projectData = $state(data?.projects);

	let limit = $state(projectData?.limit ?? 10);
	let offset = $state(projectData?.offset ?? 0);
	let total_items = $state(projectData?.total_items ?? 0);

	let currentPage = $derived(Math.floor(offset / limit) + 1);

	const convertPageToOffset = (page: number) => {
		return (page - 1) * limit;
	};

	setSlots({
		ActionArea,
		Footer: PaginationFooter
	});

	let mounted = $state(false);

	onMount(() => {
		globalBreadcrumbs.crumbs = [
			{
				title: HouseIcon,
				href: '/dashboard'
			},
			{
				title: NetworkIcon,
				href: `/workspaces/${data?.workspace?.id}`
			}
		];

		globalNavLinks.links = [
			{
				title: 'Dashboard',
				url: '/dashboard',

				icon: House
			},
			{
				title: 'Workspace',
				url: `/workspaces/${data?.workspace?.id}`,
				icon: Network
			}
		];

		return () => {};
	});

	const statusFilters = [
		{ value: 'all', label: 'All' },
		{ value: 'archived', label: 'Archived' },
		{ value: 'draft', label: 'Draft' },
		{ value: 'deployed', label: 'Deployed' }
	];

	let selectedProjectStatus = $state('all');
	let search = $state('');
	let projectFetching = $state(false);

	// Improvement 2: Derived state to know if any filter is active
	let isFilterActive = $derived(selectedProjectStatus !== 'all' || search !== '');

	const triggerContent = $derived(
		statusFilters.find((f) => f.value === selectedProjectStatus)?.label ?? 'Project Status'
	);

	const getProjectsAsync = async () => {
		if (projectFetching) return;
		projectFetching = true;

		const apiUrl = new URL(
			`/spark/api/v1/projects/workspaces/${page.params.id}/projects/`,
			page.url.origin
		);
		apiUrl.searchParams.set('offset', String(convertPageToOffset(currentPage)));
		apiUrl.searchParams.set('limit', String(limit));

		// Only add parameters if they are active
		if (selectedProjectStatus !== 'all') {
			apiUrl.searchParams.set('status', selectedProjectStatus);
		}
		if (search) {
			apiUrl.searchParams.set('search', search);
		}

		try {
			const response = await fetch(apiUrl);
			if (!response.ok) throw new Error('Failed to fetch projects');

			const result = await response.json();
			const responseData = result.data; // API data is nested

			projectItems = responseData?.items ?? [];
			offset = responseData?.offset ?? 0;
			limit = responseData?.limit ?? 10;
			total_items = responseData?.total_items ?? 0;
		} catch (error) {
			console.error(error);
			toast.error('Something went wrong while fetching projects.');
		} finally {
			projectFetching = false;
		}
	};

	watch([() => selectedProjectStatus, () => currentPage, () => search], () => {
		if (!mounted) {
			mounted = true;
			return;
		}
		getProjectsAsync();
	});

	const handleSearch = (e: SubmitEvent) => {
		e.preventDefault();
		const formData = new FormData(e.currentTarget as HTMLFormElement);
		search = formData.get('search') as string;
		currentPage = 1; // Reset to first page on new search
	};

	// Improvement 2: Function to clear all active filters
	const clearFilters = () => {
		selectedProjectStatus = 'all';
		search = '';
		currentPage = 1;
	};

	// Delete
	let deleteModal: boolean = $state(false);
	let activeProject: number | null = $state(null);
	let activeProjectProject = $derived(projectItems.find((p) => p.id === activeProject));

	const triggerDelete = (id: number) => {
		activeProject = id;
		deleteModal = true;
	};

	const handleArchive = async (status: 'archived' | 'draft') => {
		if (!activeProject) {
			toast.error('No project selected');
			return;
		}
		if (projectFetching) return;
		projectFetching = true;

		fetch(`/spark/api/v1/projects/${activeProject}/`, {
			method: 'PATCH',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ data: { status } })
		})
			.then(async (res) => {
				if (res.ok) {
					toast.info('Archive operation successful');
					projectFetching = false;
					await getProjectsAsync();
					deleteModal = false;
					activeProject = null;
				} else {
					toast.error('Archive operation failed');
				}
			})
			.catch((err) => {
				console.error(err);
				toast.error('Archive operation failed');
			})
			.finally(() => {
				projectFetching = false;
			});
	};

	const handleDelete = async () => {
		if (!activeProject) {
			toast.error('No project selected');
			return;
		}
		if (projectFetching) return;
		projectFetching = true;
		fetch(`/spark/api/v1/projects/${activeProject}/`, {
			method: 'DELETE'
		})
			.then(() => {
				toast.info('Delete operation successful');
				deleteModal = false;
				activeProject = null;
				getProjectsAsync();
			})
			.catch((err) => {
				console.error(err);
				toast.error('Delete operation failed');
			})
			.finally(() => {
				projectFetching = false;
			});
	};
</script>

<div class="container mx-auto max-w-7xl space-y-6 px-4 py-8">
	<div>
		<h1 class="text-3xl font-bold tracking-tight">Projects</h1>
		<p class="text-muted-foreground">
			A list of projects in the "{page.data.workspace.title}" workspace.
		</p>
	</div>

	<div class="min-h-[400px]">
		{#if !projectFetching && projectItems?.length > 0}
			<div class="rounded-md border">
				<Table.Root>
					<Table.Header>
						<Table.Row>
							<Table.Head class="w-[250px]">Project Name</Table.Head>
							<Table.Head>Status</Table.Head>
							<Table.Head>Domain</Table.Head>
							<Table.Head class="text-right">Actions</Table.Head>
						</Table.Row>
					</Table.Header>
					<Table.Body>
						{#each projectItems as project (project.id)}
							{@const _status = project.status}
							{@const _isArchived = _status === 'archived'}

							<Table.Row>
								<Table.Cell class="font-medium">
									{#if _isArchived}
										{project.name}
									{:else}
										<a href="/projects/{project.id}">
											{project.name}
										</a>
									{/if}
								</Table.Cell>
								<Table.Cell>
									<Badge
										class={`${project.status === 'draft' ? 'bg-blue-500 text-white dark:bg-blue-600' : ''} capitalize`}
										variant={project.status === 'deployed' ? 'default' : 'secondary'}
									>
										{project.status}
									</Badge>
								</Table.Cell>
								<Table.Cell>
									{project.subdomain?.full_uri ?? 'N/A'}
								</Table.Cell>
								<Table.Cell class="text-right">
									<DropdownMenu.Root>
										<DropdownMenu.Trigger>
											{#snippet child({ props })}
												<Button {...props} variant="ghost" class="h-8 w-8 p-0">
													<span class="sr-only">Open menu</span>
													<MoreHorizontal class="h-4 w-4" />
												</Button>
											{/snippet}
										</DropdownMenu.Trigger>
										<DropdownMenu.Content>
											<DropdownMenu.Label>Actions</DropdownMenu.Label>
											<DropdownMenu.Separator />
											{#if !_isArchived}
												<DropdownMenu.Item onclick={() => goto(`/projects/${project.id}`)}>
													View Details
												</DropdownMenu.Item>
											{/if}
											{#if !_isArchived}
												<DropdownMenu.Item onclick={() => goto(`/projects/${project.id}/edit`)}>
													Edit
												</DropdownMenu.Item>
											{/if}
											<DropdownMenu.Item
												onclick={() => {
													activeProject = project.id;

													if (_isArchived) {
														handleArchive('draft');
														return;
													}
													handleArchive('archived');
												}}
											>
												{#if _isArchived}
													Restore
												{:else}
													Archive
												{/if}
											</DropdownMenu.Item>
											<DropdownMenu.Item
												onclick={() => {
													triggerDelete(project.id);
												}}
												class="text-red-600 focus:bg-red-50 focus:text-red-700"
											>
												Delete
											</DropdownMenu.Item>
										</DropdownMenu.Content>
									</DropdownMenu.Root>
								</Table.Cell>
							</Table.Row>
						{/each}
					</Table.Body>
				</Table.Root>
			</div>
		{:else if !projectFetching && isFilterActive}
			<div
				class="flex h-full min-h-[400px] w-full items-center justify-center rounded-md border border-dashed"
			>
				<div class="flex flex-col items-center justify-center text-center">
					<SearchXIcon class="text-muted-foreground h-12 w-12" />
					<h3 class="mt-4 text-2xl font-bold tracking-tight">No projects found</h3>
					<p class="text-muted-foreground mb-4">
						Your search or filter criteria did not match any projects.
					</p>
					<Button onclick={clearFilters} variant="ghost">Clear Filters</Button>
				</div>
			</div>
		{:else if !projectFetching && !isFilterActive}
			<div
				class="flex h-full min-h-[400px] w-full items-center justify-center rounded-md border border-dashed"
			>
				<div class="flex flex-col items-center justify-center text-center">
					<h3 class="text-2xl font-bold tracking-tight">You have no projects yet</h3>
					<p class="text-muted-foreground mb-4">Get started by creating a new project.</p>
					<Button href={`/projects/create?workspaceId=${page.params.id}`}>
						<Plus class="mr-2 h-4 w-4" />
						New Project
					</Button>
				</div>
			</div>
		{/if}
	</div>
</div>

{#snippet ActionArea()}
	<div class="flex flex-col-reverse gap-4 lg:flex-row">
		<div class="flex gap-4">
			{#if projectFetching}
				<div class="my-auto flex h-full items-center justify-center">
					<LoaderCircleIcon class="animate-spin" />
				</div>
			{/if}
			<Select.Root
				type="single"
				disabled={projectFetching}
				name="project_status"
				bind:value={selectedProjectStatus}
			>
				<Select.Trigger class="w-[180px]">
					{triggerContent}
				</Select.Trigger>
				<Select.Content>
					<Select.Group>
						<Select.Label>Project Status</Select.Label>
						{#each statusFilters as status (status.value)}
							<Select.Item value={status.value} label={status.label}>
								{status.label}
							</Select.Item>
						{/each}
					</Select.Group>
				</Select.Content>
			</Select.Root>
			<Button href={`/projects/create?workspaceId=${page.params.id}`} class="ml-auto">
				<Plus class="mr-2 h-4 w-4" />
				Create Project
			</Button>
		</div>
		<SearchForm onsubmit={handleSearch} class="w-full sm:w-auto" />
	</div>
{/snippet}

{#snippet PaginationFooter()}
	{#if !projectFetching && projectItems?.length > 0}
		<Pagination {total_items} {limit} bind:page={currentPage} fetching={projectFetching} />
	{/if}
{/snippet}

<AlertDialog.Root bind:open={deleteModal}>
	<AlertDialog.Content>
		<AlertDialog.Header>
			<AlertDialog.Title>Are you absolutely sure?</AlertDialog.Title>
			<AlertDialog.Description>
				This action cannot be undone. This will permanently delete your account and remove your data
				from our servers.
			</AlertDialog.Description>
		</AlertDialog.Header>
		<AlertDialog.Footer>
			<div class="flex w-full justify-between">
				<AlertDialog.Action
					disabled={projectFetching}
					onclick={() => {
						if (!activeProjectProject) return;
						if (activeProjectProject?.status === 'archived') {
							handleArchive('draft');
							return;
						}
						handleArchive('archived');
					}}
					class={buttonVariants({
						variant: 'default'
					})}
				>
					{#if projectFetching}
						<Loader2Icon class="animate-spin" />
					{/if}

					{#if activeProjectProject?.status === 'archived'}
						Restore
					{:else}
						Archive
					{/if}
				</AlertDialog.Action>
				<div>
					<AlertDialog.Cancel disabled={projectFetching}>Cancel</AlertDialog.Cancel>
					<AlertDialog.Action
						disabled={projectFetching}
						onclick={handleDelete}
						class={buttonVariants({
							variant: 'destructive'
						})}
					>
						{#if projectFetching}
							<Loader2Icon class="animate-spin" />
						{/if}

						Delete</AlertDialog.Action
					>
				</div>
			</div>
		</AlertDialog.Footer>
	</AlertDialog.Content>
</AlertDialog.Root>
