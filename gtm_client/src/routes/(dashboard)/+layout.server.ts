import { redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';
import { OrganizationAPIService } from '$lib/api/organizations';

export const load: LayoutServerLoad = async (event) => {
	event.depends('app:invitations');
	if (!event.locals.user) {
		console.log('User not found, redirecting to login');
		redirect(302, '/');
		return;
	}

	console.log('User found, loading dashboard');
	const organizationAPIService = new OrganizationAPIService(event);
	return {
		user: event.locals.user,
		personalInvitations: await organizationAPIService.getUserPendingInvitations()
	};
};
