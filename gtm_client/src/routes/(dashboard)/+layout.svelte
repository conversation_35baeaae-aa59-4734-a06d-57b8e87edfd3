<script lang="ts">
	import AppSidebar from '$lib/shad-components/app-sidebar.svelte';
	import SiteHeader from '$lib/shad-components/site-header.svelte';
	import * as Sidebar from '$lib/shad-components/ui/sidebar/index.js';
	import InvitationBanner from '$lib/components/InvitationBanner.svelte';
	import AIAgentSidebar from '$lib/components/AIAgentSidebar.svelte';
	import { initSlots } from './layout.slots.svelte';

	let { children } = $props();

	const slots = initSlots();
	let aiAgentOpen = $state(false);
</script>

{#snippet ActionArea()}
	<div class="mx-4">
		{@render slots?.ActionArea()}
	</div>
{/snippet}

<!-- Invitation Banner - Fixed at top -->

<div class="h-screen [--header-height:calc(--spacing(14))]">
	<Sidebar.Provider class="flex h-full flex-col">
		<SiteHeader children={ActionArea} />
		<InvitationBanner />
		<div class="flex flex-1 overflow-hidden">
			<AppSidebar />
			<Sidebar.Inset class="relative flex-1 overflow-auto">
				<div class="mt-4 block lg:hidden">
					{@render ActionArea()}
				</div>
				{@render children()}
				{#if slots?.Footer}
					<div class="bg-sidebar fixed bottom-0 w-full p-4">
						{@render slots?.Footer()}
					</div>
				{/if}
			</Sidebar.Inset>

			<!-- AI Agent Sidebar -->
			<AIAgentSidebar bind:isOpen={aiAgentOpen} />
		</div>
	</Sidebar.Provider>
</div>
