<script lang="ts">
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import { organizationAPIService } from '$lib/api/organizations';

	import * as Card from '$lib/shad-components/ui/card';
	import * as Alert from '$lib/shad-components/ui/alert';
	import Button from '$lib/shad-components/ui/button/button.svelte';
	import { Separator } from '$lib/shad-components/ui/separator';

	import CheckCircleIcon from '@lucide/svelte/icons/check-circle';
	import XCircleIcon from '@lucide/svelte/icons/x-circle';
	import MailIcon from '@lucide/svelte/icons/mail';
	import UsersIcon from '@lucide/svelte/icons/users';
	import AlertTriangleIcon from '@lucide/svelte/icons/alert-triangle';
	import LoaderIcon from '@lucide/svelte/icons/loader';

	let token = $state('');
	let isLoading = $state(false);
	let isProcessing = $state(false);
	let error = $state<string | null>(null);
	let success = $state(false);
	let organizationName = $state('');
	let inviterName = $state('');

	// Get token from URL params
	$effect(() => {
		token = $page.params.token || '';
	});

	onMount(() => {
		// Auto-load invitation details if token is available
		if (token) {
			// For now, we'll just show the accept/decline interface
			// In a real implementation, you might want to fetch invitation details first
		}
	});

	const acceptInvitation = async () => {
		if (!token) return;

		isProcessing = true;
		error = null;

		try {
			const result = await organizationAPIService.acceptInvitation(token);
			organizationName = result.organization.name;
			success = true;

			// Redirect to teams page after a short delay
			setTimeout(() => {
				goto('/teams');
			}, 2000);
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to accept invitation';
		} finally {
			isProcessing = false;
		}
	};

	const declineInvitation = async () => {
		if (!token) return;

		isProcessing = true;
		error = null;

		try {
			await organizationAPIService.declineInvitation(token);
			success = true;

			// Redirect to dashboard after a short delay
			setTimeout(() => {
				goto('/dashboard');
			}, 2000);
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to decline invitation';
		} finally {
			isProcessing = false;
		}
	};
</script>

<svelte:head>
	<title>Team Invitation</title>
</svelte:head>

<div class="bg-background flex min-h-screen items-center justify-center p-4">
	<div class="w-full max-w-md space-y-6">
		{#if success}
			<Card.Root>
				<Card.Content class="p-6">
					<div class="space-y-4 text-center">
						<div
							class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100"
						>
							<CheckCircleIcon class="h-6 w-6 text-green-600" />
						</div>
						<div>
							<h2 class="text-xl font-semibold">Invitation Processed</h2>
							<p class="text-muted-foreground mt-2">
								{#if organizationName}
									You have successfully joined {organizationName}!
								{:else}
									Your response has been recorded.
								{/if}
							</p>
						</div>
						<p class="text-muted-foreground text-sm">Redirecting you to the appropriate page...</p>
					</div>
				</Card.Content>
			</Card.Root>
		{:else}
			<Card.Root>
				<Card.Header class="text-center">
					<div
						class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100"
					>
						<MailIcon class="h-6 w-6 text-blue-600" />
					</div>
					<Card.Title class="text-xl">Team Invitation</Card.Title>
					<Card.Description>You've been invited to join a team organization</Card.Description>
				</Card.Header>

				<Card.Content class="space-y-6">
					{#if error}
						<Alert.Root variant="destructive">
							<AlertTriangleIcon class="h-4 w-4" />
							<Alert.Title>Error</Alert.Title>
							<Alert.Description>{error}</Alert.Description>
						</Alert.Root>
					{/if}

					{#if !token}
						<Alert.Root variant="destructive">
							<XCircleIcon class="h-4 w-4" />
							<Alert.Title>Invalid Invitation</Alert.Title>
							<Alert.Description>
								The invitation link appears to be invalid or expired.
							</Alert.Description>
						</Alert.Root>
					{:else}
						<div class="space-y-4">
							<div class="bg-muted/30 space-y-3 rounded-lg p-4">
								<div class="flex items-center gap-2">
									<UsersIcon class="text-muted-foreground h-4 w-4" />
									<span class="text-sm font-medium">Organization Details</span>
								</div>
								<div class="text-muted-foreground text-sm">
									<p>You'll gain access to:</p>
									<ul class="mt-2 list-inside list-disc space-y-1">
										<li>Shared workspaces and projects</li>
										<li>Collaborative team features</li>
										<li>Organization-wide settings</li>
									</ul>
								</div>
							</div>

							<div class="space-y-3">
								<p class="text-muted-foreground text-center text-sm">
									Would you like to accept this invitation?
								</p>

								<div class="grid grid-cols-2 gap-3">
									<Button
										variant="outline"
										onclick={declineInvitation}
										disabled={isProcessing}
										class="w-full"
									>
										{#if isProcessing}
											<LoaderIcon class="mr-2 h-4 w-4 animate-spin" />
										{:else}
											<XCircleIcon class="mr-2 h-4 w-4" />
										{/if}
										Decline
									</Button>

									<Button onclick={acceptInvitation} disabled={isProcessing} class="w-full">
										{#if isProcessing}
											<LoaderIcon class="mr-2 h-4 w-4 animate-spin" />
										{:else}
											<CheckCircleIcon class="mr-2 h-4 w-4" />
										{/if}
										Accept
									</Button>
								</div>
							</div>
						</div>
					{/if}
				</Card.Content>

				<Card.Footer class="text-center">
					<p class="text-muted-foreground text-xs">
						By accepting, you agree to join the organization and follow its guidelines.
					</p>
				</Card.Footer>
			</Card.Root>
		{/if}

		<div class="text-center">
			<Button variant="ghost" onclick={() => goto('/dashboard')}>← Back to Dashboard</Button>
		</div>
	</div>
</div>
