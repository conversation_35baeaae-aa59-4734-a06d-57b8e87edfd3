<script lang="ts">
	import { onMount } from 'svelte';
	import { organizationAPIService, type OrganizationInvitation } from '$lib/api/organizations';
	import { Button } from '$lib/shad-components/ui/button';
	import { Card } from '$lib/shad-components/ui/card';
	import { Badge } from '$lib/shad-components/ui/badge';
	import { Alert } from '$lib/shad-components/ui/alert';
	import { withLoadingBar } from '$lib/utils/navigation';
	import { toast } from 'svelte-sonner';
	import MailIcon from '@lucide/svelte/icons/mail';
	import CheckIcon from '@lucide/svelte/icons/check';
	import XIcon from '@lucide/svelte/icons/x';
	import ClockIcon from '@lucide/svelte/icons/clock';
	import BuildingIcon from '@lucide/svelte/icons/building';
	import UserIcon from '@lucide/svelte/icons/user';
	import { page } from '$app/state';

	let pendingInvitations = $derived<OrganizationInvitation[]>(page.data.invitations ?? []);
	let isLoading = $state(true);
	let error = $state<string | null>(null);

	async function acceptInvitation(invitation: OrganizationInvitation) {
		try {
			await withLoadingBar(async () => {
				await organizationAPIService.acceptInvitation(invitation.token);
			});

			toast.success(`Successfully joined ${invitation.organization.name}!`);

			// Remove from pending list
			pendingInvitations = pendingInvitations.filter((inv) => inv.id !== invitation.id);

			// Refresh the page to update organization context
			setTimeout(() => {
				window.location.reload();
			}, 1000);
		} catch (error) {
			toast.error(error instanceof Error ? error.message : 'Failed to accept invitation');
		}
	}

	async function declineInvitation(invitation: OrganizationInvitation) {
		try {
			await withLoadingBar(async () => {
				await organizationAPIService.declineInvitation(invitation.token);
			});

			toast.success('Invitation declined');

			// Remove from pending list
			pendingInvitations = pendingInvitations.filter((inv) => inv.id !== invitation.id);
		} catch (error) {
			toast.error(error instanceof Error ? error.message : 'Failed to decline invitation');
		}
	}

	function formatTimeRemaining(expiresAt: string): string {
		const now = new Date();
		const expiry = new Date(expiresAt);
		const diffMs = expiry.getTime() - now.getTime();

		if (diffMs <= 0) return 'Expired';

		const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
		const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

		if (days > 0) return `${days}d ${hours}h remaining`;
		if (hours > 0) return `${hours}h remaining`;

		const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
		return `${minutes}m remaining`;
	}

	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}
</script>

<svelte:head>
	<title>Pending Invitations</title>
</svelte:head>

<div class="container mx-auto px-4 py-8">
	<div class="mb-8">
		<h1 class="text-3xl font-bold tracking-tight">Pending Invitations</h1>
		<p class="text-muted-foreground mt-2">Review and respond to organization invitations</p>
	</div>

	{#if error}
		<Alert.Root variant="destructive" class="mb-6">
			<Alert.Title>Error</Alert.Title>
			<Alert.Description>{error}</Alert.Description>
		</Alert.Root>
	{/if}

	{#if isLoading}
		<div class="flex items-center justify-center py-12">
			<div class="text-center">
				<div class="border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2"></div>
				<p class="text-muted-foreground">Loading invitations...</p>
			</div>
		</div>
	{:else if pendingInvitations.length === 0}
		<Card.Root class="py-12 text-center">
			<Card.Content class="pt-6">
				<MailIcon class="text-muted-foreground mx-auto mb-4 h-12 w-12" />
				<h3 class="mb-2 text-lg font-semibold">No pending invitations</h3>
				<p class="text-muted-foreground mb-4">
					You don't have any pending organization invitations at the moment.
				</p>
				<Button onclick={() => (window.location.href = '/teams')}>Go to Teams</Button>
			</Card.Content>
		</Card.Root>
	{:else}
		<div class="grid gap-6">
			{#each pendingInvitations as invitation (invitation.id)}
				<Card.Root class="overflow-hidden">
					<Card.Content class="p-6">
						<div class="flex items-start justify-between gap-6">
							<div class="flex-1">
								<div class="mb-3 flex items-center gap-3">
									<BuildingIcon class="text-muted-foreground h-5 w-5" />
									<h3 class="text-xl font-semibold">
										{invitation.organization.name}
									</h3>
									<Badge variant="secondary">
										{invitation.role}
									</Badge>
								</div>

								<div class="text-muted-foreground space-y-2 text-sm">
									<div class="flex items-center gap-2">
										<UserIcon class="h-4 w-4" />
										<span>
											Invited by {invitation.invited_by.first_name}
											{invitation.invited_by.last_name}
											({invitation.invited_by.email})
										</span>
									</div>

									<div class="flex items-center gap-2">
										<ClockIcon class="h-4 w-4" />
										<span>
											Sent {formatDate(invitation.created_at)} •
											{formatTimeRemaining(invitation.expires_at)}
										</span>
									</div>
								</div>

								{#if invitation.can_view || invitation.can_create || invitation.can_edit || invitation.can_delete}
									<div class="mt-4">
										<h4 class="mb-2 text-sm font-medium">Permissions:</h4>
										<div class="flex flex-wrap gap-2">
											{#if invitation.can_view}
												<Badge variant="outline" class="text-xs">View</Badge>
											{/if}
											{#if invitation.can_create}
												<Badge variant="outline" class="text-xs">Create</Badge>
											{/if}
											{#if invitation.can_edit}
												<Badge variant="outline" class="text-xs">Edit</Badge>
											{/if}
											{#if invitation.can_delete}
												<Badge variant="outline" class="text-xs">Delete</Badge>
											{/if}
										</div>
									</div>
								{/if}
							</div>

							<div class="flex min-w-[120px] flex-col gap-2">
								<Button onclick={() => acceptInvitation(invitation)} class="w-full">
									<CheckIcon class="mr-2 h-4 w-4" />
									Accept
								</Button>

								<Button
									variant="outline"
									onclick={() => declineInvitation(invitation)}
									class="w-full"
								>
									<XIcon class="mr-2 h-4 w-4" />
									Decline
								</Button>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	{/if}
</div>
