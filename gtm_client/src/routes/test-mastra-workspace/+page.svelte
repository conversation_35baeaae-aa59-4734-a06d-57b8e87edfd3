<script lang="ts">
	import MastraChatAgent from '$lib/components/MastraChatAgent.svelte';
</script>

<div class="container mx-auto p-8">
	<h1 class="mb-6 text-3xl font-bold">Test Mastra Chat Agent</h1>

	<div class="space-y-6">
		<!-- Info Card -->
		<div class="alert alert-info">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 24 24"
				class="h-6 w-6 shrink-0 stroke-current"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
				></path>
			</svg>
			<div>
				<h3 class="font-bold">Mastra Chat Agent with Google Gemini</h3>
				<div class="text-sm">
					This chat agent uses Google Gemini (gemini-1.5-pro) and has access to workspace tools. Try
					asking natural language questions!
				</div>
			</div>
		</div>

		<!-- Chat Interface -->
		<div class="card bg-base-100 shadow-xl">
			<div class="card-body">
				<h2 class="card-title">Chat with GTM Mixer Assistant</h2>
				<MastraChatAgent />
			</div>
		</div>

		<!-- Example Queries -->
		<div class="card bg-base-200">
			<div class="card-body">
				<h3 class="mb-2 font-semibold">Example Queries:</h3>
				<ul class="list-inside list-disc space-y-1 text-sm">
					<li>"List all my workspaces"</li>
					<li>"Create a workspace called Marketing with description 'Q1 2024 campaigns'"</li>
					<li>"Show me workspace 5"</li>
					<li>"Update workspace 3 to have title 'Sales Team'"</li>
					<li>"Delete workspace 7"</li>
				</ul>
			</div>
		</div>
	</div>
</div>
