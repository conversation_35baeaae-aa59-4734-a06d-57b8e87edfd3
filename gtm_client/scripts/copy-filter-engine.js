#!/usr/bin/env node

/**
 * Copy Filter Engine Script
 *
 * This script copies the standalone filter-engine into the GTM client
 * and ensures it's properly integrated for build processes.
 */

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Paths
const projectRoot = path.resolve(__dirname, '../../..');
const filterEngineSource = path.join(projectRoot, 'filter-engine');
const filterEngineTarget = path.join(__dirname, '../src/filter-engine');

async function copyFilterEngine() {
	try {
		console.log('🔄 Copying filter engine...');

		// Ensure target directory exists
		await fs.ensureDir(filterEngineTarget);

		// Files to copy
		const filesToCopy = ['types.ts', 'engine.ts', 'early-hints.ts', 'index.ts', 'demo-entry.ts'];

		// Copy each file
		for (const file of filesToCopy) {
			const sourcePath = path.join(filterEngineSource, file);
			const targetPath = path.join(filterEngineTarget, file);

			if (await fs.pathExists(sourcePath)) {
				await fs.copy(sourcePath, targetPath);
				console.log(`✅ Copied ${file}`);
			} else {
				console.warn(`⚠️  Source file not found: ${file}`);
			}
		}

		// Copy package.json if it exists (for dependencies)
		const packageJsonSource = path.join(filterEngineSource, 'package.json');
		if (await fs.pathExists(packageJsonSource)) {
			const packageJson = await fs.readJson(packageJsonSource);

			// Extract dependencies that might be needed
			const dependencies = packageJson.dependencies || {};
			const devDependencies = packageJson.devDependencies || {};

			console.log('📦 Filter engine dependencies:', Object.keys(dependencies));
			console.log('🔧 Filter engine dev dependencies:', Object.keys(devDependencies));
		}

		// Create a README for the copied engine
		const readmeContent = `# Filter Engine (Copied)

This directory contains a copy of the standalone filter-engine for integration
with the GTM client.

## Source
Copied from: \`${path.relative(filterEngineTarget, filterEngineSource)}\`

## Integration
- Frontend integration: \`src/lib/filter-engine/index.ts\`
- Backend integration: \`backend/filter_engine/\`
- API service: \`src/lib/api/filterEngine.ts\`
- Hooks: \`src/lib/hooks/useFilterEngine.ts\`

## Updating
To update this copy, run:
\`\`\`bash
npm run copy-filter-engine
\`\`\`

## Files
${filesToCopy.map((file) => `- ${file}`).join('\n')}
`;

		await fs.writeFile(path.join(filterEngineTarget, 'README.md'), readmeContent);

		console.log('✅ Filter engine copied successfully!');
		console.log(`📁 Target: ${filterEngineTarget}`);
	} catch (error) {
		console.error('❌ Failed to copy filter engine:', error);
		process.exit(1);
	}
}

// Check if filter engine source exists
async function checkSource() {
	const exists = await fs.pathExists(filterEngineSource);
	if (!exists) {
		console.error(`❌ Filter engine source not found: ${filterEngineSource}`);
		console.log('💡 Make sure the filter-engine directory exists in the project root');
		process.exit(1);
	}
	return true;
}

// Main execution
async function main() {
	console.log('🚀 Filter Engine Copy Script');
	console.log(`📂 Source: ${filterEngineSource}`);
	console.log(`📂 Target: ${filterEngineTarget}`);

	await checkSource();
	await copyFilterEngine();

	console.log('🎉 Done!');
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
	main().catch(console.error);
}

export { copyFilterEngine, checkSource };
